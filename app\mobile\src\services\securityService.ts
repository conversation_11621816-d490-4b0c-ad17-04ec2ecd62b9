import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import * as Keychain from 'react-native-keychain';
import Jail<PERSON>onkey from 'jail-monkey';
import DeviceInfo from 'react-native-device-info';
import EncryptedStorage from 'react-native-encrypted-storage';
import BackgroundTimer from 'react-native-background-timer';
import CryptoJS from 'react-native-crypto-js';
import { Platform } from 'react-native';

interface SecurityConfig {
  enableBiometrics: boolean;
  enablePinCode: boolean;
  autoLockTimeout: number; // in minutes
  requireReauthOnForeground: boolean;
  enableScreenshotPrevention: boolean;
  enableDeviceBinding: boolean;
}

interface PinAttempt {
  count: number;
  lastAttempt: number;
  lockedUntil?: number;
}

class SecurityService {
  private rnBiometrics = new ReactNativeBiometrics({ allowDeviceCredentials: true });
  private backgroundTimer: number | null = null;
  private lastActiveTime: number = Date.now();
  private readonly MAX_PIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly PIN_ATTEMPT_KEY = 'pin_attempts';
  private readonly DEVICE_ID_KEY = 'device_binding_id';
  private readonly SECURITY_CONFIG_KEY = 'security_config';

  async initialize(): Promise<void> {
    // Set up background timer for auto-lock
    this.startBackgroundTimer();
    
    // Check if device is compromised
    await this.checkDeviceSecurity();
    
    // Initialize device binding if enabled
    const config = await this.getSecurityConfig();
    if (config.enableDeviceBinding) {
      await this.initializeDeviceBinding();
    }
  }

  async checkDeviceSecurity(): Promise<{ isSecure: boolean; issues: string[] }> {
    const issues: string[] = [];

    // Check for jailbreak/root
    if (JailMonkey.isJailBroken()) {
      issues.push('Device is jailbroken/rooted');
    }

    // Check for debugging
    if (JailMonkey.isDebuggedMode()) {
      issues.push('App is running in debug mode');
    }

    // Check for external storage (Android)
    if (Platform.OS === 'android' && JailMonkey.isOnExternalStorage()) {
      issues.push('App is installed on external storage');
    }

    // Check for ADB enabled (Android)
    if (Platform.OS === 'android' && JailMonkey.AdbEnabled()) {
      issues.push('ADB is enabled');
    }

    return {
      isSecure: issues.length === 0,
      issues,
    };
  }

  async checkBiometricAvailability(): Promise<{
    available: boolean;
    biometryType: BiometryTypes | null;
    error?: string;
  }> {
    try {
      const { available, biometryType, error } = await this.rnBiometrics.isSensorAvailable();
      return { available, biometryType: biometryType as BiometryTypes | null, error };
    } catch (error) {
      return { available: false, biometryType: null, error: error.message };
    }
  }

  async authenticateWithBiometrics(promptMessage?: string): Promise<boolean> {
    try {
      const { available } = await this.checkBiometricAvailability();
      if (!available) {
        throw new Error('Biometric authentication not available');
      }

      const { success } = await this.rnBiometrics.simplePrompt({
        promptMessage: promptMessage || 'Authenticate to access your account',
        cancelButtonText: 'Cancel',
        fallbackPromptMessage: 'Use PIN',
      });

      return success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }

  async storeSecureToken(token: string, refreshToken?: string): Promise<void> {
    try {
      // Store tokens in Keychain for iOS and Android Keystore
      await Keychain.setInternetCredentials(
        'electrical-contractor-app',
        'auth-token',
        token,
        {
          accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
          authenticatePrompt: 'Authenticate to save credentials',
          authenticationPromptTitle: 'Authentication Required',
        }
      );

      if (refreshToken) {
        await Keychain.setInternetCredentials(
          'electrical-contractor-app',
          'refresh-token',
          refreshToken,
          {
            accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
          }
        );
      }

      // Also store encrypted version for redundancy
      const encryptedToken = this.encryptData(token);
      await EncryptedStorage.setItem('auth_token_backup', encryptedToken);
    } catch (error) {
      console.error('Error storing secure token:', error);
      throw error;
    }
  }

  async getSecureToken(): Promise<{ token: string | null; refreshToken: string | null }> {
    try {
      const authCredentials = await Keychain.getInternetCredentials('electrical-contractor-app');
      const refreshCredentials = await Keychain.getInternetCredentials('electrical-contractor-app');

      return {
        token: authCredentials ? authCredentials.password : null,
        refreshToken: refreshCredentials ? refreshCredentials.password : null,
      };
    } catch (error) {
      console.error('Error retrieving secure token:', error);
      
      // Try to retrieve from encrypted storage as fallback
      try {
        const encryptedToken = await EncryptedStorage.getItem('auth_token_backup');
        if (encryptedToken) {
          const token = this.decryptData(encryptedToken);
          return { token, refreshToken: null };
        }
      } catch (fallbackError) {
        console.error('Fallback retrieval failed:', fallbackError);
      }
      
      return { token: null, refreshToken: null };
    }
  }

  async clearSecureTokens(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials('electrical-contractor-app');
      await EncryptedStorage.removeItem('auth_token_backup');
    } catch (error) {
      console.error('Error clearing secure tokens:', error);
    }
  }

  async setPinCode(pin: string): Promise<void> {
    if (pin.length < 4 || pin.length > 8) {
      throw new Error('PIN must be between 4 and 8 digits');
    }

    const hashedPin = this.hashPin(pin);
    await EncryptedStorage.setItem('user_pin', hashedPin);
    await this.resetPinAttempts();
  }

  async verifyPinCode(pin: string): Promise<boolean> {
    try {
      // Check if account is locked
      const attempts = await this.getPinAttempts();
      if (attempts.lockedUntil && attempts.lockedUntil > Date.now()) {
        const remainingTime = Math.ceil((attempts.lockedUntil - Date.now()) / 60000);
        throw new Error(`Account locked. Try again in ${remainingTime} minutes`);
      }

      const storedPin = await EncryptedStorage.getItem('user_pin');
      if (!storedPin) {
        throw new Error('No PIN set');
      }

      const hashedPin = this.hashPin(pin);
      const isValid = storedPin === hashedPin;

      if (isValid) {
        await this.resetPinAttempts();
      } else {
        await this.incrementPinAttempts();
      }

      return isValid;
    } catch (error) {
      console.error('PIN verification error:', error);
      throw error;
    }
  }

  async hasPinCode(): Promise<boolean> {
    try {
      const pin = await EncryptedStorage.getItem('user_pin');
      return !!pin;
    } catch (error) {
      return false;
    }
  }

  private hashPin(pin: string): string {
    // Add salt for additional security
    const salt = 'electrical-contractor-2024';
    return CryptoJS.SHA256(pin + salt).toString();
  }

  private async getPinAttempts(): Promise<PinAttempt> {
    try {
      const attemptsStr = await EncryptedStorage.getItem(this.PIN_ATTEMPT_KEY);
      if (attemptsStr) {
        return JSON.parse(attemptsStr);
      }
    } catch (error) {
      console.error('Error getting PIN attempts:', error);
    }
    return { count: 0, lastAttempt: Date.now() };
  }

  private async incrementPinAttempts(): Promise<void> {
    const attempts = await this.getPinAttempts();
    attempts.count += 1;
    attempts.lastAttempt = Date.now();

    if (attempts.count >= this.MAX_PIN_ATTEMPTS) {
      attempts.lockedUntil = Date.now() + this.LOCKOUT_DURATION;
    }

    await EncryptedStorage.setItem(this.PIN_ATTEMPT_KEY, JSON.stringify(attempts));
  }

  private async resetPinAttempts(): Promise<void> {
    await EncryptedStorage.removeItem(this.PIN_ATTEMPT_KEY);
  }

  async getSecurityConfig(): Promise<SecurityConfig> {
    try {
      const configStr = await EncryptedStorage.getItem(this.SECURITY_CONFIG_KEY);
      if (configStr) {
        return JSON.parse(configStr);
      }
    } catch (error) {
      console.error('Error getting security config:', error);
    }

    // Default config
    return {
      enableBiometrics: true,
      enablePinCode: true,
      autoLockTimeout: 5, // 5 minutes
      requireReauthOnForeground: true,
      enableScreenshotPrevention: true,
      enableDeviceBinding: true,
    };
  }

  async setSecurityConfig(config: Partial<SecurityConfig>): Promise<void> {
    const currentConfig = await this.getSecurityConfig();
    const newConfig = { ...currentConfig, ...config };
    await EncryptedStorage.setItem(this.SECURITY_CONFIG_KEY, JSON.stringify(newConfig));
    
    // Restart background timer if timeout changed
    if (config.autoLockTimeout !== undefined) {
      this.startBackgroundTimer();
    }
  }

  private startBackgroundTimer(): void {
    // Clear existing timer
    if (this.backgroundTimer) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
    }

    // Check every minute if app should be locked
    this.backgroundTimer = BackgroundTimer.setInterval(async () => {
      const config = await this.getSecurityConfig();
      const timeSinceActive = Date.now() - this.lastActiveTime;
      const timeoutMs = config.autoLockTimeout * 60 * 1000;

      if (timeSinceActive >= timeoutMs) {
        // Trigger auto-lock
        await this.lockApp();
      }
    }, 60000); // Check every minute
  }

  updateLastActiveTime(): void {
    this.lastActiveTime = Date.now();
  }

  async lockApp(): Promise<void> {
    // This will be handled by the auth state management
    await EncryptedStorage.setItem('app_locked', 'true');
  }

  async unlockApp(): Promise<void> {
    await EncryptedStorage.removeItem('app_locked');
    this.updateLastActiveTime();
  }

  async isAppLocked(): Promise<boolean> {
    try {
      const locked = await EncryptedStorage.getItem('app_locked');
      return locked === 'true';
    } catch (error) {
      return false;
    }
  }

  private encryptData(data: string): string {
    const deviceId = DeviceInfo.getUniqueId();
    return CryptoJS.AES.encrypt(data, deviceId).toString();
  }

  private decryptData(encryptedData: string): string {
    const deviceId = DeviceInfo.getUniqueId();
    const bytes = CryptoJS.AES.decrypt(encryptedData, deviceId);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  async initializeDeviceBinding(): Promise<void> {
    try {
      const storedDeviceId = await EncryptedStorage.getItem(this.DEVICE_ID_KEY);
      const currentDeviceId = await DeviceInfo.getUniqueId();

      if (!storedDeviceId) {
        // First time setup
        await EncryptedStorage.setItem(this.DEVICE_ID_KEY, currentDeviceId);
      } else if (storedDeviceId !== currentDeviceId) {
        // Device mismatch - potential security issue
        throw new Error('Device binding mismatch. Please contact support.');
      }
    } catch (error) {
      console.error('Device binding error:', error);
      throw error;
    }
  }

  async verifyDeviceBinding(): Promise<boolean> {
    try {
      const storedDeviceId = await EncryptedStorage.getItem(this.DEVICE_ID_KEY);
      const currentDeviceId = await DeviceInfo.getUniqueId();
      return storedDeviceId === currentDeviceId;
    } catch (error) {
      console.error('Device verification error:', error);
      return false;
    }
  }

  async clearAllSecurityData(): Promise<void> {
    try {
      await this.clearSecureTokens();
      await EncryptedStorage.clear();
      if (this.backgroundTimer) {
        BackgroundTimer.clearInterval(this.backgroundTimer);
      }
    } catch (error) {
      console.error('Error clearing security data:', error);
    }
  }

  cleanup(): void {
    if (this.backgroundTimer) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
    }
  }
}

export const securityService = new SecurityService();
export type { SecurityConfig, PinAttempt };