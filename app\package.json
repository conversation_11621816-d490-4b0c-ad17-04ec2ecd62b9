{"name": "electrical-contracting-saas", "version": "1.0.0", "description": "Electrical contracting application with AI agents", "private": true, "workspaces": ["frontend", "backend", "shared", "agents"], "scripts": {"dev": "pnpm run --parallel dev", "dev:backend": "pnpm --filter @electrical/backend dev", "dev:frontend": "pnpm --filter @electrical/frontend dev", "dev:main": "concurrently \"pnpm --filter @electrical/backend dev\" \"pnpm --filter @electrical/frontend dev\"", "dev:simple": "pnpm --filter @electrical/backend dev & pnpm --filter @electrical/frontend dev", "build": "pnpm run -r build", "test": "pnpm run -r test", "lint": "pnpm run -r lint", "typecheck": "pnpm run -r typecheck"}, "devDependencies": {"@types/node": "^20.11.5", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}}