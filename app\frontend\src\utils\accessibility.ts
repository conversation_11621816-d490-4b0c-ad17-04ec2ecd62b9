/**
 * Accessibility utility functions for the electrical application
 */

/**
 * Focus trap utility for modals and overlays
 */
export class FocusTrap {
  private element: HTMLElement;
  private firstFocusableElement: HTMLElement | null = null;
  private lastFocusableElement: HTMLElement | null = null;
  private previousActiveElement: HTMLElement | null = null;

  constructor(element: HTMLElement) {
    this.element = element;
    this.previousActiveElement = document.activeElement as HTMLElement;
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }

  activate() {
    const focusableElements = this.getFocusableElements();
    
    if (focusableElements.length === 0) return;
    
    this.firstFocusableElement = focusableElements[0];
    this.lastFocusableElement = focusableElements[focusableElements.length - 1];
    
    this.element.addEventListener('keydown', this.handleKeyDown);
    
    // Focus the first focusable element
    this.firstFocusableElement.focus();
  }

  deactivate() {
    this.element.removeEventListener('keydown', this.handleKeyDown);
    
    // Return focus to the previously active element
    if (this.previousActiveElement) {
      this.previousActiveElement.focus();
    }
  }

  private getFocusableElements(): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'textarea:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];

    const elements = this.element.querySelectorAll(focusableSelectors.join(','));
    return Array.from(elements) as HTMLElement[];
  }

  private handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      // Let the parent component handle escape key
      return;
    }

    if (event.key !== 'Tab') return;

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === this.firstFocusableElement) {
        event.preventDefault();
        this.lastFocusableElement?.focus();
      }
    } else {
      // Tab
      if (document.activeElement === this.lastFocusableElement) {
        event.preventDefault();
        this.firstFocusableElement?.focus();
      }
    }
  }
}

/**
 * Announce to screen readers using live regions
 */
export function announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcer = document.createElement('div');
  announcer.setAttribute('role', 'status');
  announcer.setAttribute('aria-live', priority);
  announcer.setAttribute('aria-atomic', 'true');
  announcer.className = 'sr-only';
  announcer.textContent = message;
  
  document.body.appendChild(announcer);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcer);
  }, 1000);
}

/**
 * Generate unique IDs for form field associations
 */
export function generateId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if an element is visible in the viewport
 */
export function isInViewport(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

/**
 * Keyboard navigation utilities
 */
export const KeyboardNavigation = {
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown'
};

/**
 * Handle arrow key navigation in a list
 */
export function handleListKeyboardNavigation(
  event: KeyboardEvent,
  currentIndex: number,
  itemCount: number,
  onSelect: (index: number) => void,
  orientation: 'vertical' | 'horizontal' = 'vertical'
) {
  const { key } = event;
  let newIndex = currentIndex;

  const prevKey = orientation === 'vertical' ? KeyboardNavigation.ARROW_UP : KeyboardNavigation.ARROW_LEFT;
  const nextKey = orientation === 'vertical' ? KeyboardNavigation.ARROW_DOWN : KeyboardNavigation.ARROW_RIGHT;

  switch (key) {
    case prevKey:
      event.preventDefault();
      newIndex = currentIndex > 0 ? currentIndex - 1 : itemCount - 1;
      break;
    case nextKey:
      event.preventDefault();
      newIndex = currentIndex < itemCount - 1 ? currentIndex + 1 : 0;
      break;
    case KeyboardNavigation.HOME:
      event.preventDefault();
      newIndex = 0;
      break;
    case KeyboardNavigation.END:
      event.preventDefault();
      newIndex = itemCount - 1;
      break;
    default:
      return;
  }

  onSelect(newIndex);
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Trap focus within a container (useful for modals)
 */
export function trapFocus(container: HTMLElement) {
  const focusableElements = container.querySelectorAll(
    'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select, [tabindex]:not([tabindex="-1"])'
  );
  const firstFocusableElement = focusableElements[0] as HTMLElement;
  const lastFocusableElement = focusableElements[focusableElements.length - 1] as HTMLElement;

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return;

    if (e.shiftKey) {
      if (document.activeElement === firstFocusableElement) {
        lastFocusableElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastFocusableElement) {
        firstFocusableElement.focus();
        e.preventDefault();
      }
    }
  };

  container.addEventListener('keydown', handleTabKey);
  
  // Focus first element
  firstFocusableElement?.focus();

  return () => {
    container.removeEventListener('keydown', handleTabKey);
  };
}

/**
 * Screen reader only class for visually hidden content
 */
export const srOnly = 'absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0';