import React, { useState, useRef, useEffect } from 'react';
import {
  VStack,
  HStack,
  Text,
  Input,
  Box,
  Center,
  Pressable,
  Icon,
} from 'native-base';
import { TextInput, Vibration } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface PinCodeInputProps {
  length?: number;
  onComplete: (pin: string) => void;
  onBackPress?: () => void;
  error?: string;
  title?: string;
  subtitle?: string;
  hidePin?: boolean;
}

const PinCodeInput: React.FC<PinCodeInputProps> = ({
  length = 6,
  onComplete,
  onBackPress,
  error,
  title = 'Enter PIN',
  subtitle,
  hidePin = true,
}) => {
  const [pin, setPin] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(0);
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    if (pin.length === length) {
      onComplete(pin);
    }
  }, [pin, length, onComplete]);

  useEffect(() => {
    if (error) {
      Vibration.vibrate(100);
      setPin('');
      setFocusedIndex(0);
      inputRefs.current[0]?.focus();
    }
  }, [error]);

  const handleKeyPress = (key: string) => {
    if (key === 'Backspace') {
      handleBackspace();
    } else if (/^\d$/.test(key) && pin.length < length) {
      const newPin = pin + key;
      setPin(newPin);
      if (newPin.length < length) {
        setFocusedIndex(newPin.length);
        inputRefs.current[newPin.length]?.focus();
      }
    }
  };

  const handleBackspace = () => {
    if (pin.length > 0) {
      const newPin = pin.slice(0, -1);
      setPin(newPin);
      setFocusedIndex(newPin.length);
      inputRefs.current[newPin.length]?.focus();
    }
  };

  const handleNumberPadPress = (num: string) => {
    if (num === 'backspace') {
      handleBackspace();
    } else {
      handleKeyPress(num);
    }
  };

  const renderPinDots = () => {
    return (
      <HStack space={3} mb={8}>
        {Array.from({ length }).map((_, index) => (
          <Box
            key={index}
            w={12}
            h={12}
            borderRadius="full"
            borderWidth={2}
            borderColor={
              index < pin.length
                ? 'primary.500'
                : index === focusedIndex
                ? 'primary.400'
                : 'gray.300'
            }
            bg={index < pin.length ? 'primary.500' : 'transparent'}
            alignItems="center"
            justifyContent="center"
          >
            {index < pin.length && hidePin ? (
              <Box w={2} h={2} borderRadius="full" bg="white" />
            ) : index < pin.length ? (
              <Text color="white" fontSize="lg" fontWeight="bold">
                {pin[index]}
              </Text>
            ) : null}
          </Box>
        ))}
      </HStack>
    );
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', 'backspace'],
    ];

    return (
      <VStack space={3}>
        {numbers.map((row, rowIndex) => (
          <HStack key={rowIndex} space={3} justifyContent="center">
            {row.map((num, colIndex) => (
              <Pressable
                key={`${rowIndex}-${colIndex}`}
                onPress={() => num && handleNumberPadPress(num)}
                disabled={!num}
                w={20}
                h={20}
              >
                {({ isPressed }) => (
                  <Center
                    w="100%"
                    h="100%"
                    borderRadius="full"
                    bg={
                      !num
                        ? 'transparent'
                        : isPressed
                        ? 'gray.200'
                        : 'gray.100'
                    }
                  >
                    {num === 'backspace' ? (
                      <Icon
                        as={MaterialIcons}
                        name="backspace"
                        size={6}
                        color="gray.600"
                      />
                    ) : (
                      <Text fontSize="2xl" fontWeight="medium" color="gray.800">
                        {num}
                      </Text>
                    )}
                  </Center>
                )}
              </Pressable>
            ))}
          </HStack>
        ))}
      </VStack>
    );
  };

  return (
    <VStack flex={1} bg="white" safeArea>
      {onBackPress && (
        <Pressable onPress={onBackPress} p={4}>
          <Icon as={MaterialIcons} name="arrow-back" size={6} color="gray.600" />
        </Pressable>
      )}

      <Center flex={1} px={4}>
        <VStack space={2} alignItems="center" mb={8}>
          <Text fontSize="2xl" fontWeight="bold" color="gray.800">
            {title}
          </Text>
          {subtitle && (
            <Text fontSize="md" color="gray.600" textAlign="center">
              {subtitle}
            </Text>
          )}
          {error && (
            <Text fontSize="sm" color="error.500" textAlign="center" mt={2}>
              {error}
            </Text>
          )}
        </VStack>

        {renderPinDots()}

        {/* Hidden input to capture keyboard events */}
        <Input
          ref={(ref) => {
            if (ref) inputRefs.current[0] = ref as any;
          }}
          value={pin}
          onChangeText={() => {}} // Handled by onKeyPress
          onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key)}
          keyboardType="numeric"
          maxLength={length}
          position="absolute"
          opacity={0}
          autoFocus
        />

        {renderNumberPad()}
      </Center>
    </VStack>
  );
};

export default PinCodeInput;