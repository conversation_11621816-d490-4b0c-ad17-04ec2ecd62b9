import { PrismaClient, Panel, Circuit, PanelLoadCalculation } from '@prisma/client';
import { Decimal } from 'decimal.js';
import { PanelSchema, CircuitSchema, PanelLoadCalculationSchema } from '@electrical/shared';
import { z } from 'zod';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';
import { EventEmitter } from 'events';
import { getTransactionService } from './transaction.service';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

export const panelEvents = new EventEmitter();

// Panel CRUD operations
export const panelService = {
  // Create a new panel
  async createPanel(data: z.infer<typeof PanelSchema>) {
    try {
      const panel = await prisma.panel.create({
        data: {
          ...data,
          spaces_used: 0, // Will be calculated from circuits
          created_at: new Date(),
          updated_at: new Date()
        }
      });
      
      logger.info(`Panel created: ${panel.id} - ${panel.name}`);
      panelEvents.emit('panel:created', panel);
      
      return panel;
    } catch (error) {
      logger.error('Error creating panel:', error);
      throw new AppError('Failed to create panel', 500);
    }
  },

  // Get all panels for a project
  async getPanelsByProject(projectId: string) {
    try {
      const panels = await prisma.panel.findMany({
        where: { project_id: projectId },
        include: {
          circuits: {
            orderBy: { circuit_number: 'asc' }
          },
          load_calculations: {
            orderBy: { calculation_date: 'desc' },
            take: 1
          },
          feeding_panels: true,
          fed_from_panel: true
        }
      });
      
      // Calculate spaces used for each panel
      return panels.map(panel => ({
        ...panel,
        spaces_used: panel.circuits.reduce((sum, circuit) => 
          sum + (circuit.is_space ? 0 : circuit.poles), 0
        ),
        latest_load_calculation: panel.load_calculations[0] || null
      }));
    } catch (error) {
      logger.error('Error fetching panels:', error);
      throw new AppError('Failed to fetch panels', 500);
    }
  },

  // Get a single panel with full details
  async getPanel(panelId: string) {
    try {
      const panel = await prisma.panel.findUnique({
        where: { id: panelId },
        include: {
          circuits: {
            orderBy: { circuit_number: 'asc' }
          },
          load_calculations: {
            orderBy: { calculation_date: 'desc' }
          },
          feeding_panels: {
            include: {
              circuits: true
            }
          },
          fed_from_panel: true,
          project: true
        }
      });
      
      if (!panel) {
        throw new AppError('Panel not found', 404);
      }
      
      return panel;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error fetching panel:', error);
      throw new AppError('Failed to fetch panel', 500);
    }
  },

  // Update panel
  async updatePanel(panelId: string, data: Partial<z.infer<typeof PanelSchema>>) {
    try {
      const panel = await prisma.panel.update({
        where: { id: panelId },
        data: {
          ...data,
          updated_at: new Date()
        }
      });
      
      logger.info(`Panel updated: ${panel.id}`);
      panelEvents.emit('panel:updated', panel);
      
      // Recalculate loads if panel configuration changed
      if (data.ampere_rating || data.voltage_system) {
        await this.calculatePanelLoad(panelId);
      }
      
      return panel;
    } catch (error) {
      logger.error('Error updating panel:', error);
      throw new AppError('Failed to update panel', 500);
    }
  },

  // Delete panel (cascade delete circuits and calculations)
  async deletePanel(panelId: string) {
    try {
      // Check if any panels are fed from this panel
      const dependentPanels = await prisma.panel.findMany({
        where: { fed_from_panel_id: panelId }
      });
      
      if (dependentPanels.length > 0) {
        throw new AppError('Cannot delete panel with dependent sub-panels', 400);
      }
      
      await prisma.panel.delete({
        where: { id: panelId }
      });
      
      logger.info(`Panel deleted: ${panelId}`);
      panelEvents.emit('panel:deleted', panelId);
      
      return true;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting panel:', error);
      throw new AppError('Failed to delete panel', 500);
    }
  },

  // Calculate panel load with transaction support
  async calculatePanelLoad(panelId: string, userId: string = 'system') {
    // Use transaction for load calculation to ensure consistency
    const result = await transactionService.executeTransaction(async (tx) => {
      return this._calculatePanelLoadInTransaction(tx, panelId, userId);
    });

    if (!result.success) {
      throw result.error || new AppError('Failed to calculate panel load', 500);
    }

    return result.data;
  },

  // Internal method for calculating panel load within a transaction
  async _calculatePanelLoadInTransaction(tx: PrismaClient, panelId: string, userId: string) {
    try {
      const panel = await tx.panel.findUnique({
        where: { id: panelId },
        include: {
          circuits: true,
          feeding_panels: {
            include: {
              circuits: true
            }
          }
        }
      });
      
      if (!panel) {
        throw new AppError('Panel not found', 404);
      }
      
      // Initialize load values
      let phaseALoad = new Decimal(0);
      let phaseBLoad = new Decimal(0);
      let phaseCLoad = new Decimal(0);
      let neutralLoad = new Decimal(0);
      let totalConnectedLoad = new Decimal(0);
      let totalDemandLoad = new Decimal(0);
      
      // Calculate loads from circuits
      for (const circuit of panel.circuits) {
        if (circuit.is_spare || circuit.is_space) continue;
        
        const circuitLoad = new Decimal(circuit.connected_load);
        const demandLoad = circuitLoad.mul(circuit.demand_factor);
        
        // Apply continuous load factor (125% for continuous loads per NEC 210.19(A)(1))
        const calculatedLoad = circuit.continuous_load 
          ? demandLoad.mul(1.25) 
          : demandLoad;
        
        totalConnectedLoad = totalConnectedLoad.plus(circuitLoad);
        totalDemandLoad = totalDemandLoad.plus(calculatedLoad);
        
        // Distribute load across phases
        switch (circuit.poles) {
          case 1:
            // Single pole - assign to appropriate phase based on circuit number
            if (panel.phase_config === 'SINGLE_PHASE') {
              // For single phase panels, odd circuits on L1, even on L2
              if (circuit.circuit_number % 2 === 1) {
                phaseALoad = phaseALoad.plus(calculatedLoad);
              } else {
                phaseBLoad = phaseBLoad.plus(calculatedLoad);
              }
            } else {
              // For three phase panels, distribute evenly
              const phase = ((circuit.circuit_number - 1) % 3);
              if (phase === 0) phaseALoad = phaseALoad.plus(calculatedLoad);
              else if (phase === 1) phaseBLoad = phaseBLoad.plus(calculatedLoad);
              else phaseCLoad = phaseCLoad.plus(calculatedLoad);
            }
            // Add to neutral load for single pole circuits
            neutralLoad = neutralLoad.plus(calculatedLoad);
            break;
            
          case 2:
            // Two pole - split between two phases
            if (panel.phase_config === 'SINGLE_PHASE') {
              phaseALoad = phaseALoad.plus(calculatedLoad.div(2));
              phaseBLoad = phaseBLoad.plus(calculatedLoad.div(2));
            } else {
              // For three phase, depends on phase connection
              if (circuit.phase_connection === 'AB') {
                phaseALoad = phaseALoad.plus(calculatedLoad.div(2));
                phaseBLoad = phaseBLoad.plus(calculatedLoad.div(2));
              } else if (circuit.phase_connection === 'BC') {
                phaseBLoad = phaseBLoad.plus(calculatedLoad.div(2));
                phaseCLoad = phaseCLoad.plus(calculatedLoad.div(2));
              } else if (circuit.phase_connection === 'AC') {
                phaseALoad = phaseALoad.plus(calculatedLoad.div(2));
                phaseCLoad = phaseCLoad.plus(calculatedLoad.div(2));
              }
            }
            break;
            
          case 3:
            // Three pole - split evenly across all phases
            const perPhaseLoad = calculatedLoad.div(3);
            phaseALoad = phaseALoad.plus(perPhaseLoad);
            phaseBLoad = phaseBLoad.plus(perPhaseLoad);
            phaseCLoad = phaseCLoad.plus(perPhaseLoad);
            break;
        }
      }
      
      // Add loads from sub-panels
      for (const subPanel of panel.feeding_panels) {
        const subPanelCircuit = panel.circuits.find(
          c => c.circuit_number === subPanel.fed_from_circuit
        );
        
        if (subPanelCircuit) {
          // Get the latest load calculation for the sub-panel
          const subPanelLoad = await tx.panelLoadCalculation.findFirst({
            where: { panel_id: subPanel.id },
            orderBy: { calculation_date: 'desc' }
          });
          
          if (subPanelLoad) {
            totalConnectedLoad = totalConnectedLoad.plus(subPanelLoad.total_connected_load);
            totalDemandLoad = totalDemandLoad.plus(subPanelLoad.total_demand_load);
          }
        }
      }
      
      // Calculate load percentage
      const panelRatingVA = panel.ampere_rating * 
        (panel.voltage_system.includes('480') ? 480 : 
         panel.voltage_system.includes('208') ? 208 : 240) *
        (panel.phase_config.includes('THREE') ? Math.sqrt(3) : 1);
      
      const loadPercentage = totalDemandLoad.div(panelRatingVA).mul(100);
      
      // Calculate phase imbalance
      const maxPhaseLoad = Decimal.max(phaseALoad, phaseBLoad, phaseCLoad);
      const minPhaseLoad = Decimal.min(
        phaseALoad.gt(0) ? phaseALoad : maxPhaseLoad,
        phaseBLoad.gt(0) ? phaseBLoad : maxPhaseLoad,
        phaseCLoad.gt(0) ? phaseCLoad : maxPhaseLoad
      );
      
      const phaseImbalance = maxPhaseLoad.gt(0) 
        ? maxPhaseLoad.minus(minPhaseLoad).div(maxPhaseLoad).mul(100)
        : new Decimal(0);
      
      // Create load calculation record
      const loadCalculation = await tx.panelLoadCalculation.create({
        data: {
          panel_id: panelId,
          phase_a_load: phaseALoad.toNumber(),
          phase_b_load: phaseBLoad.toNumber(),
          phase_c_load: phaseCLoad.toNumber(),
          neutral_load: neutralLoad.toNumber(),
          total_connected_load: totalConnectedLoad.toNumber(),
          total_demand_load: totalDemandLoad.toNumber(),
          load_percentage: loadPercentage.toNumber(),
          phase_imbalance_percent: phaseImbalance.toNumber(),
          power_factor: 0.9, // Default power factor
          ambient_temperature: 30, // Default 30°C
          derating_factor: 1.0,
          notes: `Automatic load calculation`,
          created_by: userId,
          calculation_date: new Date()
        }
      });
      
      logger.info(`Panel load calculated: ${panelId}, Load: ${loadPercentage.toFixed(1)}%`);
      panelEvents.emit('panel:load-calculated', { panelId, loadCalculation });
      
      return loadCalculation;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error calculating panel load:', error);
      throw new AppError('Failed to calculate panel load', 500);
    }
  },

  // Get panel schedule report
  async getPanelSchedule(panelId: string) {
    try {
      const panel = await this.getPanel(panelId);
      const latestLoadCalc = await prisma.panelLoadCalculation.findFirst({
        where: { panel_id: panelId },
        orderBy: { calculation_date: 'desc' }
      });
      
      // Format circuits for panel schedule
      const scheduleData = {
        panel,
        load_calculation: latestLoadCalc,
        circuits: panel.circuits.map(circuit => ({
          ...circuit,
          voltage_drop: this.calculateVoltageDrop(circuit),
          wire_fill: this.calculateConduitFill(circuit)
        })),
        summary: {
          total_circuits: panel.circuits.length,
          spare_circuits: panel.circuits.filter(c => c.is_spare).length,
          empty_spaces: panel.circuits.filter(c => c.is_space).length,
          spaces_used: panel.circuits.reduce((sum, c) => 
            sum + (c.is_space ? 0 : c.poles), 0
          ),
          spaces_available: panel.spaces_total
        }
      };
      
      return scheduleData;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error generating panel schedule:', error);
      throw new AppError('Failed to generate panel schedule', 500);
    }
  },

  // Helper to calculate voltage drop
  calculateVoltageDrop(circuit: Circuit): number {
    // Simplified voltage drop calculation
    // V_drop = (2 * L * I * R) / 1000
    // Where L = length in feet, I = current in amps, R = resistance per 1000 ft
    
    const length = 50; // Default 50 ft run (would come from circuit details)
    const current = circuit.connected_load / circuit.voltage;
    
    // Resistance values for copper wire at 75°C (ohms per 1000 ft)
    const resistanceTable: Record<string, number> = {
      '14_AWG': 3.14,
      '12_AWG': 1.98,
      '10_AWG': 1.24,
      '8_AWG': 0.778,
      '6_AWG': 0.491,
      '4_AWG': 0.308,
      '3_AWG': 0.245,
      '2_AWG': 0.194,
      '1_AWG': 0.154,
      '1/0_AWG': 0.122,
      '2/0_AWG': 0.0967,
      '3/0_AWG': 0.0766,
      '4/0_AWG': 0.0608
    };
    
    const resistance = resistanceTable[circuit.wire_size] || 1.98;
    const voltageDrop = (2 * length * current * resistance) / 1000;
    const percentDrop = (voltageDrop / circuit.voltage) * 100;
    
    return percentDrop;
  },

  // Helper to calculate conduit fill
  calculateConduitFill(circuit: Circuit): number {
    if (!circuit.conduit_type || !circuit.conduit_size) return 0;
    
    // Wire area in square inches (simplified)
    const wireAreas: Record<string, number> = {
      '14_AWG': 0.0097,
      '12_AWG': 0.0133,
      '10_AWG': 0.0211,
      '8_AWG': 0.0366,
      '6_AWG': 0.0507,
      '4_AWG': 0.0824,
      '3_AWG': 0.0973,
      '2_AWG': 0.1158,
      '1_AWG': 0.1562
    };
    
    // Conduit area in square inches
    const conduitAreas: Record<string, number> = {
      '1/2': 0.30,
      '3/4': 0.53,
      '1': 0.86,
      '1-1/4': 1.50,
      '1-1/2': 2.04,
      '2': 3.36
    };
    
    const wireArea = (wireAreas[circuit.wire_size] || 0.0133) * circuit.wire_count;
    const conduitArea = conduitAreas[circuit.conduit_size] || 0.53;
    
    // 40% fill for more than 2 wires
    const maxFill = circuit.wire_count > 2 ? 0.40 : 0.53;
    const fillPercent = (wireArea / (conduitArea * maxFill)) * 100;
    
    return fillPercent;
  },

  // Balance panel circuits across phases with transaction
  async balancePanelLoad(panelId: string, userId: string = 'system') {
    const result = await transactionService.executeWithRetry(async (tx) => {
      // Get panel with all circuits
      const panel = await tx.panel.findUnique({
        where: { id: panelId },
        include: {
          circuits: {
            where: {
              is_spare: false,
              is_space: false
            },
            orderBy: { connected_load: 'desc' }
          }
        }
      });

      if (!panel) {
        throw new AppError('Panel not found', 404);
      }

      if (panel.phase_config === 'SINGLE_PHASE') {
        // For single phase, balance between L1 and L2
        return this._balanceSinglePhasePanel(tx, panel, userId);
      } else {
        // For three phase, balance across L1, L2, and L3
        return this._balanceThreePhasePanel(tx, panel, userId);
      }
    }, 3); // Retry up to 3 times on deadlock

    if (!result.success) {
      throw result.error || new AppError('Failed to balance panel load', 500);
    }

    // Recalculate load after balancing
    await this.calculatePanelLoad(panelId, userId);

    return result.data;
  },

  // Balance single phase panel
  async _balanceSinglePhasePanel(tx: PrismaClient, panel: any, userId: string) {
    const singlePoleCircuits = panel.circuits.filter(c => c.poles === 1);
    
    // Calculate current load on each phase
    let l1Load = 0;
    let l2Load = 0;
    
    singlePoleCircuits.forEach(circuit => {
      if (circuit.circuit_number % 2 === 1) {
        l1Load += circuit.connected_load * circuit.demand_factor;
      } else {
        l2Load += circuit.connected_load * circuit.demand_factor;
      }
    });

    logger.info(`Current panel balance: L1=${l1Load}W, L2=${l2Load}W`);

    // Sort circuits by load (descending)
    const sortedCircuits = [...singlePoleCircuits].sort(
      (a, b) => (b.connected_load * b.demand_factor) - (a.connected_load * a.demand_factor)
    );

    // Reassign circuits to balance load
    const updates = [];
    let newL1Load = 0;
    let newL2Load = 0;
    let circuitNumber = 1;

    for (const circuit of sortedCircuits) {
      const load = circuit.connected_load * circuit.demand_factor;
      
      // Assign to the phase with less load
      if (newL1Load <= newL2Load) {
        // Assign to L1 (odd circuit number)
        updates.push({
          where: { id: circuit.id },
          data: { 
            circuit_number: circuitNumber,
            updated_at: new Date()
          }
        });
        newL1Load += load;
        circuitNumber += 2; // Next odd number
      } else {
        // Assign to L2 (even circuit number)
        const evenNumber = circuitNumber % 2 === 0 ? circuitNumber : circuitNumber + 1;
        updates.push({
          where: { id: circuit.id },
          data: { 
            circuit_number: evenNumber,
            updated_at: new Date()
          }
        });
        newL2Load += load;
        circuitNumber = evenNumber + 2;
      }
    }

    // Apply all updates within transaction
    for (const update of updates) {
      await tx.circuit.update(update);
    }

    // Create audit log
    await createAuditLog({
      action: 'PANEL_BALANCE_LOAD',
      userId: userId,
      resourceType: 'PANEL',
      resourceId: panel.id,
      details: {
        before: { l1Load, l2Load },
        after: { l1Load: newL1Load, l2Load: newL2Load }
      }
    });

    logger.info(`Panel balanced: L1=${newL1Load}W, L2=${newL2Load}W`);

    return {
      panelId: panel.id,
      before: { l1Load, l2Load },
      after: { l1Load: newL1Load, l2Load: newL2Load },
      circuitsReassigned: updates.length
    };
  },

  // Balance three phase panel
  async _balanceThreePhasePanel(tx: PrismaClient, panel: any, userId: string) {
    const singlePoleCircuits = panel.circuits.filter(c => c.poles === 1);
    
    // Calculate current load on each phase
    let phaseALoad = 0;
    let phaseBLoad = 0;
    let phaseCLoad = 0;
    
    singlePoleCircuits.forEach(circuit => {
      const phase = ((circuit.circuit_number - 1) % 3);
      const load = circuit.connected_load * circuit.demand_factor;
      
      if (phase === 0) phaseALoad += load;
      else if (phase === 1) phaseBLoad += load;
      else phaseCLoad += load;
    });

    logger.info(`Current balance: A=${phaseALoad}W, B=${phaseBLoad}W, C=${phaseCLoad}W`);

    // Sort circuits by load (descending)
    const sortedCircuits = [...singlePoleCircuits].sort(
      (a, b) => (b.connected_load * b.demand_factor) - (a.connected_load * a.demand_factor)
    );

    // Reassign circuits using a greedy algorithm
    const updates = [];
    let newPhaseALoad = 0;
    let newPhaseBLoad = 0;
    let newPhaseCLoad = 0;
    const phaseAssignments = { A: [], B: [], C: [] };

    for (const circuit of sortedCircuits) {
      const load = circuit.connected_load * circuit.demand_factor;
      
      // Find phase with minimum load
      let targetPhase;
      if (newPhaseALoad <= newPhaseBLoad && newPhaseALoad <= newPhaseCLoad) {
        targetPhase = 'A';
        newPhaseALoad += load;
      } else if (newPhaseBLoad <= newPhaseCLoad) {
        targetPhase = 'B';
        newPhaseBLoad += load;
      } else {
        targetPhase = 'C';
        newPhaseCLoad += load;
      }
      
      phaseAssignments[targetPhase].push(circuit);
    }

    // Assign circuit numbers based on phase assignments
    let circuitNumber = 1;
    
    // Assign phase A circuits (1, 4, 7, 10...)
    for (const circuit of phaseAssignments.A) {
      updates.push({
        where: { id: circuit.id },
        data: { 
          circuit_number: circuitNumber,
          updated_at: new Date()
        }
      });
      circuitNumber += 3;
    }

    // Assign phase B circuits (2, 5, 8, 11...)
    circuitNumber = 2;
    for (const circuit of phaseAssignments.B) {
      updates.push({
        where: { id: circuit.id },
        data: { 
          circuit_number: circuitNumber,
          updated_at: new Date()
        }
      });
      circuitNumber += 3;
    }

    // Assign phase C circuits (3, 6, 9, 12...)
    circuitNumber = 3;
    for (const circuit of phaseAssignments.C) {
      updates.push({
        where: { id: circuit.id },
        data: { 
          circuit_number: circuitNumber,
          updated_at: new Date()
        }
      });
      circuitNumber += 3;
    }

    // Apply all updates within transaction
    for (const update of updates) {
      await tx.circuit.update(update);
    }

    // Create audit log
    await createAuditLog({
      action: 'PANEL_BALANCE_LOAD',
      userId: userId,
      resourceType: 'PANEL',
      resourceId: panel.id,
      details: {
        before: { phaseALoad, phaseBLoad, phaseCLoad },
        after: { 
          phaseALoad: newPhaseALoad, 
          phaseBLoad: newPhaseBLoad, 
          phaseCLoad: newPhaseCLoad 
        }
      }
    });

    const maxLoad = Math.max(newPhaseALoad, newPhaseBLoad, newPhaseCLoad);
    const minLoad = Math.min(newPhaseALoad, newPhaseBLoad, newPhaseCLoad);
    const imbalance = maxLoad > 0 ? ((maxLoad - minLoad) / maxLoad) * 100 : 0;

    logger.info(`Panel balanced: A=${newPhaseALoad}W, B=${newPhaseBLoad}W, C=${newPhaseCLoad}W, Imbalance=${imbalance.toFixed(1)}%`);

    return {
      panelId: panel.id,
      before: { phaseALoad, phaseBLoad, phaseCLoad },
      after: { 
        phaseALoad: newPhaseALoad, 
        phaseBLoad: newPhaseBLoad, 
        phaseCLoad: newPhaseCLoad 
      },
      imbalancePercent: imbalance,
      circuitsReassigned: updates.length
    };
  },

  // Update multiple circuits atomically
  async updateMultipleCircuits(panelId: string, circuitUpdates: any[], userId: string) {
    const result = await transactionService.executeTransaction(async (tx) => {
      // Verify panel exists
      const panel = await tx.panel.findUnique({
        where: { id: panelId }
      });

      if (!panel) {
        throw new AppError('Panel not found', 404);
      }

      // Validate all circuit updates
      const circuitIds = circuitUpdates.map(u => u.id);
      const existingCircuits = await tx.circuit.findMany({
        where: {
          id: { in: circuitIds },
          panel_id: panelId
        }
      });

      if (existingCircuits.length !== circuitUpdates.length) {
        throw new AppError('One or more circuits not found', 404);
      }

      // Apply all updates
      const updatedCircuits = [];
      for (const update of circuitUpdates) {
        const { id, ...data } = update;
        
        const updatedCircuit = await tx.circuit.update({
          where: { id },
          data: {
            ...data,
            updated_at: new Date()
          }
        });
        
        updatedCircuits.push(updatedCircuit);
      }

      // Create audit log
      await createAuditLog({
        action: 'CIRCUIT_BULK_UPDATE',
        userId: userId,
        resourceType: 'PANEL_CIRCUITS',
        resourceId: panelId,
        details: {
          circuitCount: circuitUpdates.length,
          updates: circuitUpdates
        }
      });

      // Recalculate panel load within the transaction
      await this._calculatePanelLoadInTransaction(tx, panelId, userId);

      return updatedCircuits;
    });

    if (!result.success) {
      throw result.error || new AppError('Failed to update circuits', 500);
    }

    panelEvents.emit('panel:circuits-updated', { panelId, circuits: result.data });

    return result.data;
  }
};