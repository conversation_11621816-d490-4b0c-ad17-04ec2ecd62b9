import { Router } from 'express';
import * as jwt from 'jsonwebtoken';
import { z } from 'zod';
import { prisma } from '../index';
import { config } from '../config';
import { AppError } from '../middleware/errorHandler';
import { hashPassword, verifyPassword } from '../security/crypto';
import { authenticate, AuthRequest } from '../middleware/auth';
import { Request } from 'express';
import { csrfProtection, getCsrfToken } from '../security/csrf';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';
import { validateInput } from '../security/validators';
import { rateLimitMiddleware } from '../security/rate-limiting';
import { createSession, destroySession } from '../security/session';

const router: Router = Router();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(100),
  name: z.string().min(1).max(100),
  role: z.enum(['electrician', 'foreman', 'admin', 'estimator']).default('electrician')
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string()
});

const refreshSchema = z.object({
  refreshToken: z.string()
});

// Get CSRF token
router.get('/csrf-token', authenticate, getCsrfToken);

// Register new user
router.post('/register', 
  rateLimitMiddleware.auth,
  validateInput(registerSchema),
  async (req, res, next) => {
  try {
    const data = req.body;
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email }
    });
    
    if (existingUser) {
      throw new AppError(409, 'User already exists', true, 'USER_EXISTS');
    }
    
    // Hash password with bcrypt
    const passwordHash = await hashPassword(data.password);
    
    // Create user
    const user = await prisma.user.create({
      data: {
        email: data.email,
        password_hash: passwordHash,
        name: data.name,
        role: data.role
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        created_at: true
      }
    });
    
    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);
    
    // Save refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refresh_token: refreshToken }
    });
    
    res.status(201).json({
      user,
      accessToken,
      refreshToken
    });
  } catch (error) {
    next(error);
  }
});

// Login
router.post('/login', 
  rateLimitMiddleware.auth,
  validateInput(loginSchema),
  async (req, res, next) => {
  try {
    const data = req.body;
    const ipAddress = req.ip || 'unknown';
    
    // Find user
    const user = await prisma.user.findUnique({
      where: { email: data.email }
    });
    
    if (!user || user.deleted_at) {
      await createAuditLog({
        action: AUDIT_ACTIONS.LOGIN_FAILED,
        resourceType: 'auth',
        details: { email: data.email, reason: 'user_not_found' },
        ipAddress
      });
      throw new AppError(401, 'Invalid credentials', true, 'INVALID_CREDENTIALS');
    }
    
    // Verify password with bcrypt
    const validPassword = await verifyPassword(data.password, user.password_hash);
    
    if (!validPassword) {
      await createAuditLog({
        action: AUDIT_ACTIONS.LOGIN_FAILED,
        userId: user.id,
        resourceType: 'auth',
        details: { reason: 'invalid_password' },
        ipAddress
      });
      throw new AppError(401, 'Invalid credentials', true, 'INVALID_CREDENTIALS');
    }
    
    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);
    
    // Save refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refresh_token: refreshToken }
    });
    
    // Create session
    const session = await createSession(user.id, user.email, user.role, req);
    
    // Log successful login
    await createAuditLog({
      action: AUDIT_ACTIONS.LOGIN_SUCCESS,
      userId: user.id,
      resourceType: 'auth',
      ipAddress
    });
    
    res.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      accessToken,
      refreshToken,
      sessionId: session.sessionId,
      expiresAt: session.expiresAt
    });
  } catch (error) {
    next(error);
  }
});

// Refresh token
router.post('/refresh', async (req, res, next) => {
  try {
    const data = refreshSchema.parse(req.body);
    
    // Verify refresh token
    const decoded = jwt.verify(data.refreshToken, config.jwt.refreshSecret) as {
      userId: string;
    };
    
    // Find user and verify refresh token matches
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });
    
    if (!user || user.refresh_token !== data.refreshToken || user.deleted_at) {
      throw new AppError(401, 'Invalid refresh token', true, 'INVALID_REFRESH_TOKEN');
    }
    
    // Generate new tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);
    
    // Save new refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: { refresh_token: refreshToken }
    });
    
    res.json({
      accessToken,
      refreshToken
    });
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      next(new AppError(401, 'Refresh token expired', true, 'REFRESH_TOKEN_EXPIRED'));
    } else if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError(401, 'Invalid refresh token', true, 'INVALID_REFRESH_TOKEN'));
    } else {
      next(error);
    }
  }
});

// Logout
router.post('/logout', 
  authenticate,
  csrfProtection(),
  async (req: AuthRequest, res, next) => {
  try {
    if (req.user) {
      // Clear refresh token
      await prisma.user.update({
        where: { id: req.user.userId },
        data: { refresh_token: null }
      });
      
      // Destroy session
      const sessionId = req.headers['x-session-id'] as string;
      if (sessionId) {
        await destroySession(sessionId);
      }
      
      // Log logout
      await createAuditLog({
        action: AUDIT_ACTIONS.LOGOUT,
        userId: req.user.userId,
        resourceType: 'auth'
      });
    }
    
    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    next(error);
  }
});

// Get current user
router.get('/me', authenticate, async (req: AuthRequest, res, next) => {
  try {
    if (!req.user) {
      throw new AppError(401, 'Not authenticated', true, 'NOT_AUTHENTICATED');
    }
    
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        created_at: true
      }
    });
    
    if (!user) {
      throw new AppError(404, 'User not found', true, 'USER_NOT_FOUND');
    }
    
    res.json(user);
  } catch (error) {
    next(error);
  }
});

// Helper functions
function generateAccessToken(user: { id: string; email: string; role: string }): string {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role
  };
  const secret: jwt.Secret = config.jwt.secret;
  const options: jwt.SignOptions = {
    expiresIn: config.jwt.expiresIn as jwt.SignOptions['expiresIn']
  };
  return jwt.sign(payload, secret, options);
}

function generateRefreshToken(user: { id: string }): string {
  const payload = {
    userId: user.id
  };
  const secret: jwt.Secret = config.jwt.refreshSecret;
  const options: jwt.SignOptions = {
    expiresIn: config.jwt.refreshExpiresIn as jwt.SignOptions['expiresIn']
  };
  return jwt.sign(payload, secret, options);
}

export { router as authRouter };