Each agent file contains:
- **Specific implementation patterns** unique to that agent's domain
- **Electrical industry requirements** relevant to their function  
- **Communication protocols** for inter-agent coordination
- **Best practices** for their area of expertise
- **Code examples** that should be implemented exactly

By explicitly referencing all 9 agent files in `C:\Projects\electrical\agents\`, Claude Code will:
1. Build a complete mental model of the entire system
2. Understand how agents must work together
3. Implement the exact specifications you've defined
4. Maintain consistency across all components
5. Follow electrical industry best practices throughout

**Remember**: The agents work as a team - the Project Manager orchestrates, the Coding Agent builds, the UI Designer creates interfaces, the Research Agent ensures compliance, and all others play their specialized roles. The system only works when all agents are properly implemented and integrated!# Initial Claude Code CLI Prompt for Electrical Contracting AI System

## Pre-Flight Check Command

Before starting, verify <PERSON> can access all agent files:
```bash
claude-code "List all markdown files in C:\Projects\electrical\agents\ and provide a one-line summary of each agent's primary responsibility based on reading the first few lines of each file."
```

## Primary Command

```bash
claude-code "Initialize a full-stack electrical contracting application with autonomous AI agents. Read and implement the agent architecture from ALL 9 agent markdown files located in C:\Projects\electrical\agents\: backend-database-agent.md, coding-agent.md, debugging-agent.md, frontend-agent.md, memory-agent.md, project-manager-agent.md, prompt-engineering-agent.md, research-agent.md, and ui-designer-agent.md. Create a React + TypeScript frontend, Node.js + Express backend with SQLite database, and implement the multi-agent system starting with the Project Manager, Coding, and Backend agents. The application should handle electrical estimating, quotes, invoices, NEC-compliant calculations, material pricing, and job management. Use the exact specifications from each agent's markdown file for their implementation."
```

## Detailed Multi-Step Initialization Prompt

```bash
claude-code "
CONTEXT: I'm building an advanced electrical contracting application with an autonomous multi-agent AI system. I have 9 detailed agent specification files in C:\Projects\electrical\agents\:
- backend-database-agent.md (Backend and database architecture)
- coding-agent.md (Full-stack development implementation)
- debugging-agent.md (Error handling and troubleshooting)
- frontend-agent.md (React UI implementation)
- memory-agent.md (Persistent knowledge and learning)
- project-manager-agent.md (Central orchestration and coordination)
- prompt-engineering-agent.md (AI prompt optimization)
- research-agent.md (Industry intelligence and compliance)
- ui-designer-agent.md (User interface design patterns)

READ ALL 9 AGENT FILES BEFORE STARTING IMPLEMENTATION.

OBJECTIVE: Create the foundational architecture and implement the core agents to build a full-stack application for electrical contractors that includes:
- Estimating and quoting system
- NEC-compliant electrical calculations
- Material and labor tracking
- Invoice generation
- Real-time pricing updates
- Offline-capable PWA
- Gemini AI integration for document/image processing

STEP 1 - PROJECT INITIALIZATION:
1. Create project structure at C:\Projects\electrical\app
2. Initialize monorepo with /frontend, /backend, /shared, /agents directories
3. Set up package.json files with all required dependencies
4. Configure TypeScript for full-stack development
5. Set up ESLint and Prettier with electrical industry code standards

STEP 2 - DATABASE SETUP:
1. Read backend-database-agent.md from C:\Projects\electrical\agents\ for exact schema
2. Create SQLite database with WAL mode enabled
3. Implement all tables: customers, projects, estimates, material_items, labor_items, calculation_log, material_price_history
4. Set up Prisma ORM with proper models
5. Create seed data for electrical materials and labor rates

STEP 3 - BACKEND IMPLEMENTATION:
1. Read backend-database-agent.md and coding-agent.md from agents folder
2. Create Express server with TypeScript
3. Implement authentication with JWT
4. Create RESTful APIs for all entities
5. Add electrical calculation endpoints (load calc, voltage drop, conduit fill)
6. Implement WebSocket support with Socket.io
7. Set up BullMQ for background job processing
8. Add Redis for caching
9. Implement security middleware

STEP 4 - FRONTEND FOUNDATION:
1. Read frontend-agent.md and ui-designer-agent.md from agents folder
2. Create React 18 app with Vite and TypeScript
3. Set up Tailwind CSS with electrical contractor theme
4. Implement routing with React Router
5. Add Zustand for state management
6. Configure React Query for API calls
7. Set up component library structure
8. Implement offline support with service workers

STEP 5 - CORE AGENT IMPLEMENTATION:
1. Read ALL agent files, especially project-manager-agent.md, coding-agent.md, and memory-agent.md
2. Create agent communication system using EventEmitter pattern
3. Implement Project Manager agent as central coordinator
4. Add message queue for inter-agent communication
5. Set up agent status monitoring
6. Create shared memory system using Redis
7. Implement agent task assignment logic

STEP 6 - ELECTRICAL FEATURES:
1. Reference research-agent.md for NEC compliance requirements
2. Create load calculation module with NEC 2023 compliance
3. Implement voltage drop calculator
4. Add conduit fill calculator
5. Create material takeoff system
6. Implement labor estimation with NECA standards
7. Add permit tracking functionality
8. Create quote/estimate generator with PDF export

STEP 7 - AI INTEGRATIONS:
1. Reference prompt-engineering-agent.md for optimization patterns
2. Integrate Gemini AI for image analysis of job sites
3. Add OCR for document processing
4. Implement Claude API for complex calculations
5. Create material price scraping system (with rate limiting)
6. Add intelligent estimation suggestions

STEP 8 - TESTING AND DOCUMENTATION:
1. Reference debugging-agent.md for testing strategies
2. Set up Jest for unit testing
3. Add Cypress for E2E testing
4. Create test suites for all electrical calculations
5. Generate API documentation with Swagger
6. Create user guides for contractors

SPECIAL REQUIREMENTS:
- Use Decimal.js for ALL financial and electrical calculations
- Implement proper error boundaries for safety-critical features
- Ensure offline functionality for field use
- Support multiple device types (phone, tablet, desktop)
- Follow electrical industry UI patterns (high contrast, large touch targets)
- Log all calculations for compliance auditing

IMPORTANT: Read and follow the specifications in ALL 9 agent files located in C:\Projects\electrical\agents\. Each agent has specific implementation requirements that must be followed exactly. Start with the core functionality and build incrementally. After initial setup, report the project structure and next steps.
"
```

## Quick Start Commands After Initial Setup

### 1. Start Development Environment
```bash
claude-code "Start all development servers for the electrical contracting app: frontend on port 3000, backend on port 3001, and Redis. Then run database migrations and seed electrical materials data."
```

### 2. Add Specific Agents
```bash
claude-code "Implement the Research Agent from C:\Projects\electrical\agents\research-agent.md with NEC code lookup, material pricing research, and labor rate analysis capabilities. Integrate it with the existing agent communication system."
```

### 3. Create Electrical Calculation Features
```bash
claude-code "Using specifications from C:\Projects\electrical\agents\coding-agent.md and C:\Projects\electrical\agents\backend-database-agent.md, implement the complete residential load calculation feature following NEC Article 220, including general lighting, small appliances, laundry, and demand factors. Create UI components, API endpoints, and calculation service with full test coverage."
```

### 4. Add Offline Capabilities
```bash
claude-code "Implement offline-first architecture for field use. Add service worker, IndexedDB storage, background sync for estimates, and offline calculation capabilities. Show sync status in UI."
```

### 5. Integrate AI Features
```bash
claude-code "Using specifications from C:\Projects\electrical\agents\prompt-engineering-agent.md and C:\Projects\electrical\agents\coding-agent.md, integrate Gemini Vision API for analyzing electrical job site photos. Create UI for photo capture, implement image analysis for identifying panels, conduits, and safety hazards, and store results in the database."
```

### 6. Create Agent Communication Diagram
```bash
claude-code "After reading all agent files in C:\Projects\electrical\agents\, create a visual diagram showing how all 9 agents communicate and coordinate. Generate a mermaid diagram file showing the Project Manager as the central hub with connections to all other agents, including message types and data flow."
```

## Verification Commands

### Check Implementation Status
```bash
claude-code "Analyze the current implementation status of the electrical contracting app. Compare the implementation against ALL 9 agent specifications in C:\Projects\electrical\agents\. List completed features, working agents, and what remains to be implemented from each agent file: backend-database-agent.md, coding-agent.md, debugging-agent.md, frontend-agent.md, memory-agent.md, project-manager-agent.md, prompt-engineering-agent.md, research-agent.md, and ui-designer-agent.md."
```

### Run System Tests
```bash
claude-code "Run all tests for the electrical contracting app including unit tests for calculations, integration tests for APIs, and agent communication tests. Verify each agent's implementation against its specification file in C:\Projects\electrical\agents\. Generate a coverage report showing which agent specifications are fully implemented."
```

### Validate Agent Integration
```bash
claude-code "Validate that all 9 agents from C:\Projects\electrical\agents\ are properly integrated: 1) Project Manager orchestrates tasks, 2) Coding Agent implements features, 3) Backend Agent handles data, 4) Frontend Agent renders UI, 5) UI Designer patterns are followed, 6) Research Agent provides compliance data, 7) Memory Agent persists knowledge, 8) Debugging Agent handles errors, 9) Prompt Engineering Agent optimizes AI calls. Run integration tests for inter-agent communication and generate a status report."
```

## Important Notes

1. **Agent Files Location**: All 9 agent specification files are in `C:\Projects\electrical\agents\`
2. **Read All Agents**: Ensure Claude Code reads ALL agent files before implementation
3. **Start Small**: Begin with the initial prompt and build incrementally
4. **Reference Agent Files**: Always tell Claude Code to read the specific agent markdown files
5. **Verify Each Step**: Test core functionality before adding complex features
6. **Safety First**: Ensure all electrical calculations are thoroughly tested
7. **Keep Context**: Include previous implementation details in subsequent prompts

## Agent Files Reference
- `backend-database-agent.md` - Database schema and API architecture
- `coding-agent.md` - Implementation patterns and code standards
- `debugging-agent.md` - Error handling and testing strategies
- `frontend-agent.md` - React components and UI implementation
- `memory-agent.md` - Persistent storage and learning systems
- `project-manager-agent.md` - Agent orchestration and workflow
- `prompt-engineering-agent.md` - AI prompt optimization
- `research-agent.md` - Industry knowledge and compliance
- `ui-designer-agent.md` - Design patterns and UX guidelines

## Troubleshooting Prompt

If you encounter issues:
```bash
claude-code "Debug the electrical contracting app implementation. Check for errors in: database connections, API endpoints, agent communication, and electrical calculations. Review ALL agent specification files in C:\Projects\electrical\agents\ and ensure implementation matches requirements. Specifically verify implementation against: project-manager-agent.md for orchestration, backend-database-agent.md for data layer, frontend-agent.md for UI, and debugging-agent.md for error handling patterns. Provide detailed error analysis and fixes."
```

## Comprehensive System Architecture Command

To generate a complete system architecture based on all agents:
```bash
claude-code "Read ALL 9 agent specification files in C:\Projects\electrical\agents\ and create a comprehensive system architecture document that includes: 1) How each agent interacts with others, 2) Database schema from backend-database-agent.md, 3) UI components from ui-designer-agent.md and frontend-agent.md, 4) API endpoints from backend-database-agent.md, 5) Calculation modules from coding-agent.md, 6) Memory structures from memory-agent.md, 7) Testing strategies from debugging-agent.md, 8) Research capabilities from research-agent.md, and 9) Orchestration flow from project-manager-agent.md. Save this as ARCHITECTURE.md in the project root."
```

This initial prompt will create a solid foundation for your electrical contracting application with the AI agent system. The key is to reference the detailed specifications you've saved and build incrementally!