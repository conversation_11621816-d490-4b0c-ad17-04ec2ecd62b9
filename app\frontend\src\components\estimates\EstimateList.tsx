import { useState, useEffect } from 'react';
import { Plus, Search, FileText, Mail, Download, Eye } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { Estimate } from '@electrical/shared';
import { EstimateForm } from './EstimateForm';
import { format } from 'date-fns';

export function EstimateList() {
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    fetchEstimates();
  }, []);

  const fetchEstimates = async () => {
    try {
      const response = await api.get('/estimates');
      // Handle paginated response
      if (response.data && Array.isArray(response.data.data)) {
        setEstimates(response.data.data);
      } else if (Array.isArray(response.data)) {
        // Handle case where API returns array directly
        setEstimates(response.data);
      } else {
        // If response doesn't have expected structure, set empty array
        console.error('Unexpected response structure:', response.data);
        setEstimates([]);
      }
    } catch (error) {
      console.error('Error fetching estimates:', error);
      toast.error('Failed to load estimates');
      setEstimates([]); // Ensure estimates is always an array
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (id: string, status: string) => {
    try {
      await api.patch(`/estimates/${id}/status`, { status });
      toast.success('Estimate status updated');
      fetchEstimates();
    } catch (error) {
      toast.error('Failed to update status');
    }
  };

  const handleEmail = async (id: string) => {
    try {
      await api.post(`/estimates/${id}/email`);
      toast.success('Estimate emailed to customer');
    } catch (error) {
      toast.error('Failed to email estimate');
    }
  };

  const handleDownload = async (id: string) => {
    try {
      const response = await api.get(`/estimates/${id}/pdf`, { responseType: 'blob' });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `estimate-${id}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      toast.error('Failed to download estimate');
    }
  };

  const filteredEstimates = estimates.filter(estimate => {
    const matchesSearch = 
      estimate.estimate_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      estimate.customer.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'ALL' || estimate.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const styles = {
      DRAFT: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      SENT: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      VIEWED: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      ACCEPTED: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      REJECTED: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      EXPIRED: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[status as keyof typeof styles]}`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="spinner" />
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search estimates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 input"
            />
          </div>
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="input w-full sm:w-48"
        >
          <option value="ALL">All Status</option>
          <option value="DRAFT">Draft</option>
          <option value="SENT">Sent</option>
          <option value="VIEWED">Viewed</option>
          <option value="ACCEPTED">Accepted</option>
          <option value="REJECTED">Rejected</option>
          <option value="EXPIRED">Expired</option>
        </select>
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Estimate
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredEstimates.length === 0 ? (
            <li className="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
              No estimates found
            </li>
          ) : (
            filteredEstimates.map((estimate) => (
              <li key={estimate.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {estimate.estimate_number}
                        </h3>
                        {getStatusBadge(estimate.status)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        <p className="flex items-center">
                          <FileText className="h-4 w-4 mr-1" />
                          {estimate.customer.name}
                          {estimate.project && ` - ${estimate.project.name}`}
                        </p>
                        <p className="mt-1">
                          Created: {format(new Date(estimate.created_at), 'MMM d, yyyy')} | 
                          Valid until: {format(new Date(estimate.valid_until), 'MMM d, yyyy')}
                        </p>
                      </div>
                    </div>
                    <div className="ml-6 text-right">
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        ${estimate.total.toFixed(2)}
                      </p>
                      <div className="mt-2 flex space-x-2">
                        <button
                          onClick={() => window.open(`/estimates/${estimate.id}`, '_blank')}
                          className="btn-secondary btn-sm"
                          title="View"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEmail(estimate.id)}
                          className="btn-secondary btn-sm"
                          title="Email"
                          disabled={estimate.status === 'DRAFT'}
                        >
                          <Mail className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDownload(estimate.id)}
                          className="btn-secondary btn-sm"
                          title="Download PDF"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  {estimate.status === 'DRAFT' && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        onClick={() => handleStatusChange(estimate.id, 'SENT')}
                        className="btn-primary btn-sm"
                      >
                        Mark as Sent
                      </button>
                    </div>
                  )}
                </div>
              </li>
            ))
          )}
        </ul>
      </div>

      {showForm && (
        <EstimateForm
          onClose={() => setShowForm(false)}
          onSuccess={fetchEstimates}
        />
      )}
    </div>
  );
}