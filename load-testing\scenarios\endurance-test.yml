config:
  target: "{{ $processEnvironment.API_BASE_URL }}"
  phases:
    # 4-hour endurance test with moderate load
    - duration: 300
      arrivalRate: 2
      rampTo: 10
      name: "Ramp-up"
    
    - duration: 14400  # 4 hours
      arrivalRate: 10
      name: "Sustained moderate load (50 concurrent users)"
    
    - duration: 300
      arrivalRate: 10
      rampTo: 2
      name: "Cool-down"
  
  processor: "../scripts/test-data-generator.js"
  
  http:
    timeout: 30
    pool: 50
    maxSockets: 100
  
  plugins:
    metrics-by-endpoint:
      percentilesOutputFileType: "csv"
    ensure:
      thresholds:
        - http.response_time.p95: 1500
        - http.response_time.p99: 3000
        - http.codes.5xx: 0.5
        - http.request_rate: 50
  
  statsInterval: 30

before:
  flow:
    - log: "Starting 4-hour endurance test"
    - function: "setupEnduranceTest"

after:
  flow:
    - log: "Endurance test completed"
    - function: "analyzeMemoryLeaks"
    - function: "checkResourceExhaustion"

scenarios:
  # Realistic mix of operations for extended testing
  
  - name: "Typical User Session"
    weight: 30
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"
              as: "userId"
      - think: 2
      
      # Dashboard load
      - get:
          url: "/projects?userId={{ userId }}&limit=10"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$[0].id"
              as: "projectId"
      - think: 3
      
      # View project details
      - get:
          url: "/projects/{{ projectId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 2
      
      # Check panels
      - get:
          url: "/panels?projectId={{ projectId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$[0].id"
              as: "panelId"
      - think: 2
      
      # Perform calculation
      - post:
          url: "/calculations/load-calculation"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            panelId: "{{ panelId }}"
            demandFactor: 0.8
            powerFactor: 0.9
      - think: 5
      
      # Logout
      - post:
          url: "/auth/logout"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  - name: "Panel Management Session"
    weight: 20
    flow:
      - function: "authenticateUser"
      - loop:
          - get:
              url: "/panels?page={{ $randomNumber(1, 10) }}"
              headers:
                Authorization: "Bearer {{ authToken }}"
              capture:
                - json: "$[0].id"
                  as: "selectedPanelId"
          - think: 2
          - get:
              url: "/panels/{{ selectedPanelId }}/schedule"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - think: 3
          - put:
              url: "/panels/{{ selectedPanelId }}/circuits/{{ $randomNumber(1, 42) }}"
              headers:
                Authorization: "Bearer {{ authToken }}"
              json:
                description: "Updated - Endurance Test"
                notes: "Last modified: {{ $timestamp() }}"
          - think: 4
          count: 5
  
  - name: "Material Research Session"
    weight: 15
    flow:
      - function: "authenticateUser"
      - loop:
          - get:
              url: "/materials/categories"
              headers:
                Authorization: "Bearer {{ authToken }}"
              capture:
                - json: "$[0]"
                  as: "category"
          - think: 1
          - get:
              url: "/materials?category={{ category }}&limit=20"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - think: 3
          - get:
              url: "/materials/search?query={{ $randomItem(['wire', 'breaker', 'panel', 'conduit', 'box']) }}"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - think: 2
          count: 3
  
  - name: "Calculation Workflow"
    weight: 15
    flow:
      - function: "authenticateUser"
      - loop:
          - post:
              url: "/calculations/voltage-drop"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateVoltageDropData"
              json: "{{ voltageDropData }}"
          - think: 2
          - post:
              url: "/calculations/conduit-fill"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateConduitFillData"
              json: "{{ conduitFillData }}"
          - think: 2
          - post:
              url: "/calculations/wire-size"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateWireSizeData"
              json: "{{ wireSizeData }}"
          - think: 3
          count: 2
  
  - name: "Long WebSocket Session"
    weight: 10
    engine: "ws"
    flow:
      - function: "authenticateUser"
      - connect:
          url: "{{ wsUrl }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - send:
          data:
            type: "heartbeat"
            timestamp: "{{ $timestamp() }}"
      - loop:
          - think: 30
          - send:
              data:
                type: "heartbeat"
                timestamp: "{{ $timestamp() }}"
          - think: 30
          - send:
              data:
                type: "status_check"
                sessionId: "{{ $randomString(10) }}"
          count: 60  # Keep connection alive for 1 hour
  
  - name: "Report Generation"
    weight: 10
    flow:
      - function: "authenticateUser"
      - loop:
          - post:
              url: "/exports/project-report"
              headers:
                Authorization: "Bearer {{ authToken }}"
              json:
                projectId: "{{ $randomString(10) }}"
                format: "pdf"
                sections: ["summary", "panels", "estimates", "materials"]
              capture:
                - json: "$.jobId"
                  as: "reportJobId"
          - think: 5
          - loop:
              - get:
                  url: "/exports/status/{{ reportJobId }}"
                  headers:
                    Authorization: "Bearer {{ authToken }}"
              - think: 3
              count: 5
          count: 2
          think: 300  # Run every 5 minutes