import { PromptEngineeringAgent } from './prompt-engineering-agent';
import { MemoryStore } from '../infrastructure/memory-store';

describe('PromptEngineeringAgent', () => {
  let agent: PromptEngineeringAgent;
  let mockMemoryStore: MemoryStore;

  beforeEach(() => {
    mockMemoryStore = new MemoryStore(':memory:');
    agent = new PromptEngineeringAgent({
      id: 'pe-test',
      name: 'Test Prompt Engineering Agent',
      type: 'prompt-engineering',
      description: 'Test agent for prompt optimization',
      memoryStore: mockMemoryStore,
    });
  });

  afterEach(async () => {
    await agent.shutdown();
  });

  describe('optimize-prompt', () => {
    it('should optimize a basic calculation prompt', async () => {
      const input = {
        prompt: 'Calculate the voltage drop for a circuit.',
        model: 'claude' as const,
        optimizationGoals: ['token-efficiency', 'safety'],
      };

      const result = await agent['optimizePrompt'](input);

      expect(result.optimized).toBeDefined();
      expect(result.improvements).toBeInstanceOf(Array);
      expect(result.improvements.length).toBeGreaterThan(0);
      expect(result.metrics.tokenReduction).toBeDefined();
    });

    it('should add NEC references when missing', async () => {
      const input = {
        prompt: 'Calculate wire sizing for a 50A circuit',
        model: 'claude' as const,
      };

      const result = await agent['optimizePrompt'](input);

      expect(result.optimized).toContain('NEC');
      expect(result.improvements).toContain(expect.stringContaining('NEC references'));
    });

    it('should add precision requirements for calculations', async () => {
      const input = {
        prompt: 'Calculate load for a 2000 sq ft building',
        model: 'claude' as const,
      };

      const result = await agent['optimizePrompt'](input);

      expect(result.optimized.toLowerCase()).toContain('decimal');
      expect(result.improvements).toContain('Added decimal precision requirements');
    });
  });

  describe('generate-prompt', () => {
    it('should generate a calculation prompt', async () => {
      const input = {
        task: 'voltage drop calculation',
        taskType: 'calculation' as const,
        requirements: ['Show all steps', 'Include units'],
        targetModel: 'claude' as const,
      };

      const result = await agent['generatePrompt'](input);

      expect(result.prompt).toBeDefined();
      expect(result.prompt).toContain('Calculate');
      expect(result.prompt).toContain('NEC');
      expect(result.prompt).toContain('Show all steps');
      expect(result.metadata.necReferences).toBeInstanceOf(Array);
    });

    it('should generate code-generation prompt with safety', async () => {
      const input = {
        task: 'wire sizing calculator function',
        taskType: 'code-generation' as const,
        targetModel: 'gpt' as const,
      };

      const result = await agent['generatePrompt'](input);

      expect(result.prompt).toContain('MANDATORY REQUIREMENTS');
      expect(result.prompt).toContain('Input validation');
      expect(result.prompt).toContain('Error handling');
      expect(result.metadata.includesSafety).toBe(true);
    });
  });

  describe('validate-safety', () => {
    it('should validate safe prompts', async () => {
      const input = {
        prompt: `Calculate voltage drop following NEC standards.
                 Use Decimal precision for calculations.
                 Verify all inputs and include error handling.
                 Apply safety margins and validate outputs.`,
        taskType: 'calculation',
        riskLevel: 'high' as const,
      };

      const result = await agent['validateSafety'](input);

      expect(result.isSafe).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.safetyScore).toBe(1);
    });

    it('should identify unsafe prompts', async () => {
      const input = {
        prompt: 'Calculate something quickly',
        taskType: 'calculation',
        riskLevel: 'high' as const,
      };

      const result = await agent['validateSafety'](input);

      expect(result.isSafe).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.missingSafetyElements).toContain('decimal precision');
      expect(result.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('manage-context', () => {
    it('should handle context within limits', async () => {
      const input = {
        fullContext: 'Short context that fits easily',
        maxTokens: 1000,
        taskType: 'general',
      };

      const result = await agent['manageContext'](input);

      expect(result.strategy).toBe('full-context');
      expect(result.reduction).toBe(0);
      expect(result.managedContext).toBe(input.fullContext);
    });

    it('should reduce large context intelligently', async () => {
      const largeContext = `
TASK: Complex calculation
REQUIREMENTS: Many detailed requirements here...
SAFETY: Critical safety information
NEC: Important code references
EXAMPLES: ${Array(100).fill('Example text').join(' ')}
BACKGROUND: Less important background information
      `.trim();

      const input = {
        fullContext: largeContext,
        maxTokens: 100,
        priority: ['task', 'safety'],
        taskType: 'calculation',
      };

      const result = await agent['manageContext'](input);

      expect(result.strategy).toBe('selective-context');
      expect(result.reduction).toBeGreaterThan(0);
      expect(result.includedSections).toContain('task');
      expect(result.includedSections).toContain('safety');
    });
  });

  describe('generate-examples', () => {
    it('should generate voltage drop examples', async () => {
      const input = {
        taskType: 'voltage-drop' as const,
        count: 2,
        complexity: 'intermediate' as const,
      };

      const result = await agent['generateExamples'](input);

      expect(result.examples).toHaveLength(2);
      expect(result.examples[0]).toHaveProperty('prompt');
      expect(result.examples[0]).toHaveProperty('expected');
      expect(result.examples[0]).toHaveProperty('necRef');
    });

    it('should cache generated examples', async () => {
      const input = {
        taskType: 'load-calculation' as const,
        count: 1,
        complexity: 'basic' as const,
      };

      const result1 = await agent['generateExamples'](input);
      expect(result1.source).toBe('generated');

      const result2 = await agent['generateExamples'](input);
      expect(result2.source).toBe('cache');
      expect(result2.examples).toEqual(result1.examples);
    });
  });

  describe('analyze-tokens', () => {
    it('should analyze token usage', async () => {
      const input = {
        prompt: 'Calculate the voltage drop for a 100ft run of 12 AWG wire.',
        model: 'claude' as const,
        includeOptimizations: false,
      };

      const result = await agent['analyzeTokens'](input);

      expect(result.tokens).toBeGreaterThan(0);
      expect(result.estimatedCost).toBeGreaterThan(0);
      expect(result.breakdown).toBeDefined();
      expect(result.breakdown.totalLines).toBeGreaterThan(0);
    });

    it('should provide optimization suggestions', async () => {
      const input = {
        prompt: 'Please ensure that you calculate the voltage drop following National Electrical Code standards.',
        model: 'gpt' as const,
        includeOptimizations: true,
      };

      const result = await agent['analyzeTokens'](input);

      expect(result.optimizations).toBeDefined();
      expect(result.optimizations.tokenSavings).toBeGreaterThan(0);
      expect(result.optimizations.suggestions).toBeInstanceOf(Array);
    });
  });

  describe('Integration', () => {
    it('should handle full prompt optimization workflow', async () => {
      // Start with a basic prompt
      const originalPrompt = 'calculate wire size for 50 amp circuit at 240 volts';

      // Optimize it
      const optimized = await agent['optimizePrompt']({
        prompt: originalPrompt,
        model: 'claude',
        context: {
          projectType: 'residential',
          voltage: '240V',
          safetyCritical: true,
        },
        optimizationGoals: ['safety', 'accuracy', 'token-efficiency'],
      });

      // Validate the optimized prompt
      const validation = await agent['validateSafety']({
        prompt: optimized.optimized,
        taskType: 'calculation',
        riskLevel: 'high',
      });

      expect(validation.isSafe).toBe(true);
      expect(validation.safetyScore).toBeGreaterThan(0.8);

      // Analyze token usage
      const analysis = await agent['analyzeTokens']({
        prompt: optimized.optimized,
        model: 'claude',
        includeOptimizations: false,
      });

      expect(analysis.tokens).toBeLessThan(1000);
    });
  });
});