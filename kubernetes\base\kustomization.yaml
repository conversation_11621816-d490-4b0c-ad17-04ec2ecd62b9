apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: electrical-app

resources:
  - namespace.yaml
  - configmaps.yaml
  - secrets.yaml
  - backend-deployment.yaml
  - frontend-deployment.yaml
  - agents-deployment.yaml
  - postgres-statefulset.yaml
  - redis-statefulset.yaml
  - ingress.yaml
  - hpa.yaml
  - network-policies.yaml

commonLabels:
  app.kubernetes.io/name: electrical-app
  app.kubernetes.io/part-of: electrical-contracting

images:
  - name: electrical/backend
    newTag: latest
  - name: electrical/frontend
    newTag: latest
  - name: electrical/agents
    newTag: latest