import { Router } from 'express';
import { z } from 'zod';
import { PanelSchema, CircuitSchema } from '@electrical/shared';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validate';
import { panelService } from '../services/panel.service';
import { circuitService } from '../services/circuit.service';
import { asyncHandler } from '../utils/async-handler';

const router: Router = Router();

// All routes require authentication
router.use(authenticate);

// Panel routes
router.post(
  '/',
  validate(PanelSchema),
  asyncHandler(async (req, res) => {
    const panel = await panelService.createPanel(req.body);
    res.status(201).json({ success: true, data: panel });
  })
);

router.get(
  '/project/:projectId',
  asyncHandler(async (req, res) => {
    const panels = await panelService.getPanelsByProject(req.params.projectId);
    res.json({ success: true, data: panels });
  })
);

router.get(
  '/:panelId',
  asyncHandler(async (req, res) => {
    const panel = await panelService.getPanel(req.params.panelId);
    res.json({ success: true, data: panel });
  })
);

router.put(
  '/:panelId',
  validate(PanelSchema.partial()),
  asyncHandler(async (req, res) => {
    const panel = await panelService.updatePanel(req.params.panelId, req.body);
    res.json({ success: true, data: panel });
  })
);

router.delete(
  '/:panelId',
  asyncHandler(async (req, res) => {
    await panelService.deletePanel(req.params.panelId);
    res.json({ success: true, message: 'Panel deleted successfully' });
  })
);

router.post(
  '/:panelId/calculate-load',
  asyncHandler(async (req, res) => {
    const loadCalculation = await panelService.calculatePanelLoad(
      req.params.panelId,
      req.user!.id
    );
    res.json({ success: true, data: loadCalculation });
  })
);

router.get(
  '/:panelId/schedule',
  asyncHandler(async (req, res) => {
    const schedule = await panelService.getPanelSchedule(req.params.panelId);
    res.json({ success: true, data: schedule });
  })
);

// Circuit routes
router.post(
  '/:panelId/circuits',
  validate(CircuitSchema),
  asyncHandler(async (req, res) => {
    const circuit = await circuitService.createCircuit({
      ...req.body,
      panel_id: req.params.panelId
    });
    res.status(201).json({ success: true, data: circuit });
  })
);

router.get(
  '/:panelId/circuits',
  asyncHandler(async (req, res) => {
    const circuits = await circuitService.getCircuitsByPanel(req.params.panelId);
    res.json({ success: true, data: circuits });
  })
);

router.put(
  '/circuits/:circuitId',
  validate(CircuitSchema.partial()),
  asyncHandler(async (req, res) => {
    const circuit = await circuitService.updateCircuit(req.params.circuitId, req.body);
    res.json({ success: true, data: circuit });
  })
);

router.delete(
  '/circuits/:circuitId',
  asyncHandler(async (req, res) => {
    await circuitService.deleteCircuit(req.params.circuitId);
    res.json({ success: true, message: 'Circuit deleted successfully' });
  })
);

router.post(
  '/:panelId/circuits/bulk',
  validate(z.object({
    circuits: z.array(CircuitSchema)
  })),
  asyncHandler(async (req, res) => {
    const circuits = await circuitService.bulkCreateCircuits(
      req.params.panelId,
      req.body.circuits
    );
    res.status(201).json({ success: true, data: circuits });
  })
);

router.put(
  '/circuits/:circuitId/move',
  validate(z.object({
    circuit_number: z.number().int().positive()
  })),
  asyncHandler(async (req, res) => {
    await circuitService.moveCircuit(req.params.circuitId, req.body.circuit_number);
    res.json({ success: true, message: 'Circuit moved successfully' });
  })
);

router.post(
  '/:panelId/balance',
  asyncHandler(async (req, res) => {
    await circuitService.balancePanelLoads(req.params.panelId);
    res.json({ success: true, message: 'Panel loads balanced successfully' });
  })
);

export default router;