import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { WireSizeCalculation } from '../../types/electrical';
import { CalculationService } from '../../services/calculationService';
import { COMMON_VOLTAGES, NEC_TABLES } from '../../constants/electrical';

export const WireSizeCalculator: React.FC = () => {
  const [calculation, setCalculation] = useState<WireSizeCalculation>({
    current: 0,
    voltage: 120,
    phase: 1,
    wireType: 'copper',
    insulation: 'THHN',
    temperature: 86,
    conduitFill: 1,
    recommendedSize: '',
    ampacity: 0,
    derating: 1,
    timestamp: new Date(),
  });

  const [showDerating, setShowDerating] = useState(false);

  const handleCalculate = () => {
    if (calculation.current <= 0) {
      Alert.alert('Error', 'Please enter a valid current value');
      return;
    }

    const result = CalculationService.calculateWireSize(calculation);
    setCalculation(result);

    // Save for offline access
    CalculationService.saveCalculation('wire-size', result);
  };

  const renderTemperatureDerating = () => {
    const temps = [
      { temp: '60-70°F', factor: 1.0 },
      { temp: '71-77°F', factor: 0.94 },
      { temp: '78-86°F', factor: 0.88 },
      { temp: '87-95°F', factor: 0.82 },
      { temp: '96-104°F', factor: 0.75 },
      { temp: '105-113°F', factor: 0.67 },
      { temp: '114-122°F', factor: 0.58 },
    ];

    return (
      <View style={styles.deratingChart}>
        <Text style={styles.deratingTitle}>Temperature Derating Factors</Text>
        {temps.map((item, index) => (
          <View key={index} style={styles.deratingRow}>
            <Text style={styles.deratingTemp}>{item.temp}</Text>
            <View style={styles.deratingBarContainer}>
              <View 
                style={[
                  styles.deratingBar, 
                  { width: `${item.factor * 100}%` }
                ]} 
              />
              <Text style={styles.deratingFactor}>{(item.factor * 100).toFixed(0)}%</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderAmpacityTable = () => {
    const ampacityTable = calculation.wireType === 'copper' 
      ? NEC_TABLES.COPPER_AMPACITY_75C 
      : NEC_TABLES.ALUMINUM_AMPACITY_75C;

    const relevantSizes = Object.entries(ampacityTable).slice(0, 10);

    return (
      <View style={styles.ampacityTable}>
        <Text style={styles.ampacityTitle}>
          {calculation.wireType === 'copper' ? 'Copper' : 'Aluminum'} Wire Ampacities @ 75°C
        </Text>
        <View style={styles.tableHeader}>
          <Text style={styles.tableHeaderText}>AWG/MCM</Text>
          <Text style={styles.tableHeaderText}>Ampacity</Text>
          <Text style={styles.tableHeaderText}>Derated</Text>
        </View>
        {relevantSizes.map(([size, ampacity]) => {
          const derated = Math.round(ampacity * calculation.derating);
          const isRecommended = size === calculation.recommendedSize;
          return (
            <View 
              key={size} 
              style={[
                styles.tableRow,
                isRecommended && styles.recommendedRow
              ]}
            >
              <Text style={[styles.tableCell, isRecommended && styles.recommendedText]}>
                {size}
              </Text>
              <Text style={[styles.tableCell, isRecommended && styles.recommendedText]}>
                {ampacity}A
              </Text>
              <Text style={[styles.tableCell, isRecommended && styles.recommendedText]}>
                {derated}A
              </Text>
            </View>
          );
        })}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Wire Size Calculator</Text>
        <Text style={styles.subtitle}>With Temperature Derating</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Load Information</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Current Load (Amperes)</Text>
          <TextInput
            style={styles.input}
            value={calculation.current.toString()}
            onChangeText={(text) => setCalculation({ ...calculation, current: parseFloat(text) || 0 })}
            keyboardType="numeric"
            placeholder="Enter current in amps"
          />
        </View>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Voltage</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.voltage}
                onValueChange={(value) => setCalculation({ ...calculation, voltage: value })}
                style={styles.picker}
              >
                {[...COMMON_VOLTAGES.RESIDENTIAL, ...COMMON_VOLTAGES.COMMERCIAL].map(v => (
                  <Picker.Item key={v} label={`${v}V`} value={v} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Phase</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.phase}
                onValueChange={(value) => setCalculation({ ...calculation, phase: value as 1 | 3 })}
                style={styles.picker}
              >
                <Picker.Item label="Single Phase" value={1} />
                <Picker.Item label="Three Phase" value={3} />
              </Picker>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Wire Configuration</Text>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Wire Type</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.wireType}
                onValueChange={(value) => setCalculation({ ...calculation, wireType: value as 'copper' | 'aluminum' })}
                style={styles.picker}
              >
                <Picker.Item label="Copper" value="copper" />
                <Picker.Item label="Aluminum" value="aluminum" />
              </Picker>
            </View>
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Insulation Type</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.insulation}
                onValueChange={(value) => setCalculation({ ...calculation, insulation: value as any })}
                style={styles.picker}
              >
                <Picker.Item label="THHN" value="THHN" />
                <Picker.Item label="THWN" value="THWN" />
                <Picker.Item label="XHHW" value="XHHW" />
                <Picker.Item label="USE" value="USE" />
              </Picker>
            </View>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Ambient Temperature (°F)</Text>
            <TextInput
              style={styles.input}
              value={calculation.temperature.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, temperature: parseInt(text) || 86 })}
              keyboardType="numeric"
              placeholder="86"
            />
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Conductors in Conduit</Text>
            <TextInput
              style={styles.input}
              value={calculation.conduitFill.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, conduitFill: parseInt(text) || 1 })}
              keyboardType="numeric"
              placeholder="1"
            />
          </View>
        </View>
      </View>

      <TouchableOpacity
        style={styles.deratingToggle}
        onPress={() => setShowDerating(!showDerating)}
      >
        <Text style={styles.deratingToggleText}>
          {showDerating ? 'Hide' : 'Show'} Derating Chart
        </Text>
      </TouchableOpacity>

      {showDerating && renderTemperatureDerating()}

      <TouchableOpacity style={styles.calculateButton} onPress={handleCalculate}>
        <Text style={styles.calculateButtonText}>Calculate Wire Size</Text>
      </TouchableOpacity>

      {calculation.recommendedSize && (
        <View style={styles.resultSection}>
          <View style={styles.resultCard}>
            <Text style={styles.resultCardTitle}>Recommended Wire Size</Text>
            <Text style={styles.resultCardValue}>{calculation.recommendedSize} AWG/MCM</Text>
            <Text style={styles.resultCardSubtext}>
              {calculation.wireType === 'copper' ? 'Copper' : 'Aluminum'} {calculation.insulation}
            </Text>
          </View>

          <View style={styles.resultDetails}>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Base Ampacity:</Text>
              <Text style={styles.resultValue}>{calculation.ampacity} A</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Derating Factor:</Text>
              <Text style={styles.resultValue}>{(calculation.derating * 100).toFixed(0)}%</Text>
            </View>
            <View style={styles.resultRow}>
              <Text style={styles.resultLabel}>Derated Ampacity:</Text>
              <Text style={styles.resultValue}>
                {Math.round(calculation.ampacity * calculation.derating)} A
              </Text>
            </View>
          </View>

          {renderAmpacityTable()}

          <View style={styles.necReference}>
            <Text style={styles.necReferenceText}>
              Per NEC Table 310.16 and Article 310.15(B)
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  inputGroup: {
    marginBottom: 15,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  halfInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  deratingToggle: {
    alignItems: 'center',
    marginVertical: 10,
  },
  deratingToggleText: {
    color: '#2196F3',
    fontSize: 16,
    textDecorationLine: 'underline',
  },
  deratingChart: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  deratingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  deratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  deratingTemp: {
    width: 80,
    fontSize: 12,
    color: '#666',
  },
  deratingBarContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  deratingBar: {
    height: 20,
    backgroundColor: '#2196F3',
    borderRadius: 10,
  },
  deratingFactor: {
    marginLeft: 10,
    fontSize: 12,
    color: '#333',
  },
  calculateButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 18,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  calculateButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultSection: {
    marginBottom: 20,
  },
  resultCard: {
    backgroundColor: '#4CAF50',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  resultCardTitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 10,
  },
  resultCardValue: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
  },
  resultCardSubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  resultDetails: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  resultLabel: {
    fontSize: 16,
    color: '#666',
  },
  resultValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  ampacityTable: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ampacityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    padding: 10,
    borderRadius: 5,
  },
  tableHeaderText: {
    flex: 1,
    fontWeight: 'bold',
    color: '#333',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  recommendedRow: {
    backgroundColor: '#e8f5e9',
  },
  tableCell: {
    flex: 1,
    color: '#666',
  },
  recommendedText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  necReference: {
    alignItems: 'center',
    marginTop: 10,
  },
  necReferenceText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});