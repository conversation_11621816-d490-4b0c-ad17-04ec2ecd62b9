import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Project } from '@electrical/shared';
import { api } from '../../services/api';
import { 
  BuildingOfficeIcon, 
  HomeIcon, 
  CogIcon,
  BoltIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';

export const ProjectList: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const response = await api.get('/projects');
      // Handle paginated response
      if (response.data && Array.isArray(response.data.data)) {
        setProjects(response.data.data);
      } else if (Array.isArray(response.data)) {
        // Handle case where API returns array directly
        setProjects(response.data);
      } else {
        // If response doesn't have expected structure, set empty array
        console.error('Unexpected response structure:', response.data);
        setProjects([]);
      }
      setError(null);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error loading projects:', err);
      setProjects([]); // Ensure projects is always an array
    } finally {
      setLoading(false);
    }
  };

  const getProjectIcon = (type: string) => {
    switch (type) {
      case 'RESIDENTIAL':
        return <HomeIcon className="h-12 w-12 text-blue-600" />;
      case 'COMMERCIAL':
        return <BuildingOfficeIcon className="h-12 w-12 text-green-600" />;
      case 'INDUSTRIAL':
        return <CogIcon className="h-12 w-12 text-purple-600" />;
      default:
        return <BoltIcon className="h-12 w-12 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-gray-100 text-gray-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800';
      case 'ON_HOLD':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p className="font-medium">Error loading projects</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Projects</h1>
        <button
          onClick={() => navigate('/projects/new')}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          New Project
        </button>
      </div>

      {projects.length === 0 ? (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <BoltIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No projects found
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by creating a new project.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <div
              key={project.id}
              className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => navigate(`/projects/${project.id}`)}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  {getProjectIcon(project.type)}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {project.status.replace('_', ' ')}
                  </span>
                </div>
                
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {project.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {project.address}, {project.city}, {project.state}
                </p>
                
                <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Service</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {project.service_size}A
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Voltage</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {project.voltage_system}
                    </p>
                  </div>
                </div>

                {project.permit_number && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Permit: {project.permit_number}
                    </p>
                  </div>
                )}

                <div className="mt-4 flex flex-wrap gap-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/projects/${project.id}/panels`);
                    }}
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                  >
                    <BoltIcon className="h-4 w-4 mr-1" />
                    Panels
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/projects/${project.id}/permits`);
                    }}
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    Permits
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/projects/${project.id}/estimates`);
                    }}
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                  >
                    <ClipboardDocumentListIcon className="h-4 w-4 mr-1" />
                    Estimates
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};