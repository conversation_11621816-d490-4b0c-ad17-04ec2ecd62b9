import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface UIState {
  theme: 'light' | 'dark';
  isLoading: boolean;
  toasts: Toast[];
  activeModal: string | null;
  bottomSheetOpen: boolean;
}

const initialState: UIState = {
  theme: 'light',
  isLoading: false,
  toasts: [],
  activeModal: null,
  bottomSheetOpen: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    toggleTheme: state => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    showToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {
      const toast: Toast = {
        ...action.payload,
        id: `${Date.now()}-${Math.random()}`,
        duration: action.payload.duration || 3000,
      };
      state.toasts.push(toast);
    },
    hideToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(t => t.id !== action.payload);
    },
    clearToasts: state => {
      state.toasts = [];
    },
    setActiveModal: (state, action: PayloadAction<string | null>) => {
      state.activeModal = action.payload;
    },
    setBottomSheetOpen: (state, action: PayloadAction<boolean>) => {
      state.bottomSheetOpen = action.payload;
    },
  },
});

export const {
  setTheme,
  toggleTheme,
  setLoading,
  showToast,
  hideToast,
  clearToasts,
  setActiveModal,
  setBottomSheetOpen,
} = uiSlice.actions;

export default uiSlice.reducer;