import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { enhancedAuthService, EnhancedLoginCredentials } from '@services/enhancedAuthService';
import { securityService } from '@services/securityService';
import { AuthState, User, RegisterData } from '@types/auth';

interface EnhancedAuthState extends AuthState {
  isLocked: boolean;
  requiresReauth: boolean;
  biometricEnabled: boolean;
  pinEnabled: boolean;
  lastAuthTime: number | null;
}

const initialState: EnhancedAuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  isLocked: false,
  requiresReauth: false,
  biometricEnabled: false,
  pinEnabled: false,
  lastAuthTime: null,
};

export const enhancedLogin = createAsyncThunk(
  'auth/enhancedLogin',
  async (credentials: EnhancedLoginCredentials) => {
    await enhancedAuthService.initialize();
    const response = await enhancedAuthService.login(credentials);
    return response;
  }
);

export const biometricLogin = createAsyncThunk(
  'auth/biometricLogin',
  async () => {
    const response = await enhancedAuthService.loginWithBiometrics();
    return response;
  }
);

export const pinLogin = createAsyncThunk(
  'auth/pinLogin',
  async (pin: string) => {
    const response = await enhancedAuthService.loginWithPin(pin);
    return response;
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (data: RegisterData) => {
    await enhancedAuthService.initialize();
    const response = await enhancedAuthService.register(data);
    return response;
  }
);

export const logout = createAsyncThunk('auth/logout', async () => {
  await enhancedAuthService.logout();
});

export const checkAuthStatus = createAsyncThunk(
  'auth/checkAuthStatus',
  async () => {
    const status = await enhancedAuthService.checkAuthStatus();
    const config = await securityService.getSecurityConfig();
    return { ...status, config };
  }
);

export const unlockApp = createAsyncThunk(
  'auth/unlockApp',
  async () => {
    await enhancedAuthService.unlockApp();
  }
);

export const setupPin = createAsyncThunk(
  'auth/setupPin',
  async (pin: string) => {
    await enhancedAuthService.setupPin(pin);
  }
);

export const enableBiometric = createAsyncThunk(
  'auth/enableBiometric',
  async () => {
    await enhancedAuthService.enableBiometricAuth();
  }
);

const enhancedAuthSlice = createSlice({
  name: 'enhancedAuth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
    clearAuth: state => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
      state.isLocked = false;
      state.requiresReauth = false;
      state.lastAuthTime = null;
    },
    setLocked: (state, action: PayloadAction<boolean>) => {
      state.isLocked = action.payload;
      if (action.payload) {
        state.requiresReauth = true;
      }
    },
    updateLastAuthTime: state => {
      state.lastAuthTime = Date.now();
    },
  },
  extraReducers: builder => {
    builder
      // Enhanced Login
      .addCase(enhancedLogin.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(enhancedLogin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isLocked = false;
        state.requiresReauth = false;
        state.lastAuthTime = Date.now();
      })
      .addCase(enhancedLogin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      })
      // Biometric Login
      .addCase(biometricLogin.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(biometricLogin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isLocked = false;
        state.requiresReauth = false;
        state.lastAuthTime = Date.now();
      })
      .addCase(biometricLogin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Biometric login failed';
      })
      // PIN Login
      .addCase(pinLogin.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(pinLogin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isLocked = false;
        state.requiresReauth = false;
        state.lastAuthTime = Date.now();
      })
      .addCase(pinLogin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'PIN login failed';
      })
      // Register
      .addCase(register.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isLocked = false;
        state.requiresReauth = false;
        state.lastAuthTime = Date.now();
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Registration failed';
      })
      // Logout
      .addCase(logout.fulfilled, state => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
        state.isLocked = true;
        state.requiresReauth = false;
        state.lastAuthTime = null;
      })
      // Check Auth Status
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isAuthenticated = action.payload.isAuthenticated;
        state.isLocked = action.payload.isLocked;
        state.requiresReauth = action.payload.requiresReauth;
        state.biometricEnabled = action.payload.config.enableBiometrics;
        state.pinEnabled = action.payload.config.enablePinCode;
      })
      // Unlock App
      .addCase(unlockApp.fulfilled, state => {
        state.isLocked = false;
        state.requiresReauth = false;
        state.lastAuthTime = Date.now();
      })
      // Setup PIN
      .addCase(setupPin.fulfilled, state => {
        state.pinEnabled = true;
      })
      // Enable Biometric
      .addCase(enableBiometric.fulfilled, state => {
        state.biometricEnabled = true;
      });
  },
});

export const { setUser, setToken, clearAuth, setLocked, updateLastAuthTime } = enhancedAuthSlice.actions;
export default enhancedAuthSlice.reducer;