import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { z } from 'zod';
import { MemoryType } from '../infrastructure/memory-store';

// Debugging task schemas
const errorDiagnosisSchema = z.object({
  errorType: z.enum(['calculation', 'compliance', 'system', 'data', 'integration']),
  errorMessage: z.string(),
  context: z.record(z.any()),
  stackTrace: z.string().optional(),
});

const complianceFixSchema = z.object({
  report: z.object({
    violations: z.array(z.any()),
    warnings: z.array(z.any()),
    checkType: z.string(),
  }),
  parameters: z.record(z.any()),
});

const calculationVerificationSchema = z.object({
  calculationType: z.string(),
  input: z.record(z.any()),
  output: z.record(z.any()),
  necReferences: z.array(z.string()).optional(),
});

const performanceAnalysisSchema = z.object({
  component: z.string(),
  metrics: z.record(z.number()),
  threshold: z.record(z.number()).optional(),
});

// Error patterns for electrical calculations
const CALCULATION_ERROR_PATTERNS = {
  'voltage-drop-excessive': {
    pattern: /voltage drop.*exceeds.*limit/i,
    category: 'compliance',
    solutions: [
      'Increase conductor size',
      'Reduce circuit length',
      'Split load across multiple circuits',
      'Use aluminum conductors for cost-effective larger sizes',
    ],
  },
  'conduit-overfill': {
    pattern: /conduit fill.*exceeds.*percent/i,
    category: 'compliance',
    solutions: [
      'Use larger conduit size',
      'Split conductors across multiple conduits',
      'Use compact conductor types (XHHW-2)',
      'Verify conductor dimensions in calculations',
    ],
  },
  'ampacity-insufficient': {
    pattern: /ampacity.*less than.*required/i,
    category: 'safety',
    solutions: [
      'Increase wire gauge size',
      'Check temperature derating factors',
      'Verify bundling derating calculations',
      'Consider parallel conductors for large loads',
    ],
  },
  'grounding-undersized': {
    pattern: /ground.*size.*insufficient/i,
    category: 'safety',
    solutions: [
      'Size grounding conductor per Table 250.122',
      'Verify overcurrent device rating',
      'Check for parallel conductor requirements',
    ],
  },
};

// Common electrical safety issues
const SAFETY_CHECKS = {
  'arc-flash': {
    description: 'Arc flash hazard not evaluated',
    requirement: 'NEC 110.16',
    action: 'Perform arc flash study and apply warning labels',
  },
  'working-space': {
    description: 'Insufficient working clearance',
    requirement: 'NEC 110.26',
    action: 'Ensure minimum 3ft clearance in front of equipment',
  },
  'disconnect-means': {
    description: 'Missing required disconnect',
    requirement: 'NEC 430.102',
    action: 'Install properly rated disconnect within sight of equipment',
  },
  'gfci-protection': {
    description: 'Missing GFCI protection',
    requirement: 'NEC 210.8',
    action: 'Install GFCI protection for required locations',
  },
};

export class DebuggingAgent extends BaseAgent {
  private errorPatterns: Map<string, any> = new Map();
  private knownFixes: Map<string, string[]> = new Map();
  private performanceBaselines: Map<string, number> = new Map();

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'diagnose-error',
        description: 'Diagnose and provide solutions for errors',
        inputSchema: errorDiagnosisSchema,
      },
      {
        name: 'suggest-compliance-fixes',
        description: 'Suggest fixes for NEC compliance violations',
        inputSchema: complianceFixSchema,
      },
      {
        name: 'verify-calculation',
        description: 'Verify electrical calculation accuracy',
        inputSchema: calculationVerificationSchema,
      },
      {
        name: 'analyze-performance',
        description: 'Analyze system performance and bottlenecks',
        inputSchema: performanceAnalysisSchema,
      },
      {
        name: 'troubleshoot-installation',
        description: 'Troubleshoot electrical installation issues',
      },
      {
        name: 'validate-safety',
        description: 'Validate electrical safety requirements',
      },
    ];

    super({
      ...config,
      capabilities,
    });
  }

  protected async onInitialize(): Promise<void> {
    // Load error patterns
    Object.entries(CALCULATION_ERROR_PATTERNS).forEach(([key, pattern]) => {
      this.errorPatterns.set(key, pattern);
    });

    // Load known fixes from memory
    const fixes = await this.retrieveKnowledge(['error', 'fix'], 50);
    fixes.forEach(item => {
      if (item.content.error && item.content.solutions) {
        this.knownFixes.set(item.content.error, item.content.solutions);
      }
    });

    // Set performance baselines
    this.performanceBaselines.set('calculation-time', 1000); // 1 second
    this.performanceBaselines.set('api-response', 200); // 200ms
    this.performanceBaselines.set('memory-usage', 100); // 100MB

    await this.log('Debugging agent initialized', {
      level: 'info',
      errorPatterns: this.errorPatterns.size,
      knownFixes: this.knownFixes.size,
    });
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'diagnose-error':
        return this.diagnoseError(data);
      case 'suggest-compliance-fixes':
        return this.suggestComplianceFixes(data);
      case 'verify-calculation':
        return this.verifyCalculation(data);
      case 'analyze-performance':
        return this.analyzePerformance(data);
      case 'troubleshoot-installation':
        return this.troubleshootInstallation(data);
      case 'validate-safety':
        return this.validateSafety(data);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Diagnose errors and provide solutions
  private async diagnoseError(data: z.infer<typeof errorDiagnosisSchema>): Promise<any> {
    const { errorType, errorMessage, context, stackTrace } = data;

    const diagnosis = {
      errorType,
      severity: this.assessSeverity(errorType, errorMessage),
      rootCause: '',
      solutions: [] as any[],
      preventionTips: [] as string[],
      relatedErrors: [] as any[],
    };

    // Analyze error message
    for (const [key, pattern] of this.errorPatterns.entries()) {
      if (pattern.pattern.test(errorMessage)) {
        diagnosis.rootCause = key;
        diagnosis.solutions = pattern.solutions.map((solution: string) => ({
          solution,
          confidence: 0.9,
          effort: this.estimateEffort(solution),
        }));
        break;
      }
    }

    // Check known fixes
    const knownFix = this.knownFixes.get(errorMessage);
    if (knownFix) {
      diagnosis.solutions.push(...knownFix.map(fix => ({
        solution: fix,
        confidence: 0.95,
        effort: 'LOW',
        source: 'historical',
      })));
    }

    // Analyze context for specific issues
    if (errorType === 'calculation') {
      const calcDiagnosis = this.diagnoseCalculationError(context);
      diagnosis.solutions.push(...calcDiagnosis.solutions);
      diagnosis.preventionTips.push(...calcDiagnosis.preventionTips);
    }

    // Find related errors from history
    const relatedErrors = await this.findRelatedErrors(errorMessage);
    diagnosis.relatedErrors = relatedErrors;

    // Generate prevention tips
    diagnosis.preventionTips.push(...this.generatePreventionTips(errorType, diagnosis.rootCause));

    // Store diagnosis in memory
    await this.storeKnowledge(
      {
        error: errorMessage,
        diagnosis: diagnosis.rootCause,
        solutions: diagnosis.solutions.map(s => s.solution),
        timestamp: new Date(),
      },
      ['error', 'diagnosis', errorType],
      0.8
    );

    return diagnosis;
  }

  // Suggest fixes for compliance violations
  private async suggestComplianceFixes(data: z.infer<typeof complianceFixSchema>): Promise<any> {
    const { report, parameters } = data;

    const fixes = {
      violations: [] as any[],
      warnings: [] as any[],
      estimatedCost: 0,
      estimatedTime: 0,
      priority: [] as any[],
    };

    // Process violations
    for (const violation of report.violations) {
      const fix = this.generateComplianceFix(violation, parameters);
      fixes.violations.push(fix);
      fixes.estimatedCost += fix.estimatedCost;
      fixes.estimatedTime += fix.estimatedTime;
    }

    // Process warnings
    for (const warning of report.warnings) {
      const fix = this.generateWarningFix(warning, parameters);
      fixes.warnings.push(fix);
    }

    // Prioritize fixes
    fixes.priority = this.prioritizeFixes([...fixes.violations, ...fixes.warnings]);

    // Generate implementation plan
    const implementationPlan = this.generateImplementationPlan(fixes);

    return {
      ...fixes,
      implementationPlan,
      totalViolations: report.violations.length,
      totalWarnings: report.warnings.length,
      complianceScore: this.calculateComplianceScore(report),
    };
  }

  // Verify calculation accuracy
  private async verifyCalculation(data: z.infer<typeof calculationVerificationSchema>): Promise<any> {
    const { calculationType, input, output, necReferences } = data;

    const verification = {
      valid: true,
      errors: [] as any[],
      warnings: [] as any[],
      crossChecks: [] as any[],
      confidence: 1.0,
    };

    // Perform type-specific verification
    switch (calculationType) {
      case 'load':
        this.verifyLoadCalculation(input, output, verification);
        break;
      case 'voltage-drop':
        this.verifyVoltageDropCalculation(input, output, verification);
        break;
      case 'conduit-fill':
        this.verifyConduitFillCalculation(input, output, verification);
        break;
      case 'wire-size':
        this.verifyWireSizeCalculation(input, output, verification);
        break;
    }

    // Cross-check with alternative methods
    const crossCheckResult = await this.performCrossCheck(calculationType, input);
    verification.crossChecks.push(crossCheckResult);

    // Verify NEC references
    if (necReferences) {
      const refCheck = this.verifyNECReferences(calculationType, necReferences);
      if (!refCheck.valid) {
        verification.warnings.push(refCheck);
      }
    }

    // Calculate confidence score
    verification.confidence = this.calculateVerificationConfidence(verification);

    // Store verification result
    await this.storeKnowledge(
      {
        calculationType,
        inputSummary: this.summarizeInput(input),
        valid: verification.valid,
        confidence: verification.confidence,
        issues: verification.errors.length + verification.warnings.length,
      },
      ['calculation', 'verification', calculationType],
      0.6
    );

    return verification;
  }

  // Analyze system performance
  private async analyzePerformance(data: z.infer<typeof performanceAnalysisSchema>): Promise<any> {
    const { component, metrics, threshold } = data;

    const analysis = {
      component,
      status: 'HEALTHY' as 'HEALTHY' | 'WARNING' | 'CRITICAL',
      bottlenecks: [] as any[],
      recommendations: [] as string[],
      trends: {} as Record<string, string>,
      score: 100,
    };

    // Compare metrics against baselines
    for (const [metric, value] of Object.entries(metrics)) {
      const baseline = threshold?.[metric] || this.performanceBaselines.get(metric) || 0;
      
      if (baseline > 0) {
        const ratio = value / baseline;
        
        if (ratio > 2) {
          analysis.status = 'CRITICAL';
          analysis.bottlenecks.push({
            metric,
            value,
            baseline,
            severity: 'HIGH',
            impact: `${((ratio - 1) * 100).toFixed(0)}% above baseline`,
          });
        } else if (ratio > 1.5) {
          if (analysis.status === 'HEALTHY') analysis.status = 'WARNING';
          analysis.bottlenecks.push({
            metric,
            value,
            baseline,
            severity: 'MEDIUM',
            impact: `${((ratio - 1) * 100).toFixed(0)}% above baseline`,
          });
        }
        
        // Calculate score impact
        analysis.score -= Math.min((ratio - 1) * 20, 30);
      }
    }

    // Analyze trends from historical data
    const history = await this.retrieveKnowledge(['performance', component], 20);
    analysis.trends = this.analyzeTrends(history, metrics);

    // Generate recommendations
    analysis.recommendations = this.generatePerformanceRecommendations(analysis);

    // Store performance data
    await this.memoryStore.store({
      type: MemoryType.EPISODIC,
      agentId: this.id,
      content: {
        component,
        metrics,
        status: analysis.status,
        timestamp: new Date(),
      },
      metadata: {
        tags: ['performance', component],
        importance: analysis.status === 'CRITICAL' ? 0.9 : 0.5,
      },
    });

    return analysis;
  }

  // Troubleshoot installation issues
  private async troubleshootInstallation(data: any): Promise<any> {
    const { issue, symptoms, equipment, environment } = data;

    const troubleshooting = {
      possibleCauses: [] as any[],
      diagnosticSteps: [] as any[],
      solutions: [] as any[],
      safetyWarnings: [] as string[],
      toolsRequired: [] as string[],
    };

    // Identify possible causes based on symptoms
    if (symptoms.includes('no power')) {
      troubleshooting.possibleCauses.push(
        { cause: 'Tripped breaker', probability: 0.8 },
        { cause: 'Loose connection', probability: 0.6 },
        { cause: 'Damaged conductor', probability: 0.3 },
        { cause: 'Faulty device', probability: 0.4 }
      );
    }

    if (symptoms.includes('intermittent')) {
      troubleshooting.possibleCauses.push(
        { cause: 'Loose connection', probability: 0.9 },
        { cause: 'Overloaded circuit', probability: 0.5 },
        { cause: 'Voltage fluctuation', probability: 0.4 },
        { cause: 'Damaged insulation', probability: 0.3 }
      );
    }

    if (symptoms.includes('burning smell')) {
      troubleshooting.safetyWarnings.push('DANGER: Turn off power immediately!');
      troubleshooting.possibleCauses.push(
        { cause: 'Overloaded circuit', probability: 0.9 },
        { cause: 'Loose connection causing arcing', probability: 0.8 },
        { cause: 'Undersized conductor', probability: 0.6 }
      );
    }

    // Generate diagnostic steps
    troubleshooting.diagnosticSteps = this.generateDiagnosticSteps(troubleshooting.possibleCauses);

    // Generate solutions
    for (const cause of troubleshooting.possibleCauses) {
      const solutions = this.generateInstallationSolutions(cause.cause, equipment);
      troubleshooting.solutions.push(...solutions);
    }

    // Identify required tools
    troubleshooting.toolsRequired = this.identifyRequiredTools(troubleshooting.diagnosticSteps);

    // Add safety warnings based on environment
    if (environment?.includes('wet')) {
      troubleshooting.safetyWarnings.push('Use GFCI protection when working in wet locations');
    }
    if (environment?.includes('confined')) {
      troubleshooting.safetyWarnings.push('Ensure proper ventilation in confined spaces');
    }

    // Sort by probability
    troubleshooting.possibleCauses.sort((a, b) => b.probability - a.probability);

    return troubleshooting;
  }

  // Validate electrical safety requirements
  private async validateSafety(data: any): Promise<any> {
    const { installation, location, equipment } = data;

    const validation = {
      safetyChecks: [] as any[],
      violations: [] as any[],
      recommendations: [] as string[],
      requiredPPE: [] as string[],
      hazardLevel: 'LOW' as 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME',
    };

    // Perform safety checks
    for (const [checkId, check] of Object.entries(SAFETY_CHECKS)) {
      const result = this.performSafetyCheck(check, installation, location);
      validation.safetyChecks.push({
        check: checkId,
        ...check,
        passed: result.passed,
        notes: result.notes,
      });

      if (!result.passed) {
        validation.violations.push({
          requirement: check.requirement,
          description: check.description,
          action: check.action,
          severity: 'HIGH',
        });
      }
    }

    // Check arc flash hazard
    if (equipment?.includes('panel') || equipment?.includes('switchgear')) {
      validation.hazardLevel = 'HIGH';
      validation.requiredPPE.push(
        'Arc-rated clothing (min 8 cal/cm²)',
        'Arc-rated face shield',
        'Insulated gloves',
        'Safety glasses'
      );
    }

    // Location-specific safety
    if (location?.includes('bathroom') || location?.includes('kitchen')) {
      validation.recommendations.push('Ensure GFCI protection is installed and tested');
    }

    if (location?.includes('outdoor')) {
      validation.recommendations.push('Use weatherproof enclosures and fittings');
      validation.recommendations.push('Ensure proper grounding of all metallic parts');
    }

    // Calculate overall safety score
    const safetyScore = this.calculateSafetyScore(validation);

    return {
      ...validation,
      safetyScore,
      compliant: validation.violations.length === 0,
      certificationReady: safetyScore >= 95,
    };
  }

  // Helper methods

  private assessSeverity(errorType: string, errorMessage: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    // Safety-related errors are always high severity
    if (errorMessage.toLowerCase().includes('safety') || 
        errorMessage.toLowerCase().includes('shock') ||
        errorMessage.toLowerCase().includes('fire')) {
      return 'CRITICAL';
    }

    // Compliance violations
    if (errorType === 'compliance') {
      if (errorMessage.includes('exceeds') && errorMessage.includes('limit')) {
        return 'HIGH';
      }
      return 'MEDIUM';
    }

    // Calculation errors
    if (errorType === 'calculation') {
      if (errorMessage.includes('infinity') || errorMessage.includes('NaN')) {
        return 'CRITICAL';
      }
      return 'MEDIUM';
    }

    return 'LOW';
  }

  private estimateEffort(solution: string): 'LOW' | 'MEDIUM' | 'HIGH' {
    const low = ['verify', 'check', 'review', 'calculate'];
    const high = ['replace', 'install', 'rewire', 'upgrade'];

    const solutionLower = solution.toLowerCase();
    
    if (low.some(word => solutionLower.includes(word))) return 'LOW';
    if (high.some(word => solutionLower.includes(word))) return 'HIGH';
    
    return 'MEDIUM';
  }

  private diagnoseCalculationError(context: any): any {
    const diagnosis = {
      solutions: [] as any[],
      preventionTips: [] as string[],
    };

    // Check for common calculation issues
    if (context.input) {
      // Division by zero
      if (context.error?.includes('division by zero')) {
        diagnosis.solutions.push({
          solution: 'Add validation for zero values in divisor',
          confidence: 1.0,
          effort: 'LOW',
        });
        diagnosis.preventionTips.push('Always validate input values before calculations');
      }

      // Negative values where not allowed
      if (context.input.length && context.input.length < 0) {
        diagnosis.solutions.push({
          solution: 'Add validation for positive values only',
          confidence: 0.9,
          effort: 'LOW',
        });
      }

      // Missing required parameters
      const required = ['voltage', 'current', 'length'];
      const missing = required.filter(param => !context.input[param]);
      if (missing.length > 0) {
        diagnosis.solutions.push({
          solution: `Add required parameters: ${missing.join(', ')}`,
          confidence: 0.95,
          effort: 'LOW',
        });
      }
    }

    return diagnosis;
  }

  private async findRelatedErrors(errorMessage: string): Promise<any[]> {
    const errors = await this.retrieveKnowledge(['error', 'diagnosis'], 10);
    
    return errors
      .filter(item => {
        const similarity = this.calculateSimilarity(errorMessage, item.content.error);
        return similarity > 0.6;
      })
      .map(item => ({
        error: item.content.error,
        solution: item.content.solutions?.[0],
        date: item.metadata.created,
      }));
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.toLowerCase().split(/\W+/);
    const words2 = str2.toLowerCase().split(/\W+/);
    
    const common = words1.filter(word => words2.includes(word));
    return common.length / Math.max(words1.length, words2.length);
  }

  private generatePreventionTips(errorType: string, rootCause: string): string[] {
    const tips: Record<string, string[]> = {
      calculation: [
        'Validate all input values before processing',
        'Use decimal arithmetic for precision',
        'Include unit tests for edge cases',
        'Add range checks for electrical values',
      ],
      compliance: [
        'Review NEC requirements before design',
        'Use automated compliance checking',
        'Keep updated with code changes',
        'Document all design decisions',
      ],
      system: [
        'Implement proper error handling',
        'Add logging for debugging',
        'Use circuit breakers for overload protection',
        'Regular maintenance checks',
      ],
    };

    return tips[errorType] || ['Follow best practices', 'Test thoroughly'];
  }

  private generateComplianceFix(violation: any, parameters: any): any {
    const fix = {
      violation: violation.code,
      description: violation.description,
      solution: '',
      steps: [] as string[],
      estimatedCost: 0,
      estimatedTime: 0,
      materials: [] as any[],
    };

    // Generate specific fixes based on violation type
    if (violation.code === '220.12') {
      fix.solution = 'Increase lighting load calculation';
      fix.steps = [
        'Recalculate using correct VA/sq ft for building type',
        'Update panel schedule with new loads',
        'Verify feeder and service sizing',
      ];
      fix.estimatedTime = 2; // hours
      fix.estimatedCost = 200; // labor only
    }

    if (violation.code.startsWith('210.8')) {
      fix.solution = 'Install GFCI protection';
      fix.steps = [
        'Identify circuits requiring GFCI',
        'Install GFCI breakers or outlets',
        'Test all GFCI devices',
        'Label protected outlets',
      ];
      fix.materials.push({ item: 'GFCI Breaker', quantity: 1, unitCost: 45 });
      fix.estimatedTime = 1;
      fix.estimatedCost = 45 + 85; // material + labor
    }

    if (violation.code === 'Chapter 9 Table 1') {
      fix.solution = 'Resolve conduit overfill';
      fix.steps = [
        'Calculate actual fill percentage',
        'Option 1: Use larger conduit size',
        'Option 2: Split into multiple conduits',
        'Update installation drawings',
      ];
      fix.estimatedTime = 4;
      fix.estimatedCost = 500;
    }

    return fix;
  }

  private generateWarningFix(warning: any, parameters: any): any {
    return {
      warning: warning.code,
      description: warning.description,
      suggestion: warning.suggestion || 'Review and consider updating',
      optional: true,
      benefitScore: this.calculateBenefitScore(warning),
    };
  }

  private calculateBenefitScore(warning: any): number {
    // Score based on impact
    if (warning.description.includes('safety')) return 0.9;
    if (warning.description.includes('efficiency')) return 0.7;
    if (warning.description.includes('future')) return 0.5;
    return 0.3;
  }

  private prioritizeFixes(fixes: any[]): any[] {
    return fixes
      .map(fix => ({
        ...fix,
        priority: this.calculateFixPriority(fix),
      }))
      .sort((a, b) => b.priority - a.priority);
  }

  private calculateFixPriority(fix: any): number {
    let priority = 0;
    
    // Violations get high priority
    if (fix.violation) priority += 50;
    
    // Safety issues get highest priority
    if (fix.description?.toLowerCase().includes('safety')) priority += 40;
    if (fix.description?.toLowerCase().includes('fire')) priority += 40;
    if (fix.description?.toLowerCase().includes('shock')) priority += 40;
    
    // Cost considerations
    if (fix.estimatedCost < 100) priority += 10;
    if (fix.estimatedCost > 1000) priority -= 10;
    
    // Time considerations
    if (fix.estimatedTime < 2) priority += 5;
    
    return priority;
  }

  private generateImplementationPlan(fixes: any): any[] {
    const plan = [];
    
    // Group by area/system
    const grouped = this.groupFixesByArea(fixes);
    
    // Create phases
    let phase = 1;
    for (const [area, areaFixes] of Object.entries(grouped)) {
      plan.push({
        phase,
        area,
        fixes: areaFixes,
        duration: this.estimatePhaseDuration(areaFixes as any[]),
        cost: this.estimatePhaseCost(areaFixes as any[]),
      });
      phase++;
    }
    
    return plan;
  }

  private groupFixesByArea(fixes: any): Record<string, any[]> {
    const grouped: Record<string, any[]> = {
      safety: [],
      power: [],
      lighting: [],
      grounding: [],
      other: [],
    };
    
    fixes.violations.forEach((fix: any) => {
      if (fix.description.includes('GFCI') || fix.description.includes('safety')) {
        grouped.safety.push(fix);
      } else if (fix.description.includes('load') || fix.description.includes('ampacity')) {
        grouped.power.push(fix);
      } else if (fix.description.includes('lighting')) {
        grouped.lighting.push(fix);
      } else if (fix.description.includes('ground')) {
        grouped.grounding.push(fix);
      } else {
        grouped.other.push(fix);
      }
    });
    
    // Remove empty groups
    return Object.fromEntries(
      Object.entries(grouped).filter(([_, fixes]) => fixes.length > 0)
    );
  }

  private estimatePhaseDuration(fixes: any[]): number {
    return fixes.reduce((total, fix) => total + (fix.estimatedTime || 0), 0);
  }

  private estimatePhaseCost(fixes: any[]): number {
    return fixes.reduce((total, fix) => total + (fix.estimatedCost || 0), 0);
  }

  private calculateComplianceScore(report: any): number {
    const violationPenalty = report.violations.length * 20;
    const warningPenalty = report.warnings.length * 5;
    
    return Math.max(0, 100 - violationPenalty - warningPenalty);
  }

  private verifyLoadCalculation(input: any, output: any, verification: any): void {
    // Check general lighting load
    if (input.building_type && input.square_footage) {
      const expectedMin = GENERAL_LIGHTING_LOADS[input.building_type] * input.square_footage;
      if (output.general_lighting_va < expectedMin) {
        verification.errors.push({
          field: 'general_lighting_va',
          expected: expectedMin,
          actual: output.general_lighting_va,
          message: 'General lighting load below NEC minimum',
        });
        verification.valid = false;
      }
    }

    // Check small appliance circuits
    if (input.small_appliance_circuits < 2 && input.building_type === 'DWELLING') {
      verification.warnings.push({
        field: 'small_appliance_circuits',
        message: 'Dwelling units require minimum 2 small appliance circuits',
        reference: 'NEC 210.11(C)(1)',
      });
    }

    // Verify demand factor application
    if (output.demand_factor_applied && !output.demand_factor) {
      verification.errors.push({
        field: 'demand_factor',
        message: 'Demand factor applied but not calculated',
      });
    }
  }

  private verifyVoltageDropCalculation(input: any, output: any, verification: any): void {
    // Verify physics
    if (output.voltageDropPercent < 0) {
      verification.errors.push({
        field: 'voltageDropPercent',
        message: 'Voltage drop cannot be negative',
      });
      verification.valid = false;
    }

    // Check if drop exceeds input voltage
    if (output.voltageDrop > input.voltage) {
      verification.errors.push({
        field: 'voltageDrop',
        message: 'Voltage drop exceeds supply voltage',
      });
      verification.valid = false;
    }

    // Verify power loss calculation
    const expectedPowerLoss = Math.pow(input.load_amps, 2) * output.totalResistance;
    const tolerance = 0.01;
    if (Math.abs(output.powerLossWatts - expectedPowerLoss) > expectedPowerLoss * tolerance) {
      verification.warnings.push({
        field: 'powerLossWatts',
        expected: expectedPowerLoss,
        actual: output.powerLossWatts,
        message: 'Power loss calculation may be incorrect',
      });
    }
  }

  private verifyConduitFillCalculation(input: any, output: any, verification: any): void {
    // Verify fill percentage
    if (output.fillPercentage > 100) {
      verification.errors.push({
        field: 'fillPercentage',
        message: 'Fill percentage cannot exceed 100%',
      });
      verification.valid = false;
    }

    // Verify conductor count matches
    const totalConductors = input.conductors.reduce((sum: number, c: any) => sum + c.count, 0);
    if (output.maxFillPercentage === 53 && totalConductors !== 1) {
      verification.errors.push({
        field: 'maxFillPercentage',
        message: '53% fill only allowed for 1 conductor',
      });
    }
  }

  private verifyWireSizeCalculation(input: any, output: any, verification: any): void {
    // Verify ampacity is sufficient
    if (output.deratedAmpacity < input.load_amps) {
      verification.errors.push({
        field: 'deratedAmpacity',
        message: 'Derated ampacity insufficient for load',
      });
      verification.valid = false;
    }

    // Verify derating factors
    if (output.temperatureFactor > 1) {
      verification.warnings.push({
        field: 'temperatureFactor',
        message: 'Temperature derating factor should not exceed 1.0',
      });
    }

    // Check ground wire size
    const tables = ['14', '12', '10', '8', '6', '4', '3', '2', '1', '1/0', '2/0', '3/0', '4/0'];
    const groundIndex = tables.indexOf(output.groundWireSize);
    const phaseIndex = tables.indexOf(output.phaseWireSize);
    
    if (groundIndex < phaseIndex) {
      verification.warnings.push({
        field: 'groundWireSize',
        message: 'Ground wire typically not larger than phase conductor',
      });
    }
  }

  private async performCrossCheck(calculationType: string, input: any): Promise<any> {
    // Simplified cross-check calculations
    const crossCheck = {
      method: 'alternative',
      result: null as any,
      variance: 0,
      notes: '',
    };

    switch (calculationType) {
      case 'voltage-drop':
        // Use simplified voltage drop formula
        const vd = (2 * input.length_feet * input.load_amps * 0.02) / 1000;
        crossCheck.result = { voltageDrop: vd };
        crossCheck.notes = 'Simplified calculation for verification';
        break;
        
      case 'load':
        // Quick load estimate
        const quickLoad = input.square_footage * 3; // 3 VA/sq ft minimum
        crossCheck.result = { estimatedLoad: quickLoad };
        crossCheck.notes = 'Quick estimate using 3 VA/sq ft';
        break;
    }

    return crossCheck;
  }

  private verifyNECReferences(calculationType: string, references: string[]): any {
    const expectedRefs: Record<string, string[]> = {
      load: ['220.12', '220.14', '220.42'],
      'voltage-drop': ['210.19', '215.2'],
      'conduit-fill': ['Chapter 9 Table 1'],
      'wire-size': ['310.16', '310.15'],
    };

    const expected = expectedRefs[calculationType] || [];
    const missing = expected.filter(ref => !references.includes(ref));

    return {
      valid: missing.length === 0,
      missing,
      message: missing.length > 0 ? `Missing NEC references: ${missing.join(', ')}` : 'All references included',
    };
  }

  private calculateVerificationConfidence(verification: any): number {
    let confidence = 1.0;
    
    // Reduce confidence for errors and warnings
    confidence -= verification.errors.length * 0.2;
    confidence -= verification.warnings.length * 0.05;
    
    // Increase confidence for successful cross-checks
    if (verification.crossChecks.length > 0) {
      confidence += 0.1;
    }
    
    return Math.max(0, Math.min(1, confidence));
  }

  private summarizeInput(input: any): any {
    // Create a summary of input for storage
    const summary: any = {};
    
    for (const [key, value] of Object.entries(input)) {
      if (typeof value === 'number') {
        summary[key] = value;
      } else if (typeof value === 'string' && value.length < 50) {
        summary[key] = value;
      }
    }
    
    return summary;
  }

  private analyzeTrends(history: any[], currentMetrics: Record<string, number>): Record<string, string> {
    const trends: Record<string, string> = {};
    
    if (history.length < 2) return trends;
    
    // Calculate average for each metric
    const averages: Record<string, number> = {};
    history.forEach(item => {
      if (item.content.metrics) {
        for (const [key, value] of Object.entries(item.content.metrics)) {
          if (!averages[key]) averages[key] = 0;
          averages[key] += value as number;
        }
      }
    });
    
    // Compare current to average
    for (const [key, current] of Object.entries(currentMetrics)) {
      const avg = averages[key] / history.length;
      const change = ((current - avg) / avg) * 100;
      
      if (change > 20) trends[key] = 'increasing';
      else if (change < -20) trends[key] = 'decreasing';
      else trends[key] = 'stable';
    }
    
    return trends;
  }

  private generatePerformanceRecommendations(analysis: any): string[] {
    const recommendations = [];
    
    for (const bottleneck of analysis.bottlenecks) {
      if (bottleneck.metric === 'calculation-time' && bottleneck.severity === 'HIGH') {
        recommendations.push('Consider caching frequently used calculations');
        recommendations.push('Optimize algorithm complexity');
      }
      
      if (bottleneck.metric === 'memory-usage' && bottleneck.severity === 'HIGH') {
        recommendations.push('Implement memory cleanup routines');
        recommendations.push('Reduce data retention period');
      }
      
      if (bottleneck.metric === 'api-response' && bottleneck.severity === 'MEDIUM') {
        recommendations.push('Implement request batching');
        recommendations.push('Add response caching layer');
      }
    }
    
    // Trend-based recommendations
    Object.entries(analysis.trends).forEach(([metric, trend]) => {
      if (trend === 'increasing' && metric === 'error-rate') {
        recommendations.push('Investigate increasing error rate');
      }
    });
    
    return recommendations;
  }

  private generateDiagnosticSteps(causes: any[]): any[] {
    const steps = [];
    const addedSteps = new Set();
    
    for (const cause of causes) {
      const causeSteps = this.getDiagnosticStepsForCause(cause.cause);
      
      causeSteps.forEach(step => {
        if (!addedSteps.has(step.action)) {
          addedSteps.add(step.action);
          steps.push({
            ...step,
            relevantFor: [cause.cause],
          });
        } else {
          // Add to relevant causes
          const existing = steps.find(s => s.action === step.action);
          if (existing) {
            existing.relevantFor.push(cause.cause);
          }
        }
      });
    }
    
    // Sort by safety and order
    return steps.sort((a, b) => {
      if (a.safety && !b.safety) return -1;
      if (!a.safety && b.safety) return 1;
      return a.order - b.order;
    });
  }

  private getDiagnosticStepsForCause(cause: string): any[] {
    const stepMap: Record<string, any[]> = {
      'Tripped breaker': [
        { action: 'Check panel for tripped breakers', order: 1, safety: true },
        { action: 'Reset breaker and monitor', order: 2 },
        { action: 'Measure circuit load if trips again', order: 3 },
      ],
      'Loose connection': [
        { action: 'Turn off power at breaker', order: 1, safety: true },
        { action: 'Check all accessible connections', order: 2 },
        { action: 'Tighten terminals to proper torque', order: 3 },
        { action: 'Look for signs of arcing or heat', order: 4 },
      ],
      'Overloaded circuit': [
        { action: 'Measure circuit current with clamp meter', order: 1 },
        { action: 'Calculate total connected load', order: 2 },
        { action: 'Identify devices to move to other circuits', order: 3 },
      ],
    };
    
    return stepMap[cause] || [
      { action: `Investigate ${cause}`, order: 1 },
    ];
  }

  private generateInstallationSolutions(cause: string, equipment: string): any[] {
    const solutions = [];
    
    const solutionMap: Record<string, any[]> = {
      'Loose connection': [
        {
          solution: 'Tighten all terminal connections',
          steps: [
            'Turn off power and verify with tester',
            'Remove device/fixture',
            'Clean terminals if corroded',
            'Retighten to manufacturer torque specs',
            'Apply anti-oxidant compound if aluminum',
          ],
          timeEstimate: 30, // minutes
        },
      ],
      'Overloaded circuit': [
        {
          solution: 'Add dedicated circuit for high-load devices',
          steps: [
            'Identify high-current devices',
            'Run new circuit from panel',
            'Install appropriate breaker size',
            'Move devices to new circuit',
          ],
          timeEstimate: 240,
        },
        {
          solution: 'Redistribute loads across circuits',
          steps: [
            'Map all devices on affected circuit',
            'Calculate individual device loads',
            'Identify available capacity on other circuits',
            'Rewire devices to balance loads',
          ],
          timeEstimate: 120,
        },
      ],
    };
    
    return solutionMap[cause] || [{
      solution: `Address ${cause}`,
      steps: ['Consult manufacturer documentation'],
      timeEstimate: 60,
    }];
  }

  private identifyRequiredTools(diagnosticSteps: any[]): string[] {
    const tools = new Set<string>();
    
    diagnosticSteps.forEach(step => {
      if (step.action.includes('measure') || step.action.includes('Measure')) {
        tools.add('Digital multimeter');
      }
      if (step.action.includes('current')) {
        tools.add('Clamp meter');
      }
      if (step.action.includes('tighten') || step.action.includes('Tighten')) {
        tools.add('Insulated screwdrivers');
        tools.add('Torque screwdriver');
      }
      if (step.action.includes('power')) {
        tools.add('Non-contact voltage tester');
      }
    });
    
    // Always include safety gear
    tools.add('Safety glasses');
    tools.add('Insulated gloves');
    
    return Array.from(tools);
  }

  private performSafetyCheck(check: any, installation: any, location: string): any {
    const result = {
      passed: true,
      notes: [] as string[],
    };
    
    // Arc flash check
    if (check.requirement === 'NEC 110.16' && installation?.includes('panel')) {
      if (!installation.includes('label') && !installation.includes('marked')) {
        result.passed = false;
        result.notes.push('Arc flash warning labels required on panels');
      }
    }
    
    // GFCI check
    if (check.requirement === 'NEC 210.8') {
      const gfciRequired = ['bathroom', 'kitchen', 'garage', 'outdoor', 'basement'];
      if (gfciRequired.some(loc => location?.includes(loc))) {
        if (!installation?.includes('gfci')) {
          result.passed = false;
          result.notes.push(`GFCI protection required in ${location}`);
        }
      }
    }
    
    return result;
  }

  private calculateSafetyScore(validation: any): number {
    let score = 100;
    
    // Deduct for violations
    score -= validation.violations.length * 15;
    
    // Deduct for failed checks
    const failedChecks = validation.safetyChecks.filter((c: any) => !c.passed).length;
    score -= failedChecks * 10;
    
    // Deduct for hazard level
    if (validation.hazardLevel === 'HIGH') score -= 10;
    if (validation.hazardLevel === 'EXTREME') score -= 20;
    
    return Math.max(0, score);
  }
}