import { useState, useCallback, memo } from 'react';
import { Plus, Search, MoreVertical, Edit, Trash2, Phone, Mail } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { Customer } from '@electrical/shared';
import { CustomerForm } from './CustomerForm';
import { VirtualList, useVirtualListPagination } from '../ui/VirtualList';
import { useDebounce } from '../../utils/debounce';

interface CustomerItemProps {
  customer: Customer;
  onEdit: (customer: Customer) => void;
  onDelete: (id: string) => void;
}

// Memoized customer item component
const CustomerItem = memo(({ customer, onEdit, onDelete }: CustomerItemProps) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className="px-6 py-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {customer.name}
            </h3>
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              customer.type === 'COMMERCIAL' 
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : customer.type === 'INDUSTRIAL'
                ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            }`}>
              {customer.type}
            </span>
          </div>
          <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
            {customer.email && (
              <a href={`mailto:${customer.email}`} className="flex items-center hover:text-primary-600">
                <Mail className="h-4 w-4 mr-1" />
                {customer.email}
              </a>
            )}
            {customer.phone && (
              <a href={`tel:${customer.phone}`} className="flex items-center hover:text-primary-600">
                <Phone className="h-4 w-4 mr-1" />
                {customer.phone}
              </a>
            )}
          </div>
          {customer.address && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {customer.address}
              {customer.city && `, ${customer.city}`}
              {customer.state && `, ${customer.state}`}
              {customer.zip && ` ${customer.zip}`}
            </p>
          )}
          {customer.credit_limit && (
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Credit Limit: ${customer.credit_limit.toLocaleString()} • Terms: {customer.payment_terms}
            </p>
          )}
        </div>
        <div className="ml-4 relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            onBlur={() => setTimeout(() => setShowMenu(false), 200)}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <MoreVertical className="h-5 w-5 text-gray-400" />
          </button>
          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    onEdit(customer);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={() => {
                    onDelete(customer.id);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

CustomerItem.displayName = 'CustomerItem';

export function CustomerListVirtual() {
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Fetch function for pagination
  const fetchCustomers = useCallback(async (page: number) => {
    const response = await api.get('/customers', {
      params: {
        page,
        limit: 50,
        search: debouncedSearchTerm || undefined,
        sortBy: 'name'
      }
    });

    return {
      data: response.data.data as Customer[],
      totalPages: response.data.pagination.totalPages
    };
  }, [debouncedSearchTerm]);

  const {
    items: customers,
    isLoading,
    error,
    hasMore,
    loadMore,
    reset
  } = useVirtualListPagination(fetchCustomers, 50);

  // Reset when search term changes
  useCallback(() => {
    reset();
  }, [debouncedSearchTerm, reset]);

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        await api.delete(`/customers/${id}`);
        toast.success('Customer deleted successfully');
        reset(); // Refresh the list
      } catch (error) {
        toast.error('Failed to delete customer');
      }
    }
  };

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowForm(true);
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingCustomer(null);
    reset(); // Refresh the list
  };

  const renderCustomer = useCallback(
    (customer: Customer) => (
      <CustomerItem
        customer={customer}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    ),
    []
  );

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-red-600">Failed to load customers</div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="mb-6 flex justify-between items-center">
        <div className="flex-1 max-w-lg">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 input"
            />
          </div>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary ml-4"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Customer
        </button>
      </div>

      <div className="flex-1 bg-white dark:bg-gray-800 shadow rounded-md overflow-hidden">
        <VirtualList
          items={customers}
          height="calc(100vh - 250px)" // Adjust based on your layout
          itemHeight={120} // Approximate height of each customer item
          renderItem={renderCustomer}
          onLoadMore={loadMore}
          isLoading={isLoading}
          hasMore={hasMore}
          emptyMessage="No customers found"
          getItemKey={(customer) => customer.id}
          overscan={10}
          className="scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100"
        />
      </div>

      {showForm && (
        <CustomerForm
          customer={editingCustomer}
          onClose={handleFormClose}
        />
      )}
    </div>
  );
}