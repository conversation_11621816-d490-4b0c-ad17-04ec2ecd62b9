const axios = require('axios');
const fs = require('fs').promises;

const API_BASE = 'http://localhost:3001/api';

// Test data
const testProjectId = 'test-project-' + Date.now();
const testPanelId = 'test-panel-' + Date.now();

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, token = null) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
    }
  };
  
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (data) {
    config.data = data;
  }
  
  try {
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status 
    };
  }
}

async function testPermitDocuments() {
  console.log('=== PERMIT DOCUMENTS API TEST ===\n');
  
  // 1. Check available endpoints
  console.log('1. CHECKING AVAILABLE PERMIT DOCUMENT ENDPOINTS:');
  console.log('   POST   /api/permit-documents                     - Create permit document');
  console.log('   GET    /api/permit-documents/project/:projectId  - Get project permits');
  console.log('   GET    /api/permit-documents/project/:projectId/latest/:documentType');
  console.log('   PUT    /api/permit-documents/:id                 - Update permit');
  console.log('   POST   /api/permit-documents/:id/submit          - Submit permit');
  console.log('   POST   /api/permit-documents/:id/generate-pdf    - Generate permit PDF');
  console.log('   POST   /api/permit-documents/generate-load-calc-pdf');
  console.log('   POST   /api/permit-documents/generate-panel-schedule-pdf');
  console.log('   POST   /api/permit-documents/:id/generate-inspection-pdf');
  console.log('   GET    /api/permit-documents/templates           - Get templates');
  console.log('   GET    /api/permit-documents/project/:projectId/compile-reports');
  console.log('   POST   /api/permit-documents/:id/sign            - Add signature');
  console.log('   PUT    /api/permit-documents/:id/status          - Update status (admin)');
  console.log('   POST   /api/permit-documents/:id/attachments     - Upload attachments\n');
  
  // 2. Test authentication
  console.log('2. TESTING AUTHENTICATION:');
  const loginResult = await apiCall('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'admin123'
  });
  
  console.log('Login attempt:', loginResult.success ? 'SUCCESS' : 'FAILED');
  if (!loginResult.success) {
    console.log('Error:', JSON.stringify(loginResult.error, null, 2));
    console.log('\nNote: Due to authentication issues, showing example requests instead.\n');
  }
  
  const token = loginResult.success ? loginResult.data.token : 'dummy-token';
  
  // 3. Show example permit document creation
  console.log('\n3. PERMIT DOCUMENT CREATION EXAMPLE:');
  const permitData = {
    project_id: testProjectId,
    document_type: 'PERMIT_APPLICATION',
    jurisdiction: 'City Building Department',
    jurisdiction_code: 'CBD-2025',
    title: 'Electrical Permit - Panel Upgrade and Circuit Additions',
    description: 'Permit application for residential panel upgrade from 100A to 200A service with additional circuits',
    form_data: {
      // Applicant Information
      applicant_name: 'ABC Electrical Contractors',
      applicant_license: 'EC-123456',
      applicant_phone: '555-0123',
      applicant_email: '<EMAIL>',
      
      // Property Information
      property_address: '123 Main Street',
      property_city: 'Springfield',
      property_state: 'IL',
      property_zip: '62701',
      property_owner: 'John Smith',
      property_type: 'RESIDENTIAL',
      
      // Project Information
      project_type: 'ALTERATION',
      project_description: 'Upgrade electrical service from 100A to 200A, replace main panel, add circuits for EV charger and workshop',
      estimated_cost: 25000,
      square_footage: 2500,
      
      // Electrical Details
      service_size: 200,
      voltage_system: '120/240V Single Phase',
      main_disconnect_type: 'Main Breaker',
      panel_count: 1,
      total_circuits: 42,
      grounding_system: 'Ground Rod and Water Pipe',
      
      // Additional Systems
      has_generator: false,
      has_solar: true,
      solar_system_size: 8.5,
      
      // Load Calculation Summary
      total_connected_load: 45600,
      total_demand_load: 38400,
      load_calculation_method: 'STANDARD',
      
      // Code Compliance
      nec_edition: '2020',
      local_amendments: 'City Amendment 2021-03',
      
      // Contractor Information
      contractor_name: 'ABC Electrical Contractors',
      contractor_license: 'EC-123456',
      contractor_phone: '555-0123',
      contractor_email: '<EMAIL>',
      contractor_insurance: 'State Insurance Co. Policy #INS-789012'
    }
  };
  
  console.log('Request:');
  console.log(`curl -X POST ${API_BASE}/permit-documents \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(permitData, null, 2)}'`);
  
  // 4. Test different permit types
  console.log('\n\n4. DIFFERENT PERMIT TYPES:');
  
  const permitTypes = [
    {
      type: 'PERMIT_APPLICATION',
      title: 'Building Electrical Permit',
      description: 'Standard electrical permit for building work'
    },
    {
      type: 'LOW_VOLTAGE',
      title: 'Low Voltage System Permit',
      description: 'Permit for security, data, and communication systems'
    },
    {
      type: 'INSPECTION_REQUEST',
      title: 'Electrical Inspection Request',
      description: 'Request for rough-in or final inspection'
    }
  ];
  
  for (const permit of permitTypes) {
    console.log(`\n${permit.type}:`);
    console.log(`- Title: ${permit.title}`);
    console.log(`- Description: ${permit.description}`);
  }
  
  // 5. Document generation formats
  console.log('\n\n5. DOCUMENT GENERATION FORMATS:');
  console.log('Currently supported formats:');
  console.log('- PDF: Primary format for all permit documents');
  console.log('- Features:');
  console.log('  * Professional formatting with headers and footers');
  console.log('  * Automatic page breaks for long content');
  console.log('  * Embedded calculations and technical data');
  console.log('  * Digital signature support');
  console.log('  * Barcode/QR code for permit tracking');
  
  // 6. Load calculation generation
  console.log('\n\n6. LOAD CALCULATION PDF GENERATION:');
  console.log('Request:');
  console.log(`curl -X POST ${API_BASE}/permit-documents/generate-load-calc-pdf \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "project_id": "${testProjectId}",
    "panel_id": "${testPanelId}"
  }'`);
  
  console.log('\nLoad calculation includes:');
  console.log('- General lighting loads (NEC 220.12)');
  console.log('- Small appliance loads (NEC 220.52)');
  console.log('- Laundry circuit loads (NEC 220.52)');
  console.log('- Fixed appliance loads');
  console.log('- HVAC loads with demand factors');
  console.log('- Motor loads');
  console.log('- Demand factor calculations');
  console.log('- Service size determination');
  
  // 7. Panel schedule generation
  console.log('\n\n7. PANEL SCHEDULE PDF GENERATION:');
  console.log('Request:');
  console.log(`curl -X POST ${API_BASE}/permit-documents/generate-panel-schedule-pdf \\
  -H "Authorization: Bearer \${TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "panel_id": "${testPanelId}"
  }'`);
  
  console.log('\nPanel schedule includes:');
  console.log('- Panel specifications (voltage, amperage, bus rating)');
  console.log('- Circuit listing with breaker sizes');
  console.log('- Load calculations per circuit');
  console.log('- Wire sizes and conduit details');
  console.log('- Circuit descriptions and locations');
  console.log('- Spare and space tracking');
  
  // 8. Available templates
  console.log('\n\n8. JURISDICTION TEMPLATES:');
  console.log('Request:');
  console.log(`curl -X GET "${API_BASE}/permit-documents/templates?state=IL" \\
  -H "Authorization: Bearer \${TOKEN}"`);
  
  console.log('\nTemplate features:');
  console.log('- State-specific forms');
  console.log('- Pre-filled jurisdiction information');
  console.log('- Local code amendments');
  console.log('- Required attachments checklist');
  console.log('- Fee calculation formulas');
  
  // 9. Document customization options
  console.log('\n\n9. DOCUMENT CUSTOMIZATION OPTIONS:');
  console.log('- Company branding (logo, colors)');
  console.log('- Custom headers and footers');
  console.log('- Jurisdiction-specific fields');
  console.log('- Additional attachments');
  console.log('- Digital signatures');
  console.log('- Watermarks for draft documents');
  
  // 10. Compliance features
  console.log('\n\n10. COMPLIANCE WITH LOCAL JURISDICTION REQUIREMENTS:');
  console.log('- NEC code edition selection (2017, 2020, 2023)');
  console.log('- Local amendments tracking');
  console.log('- Required calculations per jurisdiction');
  console.log('- Automatic form validation');
  console.log('- Missing information alerts');
  console.log('- Submission checklist generation');
  
  // Example of complete workflow
  console.log('\n\n11. COMPLETE PERMIT WORKFLOW EXAMPLE:');
  console.log('Step 1: Create permit document');
  console.log('Step 2: Generate load calculations');
  console.log('Step 3: Generate panel schedules');
  console.log('Step 4: Generate one-line diagram');
  console.log('Step 5: Compile all documents into permit package');
  console.log('Step 6: Add digital signatures');
  console.log('Step 7: Submit to jurisdiction');
  console.log('Step 8: Track approval status');
  
  console.log('\n\n=== TEST COMPLETE ===');
}

// Run the test
testPermitDocuments().catch(console.error);