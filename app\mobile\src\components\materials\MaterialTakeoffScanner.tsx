import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { Camera, useCameraDevices, useCodeScanner } from 'react-native-vision-camera';
import { Material, MaterialTakeoff, TakeoffItem } from '../../types/electrical';
import { materialService } from '../../services/materialService';

interface Props {
  takeoff: MaterialTakeoff;
  onUpdate: (takeoff: MaterialTakeoff) => void;
}

export const MaterialTakeoffScanner: React.FC<Props> = ({ takeoff, onUpdate }) => {
  const [scannerVisible, setScannerVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Material[]>([]);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [quantity, setQuantity] = useState('1');
  const [wastePercentage, setWastePercentage] = useState('10');

  const devices = useCameraDevices();
  const device = devices.back;

  const codeScanner = useCodeScanner({
    codeTypes: ['ean-13', 'ean-8', 'code-128', 'code-39', 'qr'],
    onCodeScanned: async (codes) => {
      const code = codes[0];
      if (code?.value) {
        setScannerVisible(false);
        searchMaterial(code.value);
      }
    },
  });

  const searchMaterial = async (query: string) => {
    try {
      const results = await materialService.searchMaterials(query);
      setSearchResults(results);
      
      if (results.length === 1) {
        setSelectedMaterial(results[0]);
      } else if (results.length === 0) {
        Alert.alert('Not Found', 'No material found with this code');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to search materials');
    }
  };

  const addMaterialToTakeoff = () => {
    if (!selectedMaterial || !quantity) {
      Alert.alert('Error', 'Please select a material and enter quantity');
      return;
    }

    const qty = parseFloat(quantity);
    const waste = parseFloat(wastePercentage) / 100;
    const totalQty = qty * (1 + waste);

    const newItem: TakeoffItem = {
      materialId: selectedMaterial.id,
      material: selectedMaterial,
      quantity: qty,
      waste: waste * 100,
      totalQuantity: totalQty,
      unitCost: selectedMaterial.price,
      totalCost: totalQty * selectedMaterial.price,
    };

    const updatedTakeoff = {
      ...takeoff,
      items: [...takeoff.items, newItem],
      totalCost: takeoff.totalCost + newItem.totalCost,
    };

    onUpdate(updatedTakeoff);
    resetForm();
  };

  const removeMaterialFromTakeoff = (index: number) => {
    const itemToRemove = takeoff.items[index];
    const updatedItems = takeoff.items.filter((_, i) => i !== index);
    
    const updatedTakeoff = {
      ...takeoff,
      items: updatedItems,
      totalCost: takeoff.totalCost - itemToRemove.totalCost,
    };

    onUpdate(updatedTakeoff);
  };

  const updateItemQuantity = (index: number, newQuantity: string) => {
    const qty = parseFloat(newQuantity) || 0;
    const item = takeoff.items[index];
    const totalQty = qty * (1 + item.waste / 100);
    
    const updatedItem = {
      ...item,
      quantity: qty,
      totalQuantity: totalQty,
      totalCost: totalQty * item.unitCost,
    };

    const updatedItems = [...takeoff.items];
    updatedItems[index] = updatedItem;

    const newTotalCost = updatedItems.reduce((sum, i) => sum + i.totalCost, 0);

    onUpdate({
      ...takeoff,
      items: updatedItems,
      totalCost: newTotalCost,
    });
  };

  const resetForm = () => {
    setSelectedMaterial(null);
    setQuantity('1');
    setWastePercentage('10');
    setSearchQuery('');
    setSearchResults([]);
  };

  const renderScanner = () => {
    if (!device) return null;

    return (
      <Modal
        visible={scannerVisible}
        animationType="slide"
        onRequestClose={() => setScannerVisible(false)}
      >
        <View style={styles.scannerContainer}>
          <Camera
            style={StyleSheet.absoluteFill}
            device={device}
            isActive={scannerVisible}
            codeScanner={codeScanner}
          />
          
          <View style={styles.scannerOverlay}>
            <View style={styles.scannerHeader}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setScannerVisible(false)}
              >
                <Text style={styles.closeButtonText}>Close</Text>
              </TouchableOpacity>
              <Text style={styles.scannerTitle}>Scan Material Barcode</Text>
            </View>
            
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.searchSection}>
        <Text style={styles.sectionTitle}>Add Materials</Text>
        
        <View style={styles.searchBar}>
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search by SKU, name, or scan..."
            onSubmitEditing={() => searchMaterial(searchQuery)}
          />
          <TouchableOpacity
            style={styles.scanButton}
            onPress={() => setScannerVisible(true)}
          >
            <Text style={styles.scanButtonText}>📷 Scan</Text>
          </TouchableOpacity>
        </View>

        {searchResults.length > 0 && (
          <View style={styles.searchResults}>
            {searchResults.map((material) => (
              <TouchableOpacity
                key={material.id}
                style={[
                  styles.materialResult,
                  selectedMaterial?.id === material.id && styles.selectedMaterial,
                ]}
                onPress={() => setSelectedMaterial(material)}
              >
                <Text style={styles.materialName}>{material.name}</Text>
                <Text style={styles.materialSku}>SKU: {material.sku}</Text>
                <Text style={styles.materialPrice}>${material.price}/{material.unit}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {selectedMaterial && (
          <View style={styles.addMaterialForm}>
            <View style={styles.formRow}>
              <View style={styles.formField}>
                <Text style={styles.fieldLabel}>Quantity</Text>
                <TextInput
                  style={styles.fieldInput}
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="numeric"
                  placeholder="1"
                />
              </View>
              
              <View style={styles.formField}>
                <Text style={styles.fieldLabel}>Waste %</Text>
                <TextInput
                  style={styles.fieldInput}
                  value={wastePercentage}
                  onChangeText={setWastePercentage}
                  keyboardType="numeric"
                  placeholder="10"
                />
              </View>
            </View>

            <TouchableOpacity
              style={styles.addButton}
              onPress={addMaterialToTakeoff}
            >
              <Text style={styles.addButtonText}>Add to Takeoff</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={styles.takeoffSection}>
        <View style={styles.takeoffHeader}>
          <Text style={styles.sectionTitle}>Material Takeoff</Text>
          <Text style={styles.totalCost}>
            Total: ${takeoff.totalCost.toFixed(2)}
          </Text>
        </View>

        {takeoff.items.length === 0 ? (
          <Text style={styles.emptyText}>No materials added yet</Text>
        ) : (
          takeoff.items.map((item, index) => (
            <View key={index} style={styles.takeoffItem}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemName}>{item.material.name}</Text>
                <Text style={styles.itemSku}>SKU: {item.material.sku}</Text>
                <View style={styles.itemQuantity}>
                  <TextInput
                    style={styles.quantityInput}
                    value={item.quantity.toString()}
                    onChangeText={(text) => updateItemQuantity(index, text)}
                    keyboardType="numeric"
                  />
                  <Text style={styles.unitText}>{item.material.unit}</Text>
                  {item.waste > 0 && (
                    <Text style={styles.wasteText}>+{item.waste}% waste</Text>
                  )}
                </View>
              </View>
              
              <View style={styles.itemPricing}>
                <Text style={styles.unitPrice}>
                  ${item.unitCost}/{item.material.unit}
                </Text>
                <Text style={styles.totalPrice}>
                  ${item.totalCost.toFixed(2)}
                </Text>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeMaterialFromTakeoff(index)}
                >
                  <Text style={styles.removeButtonText}>Remove</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </View>

      {renderScanner()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchSection: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  searchBar: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  searchInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginRight: 10,
  },
  scanButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    borderRadius: 8,
    justifyContent: 'center',
  },
  scanButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  searchResults: {
    marginBottom: 15,
  },
  materialResult: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedMaterial: {
    borderColor: '#2196F3',
    backgroundColor: '#E3F2FD',
  },
  materialName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  materialSku: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  materialPrice: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: 'bold',
    marginTop: 4,
  },
  addMaterialForm: {
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  formField: {
    flex: 1,
    marginHorizontal: 5,
  },
  fieldLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  fieldInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    backgroundColor: 'white',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  takeoffSection: {
    backgroundColor: 'white',
    padding: 15,
  },
  takeoffHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  totalCost: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    marginVertical: 30,
  },
  takeoffItem: {
    flexDirection: 'row',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  itemSku: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  itemQuantity: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  quantityInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
    width: 60,
    marginRight: 8,
  },
  unitText: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  wasteText: {
    fontSize: 12,
    color: '#FF9800',
  },
  itemPricing: {
    alignItems: 'flex-end',
  },
  unitPrice: {
    fontSize: 14,
    color: '#666',
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 4,
  },
  removeButton: {
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  removeButtonText: {
    color: '#f44336',
    fontSize: 14,
  },
  scannerContainer: {
    flex: 1,
  },
  scannerOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scannerHeader: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scannerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
  },
  scanFrame: {
    width: 250,
    height: 100,
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -50,
    marginLeft: -125,
  },
  corner: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderColor: '#2196F3',
  },
  topLeft: {
    top: 0,
    left: 0,
    borderTopWidth: 4,
    borderLeftWidth: 4,
  },
  topRight: {
    top: 0,
    right: 0,
    borderTopWidth: 4,
    borderRightWidth: 4,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderBottomWidth: 4,
    borderRightWidth: 4,
  },
});