const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Checking migration status...\n');

// Check if dev.db exists
const dbPath = path.join(__dirname, 'prisma', 'dev.db');
if (fs.existsSync(dbPath)) {
    console.log('✓ Database file exists at:', dbPath);
} else {
    console.log('✗ Database file not found at:', dbPath);
    console.log('  Run: npx prisma db push');
    process.exit(1);
}

// Try to run prisma migrate status
try {
    console.log('\n--- Prisma Migration Status ---');
    const output = execSync('npx prisma migrate status', { 
        encoding: 'utf-8',
        cwd: __dirname 
    });
    console.log(output);
} catch (error) {
    console.log('Error checking migration status:', error.message);
    if (error.stdout) {
        console.log('Output:', error.stdout.toString());
    }
    if (error.stderr) {
        console.log('Error output:', error.stderr.toString());
    }
}

// Check if we can query the migrations table
try {
    console.log('\n--- Applied Migrations ---');
    const sqlite3 = require('sqlite3').verbose();
    const db = new sqlite3.Database(dbPath);
    
    db.all("SELECT id, migration_name, finished_at, applied_steps_count FROM _prisma_migrations ORDER BY finished_at", (err, rows) => {
        if (err) {
            console.log('Error querying migrations table:', err.message);
            console.log('\nThe migrations table might not exist. Try running:');
            console.log('  npx prisma migrate deploy');
        } else {
            rows.forEach(row => {
                const status = row.finished_at ? '✓' : '✗';
                console.log(`${status} ${row.migration_name} - Applied steps: ${row.applied_steps_count}`);
            });
        }
        db.close();
    });
} catch (error) {
    console.log('SQLite3 not available. Install with: npm install sqlite3');
}