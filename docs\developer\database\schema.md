# Database Schema Documentation

This document provides a comprehensive overview of the database schema for the Electrical Contracting Application.

## 🗄️ Database Overview

- **Database System**: PostgreSQL 15+
- **ORM**: Prisma
- **Migration Tool**: Prisma Migrate
- **Connection Pooling**: PgBouncer (production)

## 📊 Schema Diagram

```mermaid
erDiagram
    User ||--o{ Project : creates
    User ||--o{ Estimate : creates
    User ||--o{ CalculationLog : performs
    Customer ||--o{ Project : has
    Customer ||--o{ Estimate : receives
    Project ||--o{ Panel : contains
    Project ||--o{ Material : uses
    Project ||--o{ Inspection : undergoes
    Project ||--o{ TimeEntry : tracks
    Panel ||--o{ Circuit : contains
    Estimate ||--o{ EstimateItem : contains
    Inspection ||--o{ InspectionItem : contains
    Material ||--o{ MaterialPrice : has

    User {
        string id PK
        string email UK
        string password_hash
        string name
        string role
        string refresh_token
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }

    Customer {
        string id PK
        string name
        string email
        string phone
        string address
        string city
        string state
        string zip
        string license_number
        datetime insurance_expiry
        float credit_limit
        string payment_terms
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }

    Project {
        string id PK
        string name
        string customer_id FK
        string status
        string type
        datetime start_date
        datetime end_date
        decimal budget
        decimal spent
        integer progress
        json address
        string created_by FK
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
```

## 📋 Core Tables

### users
Stores user account information and authentication data.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'electrician',
    refresh_token TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    INDEX idx_users_email (email),
    INDEX idx_users_deleted_at (deleted_at)
);
```

**Roles:**
- `admin`: Full system access
- `foreman`: Project management, team oversight
- `electrician`: Field work, time tracking
- `estimator`: Create and manage estimates
- `inspector`: View-only access for inspections

### customers
Customer information and billing details.

```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip VARCHAR(20),
    license_number VARCHAR(100),
    insurance_expiry DATE,
    credit_limit DECIMAL(10,2),
    payment_terms VARCHAR(20) DEFAULT 'NET30',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    INDEX idx_customers_name (name),
    INDEX idx_customers_deleted_at (deleted_at)
);
```

### projects
Main project information and status tracking.

```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    customer_id UUID REFERENCES customers(id),
    status VARCHAR(50) DEFAULT 'planning',
    type VARCHAR(50),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(12,2),
    spent DECIMAL(12,2) DEFAULT 0,
    progress INTEGER DEFAULT 0,
    address JSONB,
    permit_number VARCHAR(100),
    inspection_status VARCHAR(50),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    INDEX idx_projects_customer_id (customer_id),
    INDEX idx_projects_status (status),
    INDEX idx_projects_dates (start_date, end_date)
);
```

**Status Values:**
- `planning`: Initial planning phase
- `permitted`: Permits obtained
- `active`: Work in progress
- `inspection`: Awaiting inspection
- `completed`: Work completed
- `closed`: Project closed
- `on_hold`: Temporarily paused

### panels
Electrical panel information and specifications.

```sql
CREATE TABLE panels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(255),
    type VARCHAR(50),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    voltage INTEGER,
    phase VARCHAR(20),
    amperage INTEGER,
    spaces INTEGER,
    spaces_used INTEGER DEFAULT 0,
    mounting VARCHAR(50),
    main_breaker_size INTEGER,
    feed_from VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_panels_project_id (project_id)
);
```

### circuits
Individual circuits within panels.

```sql
CREATE TABLE circuits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    panel_id UUID REFERENCES panels(id) ON DELETE CASCADE,
    circuit_number INTEGER NOT NULL,
    name VARCHAR(255),
    breaker_size INTEGER,
    pole VARCHAR(20) DEFAULT 'single',
    voltage INTEGER,
    load_watts INTEGER,
    wire_size VARCHAR(20),
    wire_type VARCHAR(50),
    conduit_type VARCHAR(50),
    conduit_size VARCHAR(20),
    length INTEGER,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'planned',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(panel_id, circuit_number),
    INDEX idx_circuits_panel_id (panel_id)
);
```

### materials
Material inventory and project usage.

```sql
CREATE TABLE materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(100),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    description TEXT,
    unit VARCHAR(50),
    cost DECIMAL(10,2),
    price DECIMAL(10,2),
    min_stock INTEGER DEFAULT 0,
    current_stock INTEGER DEFAULT 0,
    location VARCHAR(255),
    specifications JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_materials_sku (sku),
    INDEX idx_materials_barcode (barcode),
    INDEX idx_materials_category (category),
    FULLTEXT INDEX idx_materials_search (name, description)
);
```

### project_materials
Junction table for materials used in projects.

```sql
CREATE TABLE project_materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    material_id UUID REFERENCES materials(id),
    quantity DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(10,2),
    location VARCHAR(255),
    installed_date DATE,
    installed_by UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_project_materials_project (project_id),
    INDEX idx_project_materials_material (material_id)
);
```

### calculations
Stores all electrical calculations for reference and compliance.

```sql
CREATE TABLE calculations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id),
    user_id UUID REFERENCES users(id),
    type VARCHAR(50) NOT NULL,
    name VARCHAR(255),
    inputs JSONB NOT NULL,
    results JSONB NOT NULL,
    nec_references TEXT[],
    warnings TEXT[],
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_calculations_project (project_id),
    INDEX idx_calculations_type (type),
    INDEX idx_calculations_created (created_at)
);
```

**Calculation Types:**
- `wire_size`
- `voltage_drop`
- `conduit_fill`
- `load_calculation`
- `short_circuit`
- `arc_flash`
- `motor_calculation`
- `transformer_sizing`

### inspections
Inspection scheduling and results.

```sql
CREATE TABLE inspections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled',
    scheduled_date TIMESTAMP,
    completed_date TIMESTAMP,
    inspector_name VARCHAR(255),
    inspector_id VARCHAR(100),
    result VARCHAR(50),
    notes TEXT,
    checklist_template VARCHAR(100),
    items JSONB,
    signatures JSONB,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_inspections_project (project_id),
    INDEX idx_inspections_status (status),
    INDEX idx_inspections_date (scheduled_date)
);
```

### time_entries
Labor hour tracking.

```sql
CREATE TABLE time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    project_id UUID REFERENCES projects(id),
    task_type VARCHAR(100),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration INTEGER, -- minutes
    location POINT,
    notes TEXT,
    approved BOOLEAN DEFAULT FALSE,
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_time_entries_user (user_id),
    INDEX idx_time_entries_project (project_id),
    INDEX idx_time_entries_date (start_time)
);
```

### estimates
Customer estimates and quotes.

```sql
CREATE TABLE estimates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES customers(id),
    project_id UUID REFERENCES projects(id),
    status VARCHAR(50) DEFAULT 'draft',
    valid_until DATE,
    subtotal DECIMAL(12,2),
    tax_rate DECIMAL(5,2),
    tax_amount DECIMAL(12,2),
    discount_percent DECIMAL(5,2),
    discount_amount DECIMAL(12,2),
    total DECIMAL(12,2),
    terms TEXT,
    notes TEXT,
    created_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    approved_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_estimates_number (number),
    INDEX idx_estimates_customer (customer_id),
    INDEX idx_estimates_status (status)
);
```

## 🔐 Security Tables

### api_keys
API key management for integrations.

```sql
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    permissions JSONB,
    last_used TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP,
    
    INDEX idx_api_keys_user (user_id),
    INDEX idx_api_keys_hash (key_hash)
);
```

### audit_logs
Comprehensive audit trail for compliance.

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id UUID,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_entity (entity_type, entity_id),
    INDEX idx_audit_created (created_at)
);
```

## 🎯 Indexes and Performance

### Critical Indexes
```sql
-- Frequently queried combinations
CREATE INDEX idx_projects_active ON projects(customer_id, status) 
    WHERE deleted_at IS NULL AND status = 'active';

CREATE INDEX idx_materials_search_gin ON materials 
    USING gin(to_tsvector('english', name || ' ' || description));

CREATE INDEX idx_calculations_recent ON calculations(created_at DESC) 
    WHERE created_at > CURRENT_DATE - INTERVAL '30 days';

-- Spatial index for location-based queries
CREATE INDEX idx_time_entries_location ON time_entries 
    USING gist(location);
```

### Partitioning Strategy
Large tables are partitioned for performance:

```sql
-- Time entries partitioned by month
CREATE TABLE time_entries_2024_01 PARTITION OF time_entries
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Audit logs partitioned by month
CREATE TABLE audit_logs_2024_01 PARTITION OF audit_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

## 🔄 Migration Guidelines

### Creating Migrations
```bash
# Create a new migration
cd app/backend
npx prisma migrate dev --name add_new_feature

# Apply migrations
npx prisma migrate deploy
```

### Migration Best Practices
1. Always backup before migrations
2. Test migrations on staging first
3. Include rollback procedures
4. Document breaking changes
5. Use transactions for data migrations

### Example Migration
```sql
-- Migration: Add equipment tracking
BEGIN;

CREATE TABLE equipment (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    serial_number VARCHAR(100) UNIQUE,
    category VARCHAR(100),
    last_inspection DATE,
    next_inspection DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_equipment_inspection ON equipment(next_inspection);

COMMIT;
```

## 🛡️ Data Integrity

### Constraints
```sql
-- Ensure valid progress values
ALTER TABLE projects 
    ADD CONSTRAINT chk_progress CHECK (progress >= 0 AND progress <= 100);

-- Ensure end date after start date
ALTER TABLE projects 
    ADD CONSTRAINT chk_project_dates CHECK (end_date >= start_date);

-- Ensure positive values
ALTER TABLE materials 
    ADD CONSTRAINT chk_positive_stock CHECK (current_stock >= 0);
```

### Triggers
```sql
-- Update project spent amount
CREATE OR REPLACE FUNCTION update_project_spent()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE projects 
    SET spent = (
        SELECT COALESCE(SUM(quantity * unit_cost), 0)
        FROM project_materials
        WHERE project_id = NEW.project_id
    )
    WHERE id = NEW.project_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_project_spent
AFTER INSERT OR UPDATE OR DELETE ON project_materials
FOR EACH ROW EXECUTE FUNCTION update_project_spent();
```

## 📊 Views and Materialized Views

### Commonly Used Views
```sql
-- Active projects summary
CREATE VIEW v_active_projects AS
SELECT 
    p.*,
    c.name as customer_name,
    COUNT(DISTINCT i.id) as inspection_count,
    COUNT(DISTINCT pe.id) as panel_count
FROM projects p
LEFT JOIN customers c ON p.customer_id = c.id
LEFT JOIN inspections i ON p.id = i.project_id
LEFT JOIN panels pe ON p.id = pe.project_id
WHERE p.deleted_at IS NULL 
    AND p.status IN ('active', 'inspection')
GROUP BY p.id, c.name;

-- Material usage statistics
CREATE MATERIALIZED VIEW mv_material_usage AS
SELECT 
    m.id,
    m.name,
    m.category,
    COUNT(DISTINCT pm.project_id) as projects_used,
    SUM(pm.quantity) as total_quantity,
    AVG(pm.unit_cost) as avg_cost
FROM materials m
LEFT JOIN project_materials pm ON m.id = pm.material_id
GROUP BY m.id, m.name, m.category;

-- Refresh materialized view daily
CREATE INDEX idx_mv_material_usage_category ON mv_material_usage(category);
```

## 🔧 Maintenance

### Regular Tasks
```sql
-- Vacuum and analyze tables
VACUUM ANALYZE projects, materials, calculations;

-- Update table statistics
ANALYZE;

-- Reindex heavily used tables
REINDEX TABLE projects;
REINDEX TABLE calculations;
```

### Archival Strategy
```sql
-- Archive old projects
INSERT INTO projects_archive 
SELECT * FROM projects 
WHERE status = 'closed' 
    AND updated_at < CURRENT_DATE - INTERVAL '2 years';

DELETE FROM projects 
WHERE status = 'closed' 
    AND updated_at < CURRENT_DATE - INTERVAL '2 years';
```

---

For database-related questions or issues, contact the database team or refer to the [Prisma documentation](https://www.prisma.io/docs).