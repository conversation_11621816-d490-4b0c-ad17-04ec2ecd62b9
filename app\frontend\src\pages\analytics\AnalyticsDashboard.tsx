import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card } from '../../components/ui/card';
import { Tabs } from '../../components/ui/tabs';
import { Button } from '../../components/ui/button';
import { analyticsService } from '../../services/analyticsService';
import ExecutiveDashboard from '../../components/analytics/ExecutiveDashboard';
import ProjectAnalyticsDashboard from '../../components/analytics/ProjectAnalyticsDashboard';
import LaborAnalyticsDashboard from '../../components/analytics/LaborAnalyticsDashboard';
import MaterialAnalyticsDashboard from '../../components/analytics/MaterialAnalyticsDashboard';
import ElectricalCalculationsAnalytics from '../../components/analytics/ElectricalCalculationsAnalytics';
import FinancialAnalyticsDashboard from '../../components/analytics/FinancialAnalyticsDashboard';
import PredictiveAnalyticsDashboard from '../../components/analytics/PredictiveAnalyticsDashboard';
import { Download, RefreshCw, Calendar, Filter } from 'lucide-react';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

interface DateRange {
  start: Date;
  end: Date;
}

const AnalyticsDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('executive');
  const [dateRange, setDateRange] = useState<DateRange>(() => 
    analyticsService.getDateRange('last30')
  );
  const [datePreset, setDatePreset] = useState('last30');
  const [isExporting, setIsExporting] = useState(false);

  const handleDatePresetChange = (preset: string) => {
    setDatePreset(preset);
    setDateRange(analyticsService.getDateRange(preset as any));
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      setIsExporting(true);
      const blob = await analyticsService.exportAnalytics(activeTab, format, {
        dateRange,
      });
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${activeTab}-analytics-${format(new Date(), 'yyyy-MM-dd')}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success(`Analytics exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export analytics');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleRefresh = () => {
    analyticsService.clearCache();
    // Trigger refetch for all queries
    window.location.reload();
  };

  const tabs = [
    { id: 'executive', label: 'Executive Dashboard', icon: '📊' },
    { id: 'projects', label: 'Project Analytics', icon: '🏗️' },
    { id: 'labor', label: 'Labor Analytics', icon: '👷' },
    { id: 'materials', label: 'Material Analytics', icon: '📦' },
    { id: 'calculations', label: 'Electrical Calculations', icon: '⚡' },
    { id: 'financial', label: 'Financial Analytics', icon: '💰' },
    { id: 'predictive', label: 'Predictive Analytics', icon: '🔮' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Analytics Dashboard
              </h1>
              <p className="mt-1 text-gray-500 dark:text-gray-400">
                Comprehensive insights into your electrical contracting business
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Date Range Selector */}
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-gray-500" />
                <select
                  value={datePreset}
                  onChange={(e) => handleDatePresetChange(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                >
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                  <option value="last30">Last 30 Days</option>
                  <option value="last90">Last 90 Days</option>
                </select>
              </div>

              {/* Export Options */}
              <div className="relative group">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={isExporting}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
                <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 hidden group-hover:block z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExport('pdf')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Export as PDF
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Export as Excel
                    </button>
                    <button
                      onClick={() => handleExport('csv')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      Export as CSV
                    </button>
                  </div>
                </div>
              </div>

              {/* Refresh Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-4" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  px-4 py-2 text-sm font-medium rounded-md transition-colors
                  ${activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'executive' && (
            <ExecutiveDashboard dateRange={dateRange} />
          )}
          {activeTab === 'projects' && (
            <ProjectAnalyticsDashboard dateRange={dateRange} />
          )}
          {activeTab === 'labor' && (
            <LaborAnalyticsDashboard dateRange={dateRange} />
          )}
          {activeTab === 'materials' && (
            <MaterialAnalyticsDashboard dateRange={dateRange} />
          )}
          {activeTab === 'calculations' && (
            <ElectricalCalculationsAnalytics dateRange={dateRange} />
          )}
          {activeTab === 'financial' && (
            <FinancialAnalyticsDashboard dateRange={dateRange} />
          )}
          {activeTab === 'predictive' && (
            <PredictiveAnalyticsDashboard />
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;