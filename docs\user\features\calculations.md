# Electrical Calculations Guide

This guide covers all electrical calculation tools available in the application, ensuring NEC compliance and accurate results for your electrical projects.

## 📐 Available Calculators

### 1. Wire Size Calculator

Determines the appropriate conductor size based on load requirements and installation conditions.

#### Input Parameters
- **Current (Amps)**: Load current in amperes
- **Voltage**: System voltage (120V, 208V, 240V, 277V, 480V, etc.)
- **Phase**: Single-phase or three-phase
- **Wire Type**: Copper or aluminum
- **Insulation Type**: THHN, THWN, XHHW, etc.
- **Temperature Rating**: 60°C, 75°C, or 90°C
- **Number of Current-Carrying Conductors**: For derating
- **Ambient Temperature**: For temperature correction
- **Installation Method**: Conduit, cable tray, direct burial

#### Results Include
- Recommended wire size (AWG/kcmil)
- Actual ampacity after adjustments
- Voltage drop at specified distance
- NEC code references
- Derating factors applied

#### Example Calculation
```
Load: 50A continuous
Voltage: 240V single-phase
Distance: 150 feet
Wire Type: Copper THHN
Temperature: 75°C
Ambient: 30°C
Conductors in conduit: 3

Result: #6 AWG Copper
Ampacity: 65A (base) × 0.80 (adjustment) = 52A
Voltage Drop: 2.3% (acceptable)
NEC References: 310.16, 310.15(B)(2)(a)
```

### 2. Voltage Drop Calculator

Calculates voltage drop in conductors and ensures compliance with NEC recommendations.

#### Input Parameters
- **Wire Size**: AWG or kcmil
- **Wire Type**: Copper or aluminum
- **Voltage**: System voltage
- **Phase**: Single-phase or three-phase
- **Current**: Load in amperes
- **Distance**: One-way distance in feet
- **Power Factor**: Typically 0.85-1.0
- **Conduit Type**: Steel, PVC, aluminum (affects impedance)

#### Results Include
- Voltage drop in volts
- Percentage voltage drop
- Voltage at load
- Power loss in watts
- Recommended maximum distance
- Graphical representation

#### NEC Guidelines
- **Branch Circuits**: 3% maximum
- **Feeders**: 3% maximum
- **Combined**: 5% maximum total

### 3. Conduit Fill Calculator

Ensures proper conduit sizing per NEC Chapter 9 requirements.

#### Input Parameters
- **Conduit Type**: EMT, IMC, RMC, PVC, etc.
- **Conduit Size**: ½" to 6"
- **Wire Sizes**: Multiple entries allowed
- **Wire Type**: THHN, XHHW, etc.
- **Number of Each Size**: Quantity of conductors

#### Results Include
- Percentage fill
- Maximum number of conductors allowed
- Visual fill representation
- Alternative conduit size recommendations
- NEC Table references

#### Fill Limits
- **1 conductor**: 53% fill
- **2 conductors**: 31% fill
- **3+ conductors**: 40% fill

### 4. Load Calculation

Performs residential and commercial load calculations per NEC Article 220.

#### Residential (Standard Method)
1. **General Lighting Load**
   - 3 VA per square foot
   - Small appliance circuits: 1,500 VA each (minimum 2)
   - Laundry circuit: 1,500 VA

2. **Appliance Loads**
   - Range: Use Table 220.55
   - Dryer: 5,000 VA minimum
   - Water heater: Nameplate rating
   - HVAC: Larger of heating or cooling

3. **Demand Factors**
   - First 3,000 VA at 100%
   - Next 117,000 VA at 35%
   - Remainder at 25%

#### Commercial
- Lighting: Based on Table 220.12
- Receptacles: 180 VA per receptacle
- Continuous loads: 125%
- Motor loads: 125% of largest motor

#### Results Include
- Total calculated load
- Recommended service size
- Panel size recommendations
- Load summary by category
- Printable load calculation form

### 5. Motor Calculations

Comprehensive motor circuit calculations per NEC Article 430.

#### Calculations Performed
- **Full Load Current**: From NEC tables or nameplate
- **Conductor Size**: 125% of FLC
- **Overload Protection**: 115-125% of FLC
- **Short Circuit Protection**: Per Table 430.52
- **Motor Starter Size**: NEMA size selection

#### Input Parameters
- Motor horsepower
- Voltage and phase
- Service factor
- Duty cycle
- Starting method
- Ambient temperature

### 6. Short Circuit Calculator

Calculates available fault current and validates protective device ratings.

#### Input Parameters
- **Source**: Utility transformer or generator
- **Transformer**: kVA, impedance, X/R ratio
- **Conductors**: Size, length, type
- **System Voltage**: Line-to-line voltage

#### Calculations Include
- **Infinite Bus Method**: Conservative approach
- **Point-to-Point Method**: Detailed calculation
- **Arc Flash**: Incident energy levels

#### Results Include
- Symmetrical fault current
- Asymmetrical fault current
- X/R ratio at fault point
- Protective device validation
- Arc flash category
- Required PPE level

### 7. Arc Flash Calculator

Determines incident energy and arc flash boundaries per IEEE 1584.

#### Input Parameters
- **System Voltage**: 208V to 15kV
- **Fault Current**: Available short circuit current
- **Working Distance**: 18" typical
- **Arc Duration**: Based on protective device
- **Enclosure Type**: Open air, box, MCC
- **Electrode Configuration**: VCB, VCBB, HCB

#### Results Include
- **Incident Energy**: cal/cm²
- **Arc Flash Boundary**: Distance in inches
- **PPE Category**: 1-4 or Dangerous
- **Clothing Requirements**: Specific PPE needed
- **Label Template**: Printable arc flash label

### 8. Grounding Calculator

Calculates grounding electrode conductor and equipment grounding conductor sizes.

#### Based On
- Service entrance conductor size
- Largest ungrounded conductor
- Multiple grounding electrodes
- Separately derived systems

#### References
- Table 250.66: Grounding electrode conductor
- Table 250.122: Equipment grounding conductor
- Special considerations for parallel runs

## 💡 Pro Tips

### Accuracy Best Practices
1. **Always Use Actual Conditions**: Don't assume standard temperatures
2. **Include All Derating Factors**: Multiple conductors, temperature, etc.
3. **Consider Voltage Drop**: Even if wire size is adequate for ampacity
4. **Document Assumptions**: For future reference and inspections

### Time-Saving Features
- **Save Calculations**: Store for project documentation
- **Create Templates**: For common calculation scenarios
- **Batch Calculate**: Multiple circuits at once
- **Export Results**: PDF, Excel, or CAD formats

### Mobile Calculations
- All calculators work offline
- Sync results when connected
- Voice input for hands-free operation
- Camera integration for nameplate capture

## 📋 Calculation Workflows

### New Service Installation
1. Perform load calculation
2. Size service conductors
3. Calculate grounding requirements
4. Verify voltage drop
5. Document for permit

### Panel Upgrade
1. Calculate existing loads
2. Add future loads (125% continuous)
3. Size new panel
4. Verify feeder sizing
5. Check grounding adequacy

### Motor Installation
1. Determine FLC from horsepower
2. Size conductors (125% FLC)
3. Select overload protection
4. Size short circuit protection
5. Calculate voltage drop
6. Verify starter size

## 🔍 Code References

Each calculation includes relevant NEC references:

- **Article 210**: Branch Circuits
- **Article 215**: Feeders  
- **Article 220**: Load Calculations
- **Article 230**: Services
- **Article 250**: Grounding
- **Article 310**: Conductors
- **Article 430**: Motors
- **Chapter 9**: Tables

## 📊 Reporting

### Calculation Reports Include
- Input parameters
- Calculation methodology
- Results with units
- Code references
- Engineer stamp block
- QR code for verification

### Export Options
- PDF for permits
- Excel for further analysis
- AutoCAD for drawings
- Email directly to inspectors

## ⚠️ Important Notes

1. **Calculations are per 2023 NEC**: Verify local amendments
2. **Professional Judgment**: Software assists but doesn't replace expertise
3. **Local Codes**: May have additional requirements
4. **Updates**: Calculator updates with each NEC cycle
5. **Liability**: Verify all calculations independently

## 🆘 Troubleshooting

### Common Issues
- **"Invalid Input"**: Check for proper units and ranges
- **"Exceeds Limits"**: Consider parallel conductors
- **"No Solution"**: May need to adjust parameters
- **Slow Performance**: Clear calculation history in settings

### Getting Help
- Hover over any field for help text
- Click "?" for detailed explanations  
- View example calculations
- Contact support for complex scenarios

## 🎓 Training Resources

- **Video Tutorials**: In-app calculation walkthroughs
- **Practice Mode**: Try calculations without saving
- **Calculation Library**: Common scenarios
- **Webinars**: Monthly calculation training

## ✅ Best Practices Checklist

- [ ] Verify all input parameters
- [ ] Consider all adjustment factors
- [ ] Check local code amendments
- [ ] Document special conditions
- [ ] Save calculations to project
- [ ] Generate report for records
- [ ] Review with senior electrician
- [ ] Update if conditions change