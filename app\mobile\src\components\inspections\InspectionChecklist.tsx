import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { launchImageLibrary, launchCamera } from 'react-native-image-picker';
import { Inspection, InspectionItem, InspectionPhoto } from '../../types/electrical';
import { INSPECTION_CATEGORIES } from '../../constants/electrical';

interface Props {
  inspection: Inspection;
  onUpdate: (inspection: Inspection) => void;
  onPhotoCapture: (item: InspectionItem, photo: InspectionPhoto) => void;
}

export const InspectionChecklist: React.FC<Props> = ({
  inspection,
  onUpdate,
  onPhotoCapture,
}) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<InspectionItem | null>(null);

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const updateItemStatus = (
    itemId: string,
    status: InspectionItem['status']
  ) => {
    const updatedChecklist = inspection.checklist.map(item =>
      item.id === itemId ? { ...item, status } : item
    );
    onUpdate({ ...inspection, checklist: updatedChecklist });
  };

  const capturePhoto = (item: InspectionItem) => {
    Alert.alert(
      'Add Photo',
      'Choose photo source',
      [
        { text: 'Camera', onPress: () => takePhoto(item) },
        { text: 'Gallery', onPress: () => selectPhoto(item) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const takePhoto = (item: InspectionItem) => {
    launchCamera(
      {
        mediaType: 'photo',
        quality: 0.8,
        saveToPhotos: true,
      },
      (response) => {
        if (response.assets?.[0]) {
          const photo: InspectionPhoto = {
            id: Date.now().toString(),
            uri: response.assets[0].uri!,
            caption: `Photo for ${item.description}`,
            location: item.category,
            timestamp: new Date(),
          };
          onPhotoCapture(item, photo);
        }
      }
    );
  };

  const selectPhoto = (item: InspectionItem) => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 0.8,
      },
      (response) => {
        if (response.assets?.[0]) {
          const photo: InspectionPhoto = {
            id: Date.now().toString(),
            uri: response.assets[0].uri!,
            caption: `Photo for ${item.description}`,
            location: item.category,
            timestamp: new Date(),
          };
          onPhotoCapture(item, photo);
        }
      }
    );
  };

  const getStatusColor = (status: InspectionItem['status']) => {
    switch (status) {
      case 'pass': return '#4CAF50';
      case 'fail': return '#f44336';
      case 'na': return '#9E9E9E';
      case 'pending': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const getCompletionPercentage = (category: string) => {
    const items = inspection.checklist.filter(item => item.category === category);
    const completed = items.filter(item => item.status !== 'pending').length;
    return Math.round((completed / items.length) * 100);
  };

  const renderInspectionItem = (item: InspectionItem) => (
    <View key={item.id} style={styles.checklistItem}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemDescription}>{item.description}</Text>
        {item.necCode && (
          <Text style={styles.necCode}>NEC {item.necCode}</Text>
        )}
      </View>

      <View style={styles.statusButtons}>
        {(['pass', 'fail', 'na', 'pending'] as const).map((status) => (
          <TouchableOpacity
            key={status}
            style={[
              styles.statusButton,
              item.status === status && {
                backgroundColor: getStatusColor(status),
              },
            ]}
            onPress={() => updateItemStatus(item.id, status)}
          >
            <Text
              style={[
                styles.statusButtonText,
                item.status === status && styles.statusButtonTextActive,
              ]}
            >
              {status.toUpperCase()}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.itemActions}>
        <TouchableOpacity
          style={styles.photoButton}
          onPress={() => capturePhoto(item)}
        >
          <Text style={styles.photoButtonText}>
            📷 {item.photos?.length || 0} Photos
          </Text>
        </TouchableOpacity>

        {item.status === 'fail' && (
          <View style={styles.failWarning}>
            <Text style={styles.failWarningText}>
              ⚠️ Failed item requires corrective action
            </Text>
          </View>
        )}
      </View>

      {item.photos && item.photos.length > 0 && (
        <ScrollView
          horizontal
          style={styles.photoScroll}
          showsHorizontalScrollIndicator={false}
        >
          {item.photos.map((photoUri, index) => (
            <Image
              key={index}
              source={{ uri: photoUri }}
              style={styles.thumbnail}
            />
          ))}
        </ScrollView>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.summary}>
        <Text style={styles.summaryTitle}>Inspection Progress</Text>
        <View style={styles.summaryStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {inspection.checklist.filter(i => i.status === 'pass').length}
            </Text>
            <Text style={styles.statLabel}>Passed</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: '#f44336' }]}>
              {inspection.checklist.filter(i => i.status === 'fail').length}
            </Text>
            <Text style={styles.statLabel}>Failed</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: '#FF9800' }]}>
              {inspection.checklist.filter(i => i.status === 'pending').length}
            </Text>
            <Text style={styles.statLabel}>Pending</Text>
          </View>
        </View>
      </View>

      {INSPECTION_CATEGORIES.map((category) => {
        const categoryItems = inspection.checklist.filter(
          item => item.category === category
        );
        if (categoryItems.length === 0) return null;

        const isExpanded = expandedCategories.has(category);
        const completionPercentage = getCompletionPercentage(category);

        return (
          <View key={category} style={styles.categorySection}>
            <TouchableOpacity
              style={styles.categoryHeader}
              onPress={() => toggleCategory(category)}
            >
              <View style={styles.categoryTitleRow}>
                <Text style={styles.categoryTitle}>{category}</Text>
                <Text style={styles.categoryCount}>
                  {categoryItems.filter(i => i.status !== 'pending').length}/
                  {categoryItems.length}
                </Text>
              </View>
              
              <View style={styles.progressBarContainer}>
                <View
                  style={[
                    styles.progressBar,
                    {
                      width: `${completionPercentage}%`,
                      backgroundColor: completionPercentage === 100 ? '#4CAF50' : '#2196F3',
                    },
                  ]}
                />
              </View>

              <Text style={styles.expandIcon}>
                {isExpanded ? '▼' : '▶'}
              </Text>
            </TouchableOpacity>

            {isExpanded && (
              <View style={styles.categoryItems}>
                {categoryItems.map(renderInspectionItem)}
              </View>
            )}
          </View>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  summary: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  categorySection: {
    backgroundColor: 'white',
    marginBottom: 10,
  },
  categoryHeader: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  categoryTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  categoryCount: {
    fontSize: 14,
    color: '#666',
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  expandIcon: {
    fontSize: 12,
    color: '#666',
    position: 'absolute',
    right: 15,
    top: 20,
  },
  categoryItems: {
    paddingHorizontal: 15,
  },
  checklistItem: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  itemHeader: {
    marginBottom: 10,
  },
  itemDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  necCode: {
    fontSize: 12,
    color: '#2196F3',
    fontStyle: 'italic',
  },
  statusButtons: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  statusButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginHorizontal: 3,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  statusButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  statusButtonTextActive: {
    color: 'white',
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  photoButton: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 5,
  },
  photoButtonText: {
    fontSize: 14,
    color: '#333',
  },
  failWarning: {
    flex: 1,
    marginLeft: 10,
  },
  failWarningText: {
    fontSize: 12,
    color: '#f44336',
  },
  photoScroll: {
    marginTop: 10,
  },
  thumbnail: {
    width: 60,
    height: 60,
    marginRight: 8,
    borderRadius: 5,
  },
});