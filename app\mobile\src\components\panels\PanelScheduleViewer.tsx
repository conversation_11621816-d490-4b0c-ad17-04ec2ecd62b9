import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Panel, Circuit } from '../../types/electrical';
import { panelService } from '../../services/panelService';

interface Props {
  panelId: string;
  onCircuitPress?: (circuit: Circuit) => void;
}

export const PanelScheduleViewer: React.FC<Props> = ({ panelId, onCircuitPress }) => {
  const [panel, setPanel] = useState<Panel | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPanel();
  }, [panelId]);

  const loadPanel = async () => {
    try {
      setLoading(true);
      const data = await panelService.getPanel(panelId);
      setPanel(data);
    } catch (error) {
      console.error('Error loading panel:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPanel();
    setRefreshing(false);
  };

  const getCircuitStatusColor = (status: Circuit['status']) => {
    switch (status) {
      case 'on': return '#4CAF50';
      case 'off': return '#9E9E9E';
      case 'tripped': return '#f44336';
      case 'maintenance': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const getLoadPercentage = () => {
    if (!panel) return 0;
    return Math.round((panel.totalLoad / (panel.amperage * panel.voltage)) * 100);
  };

  const renderCircuitRow = (circuit: Circuit, position: 'left' | 'right') => {
    const statusColor = getCircuitStatusColor(circuit.status);

    return (
      <TouchableOpacity
        key={circuit.id}
        style={[styles.circuitRow, position === 'right' && styles.circuitRowRight]}
        onPress={() => onCircuitPress?.(circuit)}
        activeOpacity={0.7}
      >
        <View style={[styles.circuitNumber, { backgroundColor: statusColor }]}>
          <Text style={styles.circuitNumberText}>{circuit.number}</Text>
        </View>
        <View style={styles.circuitInfo}>
          <Text style={styles.circuitName} numberOfLines={1}>
            {circuit.name}
          </Text>
          <View style={styles.circuitDetails}>
            <Text style={styles.circuitDetailText}>
              {circuit.amperage}A
            </Text>
            {circuit.poles > 1 && (
              <Text style={styles.circuitDetailText}>
                {circuit.poles}P
              </Text>
            )}
            {(circuit.afci || circuit.gfci) && (
              <View style={styles.protectionBadges}>
                {circuit.afci && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>AFCI</Text>
                  </View>
                )}
                {circuit.gfci && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>GFCI</Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
        <View style={styles.circuitLoad}>
          <Text style={styles.circuitLoadText}>{circuit.load}W</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  if (!panel) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load panel schedule</Text>
      </View>
    );
  }

  const leftCircuits = panel.circuits.filter(c => c.number % 2 === 1);
  const rightCircuits = panel.circuits.filter(c => c.number % 2 === 0);

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.panelName}>{panel.name}</Text>
        <Text style={styles.panelLocation}>{panel.location}</Text>
        <View style={styles.panelSpecs}>
          <Text style={styles.specText}>
            {panel.voltage}V {panel.phase}PH {panel.amperage}A
          </Text>
          <Text style={styles.specText}>
            {panel.manufacturer} {panel.model}
          </Text>
        </View>
      </View>

      <View style={styles.loadBar}>
        <View style={styles.loadBarBackground}>
          <View
            style={[
              styles.loadBarFill,
              {
                width: `${getLoadPercentage()}%`,
                backgroundColor: getLoadPercentage() > 80 ? '#f44336' : '#4CAF50',
              },
            ]}
          />
        </View>
        <Text style={styles.loadText}>
          Load: {panel.totalLoad}W / {panel.amperage * panel.voltage}W ({getLoadPercentage()}%)
        </Text>
      </View>

      <View style={styles.scheduleContainer}>
        <View style={styles.scheduleHeader}>
          <Text style={styles.scheduleHeaderText}>Circuit Schedule</Text>
          <View style={styles.statusLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#4CAF50' }]} />
              <Text style={styles.legendText}>On</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#9E9E9E' }]} />
              <Text style={styles.legendText}>Off</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#f44336' }]} />
              <Text style={styles.legendText}>Tripped</Text>
            </View>
          </View>
        </View>

        <View style={styles.schedule}>
          <View style={styles.scheduleColumn}>
            {leftCircuits.map(circuit => renderCircuitRow(circuit, 'left'))}
          </View>
          <View style={styles.scheduleDivider} />
          <View style={styles.scheduleColumn}>
            {rightCircuits.map(circuit => renderCircuitRow(circuit, 'right'))}
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Last Updated: {new Date(panel.lastUpdated).toLocaleString()}
        </Text>
        <Text style={styles.footerText}>
          Status: <Text style={{ color: panel.status === 'active' ? '#4CAF50' : '#FF9800' }}>
            {panel.status.toUpperCase()}
          </Text>
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  panelName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  panelLocation: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  panelSpecs: {
    marginTop: 10,
  },
  specText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  loadBar: {
    backgroundColor: 'white',
    padding: 15,
    marginTop: 10,
  },
  loadBarBackground: {
    height: 20,
    backgroundColor: '#e0e0e0',
    borderRadius: 10,
    overflow: 'hidden',
  },
  loadBarFill: {
    height: 20,
    borderRadius: 10,
  },
  loadText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  scheduleContainer: {
    backgroundColor: 'white',
    margin: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  scheduleHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusLegend: {
    flexDirection: 'row',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 5,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  schedule: {
    flexDirection: 'row',
    padding: 15,
  },
  scheduleColumn: {
    flex: 1,
  },
  scheduleDivider: {
    width: 2,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 10,
  },
  circuitRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 8,
    paddingHorizontal: 5,
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
  },
  circuitRowRight: {
    flexDirection: 'row-reverse',
  },
  circuitNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circuitNumberText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  circuitInfo: {
    flex: 1,
    marginHorizontal: 10,
  },
  circuitName: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  circuitDetails: {
    flexDirection: 'row',
    marginTop: 2,
  },
  circuitDetailText: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  protectionBadges: {
    flexDirection: 'row',
    marginLeft: 5,
  },
  badge: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 5,
    paddingVertical: 1,
    borderRadius: 3,
    marginRight: 3,
  },
  badgeText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
  circuitLoad: {
    alignItems: 'flex-end',
  },
  circuitLoadText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  footer: {
    padding: 15,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
});