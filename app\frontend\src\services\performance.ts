import { onCLS, onFCP, onFID, onLCP, onTTFB, onINP, Metric } from 'web-vitals';
import { api } from './api';

// Extended Performance interface for Chrome memory API
interface PerformanceMemory {
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
}

interface ExtendedPerformance extends Performance {
  memory?: PerformanceMemory;
}

// Network Information API types
interface NetworkInformation {
  effectiveType: '2g' | '3g' | '4g' | 'slow-2g';
  downlink: number;
  rtt: number;
}

interface ExtendedNavigator extends Navigator {
  connection?: NetworkInformation;
}

interface PerformanceData {
  metric: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
  id: string;
  navigationType?: string;
  url: string;
  userAgent: string;
  timestamp: string;
}

class PerformanceMonitor {
  private buffer: PerformanceData[] = [];
  private isEnabled: boolean = true;
  private sendInterval: number = 30000; // Send metrics every 30 seconds
  private maxBufferSize: number = 50;

  constructor() {
    // Only enable in production
    this.isEnabled = import.meta.env.PROD;
    
    if (this.isEnabled) {
      this.initializeMonitoring();
      this.startBatchSending();
    }
  }

  private initializeMonitoring() {
    // Core Web Vitals
    onCLS(this.handleMetric);
    onFCP(this.handleMetric);
    onFID(this.handleMetric);
    onLCP(this.handleMetric);
    onTTFB(this.handleMetric);
    onINP(this.handleMetric);

    // Custom performance marks
    this.measureAppInitialization();
    this.measureRouteChanges();
    
    // Monitor long tasks
    this.observeLongTasks();
    
    // Monitor resource timing
    this.observeResourceTiming();
  }

  private handleMetric = (metric: Metric) => {
    const data: PerformanceData = {
      metric: metric.name,
      value: metric.value,
      rating: metric.rating || 'needs-improvement',
      delta: metric.delta,
      id: metric.id,
      navigationType: metric.navigationType,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };

    this.buffer.push(data);

    // Log to console in development
    if (import.meta.env.DEV) {
      console.log(`[Performance] ${metric.name}:`, metric.value, metric.rating);
    }

    // Send immediately if buffer is full
    if (this.buffer.length >= this.maxBufferSize) {
      this.sendMetrics();
    }
  };

  private measureAppInitialization() {
    // Measure time to interactive
    if (performance.getEntriesByType) {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigationEntry) {
        const tti = navigationEntry.loadEventEnd - navigationEntry.fetchStart;
        
        this.buffer.push({
          metric: 'TTI',
          value: tti,
          rating: tti < 3800 ? 'good' : tti < 7300 ? 'needs-improvement' : 'poor',
          id: `tti-${Date.now()}`,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  private measureRouteChanges() {
    let lastRoute = window.location.pathname;
    
    // Use MutationObserver to detect route changes in SPA
    const observer = new MutationObserver(() => {
      if (window.location.pathname !== lastRoute) {
        const routeChangeTime = performance.now();
        
        this.buffer.push({
          metric: 'route-change',
          value: routeChangeTime,
          rating: routeChangeTime < 100 ? 'good' : routeChangeTime < 300 ? 'needs-improvement' : 'poor',
          id: `route-${Date.now()}`,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        });
        
        lastRoute = window.location.pathname;
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private observeLongTasks() {
    if ('PerformanceObserver' in window && 'PerformanceLongTaskTiming' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // Tasks longer than 50ms
            this.buffer.push({
              metric: 'long-task',
              value: entry.duration,
              rating: entry.duration < 100 ? 'good' : entry.duration < 300 ? 'needs-improvement' : 'poor',
              id: `task-${Date.now()}`,
              url: window.location.href,
              userAgent: navigator.userAgent,
              timestamp: new Date().toISOString()
            });
          }
        }
      });
      
      try {
        observer.observe({ type: 'longtask', buffered: true });
      } catch (e) {
        // Long task observer not supported
      }
    }
  }

  private observeResourceTiming() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const resourceEntry = entry as PerformanceResourceTiming;
          
          // Track slow resources
          if (resourceEntry.duration > 1000) {
            this.buffer.push({
              metric: 'slow-resource',
              value: resourceEntry.duration,
              rating: resourceEntry.duration < 2000 ? 'needs-improvement' : 'poor',
              id: `resource-${Date.now()}`,
              url: resourceEntry.name,
              userAgent: navigator.userAgent,
              timestamp: new Date().toISOString()
            });
          }
        }
      });
      
      try {
        observer.observe({ type: 'resource', buffered: true });
      } catch (e) {
        // Resource timing observer not supported
      }
    }
  }

  private startBatchSending() {
    // Send metrics periodically
    setInterval(() => {
      if (this.buffer.length > 0) {
        this.sendMetrics();
      }
    }, this.sendInterval);
    
    // Send metrics before page unload
    window.addEventListener('beforeunload', () => {
      if (this.buffer.length > 0) {
        this.sendMetrics(true);
      }
    });
  }

  private async sendMetrics(useBeacon: boolean = false) {
    if (this.buffer.length === 0) return;
    
    const metrics = [...this.buffer];
    this.buffer = [];
    
    try {
      if (useBeacon && navigator.sendBeacon) {
        // Use sendBeacon for reliability during page unload
        const blob = new Blob([JSON.stringify({ metrics })], {
          type: 'application/json'
        });
        navigator.sendBeacon('/api/metrics/performance', blob);
      } else {
        // Regular API call
        await api.post('/metrics/performance', { metrics });
      }
    } catch (error) {
      // Re-add metrics to buffer if send failed
      this.buffer.unshift(...metrics);
      console.error('Failed to send performance metrics:', error);
    }
  }

  // Public methods for custom measurements
  public measureOperation(name: string, fn: () => void | Promise<void>): void | Promise<void> {
    const startMark = `${name}-start`;
    const endMark = `${name}-end`;
    
    performance.mark(startMark);
    
    const complete = (): void => {
      performance.mark(endMark);
      performance.measure(name, startMark, endMark);
      
      const measure = performance.getEntriesByName(name)[0];
      if (measure) {
        this.buffer.push({
          metric: `custom-${name}`,
          value: measure.duration,
          rating: measure.duration < 100 ? 'good' : measure.duration < 300 ? 'needs-improvement' : 'poor',
          id: `custom-${Date.now()}`,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        });
      }
      
      // Clean up
      performance.clearMarks(startMark);
      performance.clearMarks(endMark);
      performance.clearMeasures(name);
    };
    
    const result = fn();
    if (result instanceof Promise) {
      return result.finally(complete);
    } else {
      complete();
      return result;
    }
  }

  // Get current performance summary
  public getPerformanceSummary(): {
    dns: number;
    tcp: number;
    ttfb: number;
    download: number;
    domParsing: number;
    domContentLoaded: number;
    load: number;
    memory: {
      used: number;
      total: number;
      limit: number;
    } | null;
    connection: {
      effectiveType: string;
      downlink: number;
      rtt: number;
    } | null;
  } | null {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (!navigation) return null;
    
    return {
      // Navigation timing
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      ttfb: navigation.responseStart - navigation.requestStart,
      download: navigation.responseEnd - navigation.responseStart,
      domParsing: navigation.domInteractive - navigation.domLoading,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      load: navigation.loadEventEnd - navigation.loadEventStart,
      
      // Memory usage (if available)
      memory: (performance as ExtendedPerformance).memory ? {
        used: (performance as ExtendedPerformance).memory!.usedJSHeapSize,
        total: (performance as ExtendedPerformance).memory!.totalJSHeapSize,
        limit: (performance as ExtendedPerformance).memory!.jsHeapSizeLimit
      } : null,
      
      // Connection info (if available)
      connection: (navigator as ExtendedNavigator).connection ? {
        effectiveType: (navigator as ExtendedNavigator).connection!.effectiveType,
        downlink: (navigator as ExtendedNavigator).connection!.downlink,
        rtt: (navigator as ExtendedNavigator).connection!.rtt
      } : null
    };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export helper hook for React components
export function usePerformanceTracking(componentName: string): {
  trackOperation: (operationName: string, fn: () => void | Promise<void>) => void | Promise<void>;
} {
  return {
    trackOperation: (operationName: string, fn: () => void | Promise<void>): void | Promise<void> => {
      return performanceMonitor.measureOperation(`${componentName}-${operationName}`, fn);
    }
  };
}