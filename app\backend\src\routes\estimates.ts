import { Router } from 'express';
import { z } from 'zod';
import { Decimal } from 'decimal.js';
import { prisma } from '../index';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';
import { EstimateSchema, MaterialItemSchema, LaborItemSchema } from '@electrical/shared';
import { emitToProject } from '../socket';
import { io, redis } from '../index';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Pagination and filter schema
const estimateQuerySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  search: z.string().optional(),
  status: z.enum(['DRAFT', 'SENT', 'APPROVED', 'REJECTED', 'EXPIRED']).optional(),
  project_id: z.string().uuid().optional(),
  sortBy: z.enum(['created_at', 'updated_at', 'version', 'total_amount']).default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Create estimate schema
const createEstimateSchema = z.object({
  project_id: z.string().uuid(),
  profit_margin: z.number().min(0).max(50).default(15),
  contingency_percent: z.number().min(0).max(25).default(10),
  notes: z.string().optional(),
  terms: z.string().optional(),
  valid_days: z.number().min(7).max(90).default(30)
});

// Material item schema for creation
const createMaterialItemSchema = MaterialItemSchema.omit({
  id: true,
  estimate_id: true,
  created_at: true,
  updated_at: true,
  extended_cost: true,
  tax_amount: true,
  total_amount: true
});

// Labor item schema for creation
const createLaborItemSchema = LaborItemSchema.omit({
  id: true,
  estimate_id: true,
  created_at: true,
  updated_at: true,
  extended_cost: true,
  burden_amount: true,
  total_amount: true
});

// Get all estimates with pagination
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const params = estimateQuerySchema.parse(req.query);
    const skip = (params.page - 1) * params.limit;
    
    // Build where clause
    const where = {
      ...(params.search && {
        OR: [
          { project: { name: { contains: params.search, mode: 'insensitive' as const } } },
          { project: { customer: { name: { contains: params.search, mode: 'insensitive' as const } } } },
          { notes: { contains: params.search, mode: 'insensitive' as const } }
        ]
      }),
      ...(params.status && { status: params.status }),
      ...(params.project_id && { project_id: params.project_id })
    };
    
    // Get estimates with pagination
    const [estimates, total] = await Promise.all([
      prisma.estimate.findMany({
        where,
        skip,
        take: params.limit,
        orderBy: { [params.sortBy]: params.sortOrder },
        select: {
          id: true,
          project_id: true,
          version: true,
          status: true,
          valid_until: true,
          subtotal: true,
          tax_total: true,
          total_amount: true,
          profit_margin: true,
          contingency_percent: true,
          created_at: true,
          updated_at: true,
          project: {
            select: {
              id: true,
              name: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          _count: {
            select: {
              material_items: true,
              labor_items: true
            }
          }
        }
      }),
      prisma.estimate.count({ where })
    ]);
    
    res.json({
      data: estimates,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get estimate by ID
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const estimate = await prisma.estimate.findUnique({
      where: { id: req.params.id },
      include: {
        project: {
          include: {
            customer: true
          }
        },
        material_items: {
          orderBy: { category: 'asc' }
        },
        labor_items: {
          orderBy: { trade: 'asc' }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        approver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    if (!estimate) {
      throw new AppError(404, 'Estimate not found', true, 'ESTIMATE_NOT_FOUND');
    }
    
    res.json(estimate);
  } catch (error) {
    next(error);
  }
});

// Create new estimate
router.post('/', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const data = createEstimateSchema.parse(req.body);
    
    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: data.project_id }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    // Get latest version number
    const latestEstimate = await prisma.estimate.findFirst({
      where: { project_id: data.project_id },
      orderBy: { version: 'desc' }
    });
    
    const version = (latestEstimate?.version || 0) + 1;
    const valid_until = new Date();
    valid_until.setDate(valid_until.getDate() + data.valid_days);
    
    // Create estimate
    const estimate = await prisma.estimate.create({
      data: {
        project_id: data.project_id,
        version,
        status: 'DRAFT',
        valid_until,
        profit_margin: data.profit_margin,
        contingency_percent: data.contingency_percent,
        notes: data.notes,
        terms: data.terms,
        created_by: req.user!.userId
      },
      include: {
        project: {
          include: {
            customer: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    res.status(201).json(estimate);
  } catch (error) {
    next(error);
  }
});

// Clone estimate
router.post('/:id/clone', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    // Get source estimate
    const sourceEstimate = await prisma.estimate.findUnique({
      where: { id: req.params.id },
      include: {
        material_items: true,
        labor_items: true
      }
    });
    
    if (!sourceEstimate) {
      throw new AppError(404, 'Estimate not found', true, 'ESTIMATE_NOT_FOUND');
    }
    
    // Get latest version
    const latestEstimate = await prisma.estimate.findFirst({
      where: { project_id: sourceEstimate.project_id },
      orderBy: { version: 'desc' }
    });
    
    const version = (latestEstimate?.version || 0) + 1;
    const valid_until = new Date();
    valid_until.setDate(valid_until.getDate() + 30);
    
    // Create new estimate with cloned items
    const estimate = await prisma.estimate.create({
      data: {
        project_id: sourceEstimate.project_id,
        version,
        status: 'DRAFT',
        valid_until,
        profit_margin: sourceEstimate.profit_margin,
        contingency_percent: sourceEstimate.contingency_percent,
        notes: sourceEstimate.notes,
        terms: sourceEstimate.terms,
        created_by: req.user!.userId,
        material_items: {
          create: sourceEstimate.material_items.map(item => ({
            catalog_number: item.catalog_number,
            description: item.description,
            category: item.category,
            unit: item.unit,
            quantity: item.quantity,
            unit_cost: item.unit_cost,
            markup_percent: item.markup_percent,
            waste_percent: item.waste_percent,
            tax_rate: item.tax_rate,
            supplier: item.supplier,
            wire_size: item.wire_size,
            wire_type: item.wire_type,
            conduit_size: item.conduit_size,
            conduit_type: item.conduit_type,
            voltage_rating: item.voltage_rating,
            amperage_rating: item.amperage_rating,
            phase: item.phase
          }))
        },
        labor_items: {
          create: sourceEstimate.labor_items.map(item => ({
            description: item.description,
            trade: item.trade,
            hours: item.hours,
            rate: item.rate,
            overtime_hours: item.overtime_hours,
            overtime_rate: item.overtime_rate,
            burden_percent: item.burden_percent
          }))
        }
      },
      include: {
        project: {
          include: {
            customer: true
          }
        },
        material_items: true,
        labor_items: true
      }
    });
    
    // Calculate totals
    await calculateEstimateTotals(estimate.id);
    
    res.status(201).json(estimate);
  } catch (error) {
    next(error);
  }
});

// Add material item
router.post('/:id/materials', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const data = createMaterialItemSchema.parse(req.body);
    
    // Verify estimate exists and is in draft
    const estimate = await prisma.estimate.findUnique({
      where: { id: req.params.id }
    });
    
    if (!estimate) {
      throw new AppError(404, 'Estimate not found', true, 'ESTIMATE_NOT_FOUND');
    }
    
    if (estimate.status !== 'DRAFT') {
      throw new AppError(400, 'Can only modify draft estimates', true, 'ESTIMATE_NOT_DRAFT');
    }
    
    // Calculate extended amounts
    const quantity = new Decimal(data.quantity);
    const unitCost = new Decimal(data.unit_cost);
    const markupPercent = new Decimal(data.markup_percent);
    const wastePercent = new Decimal(data.waste_percent);
    const taxRate = new Decimal(data.tax_rate);
    
    const extendedCost = quantity
      .times(unitCost)
      .times(markupPercent.dividedBy(100).plus(1))
      .times(wastePercent.dividedBy(100).plus(1));
    
    const taxAmount = extendedCost.times(taxRate);
    const totalAmount = extendedCost.plus(taxAmount);
    
    // Create material item
    const materialItem = await prisma.materialItem.create({
      data: {
        catalog_number: data.catalog_number,
        description: data.description,
        category: data.category,
        unit: data.unit,
        quantity: data.quantity,
        unit_cost: data.unit_cost,
        markup_percent: data.markup_percent,
        waste_percent: data.waste_percent,
        tax_rate: data.tax_rate,
        supplier: data.supplier,
        wire_size: data.wire_size,
        wire_type: data.wire_type,
        conduit_size: data.conduit_size,
        conduit_type: data.conduit_type,
        voltage_rating: data.voltage_rating,
        amperage_rating: data.amperage_rating,
        phase: data.phase,
        estimate_id: req.params.id,
        extended_cost: extendedCost.toNumber(),
        tax_amount: taxAmount.toNumber(),
        total_amount: totalAmount.toNumber()
      }
    });
    
    // Update estimate totals
    await calculateEstimateTotals(req.params.id);
    
    // Emit update
    emitToProject(io, estimate.project_id, 'estimate:material:added', {
      estimateId: estimate.id,
      materialItem
    });
    
    res.status(201).json(materialItem);
  } catch (error) {
    next(error);
  }
});

// Add labor item
router.post('/:id/labor', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const data = createLaborItemSchema.parse(req.body);
    
    // Verify estimate exists and is in draft
    const estimate = await prisma.estimate.findUnique({
      where: { id: req.params.id }
    });
    
    if (!estimate) {
      throw new AppError(404, 'Estimate not found', true, 'ESTIMATE_NOT_FOUND');
    }
    
    if (estimate.status !== 'DRAFT') {
      throw new AppError(400, 'Can only modify draft estimates', true, 'ESTIMATE_NOT_DRAFT');
    }
    
    // Calculate extended amounts
    const hours = new Decimal(data.hours);
    const rate = new Decimal(data.rate);
    const overtimeHours = new Decimal(data.overtime_hours);
    const overtimeRate = new Decimal(data.overtime_rate);
    const burdenPercent = new Decimal(data.burden_percent);
    
    const extendedCost = hours.times(rate).plus(overtimeHours.times(overtimeRate));
    const burdenAmount = extendedCost.times(burdenPercent.dividedBy(100));
    const totalAmount = extendedCost.plus(burdenAmount);
    
    // Create labor item
    const laborItem = await prisma.laborItem.create({
      data: {
        description: data.description,
        trade: data.trade,
        hours: data.hours,
        rate: data.rate,
        overtime_hours: data.overtime_hours,
        overtime_rate: data.overtime_rate,
        burden_percent: data.burden_percent,
        estimate_id: req.params.id,
        extended_cost: extendedCost.toNumber(),
        burden_amount: burdenAmount.toNumber(),
        total_amount: totalAmount.toNumber()
      }
    });
    
    // Update estimate totals
    await calculateEstimateTotals(req.params.id);
    
    // Emit update
    emitToProject(io, estimate.project_id, 'estimate:labor:added', {
      estimateId: estimate.id,
      laborItem
    });
    
    res.status(201).json(laborItem);
  } catch (error) {
    next(error);
  }
});

// Update estimate status
router.patch('/:id/status', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const statusSchema = z.object({
      status: z.enum(['DRAFT', 'SENT', 'APPROVED', 'REJECTED', 'EXPIRED'])
    });
    
    const { status } = statusSchema.parse(req.body);
    
    const estimate = await prisma.estimate.findUnique({
      where: { id: req.params.id }
    });
    
    if (!estimate) {
      throw new AppError(404, 'Estimate not found', true, 'ESTIMATE_NOT_FOUND');
    }
    
    // Update status and approval info if approved
    const updateData: any = { status };
    
    if (status === 'APPROVED') {
      updateData.approved_by = req.user!.userId;
      updateData.approved_at = new Date();
    }
    
    const updatedEstimate = await prisma.estimate.update({
      where: { id: req.params.id },
      data: updateData,
      include: {
        project: true,
        approver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    // Emit status change
    emitToProject(io, updatedEstimate.project_id, 'estimate:status:changed', {
      estimateId: updatedEstimate.id,
      status,
      changedBy: req.user?.userId
    });
    
    res.json(updatedEstimate);
  } catch (error) {
    next(error);
  }
});

// Delete material item
router.delete('/materials/:itemId', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const item = await prisma.materialItem.findUnique({
      where: { id: req.params.itemId },
      include: {
        estimate: true
      }
    });
    
    if (!item) {
      throw new AppError(404, 'Material item not found', true, 'ITEM_NOT_FOUND');
    }
    
    if (item.estimate.status !== 'DRAFT') {
      throw new AppError(400, 'Can only modify draft estimates', true, 'ESTIMATE_NOT_DRAFT');
    }
    
    await prisma.materialItem.delete({
      where: { id: req.params.itemId }
    });
    
    // Update estimate totals
    await calculateEstimateTotals(item.estimate_id);
    
    res.json({ message: 'Material item deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Delete labor item
router.delete('/labor/:itemId', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const item = await prisma.laborItem.findUnique({
      where: { id: req.params.itemId },
      include: {
        estimate: true
      }
    });
    
    if (!item) {
      throw new AppError(404, 'Labor item not found', true, 'ITEM_NOT_FOUND');
    }
    
    if (item.estimate.status !== 'DRAFT') {
      throw new AppError(400, 'Can only modify draft estimates', true, 'ESTIMATE_NOT_DRAFT');
    }
    
    await prisma.laborItem.delete({
      where: { id: req.params.itemId }
    });
    
    // Update estimate totals
    await calculateEstimateTotals(item.estimate_id);
    
    res.json({ message: 'Labor item deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Helper function to calculate estimate totals
async function calculateEstimateTotals(estimateId: string): Promise<void> {
  const [materialItems, laborItems] = await Promise.all([
    prisma.materialItem.findMany({
      where: { estimate_id: estimateId }
    }),
    prisma.laborItem.findMany({
      where: { estimate_id: estimateId }
    })
  ]);
  
  // Calculate totals using Decimal for precision
  let materialSubtotal = new Decimal(0);
  let taxTotal = new Decimal(0);
  
  materialItems.forEach(item => {
    materialSubtotal = materialSubtotal.plus(item.extended_cost);
    taxTotal = taxTotal.plus(item.tax_amount);
  });
  
  let laborSubtotal = new Decimal(0);
  laborItems.forEach(item => {
    laborSubtotal = laborSubtotal.plus(item.total_amount);
  });
  
  const subtotal = materialSubtotal.plus(laborSubtotal);
  const totalAmount = subtotal.plus(taxTotal);
  
  // Update estimate
  await prisma.estimate.update({
    where: { id: estimateId },
    data: {
      subtotal: subtotal.toNumber(),
      tax_total: taxTotal.toNumber(),
      total_amount: totalAmount.toNumber()
    }
  });
}

export { router as estimatesRouter };