import Foundation
import UIKit

@objc(FlagSecure)
class FlagSecure: NSObject {
    
    private var secureView: UIView?
    
    @objc
    func activate() {
        DispatchQueue.main.async {
            // Add a secure view overlay when app goes to background
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(self.applicationWillResignActive),
                name: UIApplication.willResignActiveNotification,
                object: nil
            )
            
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(self.applicationDidBecomeActive),
                name: UIApplication.didBecomeActiveNotification,
                object: nil
            )
            
            // Prevent screen recording
            if #available(iOS 11.0, *) {
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(self.screenCaptureChanged),
                    name: UIScreen.capturedDidChangeNotification,
                    object: nil
                )
            }
        }
    }
    
    @objc
    func deactivate() {
        DispatchQueue.main.async {
            NotificationCenter.default.removeObserver(self)
            self.removeSecureView()
        }
    }
    
    @objc
    func isActive(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        resolve(self.secureView != nil)
    }
    
    @objc
    private func applicationWillResignActive() {
        guard let window = UIApplication.shared.keyWindow else { return }
        
        // Create a blur effect view to hide content
        let blurEffect = UIBlurEffect(style: .light)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame = window.bounds
        blurView.tag = 999999 // Unique tag to identify our secure view
        
        // Add logo or placeholder in center
        let logoImageView = UIImageView(image: UIImage(named: "AppIcon"))
        logoImageView.contentMode = .scaleAspectFit
        logoImageView.frame = CGRect(x: 0, y: 0, width: 100, height: 100)
        logoImageView.center = blurView.center
        blurView.contentView.addSubview(logoImageView)
        
        window.addSubview(blurView)
        self.secureView = blurView
    }
    
    @objc
    private func applicationDidBecomeActive() {
        self.removeSecureView()
    }
    
    @objc
    private func screenCaptureChanged() {
        if #available(iOS 11.0, *) {
            if UIScreen.main.isCaptured {
                // Screen is being recorded/captured
                self.applicationWillResignActive()
            } else {
                // Screen recording stopped
                self.removeSecureView()
            }
        }
    }
    
    private func removeSecureView() {
        self.secureView?.removeFromSuperview()
        self.secureView = nil
    }
    
    @objc
    static func requiresMainQueueSetup() -> Bool {
        return true
    }
}