import { <PERSON>u, Bell, User, LogOut, Settings } from 'lucide-react';
import { useAuthStore } from '../../stores/auth';
import { Fragment, useRef, useState } from 'react';
import { Menu as HeadlessMenu, Transition } from '@headlessui/react';
import { Link } from 'react-router-dom';
import { ThemeToggle } from '../common/ThemeToggle';
import { announce } from '../../utils/accessibility';

interface HeaderProps {
  onMenuClick: () => void;
}

export function HeaderAccessible({ onMenuClick }: HeaderProps) {
  const { user, logout } = useAuthStore();
  const [notificationCount] = useState(3); // Example notification count
  const skipLinkRef = useRef<HTMLAnchorElement>(null);

  const handleLogout = () => {
    announce('Logging out...');
    logout();
  };

  const handleMenuToggle = () => {
    onMenuClick();
    announce('Navigation menu toggled');
  };

  return (
    <>
      {/* Skip to main content link */}
      <a
        ref={skipLinkRef}
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded z-50"
      >
        Skip to main content
      </a>

      <header 
        className="fixed top-0 right-0 left-0 lg:left-64 z-40 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
        role="banner"
      >
        <div className="flex items-center justify-between px-4 sm:px-6 lg:px-8 h-16">
          <button
            type="button"
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            onClick={handleMenuToggle}
            aria-label="Toggle navigation menu"
            aria-expanded="false"
          >
            <Menu className="h-6 w-6" aria-hidden="true" />
          </button>
          
          <nav className="flex-1 flex items-center justify-end space-x-4" aria-label="Header navigation">
            {/* Notifications */}
            <button 
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 relative focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
              aria-label={`Notifications${notificationCount > 0 ? `, ${notificationCount} unread` : ''}`}
            >
              <Bell className="h-5 w-5" aria-hidden="true" />
              {notificationCount > 0 && (
                <>
                  <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full" aria-hidden="true"></span>
                  <span className="sr-only">{notificationCount} unread notifications</span>
                </>
              )}
            </button>
            
            {/* Theme toggle */}
            <ThemeToggle />
            
            {/* User menu */}
            <HeadlessMenu as="div" className="relative">
              <HeadlessMenu.Button 
                className="flex items-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                aria-label="User menu"
              >
                <User className="h-5 w-5" aria-hidden="true" />
                <span className="sr-only">Open user menu</span>
              </HeadlessMenu.Button>
              
              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <HeadlessMenu.Items 
                  className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                >
                  <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-600" role="none">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {user?.name}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      {user?.email}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 capitalize">
                      <span className="sr-only">Role: </span>{user?.role}
                    </p>
                  </div>
                  
                  <div className="py-1" role="none">
                    <HeadlessMenu.Item>
                      {({ active }) => (
                        <Link
                          to="/settings"
                          className={`${
                            active ? 'bg-gray-100 dark:bg-gray-600' : ''
                          } flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500`}
                          role="menuitem"
                        >
                          <Settings className="mr-3 h-4 w-4" aria-hidden="true" />
                          Settings
                        </Link>
                      )}
                    </HeadlessMenu.Item>
                    
                    <HeadlessMenu.Item>
                      {({ active }) => (
                        <button
                          onClick={handleLogout}
                          className={`${
                            active ? 'bg-gray-100 dark:bg-gray-600' : ''
                          } flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500`}
                          role="menuitem"
                        >
                          <LogOut className="mr-3 h-4 w-4" aria-hidden="true" />
                          Logout
                        </button>
                      )}
                    </HeadlessMenu.Item>
                  </div>
                </HeadlessMenu.Items>
              </Transition>
            </HeadlessMenu>
          </nav>
        </div>
      </header>
    </>
  );
}