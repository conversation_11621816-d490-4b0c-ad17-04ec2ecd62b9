# Code Style Guide

This guide defines the coding standards and best practices for the Electrical Contracting Application. Consistency in code style improves readability, maintainability, and collaboration.

## 📋 General Principles

1. **Clarity over Cleverness**: Write code that is easy to understand
2. **Consistency**: Follow established patterns within the codebase
3. **Self-Documenting**: Use meaningful names and clear structure
4. **DRY**: Don't Repeat Yourself, but don't over-abstract
5. **YAGNI**: You Aren't Gonna Need It - avoid premature optimization

## 🔤 Naming Conventions

### TypeScript/JavaScript

#### Variables and Functions
```typescript
// Use camelCase for variables and functions
const userId = '123';
const calculateWireSize = (current: number) => { ... };

// Use descriptive names
// ❌ Bad
const d = new Date();
const yrs = calcAge(d);

// ✅ Good
const birthDate = new Date();
const ageInYears = calculateAge(birthDate);

// Boolean variables should be questions
const isActive = true;
const hasPermission = false;
const canEdit = checkPermissions();
```

#### Classes and Interfaces
```typescript
// Use PascalCase for classes and interfaces
class ElectricalPanel {
  // ...
}

interface ProjectData {
  id: string;
  name: string;
}

// Prefix interfaces with 'I' only for dependency injection
interface IUserService {
  getUser(id: string): Promise<User>;
}
```

#### Constants
```typescript
// Use UPPER_SNAKE_CASE for true constants
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.example.com';

// Use camelCase for constant objects
const defaultConfig = {
  timeout: 5000,
  retries: 3
};
```

#### Enums
```typescript
// Use PascalCase for enum names, UPPER_SNAKE_CASE for values
enum ProjectStatus {
  PLANNING = 'PLANNING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED'
}

// Or use const assertions for lighter weight
const WireType = {
  COPPER: 'COPPER',
  ALUMINUM: 'ALUMINUM'
} as const;
```

### File Names

```bash
# Components (React)
UserProfile.tsx
PanelSchedule.tsx
WireSizeCalculator.tsx

# Utilities and helpers
string-utils.ts
date-helpers.ts
validation.ts

# Services
user.service.ts
calculation.service.ts

# Tests
user.service.test.ts
UserProfile.test.tsx

# Configuration
database.config.ts
auth.config.ts
```

## 💻 TypeScript Guidelines

### Type Safety

```typescript
// ❌ Avoid 'any'
function processData(data: any) {
  return data.value;
}

// ✅ Use proper types
interface DataItem {
  value: number;
  label: string;
}

function processData(data: DataItem): number {
  return data.value;
}

// ✅ Use unknown for truly unknown types
function parseJSON(json: string): unknown {
  return JSON.parse(json);
}
```

### Interfaces vs Types

```typescript
// Use interfaces for objects that can be extended
interface User {
  id: string;
  name: string;
}

interface ElectricalUser extends User {
  licenseNumber: string;
}

// Use types for unions, tuples, and complex types
type Status = 'active' | 'inactive' | 'pending';
type Coordinate = [number, number];
type AsyncData<T> = 
  | { status: 'loading' }
  | { status: 'error'; error: Error }
  | { status: 'success'; data: T };
```

### Function Types

```typescript
// ✅ Prefer arrow functions for callbacks
const numbers = [1, 2, 3];
const doubled = numbers.map(n => n * 2);

// ✅ Use function declarations for top-level functions
function calculateVoltage(current: number, resistance: number): number {
  return current * resistance;
}

// ✅ Type function parameters
type CalculationFn = (input: number) => number;

const applyCalculation = (value: number, fn: CalculationFn): number => {
  return fn(value);
};
```

### Generics

```typescript
// Use descriptive generic names
// ❌ Bad
function getValue<T>(obj: T, key: K): V {
  return obj[key];
}

// ✅ Good
function getValue<TObject, TKey extends keyof TObject>(
  obj: TObject,
  key: TKey
): TObject[TKey] {
  return obj[key];
}

// Common generic patterns
type Nullable<T> = T | null;
type Optional<T> = T | undefined;
type AsyncResult<T> = Promise<Result<T>>;
```

## ⚛️ React Guidelines

### Component Structure

```typescript
// 1. Imports
import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui';
import { calculateWireSize } from '@/utils/calculations';
import type { Project } from '@/types';

// 2. Types/Interfaces
interface ProjectDetailsProps {
  projectId: string;
  onUpdate: (project: Project) => void;
  className?: string;
}

// 3. Component
export const ProjectDetails: React.FC<ProjectDetailsProps> = ({
  projectId,
  onUpdate,
  className
}) => {
  // 4. State and hooks
  const [isEditing, setIsEditing] = useState(false);
  const { data: project, isLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => fetchProject(projectId)
  });

  // 5. Effects
  useEffect(() => {
    // Effect logic
  }, [projectId]);

  // 6. Handlers
  const handleSave = async () => {
    // Handler logic
  };

  // 7. Early returns
  if (isLoading) return <LoadingSpinner />;
  if (!project) return <NotFound />;

  // 8. Render
  return (
    <div className={className}>
      {/* Component JSX */}
    </div>
  );
};
```

### Hooks

```typescript
// Custom hooks should start with 'use'
function useProjectData(projectId: string) {
  const [data, setData] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProject(projectId)
      .then(setData)
      .finally(() => setLoading(false));
  }, [projectId]);

  return { data, loading };
}

// Extract complex logic into custom hooks
function useCalculations() {
  const calculate = useCallback((type: string, inputs: any) => {
    switch (type) {
      case 'wireSize':
        return calculateWireSize(inputs);
      case 'voltageDrop':
        return calculateVoltageDrop(inputs);
      default:
        throw new Error(`Unknown calculation type: ${type}`);
    }
  }, []);

  return { calculate };
}
```

### Props and State

```typescript
// Use destructuring for props
// ❌ Bad
const Component = (props: Props) => {
  return <div>{props.title}</div>;
};

// ✅ Good
const Component = ({ title, subtitle }: Props) => {
  return (
    <div>
      <h1>{title}</h1>
      <p>{subtitle}</p>
    </div>
  );
};

// Group related state
// ❌ Bad
const [firstName, setFirstName] = useState('');
const [lastName, setLastName] = useState('');
const [email, setEmail] = useState('');

// ✅ Good
const [formData, setFormData] = useState({
  firstName: '',
  lastName: '',
  email: ''
});
```

## 🎨 CSS and Styling

### Tailwind CSS

```typescript
// Use Tailwind utilities for styling
// ✅ Good
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-lg font-semibold text-gray-900">Title</h2>
  <Button className="ml-4">Action</Button>
</div>

// Use component variants
const Button = ({ variant = 'primary', size = 'md', className, ...props }) => {
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    danger: 'bg-red-600 text-white hover:bg-red-700'
  };

  const sizes = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={cn(
        'rounded-md font-medium transition-colors',
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    />
  );
};
```

### Component Classes

```typescript
// Use cn() utility for conditional classes
import { cn } from '@/utils/cn';

<div 
  className={cn(
    'base-class',
    isActive && 'active-class',
    isDisabled && 'disabled-class',
    className // Allow parent to add classes
  )}
/>
```

## 📁 Project Structure

### Backend Structure
```
backend/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Route controllers
│   ├── middleware/     # Express middleware
│   ├── models/         # Database models
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utility functions
│   ├── validators/     # Input validation
│   └── index.ts        # Entry point
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
└── prisma/
    └── schema.prisma
```

### Frontend Structure
```
frontend/
├── src/
│   ├── components/     # Reusable components
│   │   ├── ui/        # Base UI components
│   │   ├── forms/     # Form components
│   │   └── layout/    # Layout components
│   ├── pages/         # Page components
│   ├── hooks/         # Custom React hooks
│   ├── services/      # API services
│   ├── stores/        # State management
│   ├── utils/         # Utilities
│   ├── types/         # TypeScript types
│   └── main.tsx       # Entry point
└── tests/
```

## 🧪 Testing Standards

### Test Structure

```typescript
// Use descriptive test names
describe('WireSizeCalculator', () => {
  describe('calculateWireSize', () => {
    it('should return correct wire size for residential load', () => {
      const result = calculateWireSize({
        current: 20,
        voltage: 120,
        distance: 50
      });
      expect(result.wireSize).toBe('12 AWG');
    });

    it('should apply derating factors for multiple conductors', () => {
      // Test implementation
    });
  });
});

// Group related tests
describe('API: Projects', () => {
  describe('GET /projects', () => {
    it('should return paginated projects', async () => {
      // Test
    });

    it('should filter by status', async () => {
      // Test
    });
  });
});
```

### Test Best Practices

```typescript
// Use beforeEach for setup
beforeEach(() => {
  jest.clearAllMocks();
});

// Use factories for test data
const createMockProject = (overrides = {}): Project => ({
  id: 'proj_123',
  name: 'Test Project',
  status: 'active',
  ...overrides
});

// Test edge cases
it('should handle empty input gracefully', () => {
  expect(() => calculateWireSize({})).toThrow('Invalid input');
});

// Use async/await for async tests
it('should fetch user data', async () => {
  const user = await userService.getUser('123');
  expect(user).toMatchObject({
    id: '123',
    name: expect.any(String)
  });
});
```

## 📝 Documentation

### JSDoc Comments

```typescript
/**
 * Calculates the appropriate wire size based on NEC requirements
 * @param options - Calculation parameters
 * @param options.current - Load current in amperes
 * @param options.voltage - System voltage
 * @param options.distance - One-way distance in feet
 * @returns Wire size recommendation with supporting data
 * @throws {ValidationError} If input parameters are invalid
 * @example
 * const result = calculateWireSize({
 *   current: 30,
 *   voltage: 240,
 *   distance: 100
 * });
 */
export function calculateWireSize(options: WireSizeOptions): WireSizeResult {
  // Implementation
}
```

### Inline Comments

```typescript
// Use comments to explain "why", not "what"
// ❌ Bad
// Increment counter
counter++;

// ✅ Good
// Increment retry counter to track failed attempts for rate limiting
retryCount++;

// Use TODO comments with assignee
// TODO(john): Implement caching for this calculation
// FIXME(sarah): Handle edge case when voltage is 0
// NOTE: This uses the 2023 NEC temperature correction factors
```

## 🔒 Security Guidelines

```typescript
// Never log sensitive data
// ❌ Bad
console.log('User login:', { email, password });

// ✅ Good
console.log('User login attempt:', { email });

// Validate all inputs
const validateInput = (input: unknown): ValidatedInput => {
  const schema = z.object({
    email: z.string().email(),
    projectId: z.string().uuid()
  });
  
  return schema.parse(input);
};

// Use parameterized queries
// ❌ Bad
const query = `SELECT * FROM users WHERE id = '${userId}'`;

// ✅ Good
const user = await prisma.user.findUnique({
  where: { id: userId }
});
```

## 🚀 Performance Guidelines

```typescript
// Use memoization for expensive calculations
const expensiveCalculation = useMemo(() => {
  return calculateComplexValue(input);
}, [input]);

// Debounce user input
const debouncedSearch = useMemo(
  () => debounce((query: string) => {
    searchProjects(query);
  }, 300),
  []
);

// Lazy load components
const HeavyComponent = lazy(() => import('./HeavyComponent'));

// Use virtual scrolling for long lists
import { VirtualList } from '@tanstack/react-virtual';
```

## 🔄 Git Commit Messages

```bash
# Format: <type>(<scope>): <subject>

# Types:
# feat: New feature
# fix: Bug fix
# docs: Documentation changes
# style: Code style changes (formatting, etc)
# refactor: Code changes that neither fix bugs nor add features
# perf: Performance improvements
# test: Adding or updating tests
# chore: Build process or auxiliary tool changes

# Examples:
feat(calculations): add arc flash calculator
fix(auth): resolve token refresh race condition
docs(api): update endpoint documentation
refactor(projects): simplify status update logic
perf(panels): optimize circuit rendering
test(materials): add barcode scanning tests
chore(deps): update dependencies
```

## 📋 Code Review Checklist

- [ ] Code follows naming conventions
- [ ] TypeScript types are properly defined
- [ ] No use of `any` without justification
- [ ] Functions have appropriate JSDoc comments
- [ ] Tests are included for new functionality
- [ ] Error handling is comprehensive
- [ ] Security considerations addressed
- [ ] Performance impact considered
- [ ] Code is DRY but not over-abstracted
- [ ] Commit messages follow convention

---

Remember: These are guidelines, not rigid rules. Use your judgment and prioritize code clarity and maintainability. When in doubt, discuss with the team.