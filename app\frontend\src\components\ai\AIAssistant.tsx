import { useState } from 'react';
import { <PERSON><PERSON>, Send, Loader, AlertCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';

interface EstimateResult {
  estimateId: string;
  totalCost: number;
  breakdown: {
    materials: number;
    labor: number;
    overhead: number;
  };
}

interface ComplianceResult {
  compliant: boolean;
  violations: string[];
  recommendations: string[];
}

interface PricingResult {
  materials: Array<{
    name: string;
    price: number;
    unit: string;
  }>;
  totalCost: number;
}

interface DiagnosisResult {
  issue: string;
  solution: string;
  priority: 'low' | 'medium' | 'high';
}

type AIResult = EstimateResult | ComplianceResult | PricingResult | DiagnosisResult;

interface AIResponse {
  type: 'estimate' | 'compliance' | 'pricing' | 'diagnosis';
  result: AIResult;
  confidence?: number;
  suggestions?: string[];
}

export function AIAssistant(): JSX.Element {
  const [isOpen, setIsOpen] = useState(false);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<AIResponse | null>(null);

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    if (!input.trim()) return;

    setLoading(true);
    try {
      // Analyze input to determine AI action
      const action = analyzeInput(input);
      
      let result;
      switch (action.type) {
        case 'estimate':
          result = await api.post('/agents/estimate', {
            projectDescription: input,
            customerId: action.customerId || 'temp-001',
          });
          break;
        
        case 'compliance':
          result = await api.post('/agents/compliance-check', {
            parameters: action.parameters,
          });
          break;
        
        case 'pricing':
          result = await api.post('/agents/material-pricing', {
            materials: action.materials,
          });
          break;
        
        default:
          result = await api.post('/agents/diagnose-error', {
            error: input,
            context: {},
          });
      }

      setResponse({
        type: action.type,
        result: result.data.data,
      });
    } catch (error) {
      toast.error('AI assistant error');
    } finally {
      setLoading(false);
    }
  };

  interface AnalyzeResult {
    type: 'estimate' | 'compliance' | 'pricing' | 'diagnosis';
    customerId?: string;
    parameters?: Record<string, number | string>;
    materials?: string[];
  }

  const analyzeInput = (text: string): AnalyzeResult => {
    const lower = text.toLowerCase();
    
    if (lower.includes('estimate') || lower.includes('quote')) {
      return { type: 'estimate' };
    }
    
    if (lower.includes('nec') || lower.includes('compliance') || lower.includes('code')) {
      return { 
        type: 'compliance',
        parameters: extractParameters(text),
      };
    }
    
    if (lower.includes('price') || lower.includes('cost')) {
      return {
        type: 'pricing',
        materials: extractMaterials(text),
      };
    }
    
    return { type: 'diagnosis' };
  };

  const extractParameters = (text: string): Record<string, number | string> => {
    // Simple parameter extraction
    const params: Record<string, number | string> = {};
    
    const voltageMatch = text.match(/(\d+)\s*v/i);
    if (voltageMatch) params.voltage = parseInt(voltageMatch[1]);
    
    const ampMatch = text.match(/(\d+)\s*amp/i);
    if (ampMatch) params.current = parseInt(ampMatch[1]);
    
    return params;
  };

  const extractMaterials = (text: string): Array<{ name: string; quantity: number; specifications: string }> => {
    // Simple material extraction
    const materials: Array<{ name: string; quantity: number; specifications: string }> = [];
    
    if (text.includes('wire')) {
      const sizeMatch = text.match(/#?(\d+)\s*awg/i);
      materials.push({
        name: `THHN ${sizeMatch ? sizeMatch[1] : '12'} AWG`,
        quantity: 100,
        specifications: 'Copper, 600V',
      });
    }
    
    return materials;
  };

  return (
    <>
      {/* AI Assistant Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 btn-primary rounded-full p-4 shadow-lg z-50"
        title="AI Assistant"
      >
        <Bot className="h-6 w-6" />
      </button>

      {/* AI Assistant Panel */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-96 max-h-[600px] bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 flex flex-col">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Bot className="h-5 w-5 mr-2" />
              AI Assistant
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Ask about estimates, NEC compliance, or material pricing
            </p>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {response && (
              <div className="mb-4">
                {response.type === 'estimate' && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                      AI Estimate Analysis
                    </h4>
                    <pre className="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap">
                      {JSON.stringify(response.result, null, 2)}
                    </pre>
                  </div>
                )}

                {response.type === 'compliance' && (
                  <div className={`rounded-lg p-4 ${
                    response.result.compliant 
                      ? 'bg-green-50 dark:bg-green-900/20' 
                      : 'bg-red-50 dark:bg-red-900/20'
                  }`}>
                    <h4 className="font-medium mb-2">
                      NEC Compliance Check
                    </h4>
                    <p className="text-sm">
                      Status: {response.result.compliant ? '✓ Compliant' : '✗ Non-Compliant'}
                    </p>
                    {response.result.violations?.map((v: any, i: number) => (
                      <div key={i} className="mt-2 text-sm">
                        <AlertCircle className="h-4 w-4 inline mr-1" />
                        {v.description}
                      </div>
                    ))}
                  </div>
                )}

                {response.type === 'pricing' && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                      Material Pricing
                    </h4>
                    {response.result.materials?.map((m: any, i: number) => (
                      <div key={i} className="text-sm mb-2">
                        <div className="font-medium">{m.material}</div>
                        <div>Unit Price: ${m.unitPrice}</div>
                        <div>Total: ${m.totalPrice}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {loading && (
              <div className="flex items-center justify-center py-8">
                <Loader className="h-6 w-6 animate-spin text-primary-600" />
              </div>
            )}
          </div>

          <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex space-x-2">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask about electrical work..."
                className="flex-1 input"
                disabled={loading}
              />
              <button
                type="submit"
                disabled={loading || !input.trim()}
                className="btn-primary"
              >
                <Send className="h-4 w-4" />
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
}