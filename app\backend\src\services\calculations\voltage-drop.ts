import { Decimal } from 'decimal.js';
import { 
  COPPER_AMPACITY_75C,
  ALUMINUM_AMPACITY_75C,
  VOLTAGE_DROP_LIMITS,
  calculateVoltageDrop
} from '@electrical/shared';

interface VoltageDropInput {
  circuit_name: string;
  voltage: number;
  phase: '1PH' | '3PH';
  amperage: number;
  distance: number;
  conductor_size: string;
  conductor_type: 'CU' | 'AL';
  conduit_type: 'STEEL' | 'PVC' | 'AL';
  power_factor: number;
  ambient_temp_f: number;
}

interface VoltageDropResult {
  circuit_name: string;
  voltage: number;
  phase: string;
  amperage: number;
  distance: number;
  conductor_size: string;
  conductor_type: string;
  calculated_vd_volts: number;
  calculated_vd_percent: number;
  nec_limit_branch: number;
  nec_limit_total: number;
  passes_branch_limit: boolean;
  passes_total_limit: boolean;
  temperature_derating_applied: boolean;
  temperature_derating_factor: number;
  conductor_ampacity: number;
  adequate_ampacity: boolean;
  necReferences: string[];
  recommendations: string[];
}

export class VoltageDropService {
  async calculate(input: VoltageDropInput): Promise<VoltageDropResult> {
    const necRefs: string[] = ['215.2(A)(1)', '210.19(A)(1)'];
    const recommendations: string[] = [];
    
    // Calculate voltage drop
    const { voltageDrop, percentDrop } = calculateVoltageDrop(
      input.amperage,
      input.distance,
      input.voltage,
      input.conductor_size,
      input.conductor_type === 'CU',
      input.phase === '3PH',
      input.power_factor
    );
    
    // Get conductor ampacity
    const ampacityTable = input.conductor_type === 'CU' ? 
      COPPER_AMPACITY_75C : ALUMINUM_AMPACITY_75C;
    const baseAmpacity = ampacityTable[input.conductor_size as keyof typeof ampacityTable] || 0;
    
    // Apply temperature derating if needed
    let temperatureDeratingFactor = 1;
    let temperatureDeratingApplied = false;
    
    if (input.ambient_temp_f > 86) { // 30°C
      // Simplified temperature correction
      if (input.ambient_temp_f > 104) { // 40°C
        temperatureDeratingFactor = 0.88;
      } else if (input.ambient_temp_f > 95) { // 35°C
        temperatureDeratingFactor = 0.94;
      } else {
        temperatureDeratingFactor = 0.96;
      }
      temperatureDeratingApplied = true;
      necRefs.push('310.15(B)(1)');
    }
    
    const conductorAmpacity = new Decimal(baseAmpacity).times(temperatureDeratingFactor);
    const adequateAmpacity = conductorAmpacity.greaterThanOrEqualTo(input.amperage);
    
    // Check against NEC limits
    const branchLimit = VOLTAGE_DROP_LIMITS.BRANCH_CIRCUIT * 100;
    const totalLimit = VOLTAGE_DROP_LIMITS.TOTAL * 100;
    const passesBranchLimit = percentDrop.lessThanOrEqualTo(branchLimit);
    const passesTotalLimit = percentDrop.lessThanOrEqualTo(totalLimit);
    
    // Generate recommendations
    if (!passesBranchLimit) {
      recommendations.push(`Voltage drop exceeds ${branchLimit}% branch circuit limit`);
      recommendations.push(`Consider using larger conductor or reducing circuit length`);
      
      // Calculate required conductor size
      const requiredSize = this.calculateRequiredConductorSize(
        input,
        branchLimit / 100
      );
      if (requiredSize) {
        recommendations.push(`Recommended conductor size: ${requiredSize}`);
      }
    }
    
    if (!adequateAmpacity) {
      recommendations.push(`Conductor ampacity (${conductorAmpacity.toFixed(1)} A) is less than circuit amperage (${input.amperage} A)`);
      necRefs.push('310.16');
    }
    
    if (percentDrop.greaterThan(2) && percentDrop.lessThanOrEqualTo(3)) {
      recommendations.push('Voltage drop is acceptable but approaching limits');
      recommendations.push('Consider upsizing conductor for future expansion');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Voltage drop is within acceptable limits');
      recommendations.push('Conductor size is adequate for the load');
    }
    
    return {
      circuit_name: input.circuit_name,
      voltage: input.voltage,
      phase: input.phase,
      amperage: input.amperage,
      distance: input.distance,
      conductor_size: input.conductor_size,
      conductor_type: input.conductor_type,
      calculated_vd_volts: voltageDrop.toNumber(),
      calculated_vd_percent: percentDrop.toNumber(),
      nec_limit_branch: branchLimit,
      nec_limit_total: totalLimit,
      passes_branch_limit: passesBranchLimit,
      passes_total_limit: passesTotalLimit,
      temperature_derating_applied: temperatureDeratingApplied,
      temperature_derating_factor: temperatureDeratingFactor,
      conductor_ampacity: conductorAmpacity.toNumber(),
      adequate_ampacity: adequateAmpacity,
      necReferences: [...new Set(necRefs)],
      recommendations
    };
  }
  
  private calculateRequiredConductorSize(
    input: VoltageDropInput, 
    maxVdPercent: number
  ): string | null {
    const conductorSizes = input.conductor_type === 'CU' ? 
      Object.keys(COPPER_AMPACITY_75C) : Object.keys(ALUMINUM_AMPACITY_75C);
    
    // Try each conductor size until we find one that works
    for (const size of conductorSizes) {
      const { percentDrop } = calculateVoltageDrop(
        input.amperage,
        input.distance,
        input.voltage,
        size,
        input.conductor_type === 'CU',
        input.phase === '3PH',
        input.power_factor
      );
      
      if (percentDrop.lessThanOrEqualTo(maxVdPercent * 100)) {
        return size;
      }
    }
    
    return null;
  }
}