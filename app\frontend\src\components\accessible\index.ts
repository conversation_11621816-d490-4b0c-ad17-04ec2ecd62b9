/**
 * Accessible component exports
 * 
 * These components follow WCAG 2.1 AA standards with:
 * - Proper ARIA labels and roles
 * - Keyboard navigation support
 * - Focus management and trapping
 * - Screen reader announcements
 * - Minimum touch target sizes (44px)
 * - Color contrast compliance
 * - Form field associations
 * - Live regions for dynamic content
 */

// Panel components
export { PanelScheduleAccessible } from '../panels/PanelScheduleAccessible';
export { CircuitFormAccessible } from '../panels/CircuitFormAccessible';

// Modal components
export { QRCodeModalAccessible } from '../inspections/QRCodeModalAccessible';

// Layout components
export { HeaderAccessible } from '../layout/HeaderAccessible';
export { LayoutAccessible } from '../layout/LayoutAccessible';

// Form components
export {
  FormFieldAccessible,
  SelectFieldAccessible,
  TextareaFieldAccessible,
  CheckboxFieldAccessible,
  RadioGroupAccessible
} from '../common/FormFieldAccessible';

// Button components
export {
  ButtonAccessible,
  IconButtonAccessible,
  ButtonGroup
} from '../common/ButtonAccessible';

// Utility exports
export {
  FocusTrap,
  announce,
  generateId,
  isInViewport,
  KeyboardNavigation,
  handleListKeyboardNavigation,
  prefersReducedMotion,
  trapFocus,
  srOnly
} from '../../utils/accessibility';