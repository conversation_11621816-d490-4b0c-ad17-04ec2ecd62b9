/**
 * Transaction Service Usage Examples
 * 
 * This file demonstrates how to use the transaction service for various
 * critical operations in the electrical project management system.
 */

import { PrismaClient } from '@prisma/client';
import { getTransactionService, TransactionResult, updateParentChildRelation, executeStateTransition, batchCreateWithValidation } from './transaction.service';
import { AppError } from '../utils/errors';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

/**
 * Example 1: Panel Creation with Initial Circuits
 * 
 * This demonstrates creating a panel and its initial set of circuits
 * in a single transaction, ensuring data consistency.
 */
export async function createPanelWithCircuits(
  panelData: any,
  circuitsData: any[],
  userId: string
): Promise<TransactionResult<any>> {
  return transactionService.executeTransaction(async (tx) => {
    // Create the panel first
    const panel = await tx.panel.create({
      data: {
        ...panelData,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    // Create all circuits
    const circuits = await Promise.all(
      circuitsData.map(circuitData => 
        tx.circuit.create({
          data: {
            ...circuitData,
            panel_id: panel.id,
            calculated_load: circuitData.connected_load * circuitData.demand_factor,
            created_at: new Date(),
            updated_at: new Date()
          }
        })
      )
    );

    // Calculate initial panel load
    const totalConnectedLoad = circuits.reduce((sum, c) => sum + c.connected_load, 0);
    const totalDemandLoad = circuits.reduce((sum, c) => sum + c.calculated_load, 0);

    // Create initial load calculation
    const loadCalculation = await tx.panelLoadCalculation.create({
      data: {
        panel_id: panel.id,
        total_connected_load: totalConnectedLoad,
        total_demand_load: totalDemandLoad,
        load_percentage: (totalDemandLoad / (panel.ampere_rating * 240)) * 100,
        phase_a_load: 0, // Would be calculated based on circuit assignments
        phase_b_load: 0,
        phase_c_load: 0,
        neutral_load: 0,
        phase_imbalance_percent: 0,
        power_factor: 0.9,
        calculation_date: new Date(),
        created_by: userId
      }
    });

    // TODO: Create audit log using createAuditLog function
    // await createAuditLog({
    //   action: 'PANEL_CREATE',
    //   userId: userId,
    //   resourceType: 'panel',
    //   resourceId: panel.id,
    //   details: {
    //     panel_name: panel.name,
    //     circuit_count: circuits.length,
    //     total_load: totalDemandLoad
    //   }
    // });

    return { panel, circuits, loadCalculation };
  });
}

/**
 * Example 2: Permit Document Workflow
 * 
 * This demonstrates the complete permit document workflow from creation
 * to submission, with proper state transitions and audit logging.
 */
export async function permitDocumentWorkflow(
  projectId: string,
  formData: any,
  userId: string
): Promise<any> {
  // Step 1: Create the permit document
  const createResult = await transactionService.executeTransaction(async (tx) => {
    const document = await tx.permitDocument.create({
      data: {
        project_id: projectId,
        document_type: 'PERMIT_APPLICATION',
        jurisdiction: formData.jurisdiction,
        title: 'Electrical Permit Application',
        form_data: JSON.stringify(formData),
        status: 'DRAFT',
        document_version: 1,
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    await tx.auditLog.create({
      data: {
        entity_type: 'PERMIT_DOCUMENT',
        entity_id: document.id,
        action: 'CREATE',
        changes: { status: 'DRAFT' },
        user_id: userId,
        timestamp: new Date()
      }
    });

    return document;
  });

  if (!createResult.success) {
    throw createResult.error;
  }

  const documentId = createResult.data!.id;

  // Step 2: Generate PDF (simulated)
  const pdfPath = `/generated-permits/permit-${documentId}.pdf`;
  
  const generateResult = await executeStateTransition(
    prisma,
    'PERMIT_DOCUMENT',
    documentId,
    'DRAFT',
    'READY',
    async (tx) => {
      return tx.permitDocument.update({
        where: { id: documentId },
        data: {
          generated_pdf_path: pdfPath,
          generation_date: new Date(),
          file_size_bytes: 1024000,
          page_count: 5,
          status: 'READY',
          updated_at: new Date()
        }
      });
    },
    userId
  );

  if (!generateResult.success) {
    throw generateResult.error;
  }

  // Step 3: Submit the permit
  const submitResult = await executeStateTransition(
    prisma,
    'PERMIT_DOCUMENT',
    documentId,
    'READY',
    'SUBMITTED',
    async (tx) => {
      return tx.permitDocument.update({
        where: { id: documentId },
        data: {
          status: 'SUBMITTED',
          submission_date: new Date(),
          permit_number: `PERMIT-${Date.now()}`,
          updated_at: new Date()
        }
      });
    },
    userId
  );

  return {
    createResult,
    generateResult,
    submitResult
  };
}

/**
 * Example 3: Complex Calculation with Equipment Updates
 * 
 * This demonstrates performing a short circuit calculation that updates
 * panel equipment ratings and creates recommendations.
 */
export async function performShortCircuitAnalysis(
  panelId: string,
  calculationInput: any,
  userId: string
): Promise<any> {
  return transactionService.executeWithRetry(async (tx) => {
    // Get panel and validate
    const panel = await tx.panel.findUnique({
      where: { id: panelId },
      include: { circuits: true }
    });

    if (!panel) {
      throw new AppError('Panel not found', 404);
    }

    // Simulate calculation results
    const faultCurrent = 22000; // Amps
    const requiredAIC = 25000; // Amps

    // Create calculation record
    const calculation = await tx.shortCircuitCalculation.create({
      data: {
        panel_id: panelId,
        calculation_method: 'POINT_TO_POINT',
        utility_voltage: 480,
        utility_fault_current: 50000,
        utility_x_r_ratio: 8,
        asymmetrical_fault_3ph: faultCurrent,
        peak_fault_current: faultCurrent * 2.7,
        bus_bracing_adequate: panel.bus_rating >= faultCurrent,
        bus_bracing_rating: panel.bus_rating,
        calculation_date: new Date(),
        calculated_by: userId,
        valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    // Update panel with calculation results
    await tx.panel.update({
      where: { id: panelId },
      data: {
        short_circuit_rating: faultCurrent,
        short_circuit_last_calculated: new Date(),
        requires_equipment_upgrade: panel.bus_rating < faultCurrent,
        updated_at: new Date()
      }
    });

    // Find suitable replacement breakers if needed
    if (panel.bus_rating < faultCurrent) {
      const recommendedBreakers = await tx.breakerType.findMany({
        where: {
          interrupt_rating: { gte: requiredAIC },
          voltage_rating: { gte: 480 },
          ampere_rating: { gte: panel.ampere_rating }
        },
        orderBy: { list_price: 'asc' },
        take: 3
      });

      // Create equipment recommendation
      await tx.equipmentRecommendation.create({
        data: {
          panel_id: panelId,
          calculation_id: calculation.id,
          calculation_type: 'SHORT_CIRCUIT',
          required_aic_rating: requiredAIC,
          recommended_equipment: JSON.stringify(recommendedBreakers),
          notes: `Current bus rating ${panel.bus_rating}A is inadequate for ${faultCurrent}A fault current`,
          created_by: userId,
          created_at: new Date()
        }
      });
    }

    // TODO: Create comprehensive audit log
    // await createAuditLog({
    //   action: 'SHORT_CIRCUIT_ANALYSIS',
    //   userId: userId,
    //   resourceType: 'panel',
    //   resourceId: panelId,
    //   details: {
    //     calculation_id: calculation.id,
    //     fault_current: faultCurrent,
    //     equipment_adequate: panel.bus_rating >= faultCurrent,
    //     recommendations_created: panel.bus_rating < faultCurrent
    //   }
    // });

    return {
      calculation,
      equipmentAdequate: panel.bus_rating >= faultCurrent,
      upgradeRequired: panel.bus_rating < faultCurrent
    };
  }, 3); // Retry up to 3 times on deadlock
}

/**
 * Example 4: Bulk Circuit Import with Validation
 * 
 * This demonstrates importing multiple circuits with validation
 * and automatic panel load recalculation.
 */
export async function importCircuitsBatch(
  panelId: string,
  circuitData: any[],
  userId: string
): Promise<any> {
  // Validation function
  const validateCircuit = async (circuit: any): Promise<boolean> => {
    // Check required fields
    if (!circuit.circuit_number || !circuit.description || !circuit.connected_load) {
      return false;
    }
    
    // Validate circuit number is positive
    if (circuit.circuit_number <= 0 || circuit.circuit_number > 84) {
      return false;
    }
    
    // Validate load is reasonable
    if (circuit.connected_load < 0 || circuit.connected_load > 100000) {
      return false;
    }
    
    // Validate poles
    if (![1, 2, 3].includes(circuit.poles)) {
      return false;
    }
    
    return true;
  };

  // Create function
  const createCircuit = async (tx: PrismaClient, circuit: any) => {
    return tx.circuit.create({
      data: {
        panel_id: panelId,
        circuit_number: circuit.circuit_number,
        description: circuit.description,
        load_type: circuit.load_type || 'GENERAL',
        connected_load: circuit.connected_load,
        demand_factor: circuit.demand_factor || 1.0,
        calculated_load: circuit.connected_load * (circuit.demand_factor || 1.0),
        poles: circuit.poles || 1,
        voltage: circuit.voltage || 120,
        breaker_size: circuit.breaker_size || 20,
        wire_size: circuit.wire_size || '12_AWG',
        wire_count: circuit.wire_count || 3,
        continuous_load: circuit.continuous_load || false,
        is_spare: false,
        is_space: false,
        created_at: new Date(),
        updated_at: new Date()
      }
    });
  };

  const result = await batchCreateWithValidation(
    prisma,
    circuitData,
    validateCircuit,
    createCircuit
  );

  if (result.success) {
    // Trigger panel load recalculation after successful import
    await transactionService.executeTransaction(async (tx) => {
      const panel = await tx.panel.findUnique({
        where: { id: panelId },
        include: { circuits: true }
      });

      if (!panel) {
        throw new AppError('Panel not found', 404);
      }

      const totalConnectedLoad = panel.circuits.reduce((sum, c) => sum + c.connected_load, 0);
      const totalDemandLoad = panel.circuits.reduce((sum, c) => sum + c.calculated_load, 0);

      await tx.panelLoadCalculation.create({
        data: {
          panel_id: panelId,
          total_connected_load: totalConnectedLoad,
          total_demand_load: totalDemandLoad,
          load_percentage: (totalDemandLoad / (panel.ampere_rating * 240)) * 100,
          phase_a_load: 0, // Would be calculated properly
          phase_b_load: 0,
          phase_c_load: 0,
          neutral_load: 0,
          phase_imbalance_percent: 0,
          power_factor: 0.9,
          calculation_date: new Date(),
          created_by: userId
        }
      });
    });
  }

  return result;
}

/**
 * Example 5: Project Cloning with Deep Copy
 * 
 * This demonstrates cloning an entire project with all its panels,
 * circuits, and calculations in a single transaction.
 */
export async function cloneProject(
  sourceProjectId: string,
  newProjectData: any,
  userId: string
): Promise<any> {
  return transactionService.executeTransaction(async (tx) => {
    // Get source project with all related data
    const sourceProject = await tx.project.findUnique({
      where: { id: sourceProjectId },
      include: {
        panels: {
          include: {
            circuits: true,
            load_calculations: {
              orderBy: { calculation_date: 'desc' },
              take: 1
            }
          }
        }
      }
    });

    if (!sourceProject) {
      throw new AppError('Source project not found', 404);
    }

    // Create new project
    const newProject = await tx.project.create({
      data: {
        ...newProjectData,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    // Clone all panels and circuits
    const panelMapping = new Map<string, string>(); // Old ID -> New ID

    for (const sourcePanel of sourceProject.panels) {
      // Create new panel
      const newPanel = await tx.panel.create({
        data: {
          project_id: newProject.id,
          name: `${sourcePanel.name} (Copy)`,
          location: sourcePanel.location,
          voltage_system: sourcePanel.voltage_system,
          phase_config: sourcePanel.phase_config,
          ampere_rating: sourcePanel.ampere_rating,
          bus_rating: sourcePanel.bus_rating,
          main_breaker_size: sourcePanel.main_breaker_size,
          spaces_total: sourcePanel.spaces_total,
          spaces_used: sourcePanel.spaces_used,
          mounting_type: sourcePanel.mounting_type,
          enclosure_type: sourcePanel.enclosure_type,
          manufacturer: sourcePanel.manufacturer,
          model_number: sourcePanel.model_number,
          created_at: new Date(),
          updated_at: new Date()
        }
      });

      panelMapping.set(sourcePanel.id, newPanel.id);

      // Clone all circuits for this panel
      const newCircuits = await Promise.all(
        sourcePanel.circuits.map(circuit => 
          tx.circuit.create({
            data: {
              panel_id: newPanel.id,
              circuit_number: circuit.circuit_number,
              description: circuit.description,
              load_type: circuit.load_type,
              connected_load: circuit.connected_load,
              demand_factor: circuit.demand_factor,
              calculated_load: circuit.calculated_load,
              poles: circuit.poles,
              voltage: circuit.voltage,
              phase_connection: circuit.phase_connection,
              breaker_size: circuit.breaker_size,
              breaker_type: circuit.breaker_type,
              wire_size: circuit.wire_size,
              wire_material: circuit.wire_material,
              wire_count: circuit.wire_count,
              conduit_type: circuit.conduit_type,
              conduit_size: circuit.conduit_size,
              continuous_load: circuit.continuous_load,
              motor_hp: circuit.motor_hp,
              motor_fla: circuit.motor_fla,
              is_spare: circuit.is_spare,
              is_space: circuit.is_space,
              created_at: new Date(),
              updated_at: new Date()
            }
          })
        )
      );

      // Clone latest load calculation if exists
      if (sourcePanel.load_calculations.length > 0) {
        const sourceCalc = sourcePanel.load_calculations[0];
        await tx.panelLoadCalculation.create({
          data: {
            panel_id: newPanel.id,
            phase_a_load: sourceCalc.phase_a_load,
            phase_b_load: sourceCalc.phase_b_load,
            phase_c_load: sourceCalc.phase_c_load,
            neutral_load: sourceCalc.neutral_load,
            total_connected_load: sourceCalc.total_connected_load,
            total_demand_load: sourceCalc.total_demand_load,
            load_percentage: sourceCalc.load_percentage,
            phase_imbalance_percent: sourceCalc.phase_imbalance_percent,
            power_factor: sourceCalc.power_factor,
            calculation_date: new Date(),
            created_by: userId
          }
        });
      }
    }

    // Update panel relationships (fed_from_panel_id)
    for (const sourcePanel of sourceProject.panels) {
      if (sourcePanel.fed_from_panel_id) {
        const newPanelId = panelMapping.get(sourcePanel.id);
        const newFedFromId = panelMapping.get(sourcePanel.fed_from_panel_id);
        
        if (newPanelId && newFedFromId) {
          await tx.panel.update({
            where: { id: newPanelId },
            data: { 
              fed_from_panel_id: newFedFromId,
              fed_from_circuit: sourcePanel.fed_from_circuit
            }
          });
        }
      }
    }

    // TODO: Create audit log
    // await createAuditLog({
    //   action: 'PROJECT_CLONE',
    //   userId: userId,
    //   resourceType: 'project',
    //   resourceId: newProject.id,
    //   details: {
    //     source_project_id: sourceProjectId,
    //     panels_cloned: sourceProject.panels.length,
    //     total_circuits_cloned: sourceProject.panels.reduce((sum, p) => sum + p.circuits.length, 0)
    //   }
    // });

    return {
      project: newProject,
      panelsCreated: panelMapping.size,
      panelMapping: Object.fromEntries(panelMapping)
    };
  }, {
    timeout: 30000 // 30 seconds for large cloning operations
  });
}

/**
 * Example 6: Financial Transaction with Line Items
 * 
 * This demonstrates creating an invoice with line items and updating
 * project financial status in a single transaction.
 * NOTE: Commented out as Invoice model doesn't exist in current schema
 */
export async function createInvoiceWithItems(
  projectId: string,
  invoiceData: any,
  lineItems: any[],
  userId: string
): Promise<any> {
  // Invoice functionality not implemented - models don't exist
  throw new Error('Invoice functionality not implemented');
  /*
  return transactionService.executeTransaction(async (tx) => {
    // Create invoice
    const invoice = await tx.invoice.create({
      data: {
        project_id: projectId,
        invoice_number: `INV-${Date.now()}`,
        invoice_date: new Date(),
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        status: 'DRAFT',
        subtotal: 0,
        tax_rate: invoiceData.tax_rate || 0.08,
        tax_amount: 0,
        total_amount: 0,
        notes: invoiceData.notes || '',
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    // Create line items and calculate totals
    let subtotal = 0;
    
    const createdItems = await Promise.all(
      lineItems.map((item, index) => 
        tx.invoiceLineItem.create({
          data: {
            invoice_id: invoice.id,
            line_number: index + 1,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            amount: item.quantity * item.unit_price,
            created_at: new Date()
          }
        })
      )
    );

    subtotal = createdItems.reduce((sum, item) => sum + item.amount, 0);
    const taxAmount = subtotal * invoice.tax_rate;
    const totalAmount = subtotal + taxAmount;

    // Update invoice with calculated totals
    const updatedInvoice = await tx.invoice.update({
      where: { id: invoice.id },
      data: {
        subtotal: subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount
      }
    });

    // Update project financial summary
    const project = await tx.project.findUnique({
      where: { id: projectId }
    });

    if (project) {
      await tx.project.update({
        where: { id: projectId },
        data: {
          total_invoiced: project.total_invoiced + totalAmount,
          outstanding_balance: project.outstanding_balance + totalAmount,
          updated_at: new Date()
        }
      });
    }

    // Create audit log
    await tx.auditLog.create({
      data: {
        entity_type: 'INVOICE',
        entity_id: invoice.id,
        action: 'CREATE',
        changes: {
          invoice_number: invoice.invoice_number,
          line_items: createdItems.length,
          total_amount: totalAmount
        },
        user_id: userId,
        timestamp: new Date()
      }
    });

    return {
      invoice: updatedInvoice,
      lineItems: createdItems,
      totals: {
        subtotal,
        taxAmount,
        totalAmount
      }
    };
  });
  */
}

// Export all examples
export const transactionExamples = {
  createPanelWithCircuits,
  permitDocumentWorkflow,
  performShortCircuitAnalysis,
  importCircuitsBatch,
  cloneProject,
  createInvoiceWithItems
};