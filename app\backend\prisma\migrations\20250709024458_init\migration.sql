-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "password_hash" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'electrician',
    "refresh_token" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "deleted_at" DATETIME
);

-- CreateTable
CREATE TABLE "Customer" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "zip" TEXT,
    "license_number" TEXT,
    "insurance_expiry" DATETIME,
    "credit_limit" REAL,
    "payment_terms" TEXT NOT NULL DEFAULT 'NET30',
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "deleted_at" DATETIME
);

-- CreateTable
CREATE TABLE "Project" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "customer_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "zip" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "voltage_system" TEXT NOT NULL,
    "service_size" INTEGER NOT NULL,
    "square_footage" INTEGER,
    "permit_number" TEXT,
    "permit_expiry" DATETIME,
    "inspection_status" TEXT,
    "panel_count" INTEGER,
    "main_disconnect_type" TEXT,
    "grounding_system" TEXT,
    "has_generator" BOOLEAN NOT NULL DEFAULT false,
    "has_solar" BOOLEAN NOT NULL DEFAULT false,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "Project_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "Customer" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Estimate" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "project_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL DEFAULT 1,
    "status" TEXT NOT NULL,
    "valid_until" DATETIME NOT NULL,
    "subtotal" REAL NOT NULL DEFAULT 0,
    "tax_total" REAL NOT NULL DEFAULT 0,
    "total_amount" REAL NOT NULL DEFAULT 0,
    "profit_margin" REAL NOT NULL DEFAULT 15,
    "contingency_percent" REAL NOT NULL DEFAULT 10,
    "notes" TEXT,
    "terms" TEXT,
    "created_by" TEXT NOT NULL,
    "approved_by" TEXT,
    "approved_at" DATETIME,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "Estimate_approved_by_fkey" FOREIGN KEY ("approved_by") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "Estimate_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Estimate_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "MaterialItem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "estimate_id" TEXT NOT NULL,
    "catalog_number" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "unit" TEXT NOT NULL,
    "quantity" REAL NOT NULL,
    "unit_cost" REAL NOT NULL,
    "markup_percent" REAL NOT NULL DEFAULT 35,
    "waste_percent" REAL NOT NULL DEFAULT 5,
    "tax_rate" REAL NOT NULL DEFAULT 0.0875,
    "extended_cost" REAL NOT NULL DEFAULT 0,
    "tax_amount" REAL NOT NULL DEFAULT 0,
    "total_amount" REAL NOT NULL DEFAULT 0,
    "supplier" TEXT,
    "wire_size" TEXT,
    "wire_type" TEXT,
    "conduit_size" TEXT,
    "conduit_type" TEXT,
    "voltage_rating" INTEGER,
    "amperage_rating" INTEGER,
    "phase" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "MaterialItem_estimate_id_fkey" FOREIGN KEY ("estimate_id") REFERENCES "Estimate" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "LaborItem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "estimate_id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "trade" TEXT NOT NULL,
    "hours" REAL NOT NULL,
    "rate" REAL NOT NULL,
    "overtime_hours" REAL NOT NULL DEFAULT 0,
    "overtime_rate" REAL NOT NULL,
    "burden_percent" REAL NOT NULL DEFAULT 35,
    "extended_cost" REAL NOT NULL DEFAULT 0,
    "burden_amount" REAL NOT NULL DEFAULT 0,
    "total_amount" REAL NOT NULL DEFAULT 0,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "LaborItem_estimate_id_fkey" FOREIGN KEY ("estimate_id") REFERENCES "Estimate" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "CalculationLog" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "calculation_type" TEXT NOT NULL,
    "input_data" TEXT NOT NULL,
    "output_data" TEXT NOT NULL,
    "nec_references" TEXT NOT NULL,
    "performed_by" TEXT NOT NULL,
    "project_id" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "CalculationLog_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "CalculationLog_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "MaterialPriceHistory" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "catalog_number" TEXT NOT NULL,
    "supplier" TEXT NOT NULL,
    "unit_cost" REAL NOT NULL,
    "effective_date" DATETIME NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "Panel" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "project_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "panel_type" TEXT NOT NULL,
    "manufacturer" TEXT,
    "model_number" TEXT,
    "catalog_number" TEXT,
    "voltage_system" TEXT NOT NULL,
    "ampere_rating" INTEGER NOT NULL,
    "bus_rating" INTEGER NOT NULL,
    "main_breaker_size" INTEGER,
    "phase_config" TEXT NOT NULL,
    "mounting_type" TEXT NOT NULL,
    "enclosure_type" TEXT NOT NULL,
    "spaces_total" INTEGER NOT NULL,
    "spaces_used" INTEGER NOT NULL DEFAULT 0,
    "fed_from_panel_id" TEXT,
    "fed_from_circuit" INTEGER,
    "notes" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "Panel_fed_from_panel_id_fkey" FOREIGN KEY ("fed_from_panel_id") REFERENCES "Panel" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "Panel_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Circuit" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "panel_id" TEXT NOT NULL,
    "circuit_number" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "breaker_size" INTEGER NOT NULL,
    "breaker_type" TEXT NOT NULL,
    "poles" INTEGER NOT NULL,
    "phase_connection" TEXT,
    "wire_size" TEXT NOT NULL,
    "wire_type" TEXT NOT NULL,
    "wire_count" INTEGER NOT NULL,
    "conduit_type" TEXT,
    "conduit_size" TEXT,
    "voltage" INTEGER NOT NULL,
    "load_type" TEXT NOT NULL,
    "continuous_load" BOOLEAN NOT NULL DEFAULT false,
    "connected_load" REAL NOT NULL,
    "demand_factor" REAL NOT NULL DEFAULT 1.0,
    "calculated_load" REAL NOT NULL,
    "room_area" TEXT,
    "control_type" TEXT,
    "is_spare" BOOLEAN NOT NULL DEFAULT false,
    "is_space" BOOLEAN NOT NULL DEFAULT false,
    "notes" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "Circuit_panel_id_fkey" FOREIGN KEY ("panel_id") REFERENCES "Panel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "PanelLoadCalculation" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "panel_id" TEXT NOT NULL,
    "calculation_date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "phase_a_load" REAL NOT NULL DEFAULT 0,
    "phase_b_load" REAL NOT NULL DEFAULT 0,
    "phase_c_load" REAL NOT NULL DEFAULT 0,
    "neutral_load" REAL NOT NULL DEFAULT 0,
    "total_connected_load" REAL NOT NULL DEFAULT 0,
    "total_demand_load" REAL NOT NULL DEFAULT 0,
    "load_percentage" REAL NOT NULL DEFAULT 0,
    "phase_imbalance_percent" REAL NOT NULL DEFAULT 0,
    "power_factor" REAL NOT NULL DEFAULT 0.9,
    "ambient_temperature" REAL NOT NULL DEFAULT 30,
    "derating_factor" REAL NOT NULL DEFAULT 1.0,
    "notes" TEXT,
    "created_by" TEXT NOT NULL,
    CONSTRAINT "PanelLoadCalculation_panel_id_fkey" FOREIGN KEY ("panel_id") REFERENCES "Panel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "BreakerType" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "manufacturer" TEXT NOT NULL,
    "series" TEXT NOT NULL,
    "catalog_number" TEXT NOT NULL,
    "ampere_rating" INTEGER NOT NULL,
    "poles" INTEGER NOT NULL,
    "voltage_rating" INTEGER NOT NULL,
    "interrupt_rating" INTEGER NOT NULL,
    "breaker_type" TEXT NOT NULL,
    "width_inches" REAL NOT NULL,
    "list_price" REAL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "NecReference" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "nec_edition" TEXT NOT NULL,
    "article" TEXT NOT NULL,
    "section" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "Material" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "sku" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "manufacturer" TEXT NOT NULL,
    "model_number" TEXT,
    "unit" TEXT NOT NULL,
    "current_price" REAL,
    "price_updated" DATETIME,
    "in_stock" BOOLEAN NOT NULL DEFAULT true,
    "min_quantity" INTEGER NOT NULL DEFAULT 1,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "wire_size" TEXT,
    "wire_type" TEXT,
    "conduit_size" TEXT,
    "conduit_type" TEXT,
    "voltage_rating" INTEGER,
    "amperage_rating" INTEGER,
    "phase" TEXT,
    "color" TEXT
);

-- CreateTable
CREATE TABLE "ArcFlashCalculation" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "panel_id" TEXT NOT NULL,
    "calculation_method" TEXT NOT NULL DEFAULT 'IEEE_1584_2018',
    "system_voltage" REAL NOT NULL,
    "system_type" TEXT NOT NULL,
    "grounding_type" TEXT NOT NULL,
    "frequency" INTEGER NOT NULL DEFAULT 60,
    "bolted_fault_current" REAL NOT NULL,
    "fault_clearing_time" REAL NOT NULL,
    "equipment_type" TEXT NOT NULL,
    "electrode_configuration" TEXT NOT NULL,
    "enclosure_width" REAL NOT NULL,
    "enclosure_height" REAL NOT NULL,
    "enclosure_depth" REAL NOT NULL,
    "conductor_gap" REAL NOT NULL,
    "working_distance" REAL NOT NULL,
    "arcing_current_min" REAL NOT NULL,
    "arcing_current_max" REAL NOT NULL,
    "incident_energy_min" REAL NOT NULL,
    "incident_energy_max" REAL NOT NULL,
    "arc_flash_boundary" REAL NOT NULL,
    "ppe_category" INTEGER NOT NULL,
    "ppe_min_arc_rating" REAL NOT NULL,
    "shock_hazard_voltage" REAL NOT NULL,
    "limited_approach" REAL NOT NULL,
    "restricted_approach" REAL NOT NULL,
    "enclosure_correction" REAL NOT NULL DEFAULT 1.0,
    "arc_duration_max" REAL NOT NULL DEFAULT 2.0,
    "reduced_arcing_current" REAL,
    "reduced_incident_energy" REAL,
    "hazard_risk_category" INTEGER NOT NULL,
    "requires_energized_work_permit" BOOLEAN NOT NULL DEFAULT true,
    "recommended_ppe" TEXT NOT NULL,
    "safety_notes" TEXT,
    "calculated_by" TEXT NOT NULL,
    "reviewed_by" TEXT,
    "calculation_date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "valid_until" DATETIME NOT NULL,
    "report_generated" BOOLEAN NOT NULL DEFAULT false,
    "report_path" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "ArcFlashCalculation_panel_id_fkey" FOREIGN KEY ("panel_id") REFERENCES "Panel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ShortCircuitCalculation" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "panel_id" TEXT NOT NULL,
    "calculation_method" TEXT NOT NULL DEFAULT 'POINT_TO_POINT',
    "utility_voltage" REAL NOT NULL,
    "utility_fault_current" REAL NOT NULL,
    "utility_x_r_ratio" REAL NOT NULL DEFAULT 10.0,
    "transformer_id" TEXT,
    "transformer_kva" REAL,
    "transformer_impedance" REAL,
    "transformer_x_r_ratio" REAL,
    "transformer_primary_v" REAL,
    "transformer_secondary_v" REAL,
    "transformer_type" TEXT,
    "conductor_length" REAL NOT NULL,
    "conductor_size" TEXT NOT NULL,
    "conductor_material" TEXT NOT NULL DEFAULT 'COPPER',
    "conductor_type" TEXT NOT NULL,
    "conductors_per_phase" INTEGER NOT NULL DEFAULT 1,
    "conduit_type" TEXT NOT NULL,
    "motor_contribution" BOOLEAN NOT NULL DEFAULT false,
    "motor_hp_total" REAL,
    "motor_contribution_multiplier" REAL NOT NULL DEFAULT 4.0,
    "source_impedance_r" REAL NOT NULL,
    "source_impedance_x" REAL NOT NULL,
    "transformer_impedance_r" REAL,
    "transformer_impedance_x" REAL,
    "conductor_impedance_r" REAL NOT NULL,
    "conductor_impedance_x" REAL NOT NULL,
    "total_impedance_r" REAL NOT NULL,
    "total_impedance_x" REAL NOT NULL,
    "total_impedance_z" REAL NOT NULL,
    "total_x_r_ratio" REAL NOT NULL,
    "symmetrical_fault_3ph" REAL NOT NULL,
    "symmetrical_fault_lg" REAL NOT NULL,
    "symmetrical_fault_ll" REAL NOT NULL,
    "asymmetrical_fault_3ph" REAL NOT NULL,
    "peak_fault_current" REAL NOT NULL,
    "bus_bracing_adequate" BOOLEAN NOT NULL,
    "bus_bracing_rating" REAL,
    "main_breaker_adequate" BOOLEAN NOT NULL,
    "main_breaker_aic" REAL,
    "branch_breaker_adequate" BOOLEAN NOT NULL,
    "branch_breaker_aic" REAL,
    "series_rated" BOOLEAN NOT NULL DEFAULT false,
    "upstream_device" TEXT,
    "downstream_device" TEXT,
    "series_rating_aic" REAL,
    "device_time_bands" TEXT,
    "coordination_verified" BOOLEAN NOT NULL DEFAULT false,
    "coordination_notes" TEXT,
    "safety_factor" REAL NOT NULL DEFAULT 1.0,
    "calculation_notes" TEXT,
    "assumptions" TEXT,
    "calculated_by" TEXT NOT NULL,
    "reviewed_by" TEXT,
    "calculation_date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "valid_until" DATETIME NOT NULL,
    "report_generated" BOOLEAN NOT NULL DEFAULT false,
    "report_path" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "ShortCircuitCalculation_panel_id_fkey" FOREIGN KEY ("panel_id") REFERENCES "Panel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "PermitDocument" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "project_id" TEXT NOT NULL,
    "document_type" TEXT NOT NULL,
    "document_version" INTEGER NOT NULL DEFAULT 1,
    "jurisdiction" TEXT NOT NULL,
    "jurisdiction_code" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "template_id" TEXT,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "permit_number" TEXT,
    "submission_date" DATETIME,
    "approval_date" DATETIME,
    "expiration_date" DATETIME,
    "rejection_reason" TEXT,
    "inspector_notes" TEXT,
    "form_data" TEXT NOT NULL,
    "included_calculations" TEXT,
    "included_panels" TEXT,
    "attachments" TEXT,
    "signature_required" BOOLEAN NOT NULL DEFAULT false,
    "signed_by" TEXT,
    "signature_date" DATETIME,
    "signature_data" TEXT,
    "generated_pdf_path" TEXT,
    "generation_date" DATETIME,
    "file_size_bytes" INTEGER,
    "page_count" INTEGER,
    "created_by" TEXT NOT NULL,
    "reviewed_by" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "PermitDocument_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "JurisdictionTemplate" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "jurisdiction_name" TEXT NOT NULL,
    "jurisdiction_code" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "document_type" TEXT NOT NULL,
    "template_name" TEXT NOT NULL,
    "template_version" TEXT NOT NULL,
    "effective_date" DATETIME NOT NULL,
    "expiration_date" DATETIME,
    "form_fields" TEXT NOT NULL,
    "required_documents" TEXT NOT NULL,
    "fee_schedule" TEXT,
    "nec_edition" TEXT NOT NULL DEFAULT '2023',
    "local_amendments" TEXT,
    "department_name" TEXT,
    "department_phone" TEXT,
    "department_email" TEXT,
    "submission_url" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "InspectionChecklist" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "project_id" TEXT NOT NULL,
    "permit_document_id" TEXT,
    "inspection_type" TEXT NOT NULL,
    "inspection_subtype" TEXT,
    "inspection_number" INTEGER NOT NULL DEFAULT 1,
    "inspection_date" DATETIME,
    "scheduled_date" DATETIME,
    "inspector_name" TEXT,
    "inspector_id" TEXT,
    "inspector_company" TEXT,
    "inspector_phone" TEXT,
    "inspector_email" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "overall_result" TEXT,
    "reinspection_required" BOOLEAN NOT NULL DEFAULT false,
    "corrections_required" BOOLEAN NOT NULL DEFAULT false,
    "location_details" TEXT,
    "access_instructions" TEXT,
    "qr_code_id" TEXT,
    "qr_code_url" TEXT,
    "contractor_present" BOOLEAN NOT NULL DEFAULT false,
    "contractor_name" TEXT,
    "contractor_signature" TEXT,
    "inspector_signature" TEXT,
    "sign_off_date" DATETIME,
    "report_generated" BOOLEAN NOT NULL DEFAULT false,
    "report_path" TEXT,
    "report_sent_date" DATETIME,
    "created_by" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "completed_at" DATETIME,
    CONSTRAINT "InspectionChecklist_permit_document_id_fkey" FOREIGN KEY ("permit_document_id") REFERENCES "PermitDocument" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "InspectionChecklist_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "InspectionChecklistItem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "checklist_id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "item_code" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "nec_reference" TEXT,
    "inspection_criteria" TEXT,
    "common_failures" TEXT,
    "status" TEXT NOT NULL DEFAULT 'NOT_INSPECTED',
    "inspector_notes" TEXT,
    "failure_reason" TEXT,
    "correction_required" BOOLEAN NOT NULL DEFAULT false,
    "correction_description" TEXT,
    "correction_deadline" DATETIME,
    "correction_completed" BOOLEAN NOT NULL DEFAULT false,
    "correction_date" DATETIME,
    "correction_verified_by" TEXT,
    "photo_required" BOOLEAN NOT NULL DEFAULT false,
    "photos_attached" INTEGER NOT NULL DEFAULT 0,
    "severity" TEXT NOT NULL DEFAULT 'STANDARD',
    "is_code_violation" BOOLEAN NOT NULL DEFAULT false,
    "immediate_danger" BOOLEAN NOT NULL DEFAULT false,
    "measurement_required" BOOLEAN NOT NULL DEFAULT false,
    "measurement_type" TEXT,
    "measurement_value" TEXT,
    "measurement_unit" TEXT,
    "measurement_range_min" REAL,
    "measurement_range_max" REAL,
    "sequence_number" INTEGER NOT NULL DEFAULT 0,
    "is_required" BOOLEAN NOT NULL DEFAULT true,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "inspected_at" DATETIME,
    CONSTRAINT "InspectionChecklistItem_checklist_id_fkey" FOREIGN KEY ("checklist_id") REFERENCES "InspectionChecklist" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "InspectionPhoto" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "checklist_id" TEXT NOT NULL,
    "item_id" TEXT,
    "file_path" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "mime_type" TEXT NOT NULL DEFAULT 'image/jpeg',
    "caption" TEXT,
    "photo_type" TEXT NOT NULL,
    "location_tag" TEXT,
    "latitude" REAL,
    "longitude" REAL,
    "thumbnail_path" TEXT,
    "is_annotated" BOOLEAN NOT NULL DEFAULT false,
    "annotation_data" TEXT,
    "uploaded_by" TEXT NOT NULL,
    "uploaded_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "InspectionPhoto_checklist_id_fkey" FOREIGN KEY ("checklist_id") REFERENCES "InspectionChecklist" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "InspectionTemplate" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "template_name" TEXT NOT NULL,
    "inspection_type" TEXT NOT NULL,
    "nec_edition" TEXT NOT NULL DEFAULT '2023',
    "jurisdiction" TEXT,
    "description" TEXT,
    "checklist_items" TEXT NOT NULL,
    "required_photos" TEXT,
    "common_issues" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "category_order" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_deleted_at_idx" ON "User"("deleted_at");

-- CreateIndex
CREATE INDEX "Customer_name_idx" ON "Customer"("name");

-- CreateIndex
CREATE INDEX "Customer_deleted_at_idx" ON "Customer"("deleted_at");

-- CreateIndex
CREATE INDEX "Project_customer_id_idx" ON "Project"("customer_id");

-- CreateIndex
CREATE INDEX "Project_status_idx" ON "Project"("status");

-- CreateIndex
CREATE INDEX "Project_type_idx" ON "Project"("type");

-- CreateIndex
CREATE INDEX "Estimate_project_id_idx" ON "Estimate"("project_id");

-- CreateIndex
CREATE INDEX "Estimate_status_idx" ON "Estimate"("status");

-- CreateIndex
CREATE UNIQUE INDEX "Estimate_project_id_version_key" ON "Estimate"("project_id", "version");

-- CreateIndex
CREATE INDEX "MaterialItem_estimate_id_idx" ON "MaterialItem"("estimate_id");

-- CreateIndex
CREATE INDEX "MaterialItem_catalog_number_idx" ON "MaterialItem"("catalog_number");

-- CreateIndex
CREATE INDEX "MaterialItem_category_idx" ON "MaterialItem"("category");

-- CreateIndex
CREATE INDEX "LaborItem_estimate_id_idx" ON "LaborItem"("estimate_id");

-- CreateIndex
CREATE INDEX "LaborItem_trade_idx" ON "LaborItem"("trade");

-- CreateIndex
CREATE INDEX "CalculationLog_calculation_type_idx" ON "CalculationLog"("calculation_type");

-- CreateIndex
CREATE INDEX "CalculationLog_project_id_idx" ON "CalculationLog"("project_id");

-- CreateIndex
CREATE INDEX "CalculationLog_performed_by_idx" ON "CalculationLog"("performed_by");

-- CreateIndex
CREATE INDEX "MaterialPriceHistory_catalog_number_supplier_idx" ON "MaterialPriceHistory"("catalog_number", "supplier");

-- CreateIndex
CREATE INDEX "MaterialPriceHistory_effective_date_idx" ON "MaterialPriceHistory"("effective_date");

-- CreateIndex
CREATE INDEX "Panel_project_id_idx" ON "Panel"("project_id");

-- CreateIndex
CREATE INDEX "Panel_fed_from_panel_id_idx" ON "Panel"("fed_from_panel_id");

-- CreateIndex
CREATE INDEX "Circuit_panel_id_idx" ON "Circuit"("panel_id");

-- CreateIndex
CREATE INDEX "Circuit_load_type_idx" ON "Circuit"("load_type");

-- CreateIndex
CREATE UNIQUE INDEX "Circuit_panel_id_circuit_number_key" ON "Circuit"("panel_id", "circuit_number");

-- CreateIndex
CREATE INDEX "PanelLoadCalculation_panel_id_idx" ON "PanelLoadCalculation"("panel_id");

-- CreateIndex
CREATE INDEX "PanelLoadCalculation_calculation_date_idx" ON "PanelLoadCalculation"("calculation_date");

-- CreateIndex
CREATE INDEX "BreakerType_manufacturer_series_idx" ON "BreakerType"("manufacturer", "series");

-- CreateIndex
CREATE UNIQUE INDEX "BreakerType_manufacturer_catalog_number_key" ON "BreakerType"("manufacturer", "catalog_number");

-- CreateIndex
CREATE INDEX "NecReference_nec_edition_article_idx" ON "NecReference"("nec_edition", "article");

-- CreateIndex
CREATE INDEX "NecReference_category_idx" ON "NecReference"("category");

-- CreateIndex
CREATE UNIQUE INDEX "Material_sku_key" ON "Material"("sku");

-- CreateIndex
CREATE INDEX "Material_category_idx" ON "Material"("category");

-- CreateIndex
CREATE INDEX "Material_manufacturer_idx" ON "Material"("manufacturer");

-- CreateIndex
CREATE INDEX "ArcFlashCalculation_panel_id_idx" ON "ArcFlashCalculation"("panel_id");

-- CreateIndex
CREATE INDEX "ArcFlashCalculation_calculation_date_idx" ON "ArcFlashCalculation"("calculation_date");

-- CreateIndex
CREATE INDEX "ArcFlashCalculation_ppe_category_idx" ON "ArcFlashCalculation"("ppe_category");

-- CreateIndex
CREATE INDEX "ShortCircuitCalculation_panel_id_idx" ON "ShortCircuitCalculation"("panel_id");

-- CreateIndex
CREATE INDEX "ShortCircuitCalculation_calculation_date_idx" ON "ShortCircuitCalculation"("calculation_date");

-- CreateIndex
CREATE INDEX "ShortCircuitCalculation_calculation_method_idx" ON "ShortCircuitCalculation"("calculation_method");

-- CreateIndex
CREATE INDEX "PermitDocument_project_id_idx" ON "PermitDocument"("project_id");

-- CreateIndex
CREATE INDEX "PermitDocument_status_idx" ON "PermitDocument"("status");

-- CreateIndex
CREATE INDEX "PermitDocument_jurisdiction_idx" ON "PermitDocument"("jurisdiction");

-- CreateIndex
CREATE INDEX "PermitDocument_submission_date_idx" ON "PermitDocument"("submission_date");

-- CreateIndex
CREATE UNIQUE INDEX "PermitDocument_project_id_document_type_document_version_key" ON "PermitDocument"("project_id", "document_type", "document_version");

-- CreateIndex
CREATE UNIQUE INDEX "JurisdictionTemplate_jurisdiction_code_key" ON "JurisdictionTemplate"("jurisdiction_code");

-- CreateIndex
CREATE INDEX "JurisdictionTemplate_state_idx" ON "JurisdictionTemplate"("state");

-- CreateIndex
CREATE INDEX "JurisdictionTemplate_document_type_idx" ON "JurisdictionTemplate"("document_type");

-- CreateIndex
CREATE UNIQUE INDEX "JurisdictionTemplate_jurisdiction_code_document_type_key" ON "JurisdictionTemplate"("jurisdiction_code", "document_type");

-- CreateIndex
CREATE UNIQUE INDEX "InspectionChecklist_qr_code_id_key" ON "InspectionChecklist"("qr_code_id");

-- CreateIndex
CREATE INDEX "InspectionChecklist_project_id_idx" ON "InspectionChecklist"("project_id");

-- CreateIndex
CREATE INDEX "InspectionChecklist_inspection_type_idx" ON "InspectionChecklist"("inspection_type");

-- CreateIndex
CREATE INDEX "InspectionChecklist_status_idx" ON "InspectionChecklist"("status");

-- CreateIndex
CREATE INDEX "InspectionChecklist_inspection_date_idx" ON "InspectionChecklist"("inspection_date");

-- CreateIndex
CREATE INDEX "InspectionChecklist_qr_code_id_idx" ON "InspectionChecklist"("qr_code_id");

-- CreateIndex
CREATE INDEX "InspectionChecklistItem_checklist_id_idx" ON "InspectionChecklistItem"("checklist_id");

-- CreateIndex
CREATE INDEX "InspectionChecklistItem_category_idx" ON "InspectionChecklistItem"("category");

-- CreateIndex
CREATE INDEX "InspectionChecklistItem_status_idx" ON "InspectionChecklistItem"("status");

-- CreateIndex
CREATE INDEX "InspectionChecklistItem_correction_required_idx" ON "InspectionChecklistItem"("correction_required");

-- CreateIndex
CREATE UNIQUE INDEX "InspectionChecklistItem_checklist_id_item_code_key" ON "InspectionChecklistItem"("checklist_id", "item_code");

-- CreateIndex
CREATE INDEX "InspectionPhoto_checklist_id_idx" ON "InspectionPhoto"("checklist_id");

-- CreateIndex
CREATE INDEX "InspectionPhoto_item_id_idx" ON "InspectionPhoto"("item_id");

-- CreateIndex
CREATE INDEX "InspectionPhoto_photo_type_idx" ON "InspectionPhoto"("photo_type");

-- CreateIndex
CREATE INDEX "InspectionTemplate_inspection_type_idx" ON "InspectionTemplate"("inspection_type");

-- CreateIndex
CREATE INDEX "InspectionTemplate_is_active_idx" ON "InspectionTemplate"("is_active");

-- CreateIndex
CREATE UNIQUE INDEX "InspectionTemplate_inspection_type_jurisdiction_nec_edition_key" ON "InspectionTemplate"("inspection_type", "jurisdiction", "nec_edition");
