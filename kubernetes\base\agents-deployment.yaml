apiVersion: apps/v1
kind: Deployment
metadata:
  name: agents
  namespace: electrical-app
  labels:
    app: agents
    tier: background
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agents
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: agents
        tier: background
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: agents-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: agents
        image: electrical/agents:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: agents-secrets
              key: redis-url
        - name: NEO4J_URI
          valueFrom:
            secretKeyRef:
              name: agents-secrets
              key: neo4j-uri
        - name: NEO4J_USER
          valueFrom:
            secretKeyRef:
              name: agents-secrets
              key: neo4j-user
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: agents-secrets
              key: neo4j-password
        - name: CHROMADB_URL
          valueFrom:
            configMapKeyRef:
              name: agents-config
              key: chromadb-url
        - name: BACKEND_URL
          value: "http://backend:3000"
        - name: SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: agents-secrets
              key: sentry-dsn
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: agent-data
          mountPath: /app/data
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: agent-data
        persistentVolumeClaim:
          claimName: agents-pvc
      - name: tmp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - agents
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: agents
  namespace: electrical-app
  labels:
    app: agents
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: agents
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: agents-sa
  namespace: electrical-app
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: agents-pvc
  namespace: electrical-app
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: gp2