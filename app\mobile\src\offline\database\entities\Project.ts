import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Panel } from './Panel';
import { Material } from './Material';
import { Calculation } from './Calculation';
import { Photo } from './Photo';

@Entity('projects')
export class Project extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  address: string;

  @Column({ nullable: true })
  clientName: string;

  @Column({ nullable: true })
  clientPhone: string;

  @Column({ nullable: true })
  clientEmail: string;

  @Column({ type: 'text', default: 'active' })
  status: 'active' | 'completed' | 'on-hold' | 'cancelled';

  @Column({ type: 'float', default: 0 })
  estimatedBudget: number;

  @Column({ type: 'float', default: 0 })
  actualCost: number;

  @Column({ nullable: true })
  startDate: string;

  @Column({ nullable: true })
  endDate: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string for additional data

  @OneToMany(() => Panel, panel => panel.project)
  panels: Panel[];

  @OneToMany(() => Material, material => material.project)
  materials: Material[];

  @OneToMany(() => Calculation, calculation => calculation.project)
  calculations: Calculation[];

  @OneToMany(() => Photo, photo => photo.project)
  photos: Photo[];
}