// Electrical-specific types for mobile app

// Calculator Types
export interface LoadCalculation {
  id?: string;
  projectId?: string;
  panelId?: string;
  dwelling?: DwellingLoad;
  commercial?: CommercialLoad;
  totalLoad: number;
  demandFactor: number;
  calculatedLoad: number;
  necReference: string;
  timestamp: Date;
  notes?: string;
}

export interface DwellingLoad {
  squareFootage: number;
  smallApplianceCircuits: number;
  laundryCircuit: boolean;
  electricRange?: number;
  electricDryer?: number;
  electricWaterHeater?: number;
  airConditioner?: number;
  heatPump?: number;
  electricHeat?: number;
  additionalLoads?: AdditionalLoad[];
}

export interface CommercialLoad {
  lightingLoad: number;
  receptacleLoad: number;
  motorLoads: MotorLoad[];
  specialLoads: SpecialLoad[];
  demandFactors: DemandFactors;
}

export interface AdditionalLoad {
  description: string;
  watts: number;
  quantity: number;
}

export interface MotorLoad {
  hp: number;
  voltage: number;
  phase: number;
  quantity: number;
  powerFactor: number;
}

export interface SpecialLoad {
  type: 'continuous' | 'non-continuous';
  description: string;
  watts: number;
  demandFactor: number;
}

export interface DemandFactors {
  lighting: number;
  receptacle: number;
  motor: number;
}

export interface VoltageDropCalculation {
  id?: string;
  voltage: number;
  phase: 1 | 3;
  current: number;
  distance: number;
  wireSize: string;
  wireType: 'copper' | 'aluminum';
  conduitType: 'pvc' | 'emt' | 'rigid';
  temperature: number;
  powerFactor: number;
  calculatedDrop: number;
  percentageDrop: number;
  recommendation?: string;
  timestamp: Date;
}

export interface WireSizeCalculation {
  id?: string;
  current: number;
  voltage: number;
  phase: 1 | 3;
  wireType: 'copper' | 'aluminum';
  insulation: 'THHN' | 'THWN' | 'XHHW' | 'USE';
  temperature: number;
  conduitFill: number;
  length?: number;
  recommendedSize: string;
  ampacity: number;
  derating: number;
  timestamp: Date;
}

export interface ConduitFillCalculation {
  id?: string;
  conduitType: 'EMT' | 'PVC' | 'RIGID' | 'FMC' | 'LFMC';
  conduitSize: string;
  wires: WireInConduit[];
  fillPercentage: number;
  maxFill: number;
  isCompliant: boolean;
  timestamp: Date;
}

export interface WireInConduit {
  size: string;
  type: string;
  quantity: number;
  area: number;
}

// Panel Management Types
export interface Panel {
  id: string;
  projectId: string;
  name: string;
  location: string;
  type: 'main' | 'sub' | 'distribution';
  manufacturer: string;
  model: string;
  voltage: number;
  phase: 1 | 3;
  amperage: number;
  spaces: number;
  circuits: Circuit[];
  totalLoad: number;
  qrCode?: string;
  lastUpdated: Date;
  status: 'active' | 'maintenance' | 'offline';
}

export interface Circuit {
  id: string;
  panelId: string;
  number: number;
  name: string;
  type: 'lighting' | 'receptacle' | 'motor' | 'appliance' | 'hvac' | 'spare';
  amperage: number;
  poles: 1 | 2 | 3;
  voltage: number;
  load: number;
  status: 'on' | 'off' | 'tripped' | 'maintenance';
  afci: boolean;
  gfci: boolean;
  notes?: string;
  lastUpdated: Date;
}

// Inspection Types
export interface Inspection {
  id: string;
  projectId: string;
  type: 'rough' | 'final' | 'service' | 'periodic';
  status: 'scheduled' | 'in-progress' | 'passed' | 'failed' | 'pending-reinspection';
  scheduledDate: Date;
  completedDate?: Date;
  inspector?: string;
  checklist: InspectionItem[];
  photos: InspectionPhoto[];
  violations: Violation[];
  notes: string;
  signature?: string;
}

export interface InspectionItem {
  id: string;
  category: string;
  description: string;
  status: 'pass' | 'fail' | 'na' | 'pending';
  necCode?: string;
  notes?: string;
  photos?: string[];
}

export interface InspectionPhoto {
  id: string;
  uri: string;
  caption: string;
  location: string;
  timestamp: Date;
  gpsCoordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface Violation {
  id: string;
  code: string;
  description: string;
  severity: 'minor' | 'major' | 'critical';
  location: string;
  correctiveAction: string;
  photosRequired: boolean;
  resolved: boolean;
}

// Material Types
export interface Material {
  id: string;
  sku: string;
  name: string;
  category: string;
  manufacturer: string;
  unit: string;
  price: number;
  inStock: boolean;
  stockLevel?: number;
  location?: string;
  lastUpdated: Date;
}

export interface MaterialTakeoff {
  id: string;
  projectId: string;
  items: TakeoffItem[];
  totalCost: number;
  createdDate: Date;
  status: 'draft' | 'submitted' | 'approved';
}

export interface TakeoffItem {
  materialId: string;
  material: Material;
  quantity: number;
  waste: number;
  totalQuantity: number;
  unitCost: number;
  totalCost: number;
  notes?: string;
}

// Safety Types
export interface ArcFlashLabel {
  id: string;
  equipmentId: string;
  incidentEnergy: number;
  workingDistance: number;
  arcFlashBoundary: number;
  ppeCategory: 1 | 2 | 3 | 4;
  voltage: number;
  limitedApproachBoundary: number;
  restrictedApproachBoundary: number;
  prohibitedApproachBoundary: number;
  lastCalculated: Date;
}

export interface PPERequirement {
  category: number;
  requirements: string[];
  minimumArcRating: number;
  description: string;
}

export interface SafetyViolation {
  id: string;
  projectId: string;
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: string;
  reportedBy: string;
  reportedDate: Date;
  status: 'open' | 'investigating' | 'resolved';
  correctiveActions?: string[];
  photos?: string[];
}

// Job Management Types
export interface TimeEntry {
  id: string;
  projectId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  taskType: 'installation' | 'troubleshooting' | 'inspection' | 'planning' | 'travel';
  description: string;
  location?: string;
  approved: boolean;
}

export interface QuickEstimate {
  id: string;
  projectName: string;
  customer: string;
  items: EstimateItem[];
  laborHours: number;
  laborRate: number;
  materialCost: number;
  laborCost: number;
  overhead: number;
  profit: number;
  totalCost: number;
  createdDate: Date;
  expirationDate: Date;
  status: 'draft' | 'sent' | 'accepted' | 'rejected';
}

export interface EstimateItem {
  description: string;
  quantity: number;
  unit: string;
  materialCost: number;
  laborHours: number;
  totalCost: number;
}

// Communication Types
export interface TeamMessage {
  id: string;
  projectId: string;
  senderId: string;
  senderName: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  attachments?: string[];
}

export interface JobUpdate {
  id: string;
  projectId: string;
  type: 'status' | 'material' | 'labor' | 'inspection' | 'safety';
  title: string;
  description: string;
  createdBy: string;
  createdDate: Date;
  photos?: string[];
}

// Emergency Contact Types
export interface EmergencyContact {
  id: string;
  name: string;
  role: string;
  phone: string;
  alternatePhone?: string;
  priority: number;
  available24x7: boolean;
}