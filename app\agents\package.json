{"name": "@electrical/agents", "version": "1.0.0", "description": "AI agent system for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@electrical/shared": "workspace:*", "@prisma/client": "^5.8.1", "axios": "^1.6.5", "bullmq": "^5.1.5", "chromadb": "^1.7.3", "decimal.js": "^10.4.3", "eventemitter3": "^5.0.1", "ioredis": "^5.3.2", "neo4j-driver": "^5.15.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}}