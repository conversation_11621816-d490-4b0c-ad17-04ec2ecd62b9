import { errorService } from './errorService';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface PerformanceThresholds {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
  apiResponseTime?: number;
  renderTime?: number;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private observers: Map<string, PerformanceObserver> = new Map();
  private thresholds: PerformanceThresholds = {
    fcp: 1800, // 1.8s
    lcp: 2500, // 2.5s
    fid: 100, // 100ms
    cls: 0.1, // 0.1
    ttfb: 800, // 800ms
    apiResponseTime: 1000, // 1s
    renderTime: 16, // 16ms (60fps)
  };

  private constructor() {
    this.initializeObservers();
    this.measureNavigationTiming();
  }

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    // First Contentful Paint
    try {
      const fcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.recordMetric('fcp', entry.startTime, 'ms', {
              type: 'paint',
            });
            this.checkThreshold('fcp', entry.startTime);
          }
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.set('fcp', fcpObserver);
    } catch (e) {
      console.warn('Failed to observe paint metrics:', e);
    }

    // Largest Contentful Paint
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordMetric('lcp', lastEntry.startTime, 'ms', {
          size: (lastEntry as any).size,
          element: (lastEntry as any).element?.tagName,
        });
        this.checkThreshold('lcp', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);
    } catch (e) {
      console.warn('Failed to observe LCP metrics:', e);
    }

    // First Input Delay
    try {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fid = (entry as any).processingStart - entry.startTime;
          this.recordMetric('fid', fid, 'ms', {
            eventType: entry.name,
          });
          this.checkThreshold('fid', fid);
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);
    } catch (e) {
      console.warn('Failed to observe FID metrics:', e);
    }

    // Layout shifts
    try {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            this.recordMetric('cls', clsValue, 'score', {
              sources: (entry as any).sources?.length || 0,
            });
            this.checkThreshold('cls', clsValue);
          }
        }
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('cls', clsObserver);
    } catch (e) {
      console.warn('Failed to observe CLS metrics:', e);
    }

    // Long tasks
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('longTask', entry.duration, 'ms', {
            startTime: entry.startTime,
            attribution: (entry as any).attribution,
          });
          
          if (entry.duration > 50) {
            errorService.logWarning('Long task detected', {
              duration: entry.duration,
              startTime: entry.startTime,
            });
          }
        }
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.set('longtask', longTaskObserver);
    } catch (e) {
      console.warn('Failed to observe long task metrics:', e);
    }
  }

  /**
   * Measure navigation timing
   */
  private measureNavigationTiming(): void {
    if (!('performance' in window) || !performance.timing) {
      return;
    }

    // Wait for page load
    if (document.readyState === 'complete') {
      this.recordNavigationMetrics();
    } else {
      window.addEventListener('load', () => this.recordNavigationMetrics());
    }
  }

  /**
   * Record navigation metrics
   */
  private recordNavigationMetrics(): void {
    const timing = performance.timing;
    const navigation = performance.navigation;

    // Time to First Byte
    const ttfb = timing.responseStart - timing.navigationStart;
    this.recordMetric('ttfb', ttfb, 'ms', {
      type: navigation.type,
    });
    this.checkThreshold('ttfb', ttfb);

    // DOM Content Loaded
    const dcl = timing.domContentLoadedEventEnd - timing.navigationStart;
    this.recordMetric('domContentLoaded', dcl, 'ms');

    // Page Load Time
    const loadTime = timing.loadEventEnd - timing.navigationStart;
    this.recordMetric('pageLoad', loadTime, 'ms');

    // DNS Lookup
    const dnsTime = timing.domainLookupEnd - timing.domainLookupStart;
    this.recordMetric('dnsLookup', dnsTime, 'ms');

    // TCP Connection
    const tcpTime = timing.connectEnd - timing.connectStart;
    this.recordMetric('tcpConnection', tcpTime, 'ms');

    // Request Time
    const requestTime = timing.responseStart - timing.requestStart;
    this.recordMetric('requestTime', requestTime, 'ms');

    // Response Time
    const responseTime = timing.responseEnd - timing.responseStart;
    this.recordMetric('responseTime', responseTime, 'ms');

    // DOM Processing
    const domProcessing = timing.domComplete - timing.domLoading;
    this.recordMetric('domProcessing', domProcessing, 'ms');
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    name: string,
    value: number,
    unit: string,
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);

    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${value}${unit}`, metadata);
    }
  }

  /**
   * Check if metric exceeds threshold
   */
  private checkThreshold(metricName: keyof PerformanceThresholds, value: number): void {
    const threshold = this.thresholds[metricName];
    if (threshold && value > threshold) {
      errorService.logWarning(`Performance threshold exceeded for ${metricName}`, {
        metric: metricName,
        value,
        threshold,
        exceeded: value - threshold,
      });
    }
  }

  /**
   * Measure component render time
   */
  measureRender(componentName: string, fn: () => void): void {
    const start = performance.now();
    fn();
    const duration = performance.now() - start;

    this.recordMetric('componentRender', duration, 'ms', {
      component: componentName,
    });

    if (duration > this.thresholds.renderTime!) {
      errorService.logWarning('Slow component render detected', {
        component: componentName,
        duration,
        threshold: this.thresholds.renderTime,
      });
    }
  }

  /**
   * Measure async operation
   */
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      
      this.recordMetric('asyncOperation', duration, 'ms', {
        operation: name,
        status: 'success',
      });

      return result;
    } catch (error) {
      const duration = performance.now() - start;
      
      this.recordMetric('asyncOperation', duration, 'ms', {
        operation: name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Mark a custom timing
   */
  mark(name: string): void {
    if (performance.mark) {
      performance.mark(name);
    }
  }

  /**
   * Measure between two marks
   */
  measure(name: string, startMark: string, endMark?: string): void {
    if (performance.measure) {
      try {
        const measure = endMark
          ? performance.measure(name, startMark, endMark)
          : performance.measure(name, startMark);
        
        this.recordMetric('customMeasure', measure.duration, 'ms', {
          measureName: name,
        });
      } catch (e) {
        console.warn('Failed to measure performance:', e);
      }
    }
  }

  /**
   * Get all metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Get metrics summary
   */
  getSummary(): Record<string, any> {
    const summary: Record<string, any> = {};
    
    // Group metrics by name
    const grouped = this.metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    // Calculate statistics for each metric
    Object.entries(grouped).forEach(([name, values]) => {
      const sorted = values.sort((a, b) => a - b);
      summary[name] = {
        count: values.length,
        min: sorted[0],
        max: sorted[sorted.length - 1],
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        median: sorted[Math.floor(sorted.length / 2)],
        p95: sorted[Math.floor(sorted.length * 0.95)],
        p99: sorted[Math.floor(sorted.length * 0.99)],
      };
    });

    return summary;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Update thresholds
   */
  setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * Cleanup observers
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// React hook for performance monitoring
export function usePerformanceMonitor(componentName: string) {
  return {
    measureRender: (fn: () => void) => performanceMonitor.measureRender(componentName, fn),
    measureAsync: <T>(name: string, fn: () => Promise<T>) => 
      performanceMonitor.measureAsync(`${componentName}.${name}`, fn),
    mark: (name: string) => performanceMonitor.mark(`${componentName}.${name}`),
    measure: (name: string, startMark: string, endMark?: string) => 
      performanceMonitor.measure(`${componentName}.${name}`, startMark, endMark),
  };
}