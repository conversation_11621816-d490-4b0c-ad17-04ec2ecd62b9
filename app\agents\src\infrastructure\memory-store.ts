import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';
import { z } from 'zod';

// Memory types for different retention periods
export enum MemoryType {
  SHORT_TERM = 'SHORT_TERM',      // Working memory (5-10 items)
  LONG_TERM = 'LONG_TERM',        // Persistent knowledge
  EPISODIC = 'EPISODIC',          // Event-based memories
  SEMANTIC = 'SEMANTIC',          // Facts and concepts
  PROCEDURAL = 'PROCEDURAL',      // How-to knowledge
}

// Memory item schema
const memoryItemSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(MemoryType),
  agentId: z.string(),
  content: z.any(),
  metadata: z.object({
    created: z.date(),
    accessed: z.date(),
    accessCount: z.number(),
    importance: z.number().min(0).max(1),
    tags: z.array(z.string()),
  }),
  embedding: z.array(z.number()).optional(), // For semantic search
});

export type MemoryItem = z.infer<typeof memoryItemSchema>;

// Memory query options
export interface MemoryQuery {
  type?: MemoryType;
  agentId?: string;
  tags?: string[];
  minImportance?: number;
  limit?: number;
  sortBy?: 'created' | 'accessed' | 'importance';
  sortOrder?: 'asc' | 'desc';
}

// Memory store for agent persistence
export class MemoryStore extends EventEmitter {
  private memories: Map<string, MemoryItem> = new Map();
  private shortTermBuffer: Map<string, MemoryItem[]> = new Map();
  private readonly persistPath: string;
  private readonly maxShortTermItems = 10;
  private saveTimer: NodeJS.Timeout | null = null;

  constructor(persistPath: string = './data/memory') {
    super();
    this.persistPath = persistPath;
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // Ensure persist directory exists
      await fs.mkdir(this.persistPath, { recursive: true });
      
      // Load existing memories
      await this.loadFromDisk();
    } catch (error) {
      console.error('Failed to initialize memory store:', error);
    }
  }

  // Store a memory item
  async store(item: Omit<MemoryItem, 'id' | 'metadata'>): Promise<MemoryItem> {
    const memory: MemoryItem = {
      ...item,
      id: `mem-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      metadata: {
        created: new Date(),
        accessed: new Date(),
        accessCount: 0,
        importance: 0.5,
        tags: [],
        ...item.metadata,
      },
    };

    // Validate memory item
    memoryItemSchema.parse(memory);

    // Handle short-term memory differently
    if (memory.type === MemoryType.SHORT_TERM) {
      await this.addToShortTerm(memory);
    } else {
      this.memories.set(memory.id, memory);
    }

    // Schedule save to disk
    this.scheduleSave();

    // Emit event
    this.emit('memory:stored', memory);

    return memory;
  }

  // Add to short-term buffer with size limit
  private async addToShortTerm(memory: MemoryItem): Promise<void> {
    const agentBuffer = this.shortTermBuffer.get(memory.agentId) || [];
    
    // Add new memory
    agentBuffer.unshift(memory);
    
    // Maintain buffer size
    if (agentBuffer.length > this.maxShortTermItems) {
      const removed = agentBuffer.splice(this.maxShortTermItems);
      
      // Move important memories to long-term
      for (const item of removed) {
        if (item.metadata.importance > 0.7 || item.metadata.accessCount > 3) {
          const longTermMemory = { ...item, type: MemoryType.LONG_TERM };
          this.memories.set(longTermMemory.id, longTermMemory);
        }
      }
    }
    
    this.shortTermBuffer.set(memory.agentId, agentBuffer);
  }

  // Retrieve memories based on query
  async retrieve(query: MemoryQuery): Promise<MemoryItem[]> {
    let results: MemoryItem[] = [];

    // Get from long-term memories
    for (const memory of this.memories.values()) {
      if (this.matchesQuery(memory, query)) {
        results.push(memory);
      }
    }

    // Get from short-term buffer
    if (!query.type || query.type === MemoryType.SHORT_TERM) {
      for (const [agentId, buffer] of this.shortTermBuffer.entries()) {
        if (!query.agentId || query.agentId === agentId) {
          results.push(...buffer.filter(m => this.matchesQuery(m, query)));
        }
      }
    }

    // Update access metadata
    const now = new Date();
    results.forEach(memory => {
      memory.metadata.accessed = now;
      memory.metadata.accessCount++;
    });

    // Sort results
    results = this.sortMemories(results, query);

    // Apply limit
    if (query.limit) {
      results = results.slice(0, query.limit);
    }

    return results;
  }

  // Check if memory matches query
  private matchesQuery(memory: MemoryItem, query: MemoryQuery): boolean {
    if (query.type && memory.type !== query.type) return false;
    if (query.agentId && memory.agentId !== query.agentId) return false;
    if (query.minImportance && memory.metadata.importance < query.minImportance) return false;
    if (query.tags && query.tags.length > 0) {
      const hasAllTags = query.tags.every(tag => memory.metadata.tags.includes(tag));
      if (!hasAllTags) return false;
    }
    return true;
  }

  // Sort memories based on query options
  private sortMemories(memories: MemoryItem[], query: MemoryQuery): MemoryItem[] {
    const sortBy = query.sortBy || 'accessed';
    const sortOrder = query.sortOrder || 'desc';

    return memories.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'created':
          aValue = a.metadata.created.getTime();
          bValue = b.metadata.created.getTime();
          break;
        case 'accessed':
          aValue = a.metadata.accessed.getTime();
          bValue = b.metadata.accessed.getTime();
          break;
        case 'importance':
          aValue = a.metadata.importance;
          bValue = b.metadata.importance;
          break;
      }

      if (sortOrder === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });
  }

  // Update memory importance
  async updateImportance(memoryId: string, importance: number): Promise<void> {
    const memory = this.memories.get(memoryId);
    if (memory) {
      memory.metadata.importance = Math.max(0, Math.min(1, importance));
      this.scheduleSave();
    }
  }

  // Add tags to memory
  async addTags(memoryId: string, tags: string[]): Promise<void> {
    const memory = this.memories.get(memoryId);
    if (memory) {
      memory.metadata.tags = [...new Set([...memory.metadata.tags, ...tags])];
      this.scheduleSave();
    }
  }

  // Delete memory
  async delete(memoryId: string): Promise<void> {
    const deleted = this.memories.delete(memoryId);
    if (deleted) {
      this.scheduleSave();
      this.emit('memory:deleted', memoryId);
    }
  }

  // Clear all memories for an agent
  async clearAgent(agentId: string): Promise<void> {
    // Clear from long-term
    for (const [id, memory] of this.memories.entries()) {
      if (memory.agentId === agentId) {
        this.memories.delete(id);
      }
    }

    // Clear short-term buffer
    this.shortTermBuffer.delete(agentId);

    this.scheduleSave();
    this.emit('memory:cleared', agentId);
  }

  // Schedule save to disk
  private scheduleSave(): void {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }

    this.saveTimer = setTimeout(() => {
      this.saveToDisk().catch(console.error);
    }, 5000); // Save after 5 seconds of inactivity
  }

  // Save memories to disk
  private async saveToDisk(): Promise<void> {
    try {
      const data = {
        memories: Array.from(this.memories.entries()),
        shortTermBuffer: Array.from(this.shortTermBuffer.entries()),
      };

      const filePath = path.join(this.persistPath, 'memories.json');
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      
      this.emit('memory:saved');
    } catch (error) {
      console.error('Failed to save memories:', error);
      this.emit('memory:save-error', error);
    }
  }

  // Load memories from disk
  private async loadFromDisk(): Promise<void> {
    try {
      const filePath = path.join(this.persistPath, 'memories.json');
      const data = await fs.readFile(filePath, 'utf-8');
      const parsed = JSON.parse(data);

      // Restore memories with date parsing
      this.memories = new Map(
        parsed.memories.map(([id, memory]: [string, any]) => [
          id,
          {
            ...memory,
            metadata: {
              ...memory.metadata,
              created: new Date(memory.metadata.created),
              accessed: new Date(memory.metadata.accessed),
            },
          },
        ])
      );

      // Restore short-term buffers
      this.shortTermBuffer = new Map(
        parsed.shortTermBuffer.map(([agentId, buffer]: [string, any[]]) => [
          agentId,
          buffer.map(item => ({
            ...item,
            metadata: {
              ...item.metadata,
              created: new Date(item.metadata.created),
              accessed: new Date(item.metadata.accessed),
            },
          })),
        ])
      );

      this.emit('memory:loaded');
    } catch (error) {
      // File might not exist on first run
      if ((error as any).code !== 'ENOENT') {
        console.error('Failed to load memories:', error);
      }
    }
  }

  // Get memory statistics
  getStats(): {
    totalMemories: number;
    byType: Record<MemoryType, number>;
    byAgent: Record<string, number>;
  } {
    const stats = {
      totalMemories: this.memories.size,
      byType: {} as Record<MemoryType, number>,
      byAgent: {} as Record<string, number>,
    };

    // Initialize type counts
    Object.values(MemoryType).forEach(type => {
      stats.byType[type] = 0;
    });

    // Count memories
    for (const memory of this.memories.values()) {
      stats.byType[memory.type]++;
      stats.byAgent[memory.agentId] = (stats.byAgent[memory.agentId] || 0) + 1;
    }

    // Count short-term memories
    for (const [agentId, buffer] of this.shortTermBuffer.entries()) {
      stats.byType[MemoryType.SHORT_TERM] += buffer.length;
      stats.byAgent[agentId] = (stats.byAgent[agentId] || 0) + buffer.length;
      stats.totalMemories += buffer.length;
    }

    return stats;
  }
}