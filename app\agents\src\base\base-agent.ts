import { EventEmitter } from 'events';
import { MessageBus, Message, MessageType, TaskRequest, TaskResponse } from '../infrastructure/message-bus';
import { MemoryStore, MemoryType, MemoryItem } from '../infrastructure/memory-store';
import { z } from 'zod';

// Agent state
export enum AgentState {
  INITIALIZING = 'INITIALIZING',
  IDLE = 'IDLE',
  PROCESSING = 'PROCESSING',
  ERROR = 'ERROR',
  SHUTDOWN = 'SHUTDOWN',
}

// Agent capability
export interface AgentCapability {
  name: string;
  description: string;
  inputSchema?: z.ZodSchema<unknown>;
  outputSchema?: z.ZodSchema<unknown>;
}

// Base agent configuration
export interface BaseAgentConfig {
  id: string;
  name: string;
  type: string;
  description: string;
  capabilities: AgentCapability[];
  memoryStore?: MemoryStore;
}

// Base class for all agents
export abstract class BaseAgent extends EventEmitter {
  protected readonly id: string;
  protected readonly name: string;
  protected readonly type: string;
  protected readonly description: string;
  protected readonly capabilities: Map<string, AgentCapability>;
  protected readonly messageBus: MessageBus;
  protected readonly memoryStore: MemoryStore;
  protected state: AgentState = AgentState.INITIALIZING;
  protected currentTasks: Map<string, TaskRequest> = new Map();

  constructor(config: BaseAgentConfig) {
    super();
    this.id = config.id;
    this.name = config.name;
    this.type = config.type;
    this.description = config.description;
    this.capabilities = new Map(config.capabilities.map(cap => [cap.name, cap]));
    
    // Get singleton message bus
    this.messageBus = MessageBus.getInstance();
    
    // Use provided memory store or create new one
    this.memoryStore = config.memoryStore || new MemoryStore(`./data/memory/${this.id}`);
    
    this.initialize();
  }

  // Initialize the agent
  private async initialize(): Promise<void> {
    try {
      // Register with message bus
      this.messageBus.registerAgent(this.id, this.type);
      
      // Subscribe to messages
      this.messageBus.subscribe(this.id, this.handleMessage.bind(this));
      this.messageBus.subscribeBroadcast(this.handleBroadcast.bind(this));
      
      // Perform agent-specific initialization
      await this.onInitialize();
      
      // Update state
      this.setState(AgentState.IDLE);
      
      // Announce readiness
      this.announceReady();
      
      // Log initialization
      await this.log('Agent initialized successfully', { level: 'info' });
    } catch (error) {
      this.setState(AgentState.ERROR);
      await this.log('Failed to initialize agent', { level: 'error', error });
      throw error;
    }
  }

  // Abstract method for agent-specific initialization
  protected abstract onInitialize(): Promise<void>;

  // Handle incoming messages
  protected async handleMessage(message: Message): Promise<void> {
    try {
      await this.log('Received message', { level: 'debug', message });
      
      switch (message.type) {
        case MessageType.TASK_REQUEST:
          await this.handleTaskRequest(message as TaskRequest);
          break;
        case MessageType.TASK_STATUS:
          // Handle status updates from other agents
          break;
        case MessageType.MEMORY_UPDATE:
          // Handle memory updates
          break;
        default:
          await this.log('Unknown message type', { level: 'warn', message });
      }
    } catch (error) {
      await this.log('Error handling message', { level: 'error', error, message });
    }
  }

  // Handle broadcast messages
  protected async handleBroadcast(message: Message): Promise<void> {
    // Override in subclasses to handle broadcasts
  }

  // Handle task requests
  protected async handleTaskRequest(request: TaskRequest): Promise<void> {
    const { taskType, data, priority } = request.payload;
    
    // Check if we can handle this task
    if (!this.capabilities.has(taskType)) {
      await this.sendTaskResponse(request, false, null, 'Capability not supported');
      return;
    }
    
    // Check if we're already at capacity
    if (this.state === AgentState.PROCESSING && priority !== 'CRITICAL') {
      await this.sendTaskResponse(request, false, null, 'Agent is busy');
      return;
    }
    
    // Store task
    this.currentTasks.set(request.id, request);
    
    // Update state
    this.setState(AgentState.PROCESSING);
    
    try {
      // Validate input if schema is defined
      const capability = this.capabilities.get(taskType)!;
      if (capability.inputSchema) {
        capability.inputSchema.parse(data);
      }
      
      // Process the task
      const result = await this.processTask(taskType, data);
      
      // Validate output if schema is defined
      if (capability.outputSchema) {
        capability.outputSchema.parse(result);
      }
      
      // Send success response
      await this.sendTaskResponse(request, true, result);
      
      // Store in episodic memory
      await this.memoryStore.store({
        type: MemoryType.EPISODIC,
        agentId: this.id,
        content: {
          task: taskType,
          request: data,
          result: result,
          success: true,
        },
        metadata: {
          tags: ['task', taskType],
          importance: priority === 'CRITICAL' ? 1 : priority === 'HIGH' ? 0.8 : 0.5,
        },
      });
    } catch (error) {
      await this.sendTaskResponse(request, false, null, (error as Error).message);
      
      // Store failure in memory
      await this.memoryStore.store({
        type: MemoryType.EPISODIC,
        agentId: this.id,
        content: {
          task: taskType,
          request: data,
          error: (error as Error).message,
          success: false,
        },
        metadata: {
          tags: ['task', taskType, 'error'],
          importance: 0.9, // Failures are important to remember
        },
      });
    } finally {
      // Remove task and update state
      this.currentTasks.delete(request.id);
      if (this.currentTasks.size === 0) {
        this.setState(AgentState.IDLE);
      }
    }
  }

  // Abstract method for processing tasks
  protected abstract processTask(taskType: string, data: unknown): Promise<unknown>;

  // Send task response
  protected async sendTaskResponse(
    request: TaskRequest,
    success: boolean,
    result: unknown,
    error?: string
  ): Promise<void> {
    const response = MessageBus.createTaskResponse(
      this.id,
      request.from,
      request.id,
      success,
      result,
      error
    );
    
    await this.messageBus.send(response);
  }

  // Send a task request to another agent
  protected async requestTask(
    targetAgent: string,
    taskType: string,
    data: unknown,
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM'
  ): Promise<unknown> {
    return new Promise((resolve, reject) => {
      const request = MessageBus.createTaskRequest(
        this.id,
        targetAgent,
        taskType,
        data,
        priority
      );
      
      // Set up response handler
      const responseHandler = (message: Message) => {
        if (
          message.type === MessageType.TASK_RESPONSE &&
          message.correlationId === request.id
        ) {
          const response = message as TaskResponse;
          this.messageBus.unsubscribe(this.id, responseHandler);
          
          if (response.payload.success) {
            resolve(response.payload.result);
          } else {
            reject(new Error(response.payload.error || 'Task failed'));
          }
        }
      };
      
      // Subscribe to responses
      this.messageBus.subscribe(this.id, responseHandler);
      
      // Send request
      this.messageBus.send(request).catch(reject);
      
      // Timeout after 30 seconds
      setTimeout(() => {
        this.messageBus.unsubscribe(this.id, responseHandler);
        reject(new Error('Task request timeout'));
      }, 30000);
    });
  }

  // Update agent state
  protected setState(state: AgentState): void {
    const oldState = this.state;
    this.state = state;
    this.messageBus.updateAgentStatus(this.id, state);
    this.emit('state:changed', { oldState, newState: state });
  }

  // Announce agent readiness
  private announceReady(): void {
    const message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: MessageType.AGENT_READY,
      from: this.id,
      timestamp: new Date(),
      payload: {
        agentType: this.type,
        capabilities: Array.from(this.capabilities.keys()),
        status: this.state === AgentState.IDLE ? 'IDLE' : 'BUSY',
      },
    };
    
    this.messageBus.send(message);
  }

  // Log agent activity
  protected async log(
    message: string,
    metadata: { level: 'debug' | 'info' | 'warn' | 'error'; [key: string]: unknown }
  ): Promise<void> {
    const logEntry = {
      timestamp: new Date(),
      agentId: this.id,
      agentType: this.type,
      message,
      ...metadata,
    };
    
    console.log(`[${this.type}:${this.id}] ${message}`, metadata);
    
    // Store important logs in memory
    if (metadata.level === 'error' || metadata.level === 'warn') {
      await this.memoryStore.store({
        type: MemoryType.EPISODIC,
        agentId: this.id,
        content: logEntry,
        metadata: {
          tags: ['log', metadata.level],
          importance: metadata.level === 'error' ? 0.9 : 0.6,
        },
      });
    }
  }

  // Store knowledge in long-term memory
  protected async storeKnowledge(
    content: unknown,
    tags: string[],
    importance: number = 0.5
  ): Promise<MemoryItem> {
    return this.memoryStore.store({
      type: MemoryType.SEMANTIC,
      agentId: this.id,
      content,
      metadata: {
        tags,
        importance,
      },
    });
  }

  // Retrieve knowledge from memory
  protected async retrieveKnowledge(
    tags?: string[],
    limit?: number
  ): Promise<MemoryItem[]> {
    return this.memoryStore.retrieve({
      type: MemoryType.SEMANTIC,
      agentId: this.id,
      tags,
      limit,
      sortBy: 'importance',
      sortOrder: 'desc',
    });
  }

  // Shutdown the agent
  async shutdown(): Promise<void> {
    this.setState(AgentState.SHUTDOWN);
    
    // Unregister from message bus
    this.messageBus.unregisterAgent(this.id);
    
    // Clean up resources
    this.removeAllListeners();
    
    await this.log('Agent shutdown complete', { level: 'info' });
  }

  // Get agent information
  getInfo(): {
    id: string;
    name: string;
    type: string;
    description: string;
    state: AgentState;
    capabilities: string[];
  } {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      description: this.description,
      state: this.state,
      capabilities: Array.from(this.capabilities.keys()),
    };
  }
}