import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';
import { Panel } from '../../types/electrical';
import { PanelQRScanner } from '../../components/panels/PanelQRScanner';

interface Props {
  navigation: NavigationProp<any>;
}

export const PanelsScreen: React.FC<Props> = ({ navigation }) => {
  const [showScanner, setShowScanner] = useState(false);
  const [panels, setPanels] = useState<Panel[]>([]);

  const handlePanelFound = (panel: Panel) => {
    setShowScanner(false);
    navigation.navigate('PanelDetail', { panel });
  };

  const renderPanelItem = ({ item }: { item: Panel }) => {
    const loadPercentage = Math.round((item.totalLoad / (item.amperage * item.voltage)) * 100);
    const loadColor = loadPercentage > 80 ? '#f44336' : loadPercentage > 60 ? '#FF9800' : '#4CAF50';

    return (
      <TouchableOpacity
        style={styles.panelCard}
        onPress={() => navigation.navigate('PanelDetail', { panel: item })}
      >
        <View style={styles.panelHeader}>
          <View>
            <Text style={styles.panelName}>{item.name}</Text>
            <Text style={styles.panelLocation}>{item.location}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: item.status === 'active' ? '#4CAF50' : '#FF9800' }]}>
            <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
          </View>
        </View>

        <View style={styles.panelSpecs}>
          <Text style={styles.specText}>{item.voltage}V • {item.phase}PH • {item.amperage}A</Text>
          <Text style={styles.specText}>{item.manufacturer} {item.model}</Text>
        </View>

        <View style={styles.loadSection}>
          <View style={styles.loadBar}>
            <View style={[styles.loadFill, { width: `${loadPercentage}%`, backgroundColor: loadColor }]} />
          </View>
          <Text style={styles.loadText}>Load: {loadPercentage}%</Text>
        </View>

        <View style={styles.circuitInfo}>
          <Text style={styles.circuitText}>
            {item.circuits.length} circuits • {item.spaces} spaces
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Panel Management</Text>
        <TouchableOpacity
          style={styles.scanButton}
          onPress={() => setShowScanner(true)}
        >
          <Text style={styles.scanButtonText}>📷 Scan QR</Text>
        </TouchableOpacity>
      </View>

      {panels.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyIcon}>📊</Text>
          <Text style={styles.emptyTitle}>No Panels Found</Text>
          <Text style={styles.emptyText}>
            Scan a panel QR code or select a project to view panels
          </Text>
          <TouchableOpacity
            style={styles.scanEmptyButton}
            onPress={() => setShowScanner(true)}
          >
            <Text style={styles.scanEmptyButtonText}>Scan Panel QR Code</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={panels}
          renderItem={renderPanelItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
        />
      )}

      <Modal
        visible={showScanner}
        animationType="slide"
        onRequestClose={() => setShowScanner(false)}
      >
        <PanelQRScanner
          onPanelFound={handlePanelFound}
          onClose={() => setShowScanner(false)}
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  scanButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  scanButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  listContent: {
    padding: 15,
  },
  panelCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  panelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  panelName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  panelLocation: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  panelSpecs: {
    marginBottom: 15,
  },
  specText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  loadSection: {
    marginBottom: 10,
  },
  loadBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 5,
  },
  loadFill: {
    height: 8,
    borderRadius: 4,
  },
  loadText: {
    fontSize: 12,
    color: '#666',
  },
  circuitInfo: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 10,
  },
  circuitText: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  scanEmptyButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
  },
  scanEmptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});