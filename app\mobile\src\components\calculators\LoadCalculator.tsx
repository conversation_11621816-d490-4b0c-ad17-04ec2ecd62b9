import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { LoadCalculation, DwellingLoad, AdditionalLoad } from '../../types/electrical';
import { CalculationService } from '../../services/calculationService';
import { DWELLING_LOADS } from '../../constants/electrical';

export const LoadCalculator: React.FC = () => {
  const [calculationType, setCalculationType] = useState<'dwelling' | 'commercial'>('dwelling');
  const [dwellingData, setDwellingData] = useState<DwellingLoad>({
    squareFootage: 0,
    smallApplianceCircuits: 2,
    laundryCircuit: true,
    electricRange: 0,
    electricDryer: 0,
    electricWaterHeater: 0,
    airConditioner: 0,
    heatPump: 0,
    electricHeat: 0,
    additionalLoads: [],
  });
  const [result, setResult] = useState<number | null>(null);

  const handleCalculate = () => {
    if (dwellingData.squareFootage <= 0) {
      Alert.alert('Error', 'Please enter valid square footage');
      return;
    }

    const totalLoad = CalculationService.calculateDwellingLoad(dwellingData);
    setResult(totalLoad);

    // Save calculation for offline access
    const calculation: LoadCalculation = {
      dwelling: dwellingData,
      totalLoad,
      demandFactor: 1,
      calculatedLoad: totalLoad,
      necReference: 'NEC 220',
      timestamp: new Date(),
    };
    CalculationService.saveCalculation('load', calculation);
  };

  const addAdditionalLoad = () => {
    setDwellingData({
      ...dwellingData,
      additionalLoads: [
        ...(dwellingData.additionalLoads || []),
        { description: '', watts: 0, quantity: 1 },
      ],
    });
  };

  const updateAdditionalLoad = (index: number, field: string, value: any) => {
    const loads = [...(dwellingData.additionalLoads || [])];
    loads[index] = { ...loads[index], [field]: value };
    setDwellingData({ ...dwellingData, additionalLoads: loads });
  };

  const removeAdditionalLoad = (index: number) => {
    const loads = [...(dwellingData.additionalLoads || [])];
    loads.splice(index, 1);
    setDwellingData({ ...dwellingData, additionalLoads: loads });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Load Calculator</Text>
        <Text style={styles.subtitle}>NEC 220 Compliant</Text>
      </View>

      <View style={styles.typeSelector}>
        <TouchableOpacity
          style={[styles.typeButton, calculationType === 'dwelling' && styles.typeButtonActive]}
          onPress={() => setCalculationType('dwelling')}
        >
          <Text style={[styles.typeButtonText, calculationType === 'dwelling' && styles.typeButtonTextActive]}>
            Dwelling Unit
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.typeButton, calculationType === 'commercial' && styles.typeButtonActive]}
          onPress={() => setCalculationType('commercial')}
        >
          <Text style={[styles.typeButtonText, calculationType === 'commercial' && styles.typeButtonTextActive]}>
            Commercial
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>General Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Square Footage</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.squareFootage.toString()}
            onChangeText={(text) => setDwellingData({ ...dwellingData, squareFootage: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="Enter square footage"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Small Appliance Circuits (1500W each)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.smallApplianceCircuits.toString()}
            onChangeText={(text) => setDwellingData({ ...dwellingData, smallApplianceCircuits: parseInt(text) || 0 })}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Laundry Circuit (1500W)</Text>
          <Switch
            value={dwellingData.laundryCircuit}
            onValueChange={(value) => setDwellingData({ ...dwellingData, laundryCircuit: value })}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={dwellingData.laundryCircuit ? '#2196F3' : '#f4f3f4'}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Fixed Appliances</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Electric Range (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.electricRange?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, electricRange: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Electric Dryer (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.electricDryer?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, electricDryer: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Electric Water Heater (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.electricWaterHeater?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, electricWaterHeater: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>HVAC (Largest Load)</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Air Conditioner (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.airConditioner?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, airConditioner: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Heat Pump (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.heatPump?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, heatPump: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Electric Heat (W)</Text>
          <TextInput
            style={styles.input}
            value={dwellingData.electricHeat?.toString() || ''}
            onChangeText={(text) => setDwellingData({ ...dwellingData, electricHeat: parseInt(text) || 0 })}
            keyboardType="numeric"
            placeholder="0"
          />
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Additional Loads</Text>
          <TouchableOpacity style={styles.addButton} onPress={addAdditionalLoad}>
            <Text style={styles.addButtonText}>+ Add Load</Text>
          </TouchableOpacity>
        </View>

        {dwellingData.additionalLoads?.map((load, index) => (
          <View key={index} style={styles.additionalLoad}>
            <TextInput
              style={[styles.input, styles.descriptionInput]}
              value={load.description}
              onChangeText={(text) => updateAdditionalLoad(index, 'description', text)}
              placeholder="Description"
            />
            <TextInput
              style={[styles.input, styles.wattInput]}
              value={load.watts.toString()}
              onChangeText={(text) => updateAdditionalLoad(index, 'watts', parseInt(text) || 0)}
              keyboardType="numeric"
              placeholder="Watts"
            />
            <TextInput
              style={[styles.input, styles.quantityInput]}
              value={load.quantity.toString()}
              onChangeText={(text) => updateAdditionalLoad(index, 'quantity', parseInt(text) || 1)}
              keyboardType="numeric"
              placeholder="Qty"
            />
            <TouchableOpacity onPress={() => removeAdditionalLoad(index)} style={styles.removeButton}>
              <Text style={styles.removeButtonText}>X</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      <TouchableOpacity style={styles.calculateButton} onPress={handleCalculate}>
        <Text style={styles.calculateButtonText}>Calculate Load</Text>
      </TouchableOpacity>

      {result !== null && (
        <View style={styles.resultSection}>
          <Text style={styles.resultTitle}>Calculated Load</Text>
          <Text style={styles.resultValue}>{result.toLocaleString()} W</Text>
          <Text style={styles.resultSubtext}>
            {Math.round(result / 240)} A @ 240V
          </Text>
          <Text style={styles.necReference}>Per NEC Article 220</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  typeSelector: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  typeButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginHorizontal: 5,
    backgroundColor: '#f0f0f0',
  },
  typeButtonActive: {
    backgroundColor: '#2196F3',
  },
  typeButtonText: {
    fontSize: 16,
    color: '#666',
  },
  typeButtonTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  additionalLoad: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  descriptionInput: {
    flex: 3,
    marginRight: 5,
  },
  wattInput: {
    flex: 1,
    marginRight: 5,
  },
  quantityInput: {
    flex: 0.5,
    marginRight: 5,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  removeButton: {
    backgroundColor: '#f44336',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  calculateButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 18,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  calculateButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultSection: {
    backgroundColor: '#4CAF50',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 10,
  },
  resultValue: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
  },
  resultSubtext: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 5,
  },
  necReference: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 10,
  },
});