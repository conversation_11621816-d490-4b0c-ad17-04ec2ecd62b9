import { Worker } from 'bullmq';
import { getRedis } from '../services/redis';
import { materialPriceWorker } from './material-price';
import { calculationWorker } from './calculation';
import { logger } from '../index';

export function startWorkers(): void {
  const redis = getRedis();
  if (!redis) {
    logger.warn('Workers not started - Redis not available');
    return;
  }
  
  // Material price update worker
  const priceWorker = new Worker(
    'material-prices',
    materialPriceWorker,
    {
      connection: redis,
      concurrency: 5,
      removeOnComplete: { count: 100 },
      removeOnFail: { count: 50 }
    }
  );
  
  priceWorker.on('completed', (job) => {
    logger.info(`Material price job ${job.id} completed`);
  });
  
  priceWorker.on('failed', (job, err) => {
    logger.error(`Material price job ${job?.id} failed:`, err);
  });
  
  // Complex calculation worker
  const calcWorker = new Worker(
    'calculations',
    calculationWorker,
    {
      connection: redis,
      concurrency: 3,
      removeOnComplete: { count: 50 },
      removeOnFail: { count: 25 }
    }
  );
  
  calcWorker.on('completed', (job) => {
    logger.info(`Calculation job ${job.id} completed`);
  });
  
  calcWorker.on('failed', (job, err) => {
    logger.error(`Calculation job ${job?.id} failed:`, err);
  });
  
  logger.info('Background workers started');
}