# Migration Fix Guide

## Problem
The migration `20240115_performance_indexes` is failing to apply. This migration contains `CREATE INDEX IF NOT EXISTS` statements which should be safe to retry.

## Current State
- Database file exists at: `prisma/dev.db`
- Migration contains performance optimization indexes
- The subsequent migration `20240116_security_hardening` may also have issues (contains invalid SQLite syntax)

## Solution Options

### Option 1: Check Current Status (Recommended First Step)
```bash
cd /mnt/c/Projects/electrical/app/backend
node check-migration-status.js
```

This will show you:
- If the database exists
- Current migration status
- What migrations have been applied

### Option 2: Safe Fix - Apply Indexes Manually
If the migration is partially applied or stuck:

1. First, install sqlite3 if not already installed:
   ```bash
   npm install sqlite3
   ```

2. Apply the indexes manually:
   ```bash
   node apply-indexes-manually.js
   ```

3. Mark the migration as resolved:
   ```bash
   npx prisma migrate resolve --applied "20240115_performance_indexes"
   ```

### Option 3: Development Reset (⚠️ DESTRUCTIVE)
**Only use this in development when data loss is acceptable:**

```bash
# This will drop the database and reapply all migrations
npx prisma migrate reset --force
```

### Option 4: Use Prisma Deploy
Try to apply migrations using the deploy command:
```bash
npx prisma migrate deploy
```

## Fixing the Second Migration
The `20240116_security_hardening` migration has SQLite syntax errors. The `INDEX` statements inside `CREATE TABLE` are not valid in SQLite.

To fix this:

1. Create a corrected version:
   ```bash
   cp prisma/migrations/20240116_security_hardening/migration.sql prisma/migrations/20240116_security_hardening/migration.sql.backup
   ```

2. Edit the migration file to move INDEX statements outside of CREATE TABLE statements.

3. Apply the corrected migration.

## Recommended Approach

1. **First**, run the status check:
   ```bash
   node check-migration-status.js
   ```

2. **If indexes are missing**, apply them manually:
   ```bash
   node apply-indexes-manually.js
   ```

3. **Mark the migration as applied**:
   ```bash
   npx prisma migrate resolve --applied "20240115_performance_indexes"
   ```

4. **Verify the fix**:
   ```bash
   npx prisma migrate status
   ```

## Prevention
- Always test migrations in development before production
- Use `npx prisma migrate dev` for development
- Use `npx prisma migrate deploy` for production
- Keep migrations simple and atomic
- Test SQLite-specific syntax separately from PostgreSQL