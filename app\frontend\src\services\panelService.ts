import { Panel, Circuit, PanelLoadCalculation } from '@electrical/shared';
import { api } from './api';

interface PanelSchedule {
  panel: Panel;
  circuits: Circuit[];
  loadCalculation: PanelLoadCalculation | null;
  summary: {
    totalConnectedLoad: number;
    totalDemandLoad: number;
    loadPercentage: number;
    phaseImbalance: number;
    spacesUsed: number;
    spacesAvailable: number;
  };
}

export const panelService = {
  // Panel operations
  async createPanel(data: Partial<Panel>): Promise<Panel> {
    const response = await api.post('/panels', data);
    return response.data;
  },

  async getPanelsByProject(projectId: string): Promise<Panel[]> {
    const response = await api.get(`/panels/project/${projectId}`);
    return response.data;
  },

  async getPanel(panelId: string): Promise<Panel> {
    const response = await api.get(`/panels/${panelId}`);
    return response.data;
  },

  async updatePanel(panelId: string, data: Partial<Panel>): Promise<Panel> {
    const response = await api.put(`/panels/${panelId}`, data);
    return response.data;
  },

  async deletePanel(panelId: string): Promise<void> {
    await api.delete(`/panels/${panelId}`);
  },

  async calculatePanelLoad(panelId: string): Promise<PanelLoadCalculation> {
    const response = await api.post(`/panels/${panelId}/calculate-load`);
    return response.data;
  },

  async getPanelSchedule(panelId: string): Promise<PanelSchedule> {
    const response = await api.get(`/panels/${panelId}/schedule`);
    return response.data;
  },

  async balancePanel(panelId: string): Promise<Panel> {
    const response = await api.post(`/panels/${panelId}/balance`);
    return response.data;
  },

  // Circuit operations
  async createCircuit(panelId: string, data: Partial<Circuit>): Promise<Circuit> {
    const response = await api.post(`/panels/${panelId}/circuits`, data);
    return response.data;
  },

  async getCircuitsByPanel(panelId: string): Promise<Circuit[]> {
    const response = await api.get(`/panels/${panelId}/circuits`);
    return response.data;
  },

  async updateCircuit(circuitId: string, data: Partial<Circuit>): Promise<Circuit> {
    const response = await api.put(`/panels/circuits/${circuitId}`, data);
    return response.data;
  },

  async deleteCircuit(circuitId: string): Promise<void> {
    await api.delete(`/panels/circuits/${circuitId}`);
  },

  async moveCircuit(
    circuitId: string, 
    targetPanelId: string, 
    targetCircuitNumber: number
  ): Promise<Circuit> {
    const response = await api.put(`/panels/circuits/${circuitId}/move`, {
      target_panel_id: targetPanelId,
      target_circuit_number: targetCircuitNumber,
    });
    return response.data;
  },

  async bulkCreateCircuits(
    panelId: string, 
    circuits: Partial<Circuit>[]
  ): Promise<Circuit[]> {
    const response = await api.post(`/panels/${panelId}/circuits/bulk`, { circuits });
    return response.data;
  },
};