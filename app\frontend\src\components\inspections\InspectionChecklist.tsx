import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  CheckCircle, XCircle, AlertCircle, Camera, Ruler, 
  FileText, Download, ChevronDown, ChevronUp, Save,
  Clock, User, Building, Phone, Mail
} from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Alert } from '../ui/alert';
import inspectionService, { 
  InspectionChecklist as InspectionChecklistType, 
  InspectionChecklistItem,
  UpdateChecklistItemDto
} from '../../services/inspectionService';
import { format } from 'date-fns';

interface GroupedItems {
  [category: string]: InspectionChecklistItem[];
}

export const InspectionChecklist: React.FC = () => {
  const { projectId, inspectionId } = useParams<{ projectId: string; inspectionId: string }>();
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [inspection, setInspection] = useState<InspectionChecklistType | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);

  useEffect(() => {
    if (inspectionId) {
      loadInspection();
    }
  }, [inspectionId]);

  const loadInspection = async () => {
    if (!inspectionId) return;
    
    try {
      setLoading(true);
      const data = await inspectionService.getInspection(inspectionId);
      setInspection(data);
      
      // Expand all categories by default
      const categories = new Set(data.checklist_items.map(item => item.category));
      setExpandedCategories(categories);
    } catch (err) {
      setError('Failed to load inspection');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const groupItemsByCategory = (items: InspectionChecklistItem[]): GroupedItems => {
    return items.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    }, {} as GroupedItems);
  };

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const handleItemStatusChange = async (itemId: string, status: string) => {
    if (!inspection) return;

    setSaving(true);
    try {
      const updatedItem = await inspectionService.updateChecklistItem(itemId, { status });
      
      // Update local state
      setInspection({
        ...inspection,
        checklist_items: inspection.checklist_items.map(item =>
          item.id === itemId ? { ...item, ...updatedItem } : item
        )
      });
    } catch (err) {
      console.error('Failed to update item status:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleItemUpdate = async (itemId: string, data: UpdateChecklistItemDto) => {
    if (!inspection) return;

    setSaving(true);
    try {
      const updatedItem = await inspectionService.updateChecklistItem(itemId, data);
      
      // Update local state
      setInspection({
        ...inspection,
        checklist_items: inspection.checklist_items.map(item =>
          item.id === itemId ? { ...item, ...updatedItem } : item
        )
      });
    } catch (err) {
      console.error('Failed to update item:', err);
    } finally {
      setSaving(false);
    }
  };

  const handlePhotoUpload = async (file: File) => {
    if (!inspection || !selectedItem) return;

    setUploadingPhoto(true);
    try {
      const photo = await inspectionService.uploadPhoto(inspection.id, file, {
        itemId: selectedItem,
        photoType: 'DETAIL'
      });
      
      // Update photo count for the item
      const item = inspection.checklist_items.find(i => i.id === selectedItem);
      if (item) {
        await handleItemUpdate(selectedItem, {
          photosAttached: item.photos_attached + 1
        });
      }
      
      // Reload inspection to get updated photo list
      await loadInspection();
    } catch (err) {
      console.error('Failed to upload photo:', err);
    } finally {
      setUploadingPhoto(false);
      setSelectedItem(null);
    }
  };

  const handleGenerateReport = async () => {
    if (!inspection) return;
    
    try {
      const result = await inspectionService.generateReport(inspection.id);
      window.open(result.reportPath, '_blank');
    } catch (err) {
      console.error('Failed to generate report:', err);
    }
  };

  const getItemIcon = (status: string) => {
    switch (status) {
      case 'PASS':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'FAIL':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'NA':
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
      case 'CORRECTED':
        return <CheckCircle className="h-5 w-5 text-blue-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getCategoryProgress = (items: InspectionChecklistItem[]) => {
    const total = items.length;
    const completed = items.filter(i => i.status !== 'NOT_INSPECTED').length;
    return { total, completed, percentage: total > 0 ? Math.round((completed / total) * 100) : 0 };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!inspection) {
    return (
      <Alert variant="destructive">
        Inspection not found
      </Alert>
    );
  }

  const groupedItems = groupItemsByCategory(inspection.checklist_items);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">
            {inspection.inspection_type.replace(/_/g, ' ')} Inspection
          </h2>
          <p className="text-gray-600">
            {inspection.project?.customer?.name} - {inspection.project?.name}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleGenerateReport} variant="secondary">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button onClick={() => navigate(`/projects/${projectId}/inspections`)}>
            Back to List
          </Button>
        </div>
      </div>

      {/* Inspection Info */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold mb-3">Inspection Details</h3>
            <div className="space-y-2 text-sm">
              <p><strong>Status:</strong> <Badge>{inspection.status}</Badge></p>
              <p><strong>Type:</strong> {inspection.inspection_type.replace(/_/g, ' ')}</p>
              {inspection.inspection_subtype && (
                <p><strong>Subtype:</strong> {inspection.inspection_subtype.replace(/_/g, ' ')}</p>
              )}
              <p><strong>Inspection #:</strong> {inspection.inspection_number}</p>
              {inspection.scheduled_date && (
                <p><strong>Scheduled:</strong> {format(new Date(inspection.scheduled_date), 'MMM d, yyyy h:mm a')}</p>
              )}
              {inspection.inspection_date && (
                <p><strong>Inspected:</strong> {format(new Date(inspection.inspection_date), 'MMM d, yyyy')}</p>
              )}
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-3">Inspector Information</h3>
            <div className="space-y-2 text-sm">
              {inspection.inspector_name ? (
                <>
                  <p className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {inspection.inspector_name}
                  </p>
                  {inspection.inspector_company && (
                    <p className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      {inspection.inspector_company}
                    </p>
                  )}
                  {inspection.inspector_phone && (
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {inspection.inspector_phone}
                    </p>
                  )}
                  {inspection.inspector_email && (
                    <p className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      {inspection.inspector_email}
                    </p>
                  )}
                </>
              ) : (
                <p className="text-gray-500">No inspector assigned yet</p>
              )}
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-3">Location & Access</h3>
            <div className="space-y-2 text-sm">
              {inspection.location_details && (
                <p><strong>Location:</strong> {inspection.location_details}</p>
              )}
              {inspection.access_instructions && (
                <p><strong>Access:</strong> {inspection.access_instructions}</p>
              )}
              {inspection.qr_code_id && (
                <div className="mt-3">
                  <Button size="sm" variant="secondary">
                    <Download className="h-4 w-4 mr-2" />
                    Download QR Code
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Checklist Items */}
      <div className="space-y-4">
        {Object.entries(groupedItems).map(([category, items]) => {
          const progress = getCategoryProgress(items);
          const isExpanded = expandedCategories.has(category);

          return (
            <Card key={category} className="overflow-hidden">
              <div
                className="p-4 bg-gray-50 cursor-pointer flex items-center justify-between"
                onClick={() => toggleCategory(category)}
              >
                <div className="flex items-center gap-3">
                  <h3 className="font-semibold">{category.replace(/_/g, ' ')}</h3>
                  <Badge variant="secondary">
                    {progress.completed}/{progress.total}
                  </Badge>
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                </div>
                {isExpanded ? <ChevronUp /> : <ChevronDown />}
              </div>

              {isExpanded && (
                <div className="divide-y">
                  {items.map((item) => (
                    <div key={item.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start gap-4">
                        {getItemIcon(item.status)}
                        
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="font-medium">{item.description}</p>
                              {item.nec_reference && (
                                <p className="text-sm text-gray-600 mt-1">
                                  NEC {item.nec_reference}
                                </p>
                              )}
                              {item.inspection_criteria && (
                                <p className="text-sm text-gray-500 mt-2">
                                  {item.inspection_criteria}
                                </p>
                              )}
                            </div>
                            
                            <div className="flex gap-2 ml-4">
                              {item.photo_required && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    setSelectedItem(item.id);
                                    fileInputRef.current?.click();
                                  }}
                                  disabled={uploadingPhoto}
                                >
                                  <Camera className="h-4 w-4" />
                                  {item.photos_attached > 0 && (
                                    <span className="ml-1">{item.photos_attached}</span>
                                  )}
                                </Button>
                              )}
                              {item.measurement_required && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {/* TODO: Open measurement modal */}}
                                >
                                  <Ruler className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>

                          {/* Status Buttons */}
                          <div className="flex gap-2 mt-3">
                            <Button
                              size="sm"
                              variant={item.status === 'PASS' ? 'default' : 'outline'}
                              className={item.status === 'PASS' ? 'bg-green-600 hover:bg-green-700' : ''}
                              onClick={() => handleItemStatusChange(item.id, 'PASS')}
                              disabled={saving}
                            >
                              Pass
                            </Button>
                            <Button
                              size="sm"
                              variant={item.status === 'FAIL' ? 'default' : 'outline'}
                              className={item.status === 'FAIL' ? 'bg-red-600 hover:bg-red-700' : ''}
                              onClick={() => handleItemStatusChange(item.id, 'FAIL')}
                              disabled={saving}
                            >
                              Fail
                            </Button>
                            <Button
                              size="sm"
                              variant={item.status === 'NA' ? 'default' : 'outline'}
                              className={item.status === 'NA' ? 'bg-gray-600 hover:bg-gray-700' : ''}
                              onClick={() => handleItemStatusChange(item.id, 'NA')}
                              disabled={saving}
                            >
                              N/A
                            </Button>
                          </div>

                          {/* Notes and Failure Reason */}
                          {item.status === 'FAIL' && (
                            <div className="mt-3">
                              <Label htmlFor={`failure-${item.id}`}>Failure Reason</Label>
                              <Input
                                id={`failure-${item.id}`}
                                placeholder="Describe why this item failed"
                                value={item.failure_reason || ''}
                                onChange={(e) => handleItemUpdate(item.id, { failureReason: e.target.value })}
                                onBlur={() => setSaving(false)}
                              />
                            </div>
                          )}

                          {(item.status !== 'NOT_INSPECTED') && (
                            <div className="mt-3">
                              <Label htmlFor={`notes-${item.id}`}>Inspector Notes</Label>
                              <Input
                                id={`notes-${item.id}`}
                                placeholder="Add notes (optional)"
                                value={item.inspector_notes || ''}
                                onChange={(e) => handleItemUpdate(item.id, { inspectorNotes: e.target.value })}
                                onBlur={() => setSaving(false)}
                              />
                            </div>
                          )}

                          {/* Correction Info */}
                          {item.correction_required && (
                            <Alert className="mt-3">
                              <AlertCircle className="h-4 w-4" />
                              <div>
                                <p className="font-semibold">Correction Required</p>
                                <p className="text-sm">{item.correction_description}</p>
                                {item.correction_deadline && (
                                  <p className="text-sm text-gray-600">
                                    Due: {format(new Date(item.correction_deadline), 'MMM d, yyyy')}
                                  </p>
                                )}
                              </div>
                            </Alert>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Hidden file input for photo upload */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handlePhotoUpload(file);
          }
        }}
      />

      {saving && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          Saving...
        </div>
      )}
    </div>
  );
};