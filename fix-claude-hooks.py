#!/usr/bin/env python3
"""
This script shows how to fix the Claude Code hooks syntax errors.
The issue is that try/except blocks cannot be written inline.
"""

# The BROKEN version (what's causing the errors):
# try: existing = json.load(open(log_file)) if os.path.exists(log_file) else [] except: pass

# FIXED version 1 - Remove try/except and rely on os.path.exists check:
fixed_session_hook = """import json, datetime, os; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown'), 'duration': os.environ.get('CLAUDE_SESSION_DURATION', 'unknown'), 'tools_used': os.environ.get('CLAUDE_TOOLS_USED', '').split(',') if os.environ.get('CLAUDE_TOOLS_USED') else []}; log_file = os.path.expanduser('~/.claude/session-stats.json'); os.makedirs(os.path.dirname(log_file), exist_ok=True); existing = json.load(open(log_file, 'r')) if os.path.exists(log_file) else []; existing.append(stats); json.dump(existing[-100:], open(log_file, 'w'), indent=2)"""

fixed_project_hook = """import json, datetime, os, glob; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'project': os.path.basename(os.getcwd()), 'files_modified': len(glob.glob('.claude/backups/*.bak')), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown')}; report_file = '.claude/project-stats.json'; os.makedirs(os.path.dirname(report_file), exist_ok=True); existing = json.load(open(report_file, 'r')) if os.path.exists(report_file) else []; existing.append(stats); json.dump(existing[-50:], open(report_file, 'w'), indent=2)"""

# FIXED version 2 - Using context managers (better practice):
fixed_session_hook_v2 = """import json, datetime, os; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown'), 'duration': os.environ.get('CLAUDE_SESSION_DURATION', 'unknown'), 'tools_used': os.environ.get('CLAUDE_TOOLS_USED', '').split(',') if os.environ.get('CLAUDE_TOOLS_USED') else []}; log_file = os.path.expanduser('~/.claude/session-stats.json'); os.makedirs(os.path.dirname(log_file), exist_ok=True); existing = []; 
if os.path.exists(log_file):
    with open(log_file, 'r') as f:
        existing = json.load(f)
existing.append(stats)
with open(log_file, 'w') as f:
    json.dump(existing[-100:], f, indent=2)"""

# The fix is to:
# 1. Remove the invalid try/except syntax
# 2. Add mode parameter to open() calls
# 3. Ensure os.makedirs is called to create directory if needed

print("Fixed hooks created. The key changes are:")
print("1. Removed invalid 'try: ... except:' inline syntax")
print("2. Added 'r' mode to open() for reading: open(log_file, 'r')")
print("3. Added os.makedirs to ensure directory exists")
print("\nTo apply these fixes, update your Claude Code global settings with the fixed hooks.")