import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useOfflineData } from '@offline/hooks/useOfflineData';
import { ProjectRepository } from '@offline/repositories/ProjectRepository';
import { Project } from '@offline/database/entities';
import { SyncStatusBar } from '@components/offline/SyncStatusBar';
import { formatDistanceToNow } from 'date-fns';

export const OfflineProjectListScreen: React.FC = () => {
  const projectRepository = new ProjectRepository();
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'completed'>('all');

  const {
    data: projects,
    loading,
    error,
    isOnline,
    isSyncing,
    refresh,
    create,
    update,
    remove,
    sync,
  } = useOfflineData({
    repository: projectRepository,
    autoSync: true,
    syncOnMount: true,
  });

  const filteredProjects = projects.filter(project => {
    if (selectedStatus === 'all') return true;
    return project.status === selectedStatus;
  });

  const handleCreateProject = () => {
    Alert.prompt(
      'New Project',
      'Enter project name:',
      async (name) => {
        if (name) {
          await create({
            name,
            status: 'active',
            address: '',
            estimatedBudget: 0,
            actualCost: 0,
          });
        }
      }
    );
  };

  const handleDeleteProject = (project: Project) => {
    Alert.alert(
      'Delete Project',
      `Are you sure you want to delete "${project.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => remove(project.id),
        },
      ]
    );
  };

  const renderProjectItem = ({ item }: { item: Project }) => {
    const getSyncIcon = () => {
      switch (item.syncStatus) {
        case 'pending':
          return <Icon name="cloud-upload" size={16} color="#FFA726" />;
        case 'conflict':
          return <Icon name="sync-problem" size={16} color="#F44336" />;
        case 'synced':
          return <Icon name="cloud-done" size={16} color="#66BB6A" />;
        default:
          return null;
      }
    };

    return (
      <TouchableOpacity style={styles.projectCard}>
        <View style={styles.projectHeader}>
          <Text style={styles.projectName}>{item.name}</Text>
          {getSyncIcon()}
        </View>
        
        <Text style={styles.projectAddress}>{item.address || 'No address'}</Text>
        
        <View style={styles.projectFooter}>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
          
          <Text style={styles.updateTime}>
            Updated {formatDistanceToNow(new Date(item.updatedAt), { addSuffix: true })}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteProject(item)}
        >
          <Icon name="delete" size={20} color="#F44336" />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <SyncStatusBar />
      
      <View style={styles.header}>
        <Text style={styles.title}>Projects</Text>
        <TouchableOpacity onPress={handleCreateProject} style={styles.addButton}>
          <Icon name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.filterBar}>
        {(['all', 'active', 'completed'] as const).map(status => (
          <TouchableOpacity
            key={status}
            style={[
              styles.filterButton,
              selectedStatus === status && styles.filterButtonActive,
            ]}
            onPress={() => setSelectedStatus(status)}
          >
            <Text
              style={[
                styles.filterText,
                selectedStatus === status && styles.filterTextActive,
              ]}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Icon name="error" size={48} color="#F44336" />
          <Text style={styles.errorText}>Failed to load projects</Text>
          <TouchableOpacity onPress={refresh} style={styles.retryButton}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      <FlatList
        data={filteredProjects}
        keyExtractor={(item) => item.id}
        renderItem={renderProjectItem}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={loading || isSyncing}
            onRefresh={isOnline ? sync : refresh}
            colors={['#2196F3']}
          />
        }
        ListEmptyComponent={
          !loading && !error ? (
            <View style={styles.emptyContainer}>
              <Icon name="folder-open" size={64} color="#CCC" />
              <Text style={styles.emptyText}>No projects yet</Text>
              <TouchableOpacity onPress={handleCreateProject} style={styles.createButton}>
                <Text style={styles.createButtonText}>Create First Project</Text>
              </TouchableOpacity>
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBar: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  filterButtonActive: {
    backgroundColor: '#2196F3',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  filterTextActive: {
    color: 'white',
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
  },
  projectCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  projectName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  projectAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  projectFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#E3F2FD',
  },
  statusText: {
    fontSize: 12,
    color: '#2196F3',
    textTransform: 'capitalize',
  },
  updateTime: {
    fontSize: 12,
    color: '#999',
  },
  deleteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#2196F3',
    borderRadius: 8,
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
    marginBottom: 24,
  },
  createButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#2196F3',
    borderRadius: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});