import { getRedis } from './redis';
import { createHash } from 'crypto';

export class CacheService {
  private static readonly DEFAULT_TTL = 3600; // 1 hour
  private static readonly CALCULATION_TTL = 86400; // 24 hours
  private static readonly PRICE_TTL = 300; // 5 minutes

  /**
   * Generate a cache key based on the type and parameters
   */
  static generateKey(type: string, params: Record<string, unknown>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((obj, key) => {
        obj[key] = params[key];
        return obj;
      }, {} as Record<string, unknown>);
    
    const hash = createHash('sha256')
      .update(JSON.stringify(sortedParams))
      .digest('hex')
      .substring(0, 16);
    
    return `${type}:${hash}`;
  }

  /**
   * Cache electrical calculation results
   */
  static async cacheCalculation(
    type: string,
    input: Record<string, unknown>,
    output: Record<string, unknown>,
    ttl: number = CacheService.CALCULATION_TTL
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return; // Skip caching if Redis is not available
    
    const key = CacheService.generateKey(`calc:${type}`, input);
    const data = {
      input,
      output,
      cachedAt: new Date().toISOString(),
      type
    };
    
    await redis.setex(key, ttl, JSON.stringify(data));
  }

  /**
   * Get cached calculation result
   */
  static async getCachedCalculation(
    type: string,
    input: Record<string, unknown>
  ): Promise<Record<string, unknown> | null> {
    const redis = getRedis();
    if (!redis) return null; // Return null if Redis is not available
    
    const key = CacheService.generateKey(`calc:${type}`, input);
    const cached = await redis.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Cache material price data
   */
  static async cacheMaterialPrice(
    catalogNumber: string,
    priceData: Record<string, unknown>,
    ttl: number = CacheService.PRICE_TTL
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return; // Skip caching if Redis is not available
    
    const key = `price:${catalogNumber}`;
    await redis.setex(key, ttl, JSON.stringify({
      ...priceData,
      cachedAt: new Date().toISOString()
    }));
  }

  /**
   * Get cached material price
   */
  static async getCachedMaterialPrice(
    catalogNumber: string
  ): Promise<Record<string, unknown> | null> {
    const redis = getRedis();
    if (!redis) return null; // Return null if Redis is not available
    
    const key = `price:${catalogNumber}`;
    const cached = await redis.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Cache batch material prices
   */
  static async cacheMaterialPrices(
    prices: Array<{ catalogNumber: string; data: Record<string, unknown> }>,
    ttl: number = CacheService.PRICE_TTL
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return; // Skip caching if Redis is not available
    
    const pipeline = redis.pipeline();
    
    for (const { catalogNumber, data } of prices) {
      const key = `price:${catalogNumber}`;
      pipeline.setex(key, ttl, JSON.stringify({
        ...data,
        cachedAt: new Date().toISOString()
      }));
    }
    
    await pipeline.exec();
  }

  /**
   * Get batch material prices from cache
   */
  static async getCachedMaterialPrices(
    catalogNumbers: string[]
  ): Promise<Record<string, unknown>> {
    const redis = getRedis();
    if (!redis || catalogNumbers.length === 0) return {};
    
    const pipeline = redis.pipeline();
    
    for (const catalogNumber of catalogNumbers) {
      pipeline.get(`price:${catalogNumber}`);
    }
    
    const results = await pipeline.exec();
    const prices: Record<string, unknown> = {};
    
    if (!results) return prices;
    
    catalogNumbers.forEach((catalogNumber, index) => {
      const result = results[index];
      if (result && result[1]) {
        const [error, value] = result;
        if (!error && value) {
          try {
            prices[catalogNumber] = JSON.parse(value as string);
          } catch {
            // Skip invalid JSON
          }
        }
      }
    });
    
    return prices;
  }

  /**
   * Cache panel load calculation
   */
  static async cachePanelLoadCalculation(
    panelId: string,
    calculation: Record<string, any>,
    ttl: number = CacheService.CALCULATION_TTL
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const key = `panel:load:${panelId}`;
    await redis.setex(key, ttl, JSON.stringify({
      ...calculation,
      cachedAt: new Date().toISOString()
    }));
  }

  /**
   * Get cached panel load calculation
   */
  static async getCachedPanelLoadCalculation(
    panelId: string
  ): Promise<Record<string, any> | null> {
    const redis = getRedis();
    if (!redis) return null;
    
    const key = `panel:load:${panelId}`;
    const cached = await redis.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Invalidate panel load calculation cache
   */
  static async invalidatePanelLoadCalculation(panelId: string): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const key = `panel:load:${panelId}`;
    await redis.del(key);
  }

  /**
   * Cache estimate totals
   */
  static async cacheEstimateTotals(
    estimateId: string,
    totals: Record<string, any>,
    ttl: number = CacheService.DEFAULT_TTL
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const key = `estimate:totals:${estimateId}`;
    await redis.setex(key, ttl, JSON.stringify({
      ...totals,
      cachedAt: new Date().toISOString()
    }));
  }

  /**
   * Get cached estimate totals
   */
  static async getCachedEstimateTotals(
    estimateId: string
  ): Promise<Record<string, any> | null> {
    const redis = getRedis();
    if (!redis) return null;
    
    const key = `estimate:totals:${estimateId}`;
    const cached = await redis.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Invalidate estimate totals cache
   */
  static async invalidateEstimateTotals(estimateId: string): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const key = `estimate:totals:${estimateId}`;
    await redis.del(key);
  }

  /**
   * Cache list query results
   */
  static async cacheListQuery(
    type: string,
    params: Record<string, any>,
    data: any,
    ttl: number = 60 // 1 minute for list queries
  ): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const key = CacheService.generateKey(`list:${type}`, params);
    await redis.setex(key, ttl, JSON.stringify({
      data,
      cachedAt: new Date().toISOString()
    }));
  }

  /**
   * Get cached list query results
   */
  static async getCachedListQuery(
    type: string,
    params: Record<string, any>
  ): Promise<any | null> {
    const redis = getRedis();
    if (!redis) return null;
    
    const key = CacheService.generateKey(`list:${type}`, params);
    const cached = await redis.get(key);
    
    if (!cached) return null;
    
    try {
      const result = JSON.parse(cached);
      return result.data;
    } catch {
      return null;
    }
  }

  /**
   * Invalidate all caches for a specific type
   */
  static async invalidatePattern(pattern: string): Promise<void> {
    const redis = getRedis();
    if (!redis) return;
    
    const keys = await redis.keys(`${pattern}:*`);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }

  /**
   * Warm up cache with frequently used data
   */
  static async warmUpCache(): Promise<void> {
    // This method can be called on server start to pre-load frequently used data
    // Cache warmup initiated - log this through proper logging service if needed
    
    // Add specific warmup logic here based on usage patterns
    // For example: load common wire sizes, breaker ratings, etc.
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<Record<string, any>> {
    const redis = getRedis();
    if (!redis) return { dbSize: 0, info: {} };
    
    const info = await redis.info('stats');
    const dbSize = await redis.dbsize();
    
    return {
      dbSize,
      info: info.split('\n').reduce((acc, line) => {
        const [key, value] = line.split(':');
        if (key && value) {
          acc[key.trim()] = value.trim();
        }
        return acc;
      }, {} as Record<string, string>)
    };
  }
}