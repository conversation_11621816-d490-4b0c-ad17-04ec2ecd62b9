import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Input,
  Button,
  Text,
  Heading,
  Icon,
  Pressable,
  FormControl,
  WarningOutlineIcon,
  Center,
  KeyboardAvoidingView,
  ScrollView,
  Divider,
} from 'native-base';
import { Platform, AppState, AppStateStatus } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useDispatch, useSelector } from 'react-redux';
import { AuthStackScreenProps } from '@types/navigation';
import { AppDispatch, RootState } from '@store/index';
import { login } from '@store/slices/authSlice';
import { showToast } from '@store/slices/uiSlice';
import { enhancedAuthService } from '@services/enhancedAuthService';
import { securityService } from '@services/securityService';
import BiometricPrompt from '@components/auth/BiometricPrompt';
import PinCodeInput from '@components/auth/PinCodeInput';

type Props = AuthStackScreenProps<'Login'>;

const EnhancedLoginScreen: React.FC<Props> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.auth);
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showBiometricPrompt, setShowBiometricPrompt] = useState(false);
  const [showPinInput, setShowPinInput] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [hasStoredCredentials, setHasStoredCredentials] = useState(false);
  const [appStateSubscription, setAppStateSubscription] = useState<any>(null);

  useEffect(() => {
    checkAuthOptions();
    checkDeviceSecurity();
    
    // Set up app state listener for background/foreground transitions
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    setAppStateSubscription(subscription);

    return () => {
      if (appStateSubscription) {
        appStateSubscription.remove();
      }
    };
  }, []);

  const checkAuthOptions = async () => {
    // Check if biometric is available
    const { available } = await securityService.checkBiometricAvailability();
    setBiometricAvailable(available);

    // Check if user has stored credentials
    const { token } = await securityService.getSecureToken();
    setHasStoredCredentials(!!token);

    // Check security config
    const config = await securityService.getSecurityConfig();
    
    // If user has stored credentials and biometric is enabled, show biometric prompt
    if (token && config.enableBiometrics && available) {
      setShowBiometricPrompt(true);
    }
  };

  const checkDeviceSecurity = async () => {
    const { isSecure, issues } = await securityService.checkDeviceSecurity();
    if (!isSecure) {
      dispatch(showToast({
        message: 'Security issues detected on device',
        type: 'warning',
      }));
    }
  };

  const handleAppStateChange = async (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      // App came to foreground
      const config = await securityService.getSecurityConfig();
      const isLocked = await securityService.isAppLocked();
      
      if (config.requireReauthOnForeground && isLocked) {
        // Show authentication prompt
        if (config.enableBiometrics && biometricAvailable) {
          setShowBiometricPrompt(true);
        } else if (config.enablePinCode) {
          setShowPinInput(true);
        }
      }
    } else if (nextAppState === 'background') {
      // App went to background
      securityService.updateLastActiveTime();
    }
  };

  const validate = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validate()) return;

    try {
      await enhancedAuthService.initialize();
      const response = await enhancedAuthService.login({
        ...formData,
        biometricAuth: biometricAvailable,
      });
      
      dispatch(showToast({ message: 'Login successful!', type: 'success' }));
      
      // Check if PIN setup is needed
      const hasPinCode = await securityService.hasPinCode();
      const config = await securityService.getSecurityConfig();
      
      if (config.enablePinCode && !hasPinCode) {
        navigation.navigate('PinSetup' as never);
      }
    } catch (error: any) {
      dispatch(showToast({ 
        message: error.message || 'Login failed. Please try again.', 
        type: 'error' 
      }));
    }
  };

  const handleBiometricSuccess = async () => {
    try {
      const response = await enhancedAuthService.loginWithBiometrics();
      await securityService.unlockApp();
      dispatch(showToast({ message: 'Welcome back!', type: 'success' }));
    } catch (error: any) {
      dispatch(showToast({ 
        message: error.message || 'Biometric login failed', 
        type: 'error' 
      }));
      setShowBiometricPrompt(false);
    }
  };

  const handlePinComplete = async (pin: string) => {
    try {
      const response = await enhancedAuthService.loginWithPin(pin);
      await securityService.unlockApp();
      setShowPinInput(false);
      dispatch(showToast({ message: 'Welcome back!', type: 'success' }));
    } catch (error: any) {
      dispatch(showToast({ 
        message: error.message || 'Invalid PIN', 
        type: 'error' 
      }));
    }
  };

  if (showPinInput) {
    return (
      <PinCodeInput
        onComplete={handlePinComplete}
        onBackPress={() => setShowPinInput(false)}
        title="Enter PIN"
        subtitle="Enter your PIN to unlock the app"
      />
    );
  }

  return (
    <>
      <KeyboardAvoidingView
        flex={1}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <Center flex={1} px={4} bg="white">
            <VStack space={6} w="100%" maxW="300" alignItems="center">
              <VStack space={2} alignItems="center">
                <Icon as={MaterialIcons} name="electric-bolt" size={16} color="primary.500" />
                <Heading size="xl" color="primary.600">
                  Electrical Contractor
                </Heading>
                <Text fontSize="md" color="gray.600">
                  Sign in to your account
                </Text>
              </VStack>

              <VStack space={4} w="100%">
                <FormControl isInvalid={'email' in errors}>
                  <FormControl.Label>Email</FormControl.Label>
                  <Input
                    placeholder="Enter email"
                    value={formData.email}
                    onChangeText={value => setFormData({ ...formData, email: value })}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    InputLeftElement={
                      <Icon as={MaterialIcons} name="email" size={5} ml={2} color="muted.400" />
                    }
                  />
                  <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                    {errors.email}
                  </FormControl.ErrorMessage>
                </FormControl>

                <FormControl isInvalid={'password' in errors}>
                  <FormControl.Label>Password</FormControl.Label>
                  <Input
                    placeholder="Enter password"
                    value={formData.password}
                    onChangeText={value => setFormData({ ...formData, password: value })}
                    type={showPassword ? 'text' : 'password'}
                    InputLeftElement={
                      <Icon as={MaterialIcons} name="lock" size={5} ml={2} color="muted.400" />
                    }
                    InputRightElement={
                      <Pressable onPress={() => setShowPassword(!showPassword)}>
                        <Icon
                          as={MaterialIcons}
                          name={showPassword ? 'visibility' : 'visibility-off'}
                          size={5}
                          mr={2}
                          color="muted.400"
                        />
                      </Pressable>
                    }
                  />
                  <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                    {errors.password}
                  </FormControl.ErrorMessage>
                </FormControl>

                <Button
                  mt={2}
                  onPress={handleLogin}
                  isLoading={isLoading}
                  isLoadingText="Signing in..."
                >
                  Sign In
                </Button>

                {hasStoredCredentials && (
                  <>
                    <HStack alignItems="center" my={2}>
                      <Divider flex={1} />
                      <Text px={3} color="gray.500">OR</Text>
                      <Divider flex={1} />
                    </HStack>

                    <HStack space={2}>
                      {biometricAvailable && (
                        <Button
                          flex={1}
                          variant="outline"
                          onPress={() => setShowBiometricPrompt(true)}
                          startIcon={
                            <Icon as={MaterialIcons} name="fingerprint" size={5} />
                          }
                        >
                          Biometric
                        </Button>
                      )}
                      <Button
                        flex={1}
                        variant="outline"
                        onPress={() => setShowPinInput(true)}
                        startIcon={
                          <Icon as={MaterialIcons} name="pin" size={5} />
                        }
                      >
                        Use PIN
                      </Button>
                    </HStack>
                  </>
                )}

                <HStack justifyContent="space-between">
                  <Pressable onPress={() => navigation.navigate('ForgotPassword' as never)}>
                    <Text color="primary.500" fontSize="sm">
                      Forgot password?
                    </Text>
                  </Pressable>
                </HStack>
              </VStack>

              <HStack space={2}>
                <Text color="gray.600">Don't have an account?</Text>
                <Pressable onPress={() => navigation.navigate('Register' as never)}>
                  <Text color="primary.500" fontWeight="medium">
                    Sign Up
                  </Text>
                </Pressable>
              </HStack>
            </VStack>
          </Center>
        </ScrollView>
      </KeyboardAvoidingView>

      <BiometricPrompt
        isOpen={showBiometricPrompt}
        onClose={() => setShowBiometricPrompt(false)}
        onSuccess={handleBiometricSuccess}
        onFallback={() => {
          setShowBiometricPrompt(false);
          setShowPinInput(true);
        }}
        title="Welcome Back"
        subtitle="Authenticate to access your account"
      />
    </>
  );
};

export default EnhancedLoginScreen;