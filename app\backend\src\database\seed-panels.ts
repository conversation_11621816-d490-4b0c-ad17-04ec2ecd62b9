import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Real breaker types from major manufacturers
const breakerTypes = [
  // Square D QO Series
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 8.50
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 8.50
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO115AFI',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 45.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO120AFI',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 45.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO120GFI',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'GFCI',
    width_inches: 0.75,
    list_price: 95.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 28.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO230',
    ampere_rating: 30,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 28.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO240',
    ampere_rating: 40,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 28.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO250',
    ampere_rating: 50,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 28.00
  },
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO260',
    ampere_rating: 60,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 38.00
  },

  // Square D Homeline Series
  {
    manufacturer: 'Square D',
    series: 'HOM',
    catalog_number: 'HOM115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.50
  },
  {
    manufacturer: 'Square D',
    series: 'HOM',
    catalog_number: 'HOM120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.50
  },
  {
    manufacturer: 'Square D',
    series: 'HOM',
    catalog_number: 'HOM115AFI',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 38.00
  },
  {
    manufacturer: 'Square D',
    series: 'HOM',
    catalog_number: 'HOM220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 18.00
  },

  // Eaton BR Series
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.75
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.75
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BRAF115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 42.00
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 19.00
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR230',
    ampere_rating: 30,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 19.00
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR250',
    ampere_rating: 50,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 22.00
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'GFTCB220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'GFCI',
    width_inches: 1.5,
    list_price: 98.00
  },

  // Eaton CH Series
  {
    manufacturer: 'Eaton',
    series: 'CH',
    catalog_number: 'CH115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 8.25
  },
  {
    manufacturer: 'Eaton',
    series: 'CH',
    catalog_number: 'CH120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 8.25
  },
  {
    manufacturer: 'Eaton',
    series: 'CH',
    catalog_number: 'CH220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 26.00
  },

  // Siemens QP Series
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 6.00
  },
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 6.00
  },
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q115AFC',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 40.00
  },
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 20.00
  },
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q230',
    ampere_rating: 30,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 20.00
  },

  // Siemens BL Series (Bolt-on)
  {
    manufacturer: 'Siemens',
    series: 'BL',
    catalog_number: 'BL115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 12.00
  },
  {
    manufacturer: 'Siemens',
    series: 'BL',
    catalog_number: 'BL220',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 35.00
  },

  // GE THQL Series
  {
    manufacturer: 'GE',
    series: 'THQL',
    catalog_number: 'THQL1115',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.25
  },
  {
    manufacturer: 'GE',
    series: 'THQL',
    catalog_number: 'THQL1120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 5.25
  },
  {
    manufacturer: 'GE',
    series: 'THQL',
    catalog_number: 'THQL1115AF',
    ampere_rating: 15,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'AFCI',
    width_inches: 0.75,
    list_price: 38.00
  },
  {
    manufacturer: 'GE',
    series: 'THQL',
    catalog_number: 'THQL2120',
    ampere_rating: 20,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 17.00
  },
  {
    manufacturer: 'GE',
    series: 'THQL',
    catalog_number: 'THQL2130',
    ampere_rating: 30,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 17.00
  },

  // GE THQB Series (Bolt-on)
  {
    manufacturer: 'GE',
    series: 'THQB',
    catalog_number: 'THQB1120',
    ampere_rating: 20,
    poles: 1,
    voltage_rating: 120,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 0.75,
    list_price: 10.00
  },
  {
    manufacturer: 'GE',
    series: 'THQB',
    catalog_number: 'THQB2130',
    ampere_rating: 30,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 32.00
  },
  {
    manufacturer: 'GE',
    series: 'THQB',
    catalog_number: 'THQB2150',
    ampere_rating: 50,
    poles: 2,
    voltage_rating: 240,
    interrupt_rating: 10,
    breaker_type: 'STANDARD',
    width_inches: 1.5,
    list_price: 35.00
  },

  // Higher amperage breakers for main breakers and larger circuits
  {
    manufacturer: 'Square D',
    series: 'QO',
    catalog_number: 'QO3100',
    ampere_rating: 100,
    poles: 3,
    voltage_rating: 240,
    interrupt_rating: 22,
    breaker_type: 'STANDARD',
    width_inches: 2.25,
    list_price: 145.00
  },
  {
    manufacturer: 'Eaton',
    series: 'BR',
    catalog_number: 'BR3100',
    ampere_rating: 100,
    poles: 3,
    voltage_rating: 240,
    interrupt_rating: 22,
    breaker_type: 'STANDARD',
    width_inches: 2.25,
    list_price: 125.00
  },
  {
    manufacturer: 'Siemens',
    series: 'QP',
    catalog_number: 'Q3100',
    ampere_rating: 100,
    poles: 3,
    voltage_rating: 240,
    interrupt_rating: 22,
    breaker_type: 'STANDARD',
    width_inches: 2.25,
    list_price: 130.00
  }
];

// Real NEC 2023 references
const necReferences = [
  // Article 210 - Branch Circuits
  {
    nec_edition: '2023',
    article: '210',
    section: '210.8(A)',
    title: 'GFCI Protection for Dwelling Units',
    description: 'Ground-fault circuit-interrupter protection for personnel shall be provided for branch circuits supplying receptacles in dwelling unit locations including bathrooms, garages, outdoors, crawl spaces, basements, kitchens, sinks, boathouses, and laundry areas.',
    category: 'OVERCURRENT'
  },
  {
    nec_edition: '2023',
    article: '210',
    section: '210.8(B)',
    title: 'GFCI Protection for Other Than Dwelling Units',
    description: 'Ground-fault circuit-interrupter protection for personnel shall be provided for branch circuits supplying receptacles in other than dwelling unit locations including bathrooms, kitchens, rooftops, outdoors, sinks, indoor wet locations, locker rooms, garages, and crawl spaces.',
    category: 'OVERCURRENT'
  },
  {
    nec_edition: '2023',
    article: '210',
    section: '210.12(A)',
    title: 'Arc-Fault Circuit-Interrupter Protection',
    description: 'All 120-volt, single-phase, 15- and 20-ampere branch circuits supplying outlets installed in dwelling unit kitchens, family rooms, dining rooms, living rooms, parlors, libraries, dens, bedrooms, sunrooms, recreation rooms, closets, hallways, or similar rooms or areas shall be protected by arc-fault circuit-interrupter protection.',
    category: 'OVERCURRENT'
  },
  {
    nec_edition: '2023',
    article: '210',
    section: '210.19(A)(1)',
    title: 'Minimum Ampacity and Size',
    description: 'Branch-circuit conductors shall have an ampacity not less than the maximum load to be served. Conductors shall be sized to carry not less than the larger of 210.19(A)(1)(a) or (b).',
    category: 'WIRE_SIZE'
  },
  {
    nec_edition: '2023',
    article: '210',
    section: '210.21(B)',
    title: 'Outlet Device Rating',
    description: 'Where connected to a branch circuit having a rating in excess of 20 amperes, receptacles shall have an ampere rating not less than the rating of the branch circuit.',
    category: 'OVERCURRENT'
  },
  {
    nec_edition: '2023',
    article: '210',
    section: '210.52',
    title: 'Dwelling Unit Receptacle Outlets',
    description: 'This section provides requirements for 125-volt, 15- and 20-ampere receptacle outlets in dwelling units.',
    category: 'LOAD_CALC'
  },

  // Article 220 - Load Calculations
  {
    nec_edition: '2023',
    article: '220',
    section: '220.12',
    title: 'Lighting Load for Specified Occupancies',
    description: 'A unit load of not less than that specified in Table 220.12 for occupancies specified therein shall constitute the minimum lighting load. The floor area for each floor shall be calculated from the outside dimensions of the building, dwelling unit, or other area involved.',
    category: 'LOAD_CALC'
  },
  {
    nec_edition: '2023',
    article: '220',
    section: '220.14',
    title: 'Other Loads - All Occupancies',
    description: 'In all occupancies, the minimum load for each outlet for general-use receptacles and outlets not used for general illumination shall not be less than that calculated in 220.14(A) through (L).',
    category: 'LOAD_CALC'
  },
  {
    nec_edition: '2023',
    article: '220',
    section: '220.18',
    title: 'Inductive and LED Lighting Loads',
    description: 'For circuits supplying lighting units that have ballasts, transformers, autotransformers, or LED drivers, the calculated load shall be based on the total ampere ratings of such units and not on the total watts of the lamps.',
    category: 'LOAD_CALC'
  },
  {
    nec_edition: '2023',
    article: '220',
    section: '220.42',
    title: 'General Lighting Demand Factors',
    description: 'The demand factors specified in Table 220.42 shall apply to that portion of the total branch-circuit load calculated for general illumination.',
    category: 'LOAD_CALC'
  },
  {
    nec_edition: '2023',
    article: '220',
    section: '220.50',
    title: 'Motors',
    description: 'Motor loads shall be calculated in accordance with the requirements in 430.22, 430.24, and 440.6.',
    category: 'MOTOR'
  },
  {
    nec_edition: '2023',
    article: '220',
    section: '220.82',
    title: 'Dwelling Unit Optional Load Calculation',
    description: 'For a dwelling unit having the total connected load served by a single 120/240-volt or 208Y/120-volt set of 3-wire service or feeder conductors with an ampacity of 100 or greater, it shall be permissible to calculate the feeder and service loads in accordance with this section instead of the method specified in Part III of this article.',
    category: 'LOAD_CALC'
  },

  // Article 230 - Services
  {
    nec_edition: '2023',
    article: '230',
    section: '230.42',
    title: 'Minimum Size and Rating',
    description: 'Service-entrance conductors shall have an ampacity of not less than the maximum load to be served. Conductors shall be sized to carry not less than the larger of 230.42(A)(1) or (A)(2). Loads shall be determined in accordance with Part III, IV, or V of Article 220, as applicable.',
    category: 'WIRE_SIZE'
  },
  {
    nec_edition: '2023',
    article: '230',
    section: '230.70',
    title: 'General Requirements for Disconnecting Means',
    description: 'Means shall be provided to disconnect all conductors in a building or other structure from the service-entrance conductors.',
    category: 'PANEL'
  },
  {
    nec_edition: '2023',
    article: '230',
    section: '230.79',
    title: 'Rating of Service Disconnecting Means',
    description: 'The service disconnecting means shall have a rating not less than the calculated load to be carried, determined in accordance with Part III, IV, or V of Article 220, as applicable.',
    category: 'PANEL'
  },

  // Article 250 - Grounding and Bonding
  {
    nec_edition: '2023',
    article: '250',
    section: '250.24(C)',
    title: 'Grounded Conductor Brought to Service Equipment',
    description: 'A grounded conductor shall be brought to each service disconnecting means and shall be bonded to the disconnecting means enclosure.',
    category: 'GROUNDING'
  },
  {
    nec_edition: '2023',
    article: '250',
    section: '250.52',
    title: 'Grounding Electrodes',
    description: 'All grounding electrodes as described in 250.52(A)(1) through (A)(7) that are present at each building or structure served shall be bonded together to form the grounding electrode system.',
    category: 'GROUNDING'
  },
  {
    nec_edition: '2023',
    article: '250',
    section: '250.66',
    title: 'Size of Alternating-Current Grounding Electrode Conductor',
    description: 'The size of the grounding electrode conductor at the service, at each building or structure where supplied by a feeder(s) or branch circuit(s), or at a separately derived system of a grounded or ungrounded alternating-current system shall not be less than given in Table 250.66.',
    category: 'GROUNDING'
  },
  {
    nec_edition: '2023',
    article: '250',
    section: '250.122',
    title: 'Size of Equipment Grounding Conductors',
    description: 'Equipment grounding conductors of the wire type shall not be smaller than shown in Table 250.122, but in no case shall they be required to be larger than the circuit conductors supplying the equipment.',
    category: 'GROUNDING'
  },

  // Article 310 - Conductors
  {
    nec_edition: '2023',
    article: '310',
    section: '310.15(B)',
    title: 'Ampacity Tables',
    description: 'Ampacities for conductors shall be permitted to be determined by tables as provided in 310.15(B) or under engineering supervision, as provided in 310.15(C).',
    category: 'WIRE_SIZE'
  },
  {
    nec_edition: '2023',
    article: '310',
    section: '310.15(B)(3)(a)',
    title: 'Adjustment Factors for More Than Three Current-Carrying Conductors',
    description: 'Where the number of current-carrying conductors in a raceway or cable exceeds three, or where single conductors or multiconductor cables are installed without maintaining spacing for a continuous length longer than 600 mm (24 in.) and are not installed in raceways, the allowable ampacity of each conductor shall be reduced as shown in Table 310.15(B)(3)(a).',
    category: 'WIRE_SIZE'
  },
  {
    nec_edition: '2023',
    article: '310',
    section: '310.15(B)(16)',
    title: 'Allowable Ampacities of Insulated Conductors',
    description: 'Allowable ampacities of insulated conductors rated up to and including 2000 volts, 60°C through 90°C (140°F through 194°F), not more than three current-carrying conductors in raceway, cable, or earth (directly buried), based on ambient temperature of 30°C (86°F).',
    category: 'WIRE_SIZE'
  },

  // Article 408 - Panelboards
  {
    nec_edition: '2023',
    article: '408',
    section: '408.4(A)',
    title: 'Field Identification Required',
    description: 'All circuits and circuit modifications shall be legibly identified as to their clear, evident, and specific purpose or use. The identification shall include an approved degree of detail that allows each circuit to be distinguished from all others. Spare positions that contain unused overcurrent devices or switches shall be described accordingly.',
    category: 'PANEL'
  },
  {
    nec_edition: '2023',
    article: '408',
    section: '408.4(B)',
    title: 'Source of Supply',
    description: 'All panelboards supplied by a feeder in other than one- or two-family dwellings shall be marked to indicate the device or equipment where the power supply originates.',
    category: 'PANEL'
  },
  {
    nec_edition: '2023',
    article: '408',
    section: '408.30',
    title: 'General',
    description: 'All panelboards shall have a rating not less than the minimum feeder capacity required for the load calculated in accordance with Part III, IV, or V of Article 220, as applicable.',
    category: 'PANEL'
  },
  {
    nec_edition: '2023',
    article: '408',
    section: '408.36',
    title: 'Overcurrent Protection',
    description: 'In addition to the requirement of 408.30, a panelboard shall be protected by an overcurrent protective device having a rating not greater than that of the panelboard.',
    category: 'PANEL'
  },
  {
    nec_edition: '2023',
    article: '408',
    section: '408.40',
    title: 'Grounding of Panelboards',
    description: 'Panelboard cabinets and panelboard frames, if of metal, shall be in physical contact with each other and shall be connected to an equipment grounding conductor.',
    category: 'PANEL'
  },

  // Article 430 - Motors
  {
    nec_edition: '2023',
    article: '430',
    section: '430.22',
    title: 'Single Motor',
    description: 'Conductors that supply a single motor used in a continuous duty application shall have an ampacity of not less than 125 percent of the motor full-load current rating.',
    category: 'MOTOR'
  },
  {
    nec_edition: '2023',
    article: '430',
    section: '430.52',
    title: 'Rating or Setting for Individual Motor Circuit',
    description: 'The motor branch-circuit short-circuit and ground-fault protective device shall comply with 430.52(B) and either 430.52(C) or (D), as applicable.',
    category: 'MOTOR'
  }
];

// Material catalog for panel-related items
const materials = [
  // Square D Panels
  {
    sku: 'QO130L200PG',
    name: 'Square D QO 200A 30-Space Indoor Load Center',
    description: '200 amp, 30 space, 30 circuit indoor main lug load center with ground bar',
    category: 'PANELS',
    manufacturer: 'Square D',
    model_number: 'QO130L200PG',
    unit: 'EA',
    current_price: 189.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    sku: 'QO142M200PC',
    name: 'Square D QO 200A 42-Space Outdoor Main Breaker Load Center',
    description: '200 amp, 42 space, 42 circuit outdoor main breaker load center, NEMA 3R',
    category: 'PANELS',
    manufacturer: 'Square D',
    model_number: 'QO142M200PC',
    unit: 'EA',
    current_price: 425.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    sku: 'HOM2040L125PC',
    name: 'Square D Homeline 125A 20-Space Indoor Load Center',
    description: '125 amp, 20 space, 40 circuit indoor main lug load center',
    category: 'PANELS',
    manufacturer: 'Square D',
    model_number: 'HOM2040L125PC',
    unit: 'EA',
    current_price: 115.00,
    voltage_rating: 240,
    amperage_rating: 125,
    phase: '1PH'
  },

  // Eaton Panels
  {
    sku: 'BR3040B200',
    name: 'Eaton BR 200A 30-Space Indoor Main Breaker Load Center',
    description: '200 amp, 30 space, 40 circuit indoor main breaker load center',
    category: 'PANELS',
    manufacturer: 'Eaton',
    model_number: 'BR3040B200',
    unit: 'EA',
    current_price: 295.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    sku: 'CH42B200C',
    name: 'Eaton CH 200A 42-Space Indoor Main Breaker Load Center',
    description: '200 amp, 42 space, 42 circuit indoor main breaker load center',
    category: 'PANELS',
    manufacturer: 'Eaton',
    model_number: 'CH42B200C',
    unit: 'EA',
    current_price: 385.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },

  // Siemens Panels
  {
    sku: 'P3030L1125CU',
    name: 'Siemens 125A 30-Space Indoor Load Center',
    description: '125 amp, 30 space, 30 circuit indoor main lug load center with copper bus',
    category: 'PANELS',
    manufacturer: 'Siemens',
    model_number: 'P3030L1125CU',
    unit: 'EA',
    current_price: 125.00,
    voltage_rating: 240,
    amperage_rating: 125,
    phase: '1PH'
  },
  {
    sku: 'P4040B1200CU',
    name: 'Siemens 200A 40-Space Indoor Main Breaker Load Center',
    description: '200 amp, 40 space, 40 circuit indoor main breaker load center with copper bus',
    category: 'PANELS',
    manufacturer: 'Siemens',
    model_number: 'P4040B1200CU',
    unit: 'EA',
    current_price: 315.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },

  // GE Panels
  {
    sku: 'TM3220CCU',
    name: 'GE 200A 32-Space Indoor Main Breaker Load Center',
    description: '200 amp, 32 space, 40 circuit indoor main breaker load center',
    category: 'PANELS',
    manufacturer: 'GE',
    model_number: 'TM3220CCU',
    unit: 'EA',
    current_price: 265.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },

  // Wire for panel feeders
  {
    sku: 'THHN-2/0-BLK-500',
    name: '2/0 AWG THHN Black Wire - 500ft',
    description: '2/0 AWG THHN/THWN-2 stranded copper wire, black, 600V',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '20491801',
    unit: 'ROLL',
    current_price: 1850.00,
    wire_size: '2/0',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'BLACK'
  },
  {
    sku: 'THHN-3/0-BLK-500',
    name: '3/0 AWG THHN Black Wire - 500ft',
    description: '3/0 AWG THHN/THWN-2 stranded copper wire, black, 600V',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '20490101',
    unit: 'ROLL',
    current_price: 2350.00,
    wire_size: '3/0',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'BLACK'
  },
  {
    sku: 'THHN-4/0-BLK-500',
    name: '4/0 AWG THHN Black Wire - 500ft',
    description: '4/0 AWG THHN/THWN-2 stranded copper wire, black, 600V',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '20488901',
    unit: 'ROLL',
    current_price: 2950.00,
    wire_size: '4/0',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'BLACK'
  },

  // Grounding wire
  {
    sku: 'GND-6-BARE-500',
    name: '6 AWG Bare Copper Ground Wire - 500ft',
    description: '6 AWG bare copper ground wire, solid',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '10638502',
    unit: 'ROLL',
    current_price: 695.00,
    wire_size: '6',
    wire_type: 'BARE',
    voltage_rating: 600,
    color: 'BARE'
  },
  {
    sku: 'GND-4-BARE-500',
    name: '4 AWG Bare Copper Ground Wire - 500ft',
    description: '4 AWG bare copper ground wire, solid',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '10632302',
    unit: 'ROLL',
    current_price: 1095.00,
    wire_size: '4',
    wire_type: 'BARE',
    voltage_rating: 600,
    color: 'BARE'
  }
];

async function seedPanelData(): Promise<void> {
  try {
    logger.info('🌱 Starting panel data seed...');

    // Clear existing panel-related data
    await prisma.$transaction([
      prisma.panelLoadCalculation.deleteMany(),
      prisma.circuit.deleteMany(),
      prisma.panel.deleteMany(),
      prisma.breakerType.deleteMany(),
      prisma.necReference.deleteMany()
    ]);

    logger.info('✨ Cleared existing panel data');

    // Seed breaker types
    const createdBreakerTypes = await Promise.all(
      breakerTypes.map(breaker =>
        prisma.breakerType.create({ data: breaker })
      )
    );
    logger.info(`✅ Created ${createdBreakerTypes.length} breaker types`);

    // Seed NEC references
    const createdNecReferences = await Promise.all(
      necReferences.map(ref =>
        prisma.necReference.create({ data: ref })
      )
    );
    logger.info(`✅ Created ${createdNecReferences.length} NEC references`);

    // Seed materials
    const createdMaterials = await Promise.all(
      materials.map(material =>
        prisma.material.upsert({
          where: { sku: material.sku },
          update: {
            ...material,
            price_updated: new Date()
          },
          create: {
            ...material,
            price_updated: new Date()
          }
        })
      )
    );
    logger.info(`✅ Created/Updated ${createdMaterials.length} materials`);

    // Get existing projects to add panels to
    const projects = await prisma.project.findMany({
      take: 3
    });

    if (projects.length > 0) {
      // Create sample panels for first project (commercial)
      const commercialProject = projects[0];
      
      const mainPanel = await prisma.panel.create({
        data: {
          project_id: commercialProject.id,
          name: 'Main Distribution Panel',
          location: 'Electrical Room 1st Floor',
          panel_type: 'MAIN',
          manufacturer: 'Square D',
          model_number: 'NQ442L4',
          catalog_number: 'NQ442L4C',
          voltage_system: '277/480V_3PH',
          ampere_rating: 800,
          bus_rating: 800,
          main_breaker_size: 800,
          phase_config: 'THREE_PHASE_4W',
          mounting_type: 'SURFACE',
          enclosure_type: 'NEMA_1',
          spaces_total: 42,
          spaces_used: 0,
          notes: 'Main service panel with surge protection'
        }
      });

      const subPanel1 = await prisma.panel.create({
        data: {
          project_id: commercialProject.id,
          name: 'Lighting Panel A',
          location: '2nd Floor Electrical Closet',
          panel_type: 'SUB',
          manufacturer: 'Square D',
          model_number: 'NF430L2',
          catalog_number: 'NF430L2C',
          voltage_system: '277/480V_3PH',
          ampere_rating: 225,
          bus_rating: 225,
          main_breaker_size: 225,
          phase_config: 'THREE_PHASE_4W',
          mounting_type: 'FLUSH',
          enclosure_type: 'NEMA_1',
          spaces_total: 30,
          spaces_used: 0,
          fed_from_panel_id: mainPanel.id,
          fed_from_circuit: 1,
          notes: 'Lighting panel serving 2nd floor'
        }
      });

      // Add circuits to main panel
      const mainPanelCircuits = [
        {
          panel_id: mainPanel.id,
          circuit_number: 1,
          description: 'Lighting Panel A',
          breaker_size: 225,
          breaker_type: 'STANDARD',
          poles: 3,
          phase_connection: 'ABC',
          wire_size: '3/0_AWG',
          wire_type: 'THHN',
          wire_count: 4,
          conduit_type: 'EMT',
          conduit_size: '2-1/2',
          voltage: 480,
          load_type: 'FEEDER',
          continuous_load: true,
          connected_load: 65000,
          demand_factor: 1.0,
          calculated_load: 65000,
          room_area: '2nd Floor'
        },
        {
          panel_id: mainPanel.id,
          circuit_number: 4,
          description: 'HVAC Unit 1',
          breaker_size: 60,
          breaker_type: 'STANDARD',
          poles: 3,
          phase_connection: 'ABC',
          wire_size: '6_AWG',
          wire_type: 'THHN',
          wire_count: 4,
          conduit_type: 'EMT',
          conduit_size: '1-1/4',
          voltage: 480,
          load_type: 'HVAC',
          continuous_load: false,
          connected_load: 35000,
          demand_factor: 1.0,
          calculated_load: 35000,
          room_area: 'Roof',
          control_type: 'CONTACTOR'
        },
        {
          panel_id: mainPanel.id,
          circuit_number: 7,
          description: 'Elevator',
          breaker_size: 100,
          breaker_type: 'STANDARD',
          poles: 3,
          phase_connection: 'ABC',
          wire_size: '1_AWG',
          wire_type: 'THHN',
          wire_count: 4,
          conduit_type: 'EMT',
          conduit_size: '2',
          voltage: 480,
          load_type: 'MOTOR',
          continuous_load: false,
          connected_load: 45000,
          demand_factor: 1.0,
          calculated_load: 45000,
          room_area: 'Elevator Machine Room'
        }
      ];

      await Promise.all(
        mainPanelCircuits.map(circuit =>
          prisma.circuit.create({ data: circuit })
        )
      );

      // Add circuits to sub panel
      const subPanelCircuits = [
        {
          panel_id: subPanel1.id,
          circuit_number: 1,
          description: 'Office Lighting - North',
          breaker_size: 20,
          breaker_type: 'STANDARD',
          poles: 1,
          phase_connection: 'A',
          wire_size: '12_AWG',
          wire_type: 'THHN',
          wire_count: 2,
          conduit_type: 'EMT',
          conduit_size: '3/4',
          voltage: 277,
          load_type: 'LIGHTING',
          continuous_load: true,
          connected_load: 3500,
          demand_factor: 1.0,
          calculated_load: 3500,
          room_area: 'Office Area North',
          control_type: 'OCCUPANCY'
        },
        {
          panel_id: subPanel1.id,
          circuit_number: 3,
          description: 'Office Lighting - South',
          breaker_size: 20,
          breaker_type: 'STANDARD',
          poles: 1,
          phase_connection: 'B',
          wire_size: '12_AWG',
          wire_type: 'THHN',
          wire_count: 2,
          conduit_type: 'EMT',
          conduit_size: '3/4',
          voltage: 277,
          load_type: 'LIGHTING',
          continuous_load: true,
          connected_load: 3500,
          demand_factor: 1.0,
          calculated_load: 3500,
          room_area: 'Office Area South',
          control_type: 'OCCUPANCY'
        },
        {
          panel_id: subPanel1.id,
          circuit_number: 5,
          description: 'Conference Room Lighting',
          breaker_size: 20,
          breaker_type: 'STANDARD',
          poles: 1,
          phase_connection: 'C',
          wire_size: '12_AWG',
          wire_type: 'THHN',
          wire_count: 2,
          conduit_type: 'EMT',
          conduit_size: '3/4',
          voltage: 277,
          load_type: 'LIGHTING',
          continuous_load: true,
          connected_load: 2400,
          demand_factor: 1.0,
          calculated_load: 2400,
          room_area: 'Conference Room',
          control_type: 'DIMMER'
        },
        {
          panel_id: subPanel1.id,
          circuit_number: 7,
          description: 'Emergency Lighting',
          breaker_size: 20,
          breaker_type: 'STANDARD',
          poles: 1,
          phase_connection: 'A',
          wire_size: '12_AWG',
          wire_type: 'THHN',
          wire_count: 2,
          conduit_type: 'EMT',
          conduit_size: '3/4',
          voltage: 277,
          load_type: 'LIGHTING',
          continuous_load: true,
          connected_load: 1200,
          demand_factor: 1.0,
          calculated_load: 1200,
          room_area: 'All Areas',
          control_type: 'SWITCH',
          notes: 'Connected to emergency power system'
        },
        {
          panel_id: subPanel1.id,
          circuit_number: 2,
          description: 'Spare',
          breaker_size: 20,
          breaker_type: 'STANDARD',
          poles: 1,
          phase_connection: 'A',
          wire_size: '12_AWG',
          wire_type: 'THHN',
          wire_count: 2,
          voltage: 277,
          load_type: 'LIGHTING',
          continuous_load: false,
          connected_load: 0,
          demand_factor: 0,
          calculated_load: 0,
          is_spare: true
        }
      ];

      await Promise.all(
        subPanelCircuits.map(circuit =>
          prisma.circuit.create({ data: circuit })
        )
      );

      // Create panel load calculation
      await prisma.panelLoadCalculation.create({
        data: {
          panel_id: mainPanel.id,
          phase_a_load: 48700,
          phase_b_load: 47300,
          phase_c_load: 49000,
          neutral_load: 12000,
          total_connected_load: 145000,
          total_demand_load: 145000,
          load_percentage: 18.1,
          phase_imbalance_percent: 3.5,
          power_factor: 0.9,
          ambient_temperature: 30,
          derating_factor: 1.0,
          notes: 'Initial load calculation for main panel',
          created_by: 'system'
        }
      });

      // Create residential panel for second project
      if (projects.length > 1) {
        const residentialProject = projects[1];
        
        const residentialPanel = await prisma.panel.create({
          data: {
            project_id: residentialProject.id,
            name: 'Main Panel',
            location: 'Garage',
            panel_type: 'MAIN',
            manufacturer: 'Square D',
            model_number: 'QO142M200PC',
            catalog_number: 'QO142M200PC',
            voltage_system: '120/240V_1PH',
            ampere_rating: 200,
            bus_rating: 200,
            main_breaker_size: 200,
            phase_config: 'SINGLE_PHASE',
            mounting_type: 'SURFACE',
            enclosure_type: 'NEMA_1',
            spaces_total: 42,
            spaces_used: 0,
            notes: 'Main service panel with whole house surge protection'
          }
        });

        // Add typical residential circuits
        const residentialCircuits = [
          {
            panel_id: residentialPanel.id,
            circuit_number: 1,
            description: 'Kitchen Small Appliances',
            breaker_size: 20,
            breaker_type: 'GFCI',
            poles: 1,
            phase_connection: 'A',
            wire_size: '12_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 120,
            load_type: 'RECEPTACLE',
            continuous_load: false,
            connected_load: 1800,
            demand_factor: 1.0,
            calculated_load: 1800,
            room_area: 'Kitchen',
            notes: 'NEC 210.8(A)(6) - GFCI required'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 3,
            description: 'Kitchen Small Appliances',
            breaker_size: 20,
            breaker_type: 'GFCI',
            poles: 1,
            phase_connection: 'A',
            wire_size: '12_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 120,
            load_type: 'RECEPTACLE',
            continuous_load: false,
            connected_load: 1800,
            demand_factor: 1.0,
            calculated_load: 1800,
            room_area: 'Kitchen',
            notes: 'NEC 210.52(B) - Two circuits required'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 5,
            description: 'Master Bedroom',
            breaker_size: 15,
            breaker_type: 'AFCI',
            poles: 1,
            phase_connection: 'A',
            wire_size: '14_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 120,
            load_type: 'RECEPTACLE',
            continuous_load: false,
            connected_load: 1440,
            demand_factor: 1.0,
            calculated_load: 1440,
            room_area: 'Master Bedroom',
            notes: 'NEC 210.12(A) - AFCI required'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 7,
            description: 'Bathroom',
            breaker_size: 20,
            breaker_type: 'GFCI',
            poles: 1,
            phase_connection: 'A',
            wire_size: '12_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 120,
            load_type: 'RECEPTACLE',
            continuous_load: false,
            connected_load: 1800,
            demand_factor: 1.0,
            calculated_load: 1800,
            room_area: 'Bathroom',
            notes: 'NEC 210.8(A)(1) - GFCI required'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 2,
            description: 'Electric Range',
            breaker_size: 50,
            breaker_type: 'STANDARD',
            poles: 2,
            phase_connection: 'AB',
            wire_size: '6_AWG',
            wire_type: 'NM',
            wire_count: 3,
            voltage: 240,
            load_type: 'APPLIANCE',
            continuous_load: false,
            connected_load: 12000,
            demand_factor: 0.8,
            calculated_load: 9600,
            room_area: 'Kitchen',
            notes: 'NEC 220.55 - Range demand factor applied'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 4,
            description: 'Water Heater',
            breaker_size: 30,
            breaker_type: 'STANDARD',
            poles: 2,
            phase_connection: 'AB',
            wire_size: '10_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 240,
            load_type: 'APPLIANCE',
            continuous_load: true,
            connected_load: 4500,
            demand_factor: 1.0,
            calculated_load: 4500,
            room_area: 'Garage',
            notes: 'NEC 422.13 - Continuous load'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 6,
            description: 'Dryer',
            breaker_size: 30,
            breaker_type: 'STANDARD',
            poles: 2,
            phase_connection: 'AB',
            wire_size: '10_AWG',
            wire_type: 'NM',
            wire_count: 3,
            voltage: 240,
            load_type: 'APPLIANCE',
            continuous_load: false,
            connected_load: 5000,
            demand_factor: 1.0,
            calculated_load: 5000,
            room_area: 'Laundry',
            notes: 'NEC 220.54 - Dryer circuit'
          },
          {
            panel_id: residentialPanel.id,
            circuit_number: 8,
            description: 'HVAC',
            breaker_size: 40,
            breaker_type: 'STANDARD',
            poles: 2,
            phase_connection: 'AB',
            wire_size: '8_AWG',
            wire_type: 'NM',
            wire_count: 2,
            voltage: 240,
            load_type: 'HVAC',
            continuous_load: false,
            connected_load: 7500,
            demand_factor: 1.0,
            calculated_load: 7500,
            room_area: 'Outside',
            control_type: 'CONTACTOR',
            notes: 'NEC 440 - Air conditioning circuit'
          }
        ];

        await Promise.all(
          residentialCircuits.map(circuit =>
            prisma.circuit.create({ data: circuit })
          )
        );

        // Create load calculation for residential panel
        await prisma.panelLoadCalculation.create({
          data: {
            panel_id: residentialPanel.id,
            phase_a_load: 21340,
            phase_b_load: 20860,
            neutral_load: 8840,
            total_connected_load: 42200,
            total_demand_load: 39640,
            load_percentage: 19.8,
            phase_imbalance_percent: 2.3,
            power_factor: 0.95,
            ambient_temperature: 30,
            derating_factor: 1.0,
            notes: 'Load calculation per NEC 220.82 optional method',
            created_by: 'system'
          }
        });
      }

      logger.info('✅ Created sample panels with circuits and load calculations');
    }

    logger.info('🎉 Panel data seeding completed successfully!');

    // Display summary
    const summary = await prisma.$transaction([
      prisma.breakerType.count(),
      prisma.necReference.count(),
      prisma.material.count({ where: { category: { in: ['PANELS', 'BREAKERS'] } } }),
      prisma.panel.count(),
      prisma.circuit.count(),
      prisma.panelLoadCalculation.count()
    ]);

    logger.info('\n📊 Panel Data Summary:');
    logger.info(`   Breaker Types: ${summary[0]}`);
    logger.info(`   NEC References: ${summary[1]}`);
    logger.info(`   Panel Materials: ${summary[2]}`);
    logger.info(`   Panels: ${summary[3]}`);
    logger.info(`   Circuits: ${summary[4]}`);
    logger.info(`   Load Calculations: ${summary[5]}`);

  } catch (error) {
    logger.error({ message: '❌ Error seeding panel data', error });
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedPanelData().catch((error) => {
  logger.error({ message: 'Failed to seed panel data', error });
  process.exit(1);
});