import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Picker } from '@react-native-picker/picker';
import { InventoryLocation, InventoryTransaction, ScannedItem } from '../../types/barcode';
import { barcodeScannerService } from '../../services/barcodeScannerService';
import { EnhancedBarcodeScanner } from '../scanner/EnhancedBarcodeScanner';

interface InventoryItem {
  materialId: string;
  name: string;
  sku: string;
  quantity: number;
  location: string;
  lastUpdated: Date;
}

export const InventoryManagement: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [locations, setLocations] = useState<InventoryLocation[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showScanner, setShowScanner] = useState(false);
  const [scanMode, setScanMode] = useState<'add' | 'transfer' | 'adjust'>('add');
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [transferItems, setTransferItems] = useState<ScannedItem[]>([]);
  const [toLocation, setToLocation] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [newLocationName, setNewLocationName] = useState('');
  const [newLocationType, setNewLocationType] = useState<InventoryLocation['type']>('warehouse');

  useEffect(() => {
    loadLocations();
    if (selectedLocation) {
      loadInventory();
    }
  }, [selectedLocation]);

  const loadLocations = async () => {
    // Mock data - replace with API call
    const mockLocations: InventoryLocation[] = [
      { id: '1', name: 'Main Warehouse', type: 'warehouse', address: '123 Main St' },
      { id: '2', name: 'Truck 101', type: 'truck', manager: 'John Doe' },
      { id: '3', name: 'Oak Street Job', type: 'job-site', address: '456 Oak St' },
      { id: '4', name: 'Shop Storage', type: 'shop', manager: 'Jane Smith' },
    ];
    setLocations(mockLocations);
    if (!selectedLocation && mockLocations.length > 0) {
      setSelectedLocation(mockLocations[0].id);
    }
  };

  const loadInventory = async () => {
    // Mock data - replace with API call
    const mockInventory: InventoryItem[] = [
      {
        materialId: '1',
        name: '12 AWG THHN Wire - Black',
        sku: 'THHN-12-BLK',
        quantity: 2500,
        location: selectedLocation,
        lastUpdated: new Date(),
      },
      {
        materialId: '2',
        name: '1/2" EMT Conduit',
        sku: 'EMT-0.5-10',
        quantity: 150,
        location: selectedLocation,
        lastUpdated: new Date(),
      },
    ];
    setInventory(mockInventory);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadInventory();
    setRefreshing(false);
  };

  const handleScanComplete = async (items: ScannedItem[]) => {
    switch (scanMode) {
      case 'add':
        await barcodeScannerService.addToInventory(items, selectedLocation);
        break;
      case 'transfer':
        setTransferItems(items);
        setShowTransferModal(true);
        break;
      case 'adjust':
        // Handle inventory adjustment
        break;
    }
    setShowScanner(false);
    loadInventory();
  };

  const handleTransfer = async () => {
    if (!toLocation) {
      Alert.alert('Error', 'Please select destination location');
      return;
    }

    await barcodeScannerService.transferInventory(
      transferItems,
      selectedLocation,
      toLocation,
      'Transfer via barcode scan'
    );

    setShowTransferModal(false);
    setTransferItems([]);
    setToLocation('');
    loadInventory();
  };

  const createNewLocation = async () => {
    if (!newLocationName) {
      Alert.alert('Error', 'Please enter location name');
      return;
    }

    const newLocation: InventoryLocation = {
      id: Date.now().toString(),
      name: newLocationName,
      type: newLocationType,
    };

    // Add to locations - replace with API call
    setLocations([...locations, newLocation]);
    setShowLocationModal(false);
    setNewLocationName('');
    setSelectedLocation(newLocation.id);
  };

  const filteredInventory = inventory.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getLocationIcon = (type: InventoryLocation['type']) => {
    switch (type) {
      case 'warehouse': return 'warehouse';
      case 'truck': return 'local-shipping';
      case 'job-site': return 'construction';
      case 'shop': return 'store';
      default: return 'place';
    }
  };

  const getLocationById = (id: string) => locations.find(loc => loc.id === id);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Inventory Management</Text>
        <TouchableOpacity onPress={() => setShowLocationModal(true)}>
          <Icon name="add-location" size={24} color="#2196F3" />
        </TouchableOpacity>
      </View>

      {/* Location Selector */}
      <View style={styles.locationSelector}>
        <Icon name={getLocationIcon(getLocationById(selectedLocation)?.type || 'warehouse')} size={24} color="#666" />
        <Picker
          selectedValue={selectedLocation}
          onValueChange={setSelectedLocation}
          style={styles.picker}
        >
          {locations.map(location => (
            <Picker.Item
              key={location.id}
              label={location.name}
              value={location.id}
            />
          ))}
        </Picker>
      </View>

      {/* Search Bar */}
      <View style={styles.searchBar}>
        <Icon name="search" size={20} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder="Search inventory..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
          onPress={() => {
            setScanMode('add');
            setShowScanner(true);
          }}
        >
          <Icon name="add-circle" size={20} color="white" />
          <Text style={styles.actionButtonText}>Add Stock</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#2196F3' }]}
          onPress={() => {
            setScanMode('transfer');
            setShowScanner(true);
          }}
        >
          <Icon name="swap-horiz" size={20} color="white" />
          <Text style={styles.actionButtonText}>Transfer</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF9800' }]}
          onPress={() => {
            setScanMode('adjust');
            setShowScanner(true);
          }}
        >
          <Icon name="tune" size={20} color="white" />
          <Text style={styles.actionButtonText}>Adjust</Text>
        </TouchableOpacity>
      </View>

      {/* Inventory List */}
      <ScrollView
        style={styles.inventoryList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {filteredInventory.map(item => (
          <View key={item.materialId} style={styles.inventoryItem}>
            <View style={styles.itemInfo}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemSku}>SKU: {item.sku}</Text>
              <Text style={styles.itemUpdated}>
                Updated: {item.lastUpdated.toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.itemQuantity}>
              <Text style={styles.quantityNumber}>{item.quantity}</Text>
              <Text style={styles.quantityUnit}>units</Text>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Summary */}
      <View style={styles.summary}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Items</Text>
          <Text style={styles.summaryValue}>{inventory.length}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Units</Text>
          <Text style={styles.summaryValue}>
            {inventory.reduce((sum, item) => sum + item.quantity, 0)}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Low Stock</Text>
          <Text style={[styles.summaryValue, { color: '#f44336' }]}>
            {inventory.filter(item => item.quantity < 10).length}
          </Text>
        </View>
      </View>

      {/* Scanner */}
      <EnhancedBarcodeScanner
        visible={showScanner}
        mode="batch"
        onClose={() => setShowScanner(false)}
        onScanComplete={handleScanComplete}
        locationId={selectedLocation}
      />

      {/* Transfer Modal */}
      <Modal
        visible={showTransferModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowTransferModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Transfer Items</Text>
            
            <Text style={styles.modalLabel}>From: {getLocationById(selectedLocation)?.name}</Text>
            
            <View style={styles.modalPicker}>
              <Text style={styles.modalLabel}>To:</Text>
              <Picker
                selectedValue={toLocation}
                onValueChange={setToLocation}
                style={styles.picker}
              >
                <Picker.Item label="Select Location" value="" />
                {locations
                  .filter(loc => loc.id !== selectedLocation)
                  .map(location => (
                    <Picker.Item
                      key={location.id}
                      label={location.name}
                      value={location.id}
                    />
                  ))}
              </Picker>
            </View>

            <Text style={styles.modalLabel}>Items to Transfer:</Text>
            <ScrollView style={styles.transferItemsList}>
              {transferItems.map(item => (
                <View key={item.id} style={styles.transferItem}>
                  <Text>{item.material?.name || item.barcode}</Text>
                  <Text>Qty: {item.quantity}</Text>
                </View>
              ))}
            </ScrollView>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowTransferModal(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleTransfer}
              >
                <Text style={styles.buttonText}>Transfer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* New Location Modal */}
      <Modal
        visible={showLocationModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Create New Location</Text>
            
            <TextInput
              style={styles.modalInput}
              placeholder="Location Name"
              value={newLocationName}
              onChangeText={setNewLocationName}
            />

            <View style={styles.modalPicker}>
              <Text style={styles.modalLabel}>Type:</Text>
              <Picker
                selectedValue={newLocationType}
                onValueChange={setNewLocationType}
                style={styles.picker}
              >
                <Picker.Item label="Warehouse" value="warehouse" />
                <Picker.Item label="Truck" value="truck" />
                <Picker.Item label="Job Site" value="job-site" />
                <Picker.Item label="Shop" value="shop" />
              </Picker>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowLocationModal(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={createNewLocation}
              >
                <Text style={styles.buttonText}>Create</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  locationSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    marginBottom: 10,
  },
  picker: {
    flex: 1,
    height: 50,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
  },
  actionButtonText: {
    color: 'white',
    marginLeft: 5,
    fontWeight: '600',
  },
  inventoryList: {
    flex: 1,
    paddingHorizontal: 15,
  },
  inventoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    elevation: 2,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemSku: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemUpdated: {
    fontSize: 12,
    color: '#999',
  },
  itemQuantity: {
    alignItems: 'center',
    marginLeft: 15,
  },
  quantityNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  quantityUnit: {
    fontSize: 12,
    color: '#666',
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'white',
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalLabel: {
    fontSize: 16,
    marginBottom: 8,
    color: '#666',
  },
  modalPicker: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 15,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 15,
  },
  transferItemsList: {
    maxHeight: 200,
    marginBottom: 20,
  },
  transferItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#666',
  },
  confirmButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});