import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Panel, Circuit, PanelLoadCalculation } from '@electrical/shared';
import { panelService } from '../../services/panelService';
import { useAuthStore } from '../../stores/auth';
import { CircuitFormAccessible } from './CircuitFormAccessible';
import { PanelLoadVisualization } from './PanelLoadVisualization';
import { 
  BoltIcon, 
  PrinterIcon, 
  ArrowPathIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';
import { announce, KeyboardNavigation, srOnly } from '../../utils/accessibility';

interface DragItem {
  circuitId: string;
  circuitNumber: number;
  type: 'CIRCUIT';
}

interface CircuitSlotProps {
  circuit: Circuit | null;
  slotNumber: number;
  phase: string;
  onDrop: (circuitId: string, targetSlot: number) => void;
  onEdit: (circuit: Circuit) => void;
  onAdd: (slotNumber: number) => void;
  isOddSide: boolean;
  isFocused: boolean;
  onFocus: () => void;
  onKeyboardMove?: (circuit: Circuit, direction: 'up' | 'down') => void;
}

const CircuitSlot: React.FC<CircuitSlotProps> = ({
  circuit,
  slotNumber,
  phase,
  onDrop,
  onEdit,
  onAdd,
  isOddSide,
  isFocused,
  onFocus,
  onKeyboardMove
}) => {
  const slotRef = useRef<HTMLDivElement>(null);
  
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'CIRCUIT',
    item: circuit ? { 
      circuitId: circuit.id, 
      circuitNumber: circuit.circuit_number, 
      type: 'CIRCUIT' 
    } : null,
    canDrag: !!circuit && !circuit.is_space,
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }), [circuit]);

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'CIRCUIT',
    drop: (item: DragItem) => {
      if (item.circuitNumber !== slotNumber) {
        onDrop(item.circuitId, slotNumber);
      }
    },
    canDrop: (item: DragItem) => item.circuitNumber !== slotNumber,
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }), [slotNumber, onDrop]);

  const getBreakerTypeColor = (type: string) => {
    switch (type) {
      case 'GFCI': return 'bg-green-100 text-green-800 border-green-300';
      case 'AFCI': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'GFCI_AFCI': return 'bg-indigo-100 text-indigo-800 border-indigo-300';
      case 'SPACE_ONLY': return 'bg-gray-100 text-gray-400 border-gray-200';
      default: return 'bg-blue-50 text-blue-800 border-blue-200';
    }
  };

  const getBreakerTypeLabel = (type: string) => {
    switch (type) {
      case 'GFCI': return 'GFCI Protected';
      case 'AFCI': return 'AFCI Protected';
      case 'GFCI_AFCI': return 'GFCI/AFCI Dual Function';
      case 'SPACE_ONLY': return 'Empty Space';
      default: return 'Standard Breaker';
    }
  };

  const getLoadTypeIcon = (loadType: string) => {
    switch (loadType) {
      case 'LIGHTING': return '💡';
      case 'RECEPTACLE': return '🔌';
      case 'MOTOR': return '⚙️';
      case 'HVAC': return '❄️';
      case 'APPLIANCE': return '🔧';
      case 'FEEDER': return '⚡';
      case 'EQUIPMENT': return '🏭';
      default: return '⚡';
    }
  };

  const getLoadTypeLabel = (loadType: string) => {
    switch (loadType) {
      case 'LIGHTING': return 'Lighting';
      case 'RECEPTACLE': return 'Receptacle';
      case 'MOTOR': return 'Motor';
      case 'HVAC': return 'HVAC';
      case 'APPLIANCE': return 'Appliance';
      case 'FEEDER': return 'Feeder';
      case 'EQUIPMENT': return 'Equipment';
      default: return 'General';
    }
  };

  const phaseColor = {
    'A': 'bg-red-50 border-red-200',
    'B': 'bg-black text-white border-gray-800',
    'C': 'bg-blue-50 border-blue-200',
  }[phase] || 'bg-gray-50 border-gray-200';

  const phaseTextColor = {
    'A': 'text-red-800 bg-red-100',
    'B': 'text-white bg-black',
    'C': 'text-blue-800 bg-blue-100',
  }[phase] || 'text-gray-800 bg-gray-100';

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!circuit || !onKeyboardMove) return;

    switch (e.key) {
      case KeyboardNavigation.ARROW_UP:
        e.preventDefault();
        onKeyboardMove(circuit, 'up');
        announce(`Moving circuit ${circuit.description} up`);
        break;
      case KeyboardNavigation.ARROW_DOWN:
        e.preventDefault();
        onKeyboardMove(circuit, 'down');
        announce(`Moving circuit ${circuit.description} down`);
        break;
      case KeyboardNavigation.ENTER:
      case KeyboardNavigation.SPACE:
        e.preventDefault();
        if (circuit) {
          onEdit(circuit);
        } else {
          onAdd(slotNumber);
        }
        break;
    }
  };

  useEffect(() => {
    if (isFocused && slotRef.current) {
      slotRef.current.focus();
    }
  }, [isFocused]);

  return (
    <div
      ref={(node) => {
        drag(drop(node));
        slotRef.current = node;
      }}
      tabIndex={0}
      role="button"
      aria-label={
        circuit 
          ? `Slot ${slotNumber}, Phase ${phase}, ${circuit.description}, ${circuit.breaker_size} amp ${getBreakerTypeLabel(circuit.breaker_type)}, ${getLoadTypeLabel(circuit.load_type)} load, ${circuit.calculated_load?.toFixed(0)} watts`
          : `Slot ${slotNumber}, Phase ${phase}, Empty slot. Press Enter to add a circuit`
      }
      aria-describedby={circuit?.continuous_load ? `continuous-load-${slotNumber}` : undefined}
      onFocus={onFocus}
      onKeyDown={handleKeyDown}
      className={`
        relative min-h-[60px] p-2 border-2 rounded-lg transition-all
        focus:ring-2 focus:ring-blue-500 focus:outline-none
        ${isOver && canDrop ? 'border-blue-500 bg-blue-50' : ''}
        ${isDragging ? 'opacity-50' : ''}
        ${circuit ? getBreakerTypeColor(circuit.breaker_type) : 'bg-gray-50 border-dashed border-gray-300'}
        ${circuit && circuit.poles > 1 ? 'row-span-' + circuit.poles : ''}
        ${isFocused ? 'ring-2 ring-blue-500' : ''}
      `}
      style={{
        gridRow: circuit && circuit.poles > 1 ? `span ${circuit.poles}` : undefined
      }}
    >
      {/* Slot number */}
      <div className={`absolute top-1 ${isOddSide ? 'left-1' : 'right-1'} text-xs font-mono text-gray-500`}>
        <span aria-hidden="true">{slotNumber}</span>
      </div>
      
      {/* Phase indicator with visual and text */}
      <div className={`absolute top-1 ${isOddSide ? 'right-1' : 'left-1'}`}>
        <span 
          className={`inline-block w-5 h-5 text-xs font-bold rounded-full text-center leading-5 ${phaseTextColor}`}
          aria-label={`Phase ${phase}`}
        >
          {phase}
        </span>
      </div>

      {circuit ? (
        <div 
          className="cursor-pointer mt-6"
          onClick={() => onEdit(circuit)}
        >
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium">
              {circuit.breaker_size}A
              {circuit.poles > 1 && ` ${circuit.poles}P`}
            </span>
            <span className="text-lg" aria-hidden="true">
              {getLoadTypeIcon(circuit.load_type)}
            </span>
          </div>
          <div className="text-xs text-gray-600 truncate">
            {circuit.description}
          </div>
          {circuit.continuous_load && (
            <div 
              id={`continuous-load-${slotNumber}`}
              className="text-xs text-amber-600 mt-1"
            >
              Continuous Load (125% factor applied)
            </div>
          )}
          <div className="text-xs text-gray-500 mt-1">
            <span aria-label={`${circuit.calculated_load?.toFixed(0)} watts`}>
              {circuit.calculated_load?.toFixed(0)}W
            </span>
            {circuit.room_area && (
              <span aria-label={`Location: ${circuit.room_area}`}>
                {` • ${circuit.room_area}`}
              </span>
            )}
          </div>
        </div>
      ) : (
        <button
          onClick={() => onAdd(slotNumber)}
          className="w-full h-full flex items-center justify-center text-gray-400 hover:text-gray-600"
          aria-label={`Add circuit to slot ${slotNumber}`}
        >
          <PlusIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      )}
    </div>
  );
};

interface PanelScheduleData extends Panel {
  circuits: Circuit[];
  load_calculations: PanelLoadCalculation[];
}

export const PanelScheduleAccessible: React.FC = () => {
  const { projectId, panelId } = useParams<{ projectId: string; panelId: string }>();
  const navigate = useNavigate();
  const { token } = useAuthStore();
  const printRef = useRef<HTMLDivElement>(null);
  const skipLinkRef = useRef<HTMLAnchorElement>(null);
  
  const [panel, setPanel] = useState<PanelScheduleData | null>(null);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [editingCircuit, setEditingCircuit] = useState<Circuit | null>(null);
  const [showCircuitForm, setShowCircuitForm] = useState(false);
  const [newCircuitSlot, setNewCircuitSlot] = useState<number | null>(null);
  const [focusedSlot, setFocusedSlot] = useState<number | null>(null);

  useEffect(() => {
    if (panelId && token) {
      loadPanel();
    }
  }, [panelId, token]);

  const loadPanel = async () => {
    if (!panelId || !token) return;
    
    try {
      setLoading(true);
      const data = await panelService.getPanel(panelId, token);
      setPanel(data as PanelScheduleData);
      announce('Panel schedule loaded');
    } catch (error) {
      console.error('Error loading panel:', error);
      announce('Error loading panel schedule', 'assertive');
    } finally {
      setLoading(false);
    }
  };

  const handleCircuitDrop = async (circuitId: string, targetSlot: number) => {
    if (!token || !panel) return;
    
    try {
      const circuit = panel.circuits.find(c => c.id === circuitId);
      if (!circuit) return;

      await panelService.updateCircuit(circuitId, {
        circuit_number: targetSlot
      }, token);
      
      await loadPanel();
      announce(`Circuit moved to slot ${targetSlot}`);
    } catch (error) {
      console.error('Error moving circuit:', error);
      announce('Error moving circuit', 'assertive');
    }
  };

  const handleKeyboardMove = useCallback(async (circuit: Circuit, direction: 'up' | 'down') => {
    if (!token || !panel) return;
    
    const currentSlot = circuit.circuit_number;
    const targetSlot = direction === 'up' 
      ? Math.max(1, currentSlot - 2) 
      : Math.min(panel.spaces_total, currentSlot + 2);
    
    if (targetSlot === currentSlot) {
      announce(`Cannot move circuit ${direction}`, 'assertive');
      return;
    }
    
    await handleCircuitDrop(circuit.id, targetSlot);
    setFocusedSlot(targetSlot);
  }, [token, panel]);

  const handleCalculateLoad = async () => {
    if (!panelId || !token) return;
    
    try {
      setCalculating(true);
      announce('Calculating panel load...');
      await panelService.calculatePanelLoad(panelId, token);
      await loadPanel();
      announce('Panel load calculation complete');
    } catch (error) {
      console.error('Error calculating load:', error);
      announce('Error calculating panel load', 'assertive');
    } finally {
      setCalculating(false);
    }
  };

  const handleBalancePanel = async () => {
    if (!panelId || !token) return;
    
    try {
      setCalculating(true);
      announce('Balancing panel loads...');
      await panelService.balancePanel(panelId, token);
      await loadPanel();
      announce('Panel loads balanced successfully');
    } catch (error) {
      console.error('Error balancing panel:', error);
      announce('Error balancing panel loads', 'assertive');
    } finally {
      setCalculating(false);
    }
  };

  const handlePrint = () => {
    window.print();
    announce('Print dialog opened');
  };

  const getPhaseForSlot = (slotNumber: number): string => {
    if (!panel) return 'A';
    
    if (panel.phase_config === 'SINGLE_PHASE') {
      return 'A';
    } else if (panel.phase_config === 'THREE_PHASE_3W' || panel.phase_config === 'THREE_PHASE_4W') {
      const position = ((slotNumber - 1) % 6) + 1;
      if (position === 1 || position === 2) return 'A';
      if (position === 3 || position === 4) return 'B';
      return 'C';
    }
    
    return 'A';
  };

  const getCircuitMap = (): Map<number, Circuit> => {
    const map = new Map<number, Circuit>();
    panel?.circuits.forEach(circuit => {
      map.set(circuit.circuit_number, circuit);
      // Handle multi-pole breakers
      if (circuit.poles > 1) {
        for (let i = 1; i < circuit.poles; i++) {
          map.set(circuit.circuit_number + i, circuit);
        }
      }
    });
    return map;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[600px]" role="status" aria-live="polite">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" aria-hidden="true"></div>
        <span className={srOnly}>Loading panel schedule...</span>
      </div>
    );
  }

  if (!panel) {
    return (
      <div className="text-center py-12" role="alert">
        <p className="text-gray-500">Panel not found</p>
      </div>
    );
  }

  const circuitMap = getCircuitMap();
  const latestLoadCalc = panel.load_calculations[0];
  
  // Calculate load summary
  const totalConnectedLoad = panel.circuits.reduce((sum, c) => 
    sum + (c.is_spare || c.is_space ? 0 : c.calculated_load || 0), 0
  );
  
  const maxLoad = panel.ampere_rating * 
    (panel.voltage_system === '120/240V_1PH' ? 240 : 
     panel.voltage_system === '208V_3PH' ? 208 * 1.732 :
     panel.voltage_system === '240V_3PH' ? 240 * 1.732 :
     panel.voltage_system === '480V_3PH' ? 480 * 1.732 : 277 * 1.732);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="max-w-7xl mx-auto">
        {/* Skip navigation link */}
        <a 
          ref={skipLinkRef}
          href="#panel-circuits"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded"
        >
          Skip to circuit schedule
        </a>

        {/* Header */}
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {panel.name} - Panel Schedule
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {panel.location} • {panel.ampere_rating}A {panel.voltage_system}
            </p>
          </div>
          
          <div className="flex space-x-3" role="toolbar" aria-label="Panel actions">
            <button
              onClick={handleBalancePanel}
              disabled={calculating}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              aria-label="Balance loads across phases"
              aria-busy={calculating}
            >
              <ScaleIcon className="h-4 w-4 mr-2" aria-hidden="true" />
              Balance Loads
            </button>
            
            <button
              onClick={handleCalculateLoad}
              disabled={calculating}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              aria-label="Calculate panel load"
              aria-busy={calculating}
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${calculating ? 'animate-spin' : ''}`} aria-hidden="true" />
              Calculate Load
            </button>
            
            <button
              onClick={handlePrint}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              aria-label="Print panel schedule"
            >
              <PrinterIcon className="h-4 w-4 mr-2" aria-hidden="true" />
              Print Schedule
            </button>
          </div>
        </div>

        {/* Load Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6" role="region" aria-labelledby="load-summary-heading">
          <h2 id="load-summary-heading" className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Load Summary
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Total Load */}
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total Connected Load</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                <span aria-label={`${(totalConnectedLoad / 1000).toFixed(1)} kilowatts`}>
                  {(totalConnectedLoad / 1000).toFixed(1)} kW
                </span>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                <span aria-label={`${((totalConnectedLoad / maxLoad) * 100).toFixed(1)} percent of panel rating`}>
                  {((totalConnectedLoad / maxLoad) * 100).toFixed(1)}% of panel rating
                </span>
              </p>
            </div>

            {/* Phase Balance (for 3-phase panels) */}
            {panel.phase_config !== 'SINGLE_PHASE' && latestLoadCalc && (
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Phase Balance</p>
                <div className="mt-2 space-y-1" role="list">
                  <div className="flex justify-between text-sm" role="listitem">
                    <span>Phase A:</span>
                    <span className="font-medium" aria-label={`Phase A: ${(latestLoadCalc.phase_a_load / 1000).toFixed(1)} kilowatts`}>
                      {(latestLoadCalc.phase_a_load / 1000).toFixed(1)} kW
                    </span>
                  </div>
                  <div className="flex justify-between text-sm" role="listitem">
                    <span>Phase B:</span>
                    <span className="font-medium" aria-label={`Phase B: ${(latestLoadCalc.phase_b_load / 1000).toFixed(1)} kilowatts`}>
                      {(latestLoadCalc.phase_b_load / 1000).toFixed(1)} kW
                    </span>
                  </div>
                  <div className="flex justify-between text-sm" role="listitem">
                    <span>Phase C:</span>
                    <span className="font-medium" aria-label={`Phase C: ${(latestLoadCalc.phase_c_load / 1000).toFixed(1)} kilowatts`}>
                      {(latestLoadCalc.phase_c_load / 1000).toFixed(1)} kW
                    </span>
                  </div>
                </div>
                {latestLoadCalc.phase_imbalance_percent > 10 && (
                  <div className="flex items-center mt-2 text-amber-600" role="alert">
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" aria-hidden="true" />
                    <span className="text-sm">
                      {latestLoadCalc.phase_imbalance_percent.toFixed(1)}% phase imbalance detected
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Space Usage */}
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Space Usage</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                <span aria-label={`${panel.spaces_used} of ${panel.spaces_total} spaces used`}>
                  {panel.spaces_used} / {panel.spaces_total}
                </span>
              </p>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2" role="progressbar" aria-valuenow={panel.spaces_used} aria-valuemin={0} aria-valuemax={panel.spaces_total}>
                <div 
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${(panel.spaces_used / panel.spaces_total) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Panel Schedule Grid */}
        <div id="panel-circuits" ref={printRef} className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden print:shadow-none">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 print:text-center">
              Circuit Schedule
            </h2>
            
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4" role="status" aria-live="polite">
              <p>Use arrow keys to navigate between slots. Press Enter or Space to edit a circuit.</p>
              <p>Use Up/Down arrows while focused on a circuit to move it to a different slot.</p>
            </div>
            
            <div className="flex gap-8">
              {/* Left Side (Odd circuits) */}
              <div className="flex-1">
                <div className="text-center text-sm font-medium text-gray-500 mb-2">
                  Left Side (Odd Circuits)
                </div>
                <div className="grid grid-cols-1 gap-2" role="group" aria-label="Odd numbered circuit slots">
                  {Array.from({ length: Math.ceil(panel.spaces_total / 2) }, (_, i) => {
                    const slotNumber = i * 2 + 1;
                    const circuit = circuitMap.get(slotNumber);
                    const phase = getPhaseForSlot(slotNumber);
                    
                    return (
                      <CircuitSlot
                        key={slotNumber}
                        circuit={circuit || null}
                        slotNumber={slotNumber}
                        phase={phase}
                        onDrop={handleCircuitDrop}
                        onEdit={setEditingCircuit}
                        onAdd={(slot) => {
                          setNewCircuitSlot(slot);
                          setShowCircuitForm(true);
                        }}
                        isOddSide={true}
                        isFocused={focusedSlot === slotNumber}
                        onFocus={() => setFocusedSlot(slotNumber)}
                        onKeyboardMove={handleKeyboardMove}
                      />
                    );
                  })}
                </div>
              </div>

              {/* Center Bus */}
              <div className="w-16 relative" aria-label={`Electrical bus, rated ${panel.bus_rating} amps`}>
                <div className="absolute inset-0 bg-gradient-to-b from-gray-300 to-gray-400 rounded-lg">
                  <div className="text-center text-xs font-bold text-gray-700 pt-2">
                    BUS
                  </div>
                  <div className="text-center text-xs text-gray-600 mt-1">
                    {panel.bus_rating}A
                  </div>
                </div>
              </div>

              {/* Right Side (Even circuits) */}
              <div className="flex-1">
                <div className="text-center text-sm font-medium text-gray-500 mb-2">
                  Right Side (Even Circuits)
                </div>
                <div className="grid grid-cols-1 gap-2" role="group" aria-label="Even numbered circuit slots">
                  {Array.from({ length: Math.ceil(panel.spaces_total / 2) }, (_, i) => {
                    const slotNumber = i * 2 + 2;
                    if (slotNumber > panel.spaces_total) return null;
                    
                    const circuit = circuitMap.get(slotNumber);
                    const phase = getPhaseForSlot(slotNumber);
                    
                    return (
                      <CircuitSlot
                        key={slotNumber}
                        circuit={circuit || null}
                        slotNumber={slotNumber}
                        phase={phase}
                        onDrop={handleCircuitDrop}
                        onEdit={setEditingCircuit}
                        onAdd={(slot) => {
                          setNewCircuitSlot(slot);
                          setShowCircuitForm(true);
                        }}
                        isOddSide={false}
                        isFocused={focusedSlot === slotNumber}
                        onFocus={() => setFocusedSlot(slotNumber)}
                        onKeyboardMove={handleKeyboardMove}
                      />
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Legend */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 print:break-inside-avoid">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Breaker Type Legend</h3>
              <div className="flex flex-wrap gap-4 text-sm" role="list" aria-label="Breaker type color coding">
                <div className="flex items-center" role="listitem">
                  <div className="w-4 h-4 bg-blue-100 border border-blue-300 rounded mr-2" aria-hidden="true"></div>
                  <span>Standard</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2" aria-hidden="true"></div>
                  <span>GFCI Protected</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="w-4 h-4 bg-purple-100 border border-purple-300 rounded mr-2" aria-hidden="true"></div>
                  <span>AFCI Protected</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="w-4 h-4 bg-indigo-100 border border-indigo-300 rounded mr-2" aria-hidden="true"></div>
                  <span>GFCI/AFCI Dual Function</span>
                </div>
              </div>

              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2 mt-4">Phase Color Coding</h3>
              <div className="flex flex-wrap gap-4 text-sm" role="list" aria-label="Phase color coding">
                <div className="flex items-center" role="listitem">
                  <div className="w-5 h-5 bg-red-100 border border-red-200 rounded-full mr-2 flex items-center justify-center text-xs font-bold text-red-800" aria-hidden="true">A</div>
                  <span>Phase A (Red)</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="w-5 h-5 bg-black border border-gray-800 rounded-full mr-2 flex items-center justify-center text-xs font-bold text-white" aria-hidden="true">B</div>
                  <span>Phase B (Black)</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="w-5 h-5 bg-blue-100 border border-blue-200 rounded-full mr-2 flex items-center justify-center text-xs font-bold text-blue-800" aria-hidden="true">C</div>
                  <span>Phase C (Blue)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Print-only footer */}
        <div className="hidden print:block mt-8 text-sm text-gray-600">
          <div className="border-t pt-4">
            <p>Panel: {panel.name} - {panel.location}</p>
            <p>Rating: {panel.ampere_rating}A {panel.voltage_system}</p>
            <p>Printed: {new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Load Visualization */}
      <div className="mt-6">
        <PanelLoadVisualization 
          panel={panel} 
          circuits={panel.circuits}
          loadCalculation={latestLoadCalc}
        />
      </div>

      {/* Circuit Form Modal */}
      {(showCircuitForm || editingCircuit) && (
        <CircuitFormAccessible
          panelId={panelId!}
          circuit={editingCircuit}
          defaultSlot={newCircuitSlot || undefined}
          voltage={panel.voltage_system.includes('120') ? 120 : 
                  panel.voltage_system.includes('208') ? 208 :
                  panel.voltage_system.includes('240') ? 240 :
                  panel.voltage_system.includes('277') ? 277 : 480}
          phaseConfig={panel.phase_config}
          onClose={() => {
            setShowCircuitForm(false);
            setEditingCircuit(null);
            setNewCircuitSlot(null);
          }}
          onSave={() => {
            setShowCircuitForm(false);
            setEditingCircuit(null);
            setNewCircuitSlot(null);
            loadPanel();
          }}
        />
      )}

      {/* Add print styles */}
      <style jsx>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #root > * {
            visibility: visible;
          }
          .print\\:shadow-none {
            box-shadow: none !important;
          }
          .print\\:text-center {
            text-align: center !important;
          }
          .print\\:break-inside-avoid {
            break-inside: avoid !important;
          }
          .print\\:block {
            display: block !important;
          }
        }
      `}</style>
    </DndProvider>
  );
};