{"name": "@electrical/shared", "version": "1.0.0", "description": "Shared types and utilities for electrical contracting application", "type": "module", "main": "src/index.ts", "types": "src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": "./src/index.ts", "default": "./src/index.ts"}, "./package.json": "./package.json"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint 'src/**/*.ts' --ignore-pattern '*.d.ts'", "typecheck": "tsc --noEmit"}, "dependencies": {"decimal.js": "^10.4.3", "zod": "^3.22.4"}, "devDependencies": {"@types/decimal.js": "^7.4.3", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "typescript": "^5.3.3"}}