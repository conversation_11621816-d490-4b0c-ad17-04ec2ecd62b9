import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SyncEngine } from '@offline/sync/SyncEngine';
import { formatDistanceToNow } from 'date-fns';

interface ConflictData {
  entityType: string;
  entityId: string;
  localData: any;
  remoteData: any;
}

interface ConflictResolutionModalProps {
  visible: boolean;
  conflict: ConflictData | null;
  onClose: () => void;
  onResolved: () => void;
}

export const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  visible,
  conflict,
  onClose,
  onResolved,
}) => {
  const [resolving, setResolving] = useState(false);
  const [selectedResolution, setSelectedResolution] = useState<'local' | 'remote' | null>(null);

  if (!conflict) return null;

  const handleResolve = async () => {
    if (!selectedResolution) return;

    setResolving(true);
    try {
      const syncEngine = SyncEngine.getInstance();
      await syncEngine.resolveConflict(
        conflict.entityType,
        conflict.entityId,
        selectedResolution
      );
      onResolved();
      onClose();
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
    } finally {
      setResolving(false);
    }
  };

  const renderDataComparison = () => {
    const fields = new Set([
      ...Object.keys(conflict.localData),
      ...Object.keys(conflict.remoteData),
    ]);

    const differences: string[] = [];
    fields.forEach(field => {
      if (conflict.localData[field] !== conflict.remoteData[field]) {
        differences.push(field);
      }
    });

    return differences.map(field => (
      <View key={field} style={styles.comparisonRow}>
        <Text style={styles.fieldName}>{field}:</Text>
        <View style={styles.values}>
          <TouchableOpacity
            style={[
              styles.valueContainer,
              selectedResolution === 'local' && styles.selectedValue,
            ]}
            onPress={() => setSelectedResolution('local')}
          >
            <Text style={styles.valueLabel}>Local</Text>
            <Text style={styles.valueText}>
              {JSON.stringify(conflict.localData[field], null, 2)}
            </Text>
            <Text style={styles.timestamp}>
              {formatDistanceToNow(new Date(conflict.localData.updatedAt), { addSuffix: true })}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.valueContainer,
              selectedResolution === 'remote' && styles.selectedValue,
            ]}
            onPress={() => setSelectedResolution('remote')}
          >
            <Text style={styles.valueLabel}>Remote</Text>
            <Text style={styles.valueText}>
              {JSON.stringify(conflict.remoteData[field], null, 2)}
            </Text>
            <Text style={styles.timestamp}>
              {formatDistanceToNow(new Date(conflict.remoteData.updatedAt), { addSuffix: true })}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    ));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Icon name="sync-problem" size={24} color="#FFA726" />
            <Text style={styles.title}>Resolve Sync Conflict</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.subtitle}>
            {conflict.entityType} has conflicting changes
          </Text>

          <ScrollView style={styles.comparisonContainer}>
            {renderDataComparison()}
          </ScrollView>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[
                styles.button,
                styles.localButton,
                selectedResolution === 'local' && styles.selectedButton,
              ]}
              onPress={() => setSelectedResolution('local')}
            >
              <Icon name="phone-android" size={20} color="white" />
              <Text style={styles.buttonText}>Keep Local Changes</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.remoteButton,
                selectedResolution === 'remote' && styles.selectedButton,
              ]}
              onPress={() => setSelectedResolution('remote')}
            >
              <Icon name="cloud" size={20} color="white" />
              <Text style={styles.buttonText}>Use Remote Changes</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[
              styles.resolveButton,
              !selectedResolution && styles.disabledButton,
            ]}
            onPress={handleResolve}
            disabled={!selectedResolution || resolving}
          >
            {resolving ? (
              <ActivityIndicator color="white" />
            ) : (
              <>
                <Icon name="check" size={20} color="white" />
                <Text style={styles.resolveButtonText}>
                  Apply Resolution
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  comparisonContainer: {
    maxHeight: 300,
    marginBottom: 20,
  },
  comparisonRow: {
    marginBottom: 16,
  },
  fieldName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textTransform: 'capitalize',
  },
  values: {
    flexDirection: 'row',
    gap: 8,
  },
  valueContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedValue: {
    borderColor: '#2196F3',
    backgroundColor: '#E3F2FD',
  },
  valueLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  valueText: {
    fontSize: 12,
    color: '#333',
  },
  timestamp: {
    fontSize: 10,
    color: '#999',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  localButton: {
    backgroundColor: '#4CAF50',
  },
  remoteButton: {
    backgroundColor: '#2196F3',
  },
  selectedButton: {
    opacity: 0.8,
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  resolveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF6B6B',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#CCC',
  },
  resolveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});