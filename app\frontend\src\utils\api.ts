import { api } from '../services/api';
import { AxiosRequestConfig } from 'axios';

/**
 * Utility function for making API requests
 * This is a wrapper around the axios instance to provide a simpler interface
 */
export async function apiRequest<T = any>(
  url: string,
  options?: AxiosRequestConfig
): Promise<T> {
  const response = await api.request<T>({
    url,
    ...options,
  });
  return response.data;
}