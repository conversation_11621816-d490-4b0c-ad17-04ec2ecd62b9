import React, { useEffect, useState } from 'react';
import { StyleSheet, Linking } from 'react-native';
import { Box, Text, Button, VStack, Icon } from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
  CameraPermissionStatus,
} from 'react-native-vision-camera';
import { RootStackScreenProps } from '@types/navigation';

type Props = RootStackScreenProps<'BarcodeScanner'>;

const BarcodeScannerScreen: React.FC<Props> = ({ navigation, route }) => {
  const { onScan } = route.params;
  const [permissionStatus, setPermissionStatus] = useState<CameraPermissionStatus>('not-determined');
  const [isScanning, setIsScanning] = useState(true);
  const device = useCameraDevice('back');

  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    const status = await Camera.getCameraPermissionStatus();
    setPermissionStatus(status);
    
    if (status === 'not-determined') {
      const newStatus = await Camera.requestCameraPermission();
      setPermissionStatus(newStatus);
    }
  };

  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13', 'ean-8', 'code-128', 'code-39', 'code-93'],
    onCodeScanned: codes => {
      if (isScanning && codes.length > 0) {
        setIsScanning(false);
        const code = codes[0];
        onScan(code.value || '');
        navigation.goBack();
      }
    },
  });

  if (permissionStatus === 'not-determined') {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="black">
        <Text color="white">Checking camera permissions...</Text>
      </Box>
    );
  }

  if (permissionStatus === 'denied') {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="black" px={4}>
        <VStack space={4} alignItems="center">
          <Icon as={MaterialIcons} name="camera-alt" size={16} color="white" />
          <Text color="white" textAlign="center" fontSize="lg">
            Camera Permission Required
          </Text>
          <Text color="gray.300" textAlign="center">
            This app needs camera access to scan barcodes and QR codes.
          </Text>
          <Button onPress={() => Linking.openSettings()}>
            Open Settings
          </Button>
        </VStack>
      </Box>
    );
  }

  if (!device) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="black">
        <Text color="white">No camera device found</Text>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="black">
      <Camera
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        codeScanner={codeScanner}
      />
      
      {/* Scanning overlay */}
      <Box position="absolute" top={0} left={0} right={0} bottom={0}>
        <Box flex={1} justifyContent="center" alignItems="center">
          <Box
            width={250}
            height={250}
            borderWidth={2}
            borderColor="white"
            borderRadius="lg"
            position="relative"
          >
            {/* Corner markers */}
            <Box position="absolute" top={-2} left={-2} width={8} height={8}>
              <Box width={8} height={2} bg="primary.500" />
              <Box width={2} height={8} bg="primary.500" position="absolute" />
            </Box>
            <Box position="absolute" top={-2} right={-2} width={8} height={8}>
              <Box width={8} height={2} bg="primary.500" position="absolute" right={0} />
              <Box width={2} height={8} bg="primary.500" position="absolute" right={0} />
            </Box>
            <Box position="absolute" bottom={-2} left={-2} width={8} height={8}>
              <Box width={8} height={2} bg="primary.500" position="absolute" bottom={0} />
              <Box width={2} height={8} bg="primary.500" position="absolute" bottom={0} />
            </Box>
            <Box position="absolute" bottom={-2} right={-2} width={8} height={8}>
              <Box width={8} height={2} bg="primary.500" position="absolute" bottom={0} right={0} />
              <Box width={2} height={8} bg="primary.500" position="absolute" bottom={0} right={0} />
            </Box>
          </Box>
          
          <Text color="white" mt={8} fontSize="lg">
            {isScanning ? 'Align code within frame' : 'Processing...'}
          </Text>
        </Box>
      </Box>
    </Box>
  );
};

export default BarcodeScannerScreen;