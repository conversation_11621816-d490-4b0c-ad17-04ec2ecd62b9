import AsyncStorage from '@react-native-async-storage/async-storage';
import { QuickEstimate } from '../types/electrical';
import { secureApi } from './secureApi';

const ESTIMATE_CACHE_KEY = 'estimate_cache_';
const OFFLINE_ESTIMATES_KEY = 'offline_estimates';

class EstimateService {
  // Save estimate
  async saveEstimate(estimate: QuickEstimate): Promise<QuickEstimate> {
    try {
      const response = await secureApi.post('/estimates', estimate);
      const savedEstimate = response.data;
      await this.cacheEstimate(savedEstimate);
      return savedEstimate;
    } catch (error) {
      // Save offline
      const offlineEstimate = {
        ...estimate,
        id: `offline_${Date.now()}`,
        status: 'draft',
      };
      await this.cacheEstimate(offlineEstimate);
      await this.queueOfflineEstimate(offlineEstimate);
      return offlineEstimate;
    }
  }

  // Get estimate by ID
  async getEstimate(estimateId: string): Promise<QuickEstimate> {
    try {
      const response = await secureApi.get(`/estimates/${estimateId}`);
      const estimate = response.data;
      await this.cacheEstimate(estimate);
      return estimate;
    } catch (error) {
      const cached = await this.getCachedEstimate(estimateId);
      if (cached) return cached;
      throw error;
    }
  }

  // Get project estimates
  async getProjectEstimates(projectId: string): Promise<QuickEstimate[]> {
    try {
      const response = await secureApi.get(`/projects/${projectId}/estimates`);
      const estimates = response.data;
      
      for (const estimate of estimates) {
        await this.cacheEstimate(estimate);
      }
      
      return estimates;
    } catch (error) {
      return this.getCachedProjectEstimates(projectId);
    }
  }

  // Update estimate status
  async updateEstimateStatus(
    estimateId: string,
    status: QuickEstimate['status']
  ): Promise<QuickEstimate> {
    try {
      const response = await secureApi.patch(`/estimates/${estimateId}`, { status });
      const updated = response.data;
      await this.cacheEstimate(updated);
      return updated;
    } catch (error) {
      // Update offline
      const cached = await this.getCachedEstimate(estimateId);
      if (cached) {
        cached.status = status;
        await this.cacheEstimate(cached);
        await this.queueOfflineUpdate({ estimateId, status });
        return cached;
      }
      throw error;
    }
  }

  // Generate PDF
  async generatePDF(estimateId: string): Promise<string> {
    try {
      const response = await secureApi.get(`/estimates/${estimateId}/pdf`, {
        responseType: 'blob'
      });
      
      // Convert blob to base64 for React Native
      const reader = new FileReader();
      return new Promise((resolve, reject) => {
        reader.onloadend = () => {
          const base64data = reader.result as string;
          resolve(base64data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(response.data);
      });
    } catch (error) {
      throw error;
    }
  }

  // Send estimate to customer
  async sendEstimate(estimateId: string, email: string): Promise<void> {
    try {
      await secureApi.post(`/estimates/${estimateId}/send`, { email });
    } catch (error) {
      // Queue for later sending
      await this.queueOfflineAction({
        type: 'send',
        estimateId,
        email,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  // Cache management
  private async cacheEstimate(estimate: QuickEstimate): Promise<void> {
    const key = `${ESTIMATE_CACHE_KEY}${estimate.id}`;
    await AsyncStorage.setItem(key, JSON.stringify(estimate));
    
    // Update offline list
    const offlineList = await this.getOfflineEstimatesList();
    if (!offlineList.includes(estimate.id)) {
      offlineList.push(estimate.id);
      await AsyncStorage.setItem(OFFLINE_ESTIMATES_KEY, JSON.stringify(offlineList));
    }
  }

  private async getCachedEstimate(estimateId: string): Promise<QuickEstimate | null> {
    const key = `${ESTIMATE_CACHE_KEY}${estimateId}`;
    const cached = await AsyncStorage.getItem(key);
    return cached ? JSON.parse(cached) : null;
  }

  private async getCachedProjectEstimates(projectId: string): Promise<QuickEstimate[]> {
    const estimateIds = await this.getOfflineEstimatesList();
    const estimates: QuickEstimate[] = [];
    
    for (const id of estimateIds) {
      const estimate = await this.getCachedEstimate(id);
      if (estimate && estimate.projectName === projectId) {
        estimates.push(estimate);
      }
    }
    
    return estimates;
  }

  private async getOfflineEstimatesList(): Promise<string[]> {
    const list = await AsyncStorage.getItem(OFFLINE_ESTIMATES_KEY);
    return list ? JSON.parse(list) : [];
  }

  private async queueOfflineEstimate(estimate: QuickEstimate): Promise<void> {
    const queueKey = 'estimate_offline_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    const estimates = queue ? JSON.parse(queue) : [];
    
    estimates.push({
      type: 'create',
      data: estimate,
      timestamp: new Date().toISOString(),
    });
    
    await AsyncStorage.setItem(queueKey, JSON.stringify(estimates));
  }

  private async queueOfflineUpdate(update: any): Promise<void> {
    const queueKey = 'estimate_offline_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    const updates = queue ? JSON.parse(queue) : [];
    
    updates.push({
      type: 'update',
      ...update,
      timestamp: new Date().toISOString(),
    });
    
    await AsyncStorage.setItem(queueKey, JSON.stringify(updates));
  }

  private async queueOfflineAction(action: any): Promise<void> {
    const queueKey = 'estimate_offline_actions';
    const queue = await AsyncStorage.getItem(queueKey);
    const actions = queue ? JSON.parse(queue) : [];
    
    actions.push(action);
    await AsyncStorage.setItem(queueKey, JSON.stringify(actions));
  }

  // Sync offline data
  async syncOfflineEstimates(): Promise<void> {
    // Sync queued estimates
    const queueKey = 'estimate_offline_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    if (queue) {
      const items = JSON.parse(queue);
      const failed = [];
      
      for (const item of items) {
        try {
          if (item.type === 'create') {
            await secureApi.post('/estimates', item.data);
          } else if (item.type === 'update') {
            await secureApi.patch(`/estimates/${item.estimateId}`, { status: item.status });
          }
        } catch (error) {
          failed.push(item);
        }
      }
      
      if (failed.length > 0) {
        await AsyncStorage.setItem(queueKey, JSON.stringify(failed));
      } else {
        await AsyncStorage.removeItem(queueKey);
      }
    }

    // Sync actions
    const actionsKey = 'estimate_offline_actions';
    const actions = await AsyncStorage.getItem(actionsKey);
    if (actions) {
      const items = JSON.parse(actions);
      const failed = [];
      
      for (const action of items) {
        try {
          if (action.type === 'send') {
            await secureApi.post(`/estimates/${action.estimateId}/send`, { email: action.email });
          }
        } catch (error) {
          failed.push(action);
        }
      }
      
      if (failed.length > 0) {
        await AsyncStorage.setItem(actionsKey, JSON.stringify(failed));
      } else {
        await AsyncStorage.removeItem(actionsKey);
      }
    }
  }

  // Clear cache
  async clearCache(): Promise<void> {
    const allKeys = await AsyncStorage.getAllKeys();
    const estimateKeys = allKeys.filter(key => 
      key.startsWith(ESTIMATE_CACHE_KEY) || 
      key === OFFLINE_ESTIMATES_KEY
    );
    
    await AsyncStorage.multiRemove(estimateKeys);
    await AsyncStorage.removeItem('estimate_offline_queue');
    await AsyncStorage.removeItem('estimate_offline_actions');
  }
}

export const estimateService = new EstimateService();