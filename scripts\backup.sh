#!/bin/bash

# Database Backup Script
# Usage: ./backup.sh [environment]

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_DIR="/backups"
RETENTION_DAYS=30
S3_BUCKET="electrical-backups-${ENVIRONMENT}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Database credentials from environment
DB_HOST=${DB_HOST:-postgres}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-electrical_contracting}
DB_USER=${DB_USER:-electrical}
DB_PASSWORD=${DB_PASSWORD}

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
}

# Backup PostgreSQL
backup_postgres() {
    log_info "Starting PostgreSQL backup..."
    
    local backup_file="${BACKUP_DIR}/postgres_${ENVIRONMENT}_${TIMESTAMP}.sql.gz"
    
    PGPASSWORD=$DB_PASSWORD pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --clean \
        --no-owner \
        --no-privileges \
        --if-exists | gzip > "$backup_file"
    
    if [[ -f "$backup_file" ]]; then
        log_info "PostgreSQL backup completed: $backup_file"
        echo "$backup_file"
    else
        log_error "PostgreSQL backup failed"
        return 1
    fi
}

# Backup Redis
backup_redis() {
    log_info "Starting Redis backup..."
    
    local backup_file="${BACKUP_DIR}/redis_${ENVIRONMENT}_${TIMESTAMP}.rdb"
    
    # Trigger Redis BGSAVE
    redis-cli -h redis -a "$REDIS_PASSWORD" BGSAVE
    
    # Wait for backup to complete
    while [[ $(redis-cli -h redis -a "$REDIS_PASSWORD" LASTSAVE) -eq $(redis-cli -h redis -a "$REDIS_PASSWORD" LASTSAVE) ]]; do
        sleep 1
    done
    
    # Copy dump file
    cp /data/dump.rdb "$backup_file"
    
    if [[ -f "$backup_file" ]]; then
        log_info "Redis backup completed: $backup_file"
        echo "$backup_file"
    else
        log_error "Redis backup failed"
        return 1
    fi
}

# Backup Neo4j
backup_neo4j() {
    log_info "Starting Neo4j backup..."
    
    local backup_file="${BACKUP_DIR}/neo4j_${ENVIRONMENT}_${TIMESTAMP}.tar.gz"
    
    # Use Neo4j backup command
    neo4j-admin backup \
        --database=neo4j \
        --backup-dir="${BACKUP_DIR}/neo4j_temp" \
        --verbose
    
    # Compress backup
    tar -czf "$backup_file" -C "${BACKUP_DIR}/neo4j_temp" .
    rm -rf "${BACKUP_DIR}/neo4j_temp"
    
    if [[ -f "$backup_file" ]]; then
        log_info "Neo4j backup completed: $backup_file"
        echo "$backup_file"
    else
        log_error "Neo4j backup failed"
        return 1
    fi
}

# Upload to S3
upload_to_s3() {
    local file=$1
    local filename=$(basename "$file")
    
    log_info "Uploading $filename to S3..."
    
    aws s3 cp "$file" "s3://${S3_BUCKET}/$(date +%Y/%m/%d)/${filename}" \
        --storage-class STANDARD_IA \
        --server-side-encryption AES256
    
    if [[ $? -eq 0 ]]; then
        log_info "Upload completed: $filename"
    else
        log_error "Upload failed: $filename"
        return 1
    fi
}

# Clean old backups
clean_old_backups() {
    log_info "Cleaning old backups..."
    
    # Clean local backups
    find "$BACKUP_DIR" -name "*.sql.gz" -o -name "*.rdb" -o -name "*.tar.gz" -mtime +7 -delete
    
    # Clean S3 backups older than retention period
    local cutoff_date=$(date -d "${RETENTION_DAYS} days ago" +%Y-%m-%d)
    
    aws s3api list-objects-v2 \
        --bucket "$S3_BUCKET" \
        --query "Contents[?LastModified<='${cutoff_date}'].Key" \
        --output text | \
    while read -r key; do
        if [[ -n "$key" ]]; then
            log_info "Deleting old backup: $key"
            aws s3 rm "s3://${S3_BUCKET}/${key}"
        fi
    done
}

# Verify backup
verify_backup() {
    local file=$1
    
    log_info "Verifying backup: $file"
    
    if [[ "$file" == *.sql.gz ]]; then
        # Test PostgreSQL backup
        if gunzip -t "$file" 2>/dev/null; then
            log_info "PostgreSQL backup verification passed"
            return 0
        else
            log_error "PostgreSQL backup verification failed"
            return 1
        fi
    elif [[ "$file" == *.rdb ]]; then
        # Test Redis backup
        if redis-check-rdb "$file" &>/dev/null; then
            log_info "Redis backup verification passed"
            return 0
        else
            log_error "Redis backup verification failed"
            return 1
        fi
    elif [[ "$file" == *.tar.gz ]]; then
        # Test tar archive
        if tar -tzf "$file" &>/dev/null; then
            log_info "Archive verification passed"
            return 0
        else
            log_error "Archive verification failed"
            return 1
        fi
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    # Send to Slack webhook if configured
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local color="good"
        [[ "$status" == "error" ]] && color="danger"
        
        curl -X POST "$SLACK_WEBHOOK_URL" \
            -H 'Content-Type: application/json' \
            -d "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"Backup ${status} - ${ENVIRONMENT}\",
                    \"text\": \"$message\",
                    \"timestamp\": \"$(date -u +%s)\"
                }]
            }"
    fi
}

# Main backup process
main() {
    log_info "Starting backup process for $ENVIRONMENT environment"
    
    create_backup_dir
    
    local failed=0
    local backup_files=()
    
    # Perform backups
    if backup_file=$(backup_postgres); then
        backup_files+=("$backup_file")
        verify_backup "$backup_file" || ((failed++))
    else
        ((failed++))
    fi
    
    if backup_file=$(backup_redis); then
        backup_files+=("$backup_file")
        verify_backup "$backup_file" || ((failed++))
    else
        ((failed++))
    fi
    
    if backup_file=$(backup_neo4j); then
        backup_files+=("$backup_file")
        verify_backup "$backup_file" || ((failed++))
    else
        ((failed++))
    fi
    
    # Upload to S3
    for file in "${backup_files[@]}"; do
        upload_to_s3 "$file" || ((failed++))
    done
    
    # Clean old backups
    clean_old_backups
    
    # Send notification
    if [[ $failed -eq 0 ]]; then
        log_info "Backup process completed successfully"
        send_notification "success" "All backups completed successfully for $ENVIRONMENT"
    else
        log_error "Backup process completed with $failed errors"
        send_notification "error" "Backup process failed with $failed errors for $ENVIRONMENT"
        exit 1
    fi
}

# Run main function
main "$@"