# Performance Optimization Guide

This guide provides comprehensive strategies and techniques for optimizing the performance of the Electrical Contracting Application across all layers of the stack.

## 📊 Performance Metrics & Goals

### Target Metrics
- **API Response Time**: < 200ms (p95)
- **Page Load Time**: < 2s (p95)
- **Time to Interactive**: < 3s
- **First Contentful Paint**: < 1s
- **Database Query Time**: < 100ms (p95)
- **Mobile App Launch**: < 3s
- **Offline Sync**: < 30s for full project

### Monitoring Tools
- **Frontend**: Web Vitals, Lighthouse
- **Backend**: Prometheus, Grafana
- **Database**: pg_stat_statements, slow query log
- **APM**: Sentry Performance Monitoring
- **Real User Monitoring**: Google Analytics

## 🚀 Frontend Optimization

### Code Splitting

```typescript
// Lazy load heavy components
const PanelSchedule = lazy(() => import('./components/PanelSchedule'));
const ArcFlashCalculator = lazy(() => import('./components/ArcFlashCalculator'));

// Route-based code splitting
const routes = [
  {
    path: '/panels/:id',
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <PanelSchedule />
      </Suspense>
    )
  }
];

// Component-level splitting for large features
const HeavyChart = lazy(() => 
  import('./components/Charts').then(module => ({
    default: module.HeavyChart
  }))
);
```

### Bundle Optimization

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': ['@headlessui/react', 'clsx', 'tailwind-merge'],
          'vendor-utils': ['date-fns', 'lodash-es', 'zod'],
          'vendor-charts': ['recharts', 'd3-scale', 'd3-shape']
        }
      }
    },
    // Enable CSS code splitting
    cssCodeSplit: true,
    // Minify for production
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

### Image Optimization

```typescript
// components/OptimizedImage.tsx
interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  priority = false
}) => {
  const [isIntersecting, setIsIntersecting] = useState(priority);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (priority) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  return (
    <div ref={imgRef} style={{ width, height }}>
      {isIntersecting ? (
        <picture>
          <source 
            srcSet={`${src}?w=${width}&format=webp`} 
            type="image/webp" 
          />
          <source 
            srcSet={`${src}?w=${width}&format=jpg`} 
            type="image/jpeg" 
          />
          <img
            src={`${src}?w=${width}&format=jpg`}
            alt={alt}
            width={width}
            height={height}
            loading={priority ? 'eager' : 'lazy'}
          />
        </picture>
      ) : (
        <div className="bg-gray-200 animate-pulse" />
      )}
    </div>
  );
};
```

### Virtual Scrolling

```typescript
// components/VirtualProjectList.tsx
import { useVirtualizer } from '@tanstack/react-virtual';

export const VirtualProjectList: React.FC<{ projects: Project[] }> = ({ 
  projects 
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: projects.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 80, // Estimated row height
    overscan: 5 // Number of items to render outside visible area
  });

  return (
    <div ref={parentRef} className="h-[600px] overflow-auto">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <ProjectRow project={projects[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Memoization Strategies

```typescript
// Expensive calculation memoization
const MemoizedCalculator: React.FC<CalculatorProps> = memo(({ 
  input,
  onResult 
}) => {
  const result = useMemo(() => {
    return performExpensiveCalculation(input);
  }, [input]);

  const formattedResult = useMemo(() => {
    return formatCalculationResult(result);
  }, [result]);

  return <CalculatorDisplay result={formattedResult} />;
}, (prevProps, nextProps) => {
  // Custom comparison for memo
  return (
    prevProps.input.current === nextProps.input.current &&
    prevProps.input.voltage === nextProps.input.voltage
  );
});

// Memoized callbacks
const ProjectList: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  
  const handleProjectUpdate = useCallback((id: string, data: Partial<Project>) => {
    setProjects(prev => prev.map(p => 
      p.id === id ? { ...p, ...data } : p
    ));
  }, []);

  const sortedProjects = useMemo(() => {
    return [...projects].sort((a, b) => 
      b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  }, [projects]);

  return <VirtualProjectList projects={sortedProjects} />;
};
```

## 🔧 Backend Optimization

### Database Query Optimization

```typescript
// Use query builders for complex queries
class ProjectRepository {
  async getProjectsWithStats(userId: string, filters: ProjectFilters) {
    // Bad: N+1 query problem
    // const projects = await prisma.project.findMany({ where: { userId } });
    // for (const project of projects) {
    //   project.taskCount = await prisma.task.count({ where: { projectId: project.id } });
    // }

    // Good: Single query with aggregation
    return await prisma.project.findMany({
      where: {
        userId,
        status: filters.status,
        startDate: {
          gte: filters.dateFrom,
          lte: filters.dateTo
        }
      },
      include: {
        _count: {
          select: {
            tasks: true,
            materials: true,
            timeEntries: true
          }
        },
        customer: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: filters.limit || 20,
      skip: filters.offset || 0
    });
  }

  // Use raw queries for complex aggregations
  async getProjectMetrics(projectId: string) {
    const metrics = await prisma.$queryRaw<MetricsResult[]>`
      SELECT 
        COUNT(DISTINCT t.id) as task_count,
        COUNT(DISTINCT m.id) as material_count,
        SUM(m.quantity * m.unit_cost) as material_cost,
        SUM(te.duration) as total_hours,
        AVG(t.completion_percentage) as avg_completion
      FROM projects p
      LEFT JOIN tasks t ON t.project_id = p.id
      LEFT JOIN project_materials m ON m.project_id = p.id
      LEFT JOIN time_entries te ON te.project_id = p.id
      WHERE p.id = ${projectId}
      GROUP BY p.id
    `;

    return metrics[0];
  }
}
```

### Caching Strategy

```typescript
// Redis caching implementation
class CacheService {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: 0,
      retryStrategy: (times) => Math.min(times * 50, 2000)
    });
  }

  async get<T>(key: string): Promise<T | null> {
    const data = await this.redis.get(key);
    return data ? JSON.parse(data) : null;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serialized = JSON.stringify(value);
    if (ttl) {
      await this.redis.setex(key, ttl, serialized);
    } else {
      await this.redis.set(key, serialized);
    }
  }

  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  // Cache-aside pattern implementation
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    let data = await this.get<T>(key);
    
    if (!data) {
      data = await factory();
      await this.set(key, data, ttl);
    }
    
    return data;
  }
}

// Usage in service layer
class CalculationService {
  constructor(
    private cache: CacheService,
    private repository: CalculationRepository
  ) {}

  async getCalculation(id: string): Promise<Calculation> {
    const cacheKey = `calculation:${id}`;
    
    return await this.cache.getOrSet(
      cacheKey,
      () => this.repository.findById(id),
      3600 // 1 hour TTL
    );
  }

  async performCalculation(input: CalculationInput): Promise<CalculationResult> {
    // Check cache for identical calculations
    const cacheKey = `calc:${hash(input)}`;
    const cached = await this.cache.get<CalculationResult>(cacheKey);
    
    if (cached) {
      return { ...cached, fromCache: true };
    }

    const result = await this.calculate(input);
    
    // Cache the result
    await this.cache.set(cacheKey, result, 86400); // 24 hour TTL
    
    return result;
  }
}
```

### Connection Pooling

```typescript
// Database connection pooling configuration
import { Pool } from 'pg';

const pool = new Pool({
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20, // Maximum pool size
  idleTimeoutMillis: 30000, // Close idle clients after 30s
  connectionTimeoutMillis: 2000, // Return error after 2s if no connection available
});

// Prisma connection pooling
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: ['error', 'warn'],
  // Connection pool configuration
  // These are passed to the underlying database driver
  // For PostgreSQL:
  // ?connection_limit=20&pool_timeout=2
});

// Redis connection pooling
const redisPool = createPool({
  create: async () => {
    const client = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD
    });
    
    await client.ping();
    return client;
  },
  destroy: async (client) => {
    await client.quit();
  },
  min: 2,
  max: 10,
  acquireTimeoutMillis: 3000
});
```

### API Response Optimization

```typescript
// Field filtering
router.get('/api/projects', async (req, res) => {
  const fields = req.query.fields?.split(',') || [];
  const include = req.query.include?.split(',') || [];
  
  const projects = await prisma.project.findMany({
    select: fields.length > 0 ? 
      Object.fromEntries(fields.map(f => [f, true])) : 
      undefined,
    include: include.length > 0 ?
      Object.fromEntries(include.map(i => [i, true])) :
      undefined
  });
  
  res.json(projects);
});

// Response compression
import compression from 'compression';

app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6 // Balance between speed and compression ratio
}));

// ETags for caching
import etag from 'etag';

app.use((req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data: any) {
    if (req.method === 'GET' && res.statusCode === 200) {
      const etagValue = etag(JSON.stringify(data));
      res.set('ETag', etagValue);
      
      if (req.headers['if-none-match'] === etagValue) {
        return res.status(304).end();
      }
    }
    
    return originalSend.call(this, data);
  };
  
  next();
});
```

## 💾 Database Optimization

### Indexing Strategy

```sql
-- Composite indexes for common queries
CREATE INDEX idx_projects_user_status_date 
ON projects(user_id, status, created_at DESC)
WHERE deleted_at IS NULL;

-- Partial indexes for filtered queries
CREATE INDEX idx_projects_active 
ON projects(updated_at DESC) 
WHERE status = 'active' AND deleted_at IS NULL;

-- Expression indexes for calculated fields
CREATE INDEX idx_materials_search 
ON materials USING gin(
  to_tsvector('english', name || ' ' || coalesce(description, ''))
);

-- BRIN indexes for time-series data
CREATE INDEX idx_time_entries_created 
ON time_entries USING brin(created_at);

-- Covering indexes to avoid table lookups
CREATE INDEX idx_calculations_covering 
ON calculations(project_id, type, created_at) 
INCLUDE (result, user_id);
```

### Query Optimization

```sql
-- Use CTEs for complex queries
WITH project_stats AS (
  SELECT 
    project_id,
    COUNT(*) as task_count,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks
  FROM tasks
  GROUP BY project_id
),
material_costs AS (
  SELECT 
    project_id,
    SUM(quantity * unit_cost) as total_cost
  FROM project_materials
  GROUP BY project_id
)
SELECT 
  p.*,
  COALESCE(ps.task_count, 0) as task_count,
  COALESCE(ps.completed_tasks, 0) as completed_tasks,
  COALESCE(mc.total_cost, 0) as material_cost
FROM projects p
LEFT JOIN project_stats ps ON p.id = ps.project_id
LEFT JOIN material_costs mc ON p.id = mc.project_id
WHERE p.user_id = $1 AND p.status = 'active';

-- Use window functions for ranking
SELECT 
  *,
  ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY created_at DESC) as rn
FROM projects
WHERE rn <= 5; -- Last 5 projects per customer

-- Optimize pagination with keyset pagination
SELECT * FROM projects
WHERE (created_at, id) < ($1, $2)
ORDER BY created_at DESC, id DESC
LIMIT 20;
```

### Database Configuration

```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1; -- For SSD
ALTER SYSTEM SET effective_io_concurrency = 200; -- For SSD
ALTER SYSTEM SET work_mem = '32MB';
ALTER SYSTEM SET min_wal_size = '1GB';
ALTER SYSTEM SET max_wal_size = '4GB';

-- Connection pooling settings
ALTER SYSTEM SET max_connections = 200;

-- Parallel query settings
ALTER SYSTEM SET max_parallel_workers_per_gather = 4;
ALTER SYSTEM SET max_parallel_workers = 8;
ALTER SYSTEM SET parallel_leader_participation = on;

-- Reload configuration
SELECT pg_reload_conf();
```

## 📱 Mobile Performance

### React Native Optimization

```typescript
// Use InteractionManager for expensive operations
import { InteractionManager } from 'react-native';

const ExpensiveComponent: React.FC = () => {
  const [data, setData] = useState(null);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      // Expensive operation after animations complete
      loadExpensiveData().then(setData);
    });
  }, []);

  return data ? <DataDisplay data={data} /> : <Skeleton />;
};

// Optimize list rendering
import { FlashList } from '@shopify/flash-list';

const OptimizedList: React.FC<{ items: Item[] }> = ({ items }) => {
  const renderItem = useCallback(({ item }) => (
    <MemoizedItem item={item} />
  ), []);

  const keyExtractor = useCallback((item: Item) => item.id, []);

  return (
    <FlashList
      data={items}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      estimatedItemSize={100}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
    />
  );
};

// Image optimization
import FastImage from 'react-native-fast-image';

const OptimizedImage: React.FC<{ uri: string }> = ({ uri }) => (
  <FastImage
    source={{
      uri,
      priority: FastImage.priority.normal,
      cache: FastImage.cacheControl.immutable
    }}
    resizeMode={FastImage.resizeMode.cover}
    style={styles.image}
  />
);
```

### Offline Performance

```typescript
// Efficient offline data storage
class OfflineStorage {
  private db: SQLiteDatabase;

  async initialize() {
    this.db = await SQLite.openDatabase({
      name: 'electrical.db',
      location: 'default'
    });

    await this.createTables();
    await this.createIndexes();
  }

  private async createIndexes() {
    await this.db.executeSql(`
      CREATE INDEX IF NOT EXISTS idx_projects_sync 
      ON projects(sync_status, updated_at);
      
      CREATE INDEX IF NOT EXISTS idx_calculations_project 
      ON calculations(project_id, created_at DESC);
      
      CREATE INDEX IF NOT EXISTS idx_queue_status 
      ON sync_queue(status, priority DESC, created_at);
    `);
  }

  // Batch operations for better performance
  async batchInsert(table: string, records: any[]) {
    const chunks = chunk(records, 100); // Process in chunks
    
    for (const chunk of chunks) {
      await this.db.transaction(async (tx) => {
        for (const record of chunk) {
          await tx.executeSql(
            `INSERT OR REPLACE INTO ${table} VALUES (${placeholders(record)})`,
            Object.values(record)
          );
        }
      });
    }
  }

  // Optimize sync queries
  async getUnsyncedRecords(limit: number = 50) {
    const results = await this.db.executeSql(`
      SELECT * FROM sync_queue 
      WHERE status = 'pending' 
      ORDER BY priority DESC, created_at ASC 
      LIMIT ?
    `, [limit]);

    return results[0].rows.raw();
  }
}
```

## 🔍 Monitoring & Profiling

### Performance Monitoring Setup

```typescript
// Frontend monitoring with Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

const sendToAnalytics = (metric: Metric) => {
  // Send to your analytics endpoint
  fetch('/api/analytics/performance', {
    method: 'POST',
    body: JSON.stringify({
      name: metric.name,
      value: metric.value,
      delta: metric.delta,
      id: metric.id,
      url: window.location.href,
      timestamp: Date.now()
    })
  });
};

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);

// Custom performance marks
performance.mark('calculation-start');
await performCalculation();
performance.mark('calculation-end');
performance.measure('calculation-duration', 'calculation-start', 'calculation-end');

const measure = performance.getEntriesByName('calculation-duration')[0];
console.log(`Calculation took ${measure.duration}ms`);
```

### Backend Performance Monitoring

```typescript
// Prometheus metrics
import { register, Counter, Histogram, Gauge } from 'prom-client';

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

const calculationCounter = new Counter({
  name: 'calculations_total',
  help: 'Total number of calculations performed',
  labelNames: ['type', 'status']
});

// Middleware to track metrics
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || 'unknown', res.statusCode.toString())
      .observe(duration);
  });
  
  next();
});

// Expose metrics endpoint
app.get('/metrics', async (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});
```

## ✅ Performance Checklist

### Development
- [ ] Enable React DevTools Profiler
- [ ] Use Chrome DevTools Performance tab
- [ ] Run Lighthouse audits regularly
- [ ] Monitor bundle sizes with webpack-bundle-analyzer
- [ ] Profile database queries with EXPLAIN ANALYZE

### Before Deployment
- [ ] Run load tests with K6/Artillery
- [ ] Verify all database indexes are in place
- [ ] Check for N+1 queries
- [ ] Ensure proper caching headers
- [ ] Validate image optimization
- [ ] Test on slower devices/networks

### Production Monitoring
- [ ] Set up Real User Monitoring (RUM)
- [ ] Configure APM (Sentry, New Relic)
- [ ] Monitor database slow queries
- [ ] Track API response times
- [ ] Set up alerts for performance degradation
- [ ] Regular performance audits

## 📚 Additional Resources

- [Web.dev Performance Guide](https://web.dev/performance/)
- [React Performance Documentation](https://react.dev/learn/render-and-commit)
- [PostgreSQL Performance Tuning](https://wiki.postgresql.org/wiki/Performance_Optimization)
- [Node.js Performance Best Practices](https://nodejs.org/en/docs/guides/simple-profiling/)

---

Remember: Measure first, optimize second. Always profile before optimizing to ensure you're solving the right problems.