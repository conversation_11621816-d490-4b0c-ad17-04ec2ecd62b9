{"name": "electrical-backend", "version": "1.0.0", "description": "Backend API for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "node dist/index.js", "start": "node dist/index.js"}, "dependencies": {"express": "^4.18.2", "dotenv": "^16.3.1", "@prisma/client": "^5.8.1", "cors": "^2.8.5", "helmet": "^7.1.0", "winston": "^3.11.0", "zod": "^3.22.4", "decimal.js": "^10.4.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "keywords": [], "author": "", "license": "ISC"}