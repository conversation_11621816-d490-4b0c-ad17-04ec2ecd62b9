{"name": "@electrical/backend", "version": "1.0.0", "description": "Backend API for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "node dist/index.js", "dev:watch": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:html": "jest --coverage --coverageReporters=html", "test:load-calculation": "jest src/services/calculations/__tests__/load-calculation.test.ts", "test:voltage-drop": "jest src/services/calculations/__tests__/voltage-drop.test.ts", "test:wire-size": "jest src/services/calculations/__tests__/wire-size.test.ts", "test:conduit-fill": "jest src/services/calculations/__tests__/conduit-fill.test.ts", "test:calculations": "jest src/services/calculations/__tests__", "test:ci": "jest --ci --coverage --maxWorkers=2", "lint": "eslint src --ext .ts", "typecheck": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx src/database/seed.ts"}, "dependencies": {"@prisma/client": "^5.8.1", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "bullmq": "^5.1.5", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^3.3.1", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "rate-limiter-flexible": "^3.0.0", "socket.io": "^4.6.1", "validator": "^13.11.0", "winston": "^3.11.0", "zod": "^3.22.4", "qrcode": "^1.5.3", "csv-parser": "^3.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.11.5", "@types/validator": "^13.11.7", "@types/qrcode": "^1.5.5", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "prisma": "^5.8.1", "supertest": "^6.3.4", "tsx": "^4.7.0", "typescript": "^5.3.3"}}