# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Electrical Contracting Application - a comprehensive SaaS platform for electrical contractors with web, mobile, and AI agent components. It's built as a monorepo using pnpm workspaces with the following structure:

- `/app/backend` - Node.js/Express API with Prisma ORM
- `/app/frontend` - React web application 
- `/app/mobile` - React Native mobile app
- `/app/agents` - Multi-agent AI system
- `/app/shared` - Shared TypeScript types

## Essential Development Commands

### Initial Setup and Development
```bash
# Install all dependencies (from /app directory)
pnpm install

# Start main application (backend + frontend)
pnpm run dev:main

# Or use the interactive startup
START_HERE.bat
```

### Testing
```bash
# Run all tests
pnpm run test

# Backend calculation tests
cd app/backend && npm run test:calculations

# Frontend tests
cd app/frontend && npm run test

# Mobile tests  
cd app/mobile && npm run test
```

### Code Quality
```bash
# Lint and type check all workspaces
pnpm run lint
pnpm run typecheck
```

### Database Operations
```bash
cd app/backend

# Apply migrations
npx prisma migrate deploy

# Create new migration
npx prisma migrate dev

# Seed database
npm run db:seed

# Open Prisma Studio
npx prisma studio
```

## Architecture Patterns

### Backend Architecture
- **Layered Architecture**: Routes → Services → Database
- **Security Middleware**: Authentication, rate limiting, CSRF protection
- **Event-Driven**: EventEmitter for domain events, BullMQ for job processing
- **Real-time**: Socket.io for live updates
- **Field Encryption**: Automatic encryption for sensitive data (SSN, bank accounts)

### Frontend Architecture
- **State Management**: Zustand for global state, TanStack Query for server state
- **Component Structure**: Feature-based organization with shared components
- **Protected Routes**: Authentication-based routing
- **Offline Support**: Custom hooks for offline sync

### Mobile Architecture
- **Offline-First**: Complete offline capability with SQLite and sync
- **Security**: Biometric auth, secure storage, jail detection
- **Background Sync**: Automatic data synchronization

### AI Agent System
- **Multi-Agent**: Specialized agents communicating via message bus
- **Memory Store**: ChromaDB for vector storage, Neo4j for relationships
- **Task Routing**: Capability-based routing through Project Manager agent

## Key Domain Concepts

### Electrical Calculations
- **Load Calculations**: NEC Article 220 compliant
- **Voltage Drop**: Per NEC requirements
- **Wire Sizing**: Based on ampacity tables
- **Conduit Fill**: NEC Chapter 9 tables
- **Arc Flash**: IEEE 1584-2018 calculations
- **Short Circuit**: Fault current analysis

### Data Model
- **Projects** → **Panels** → **Circuits** hierarchy
- **Estimates** with versioning and material/labor items
- **Permits** and **Inspections** workflow
- **Users** with roles: electrician, foreman, admin, estimator

## Development Guidelines

### Code Conventions
- TypeScript strict mode enabled
- Zod schemas for runtime validation
- Consistent error handling with typed errors
- No console.log in production code
- Prefer functional components in React

### Security Requirements
- Never commit secrets or API keys
- Use environment variables for configuration
- Validate all user inputs
- Sanitize outputs to prevent XSS
- Use parameterized queries (handled by Prisma)

### Testing Requirements
- Test electrical calculations thoroughly
- Mock external services in tests
- Test offline scenarios for mobile
- Ensure accessibility compliance

## Common Development Tasks

### Adding a New API Endpoint
1. Define Zod schema in `/app/shared/src/schemas/`
2. Create route in `/app/backend/src/routes/`
3. Implement service in `/app/backend/src/services/`
4. Add tests in `__tests__` directory
5. Update API documentation

### Adding a New Component
1. Check existing components for patterns
2. Use TypeScript interfaces for props
3. Implement accessibility features
4. Add to appropriate feature directory
5. Create stories for Storybook if applicable

### Working with Electrical Calculations
1. Reference NEC tables in `/app/backend/src/data/`
2. Use calculation services in `/app/backend/src/services/calculations/`
3. Validate against test cases in `__tests__`
4. Ensure results match NEC requirements

## Important URLs
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- API Docs: http://localhost:3001/api-docs

## Troubleshooting

### Common Issues
- If migrations fail, check `app/backend/prisma/migrations/`
- For dependency issues, delete `node_modules` and run `pnpm install`
- Mobile build issues often require `cd app/mobile && npm run clean`
- Agent memory issues may require ChromaDB reset

### Environment Requirements
- Node.js 18+ (20+ recommended)
- pnpm package manager
- SQLite for development
- Redis for caching (optional in dev)