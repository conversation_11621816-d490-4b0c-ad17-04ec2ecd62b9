import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '../stores/auth';
import { errorService } from './errorService';
import { ApiInterceptors, apiClient } from '../utils/api-interceptors';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/error-handler';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Use the enhanced API client with interceptors
export const api = apiClient;

// Token management
let authToken: string | null = null;

export const setAuthToken = (token: string | null): void => {
  authToken = token;
  if (token) {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete api.defaults.headers.common['Authorization'];
  }
};

// Reset correlation ID on route change
export const resetCorrelationId = (): void => {
  ApiInterceptors.resetCorrelationId();
};

// Note: Interceptors are already set up in ApiInterceptors
// The following interceptors are kept for backward compatibility
// but the main logic is in api-interceptors.ts

// Additional custom interceptor for app-specific needs
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Any additional app-specific request modifications
    return config;
  },
  (error: AxiosError) => {
    // Let the enhanced error handler manage this
    return Promise.reject(error); 
    });
    return Promise.reject(error);
  }
);

// Response interceptor
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: Error | AxiosError) => void;
}> = [];

const processQueue = (error: AxiosError | null): void => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve();
    }
  });
  
  failedQueue = [];
};

api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
      correlationId?: string;
    };
    
    // Log non-401 errors
    if (error.response?.status !== 401) {
      errorService.logError(error, {
        phase: 'response',
        correlationId: originalRequest?.correlationId,
        url: originalRequest?.url,
        method: originalRequest?.method,
        status: error.response?.status,
      });
    }
    
    // Handle 401 Unauthorized - token refresh
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(() => api(originalRequest))
          .catch((err) => Promise.reject(err));
      }
      
      originalRequest._retry = true;
      isRefreshing = true;
      
      try {
        await useAuthStore.getState().refreshAccessToken();
        processQueue(null);
        return api(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError as AxiosError);
        useAuthStore.getState().logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    // Handle other specific error codes
    if (error.response?.status === 503) {
      errorService.logWarning('Service temporarily unavailable', {
        url: originalRequest?.url,
        correlationId: originalRequest?.correlationId,
      });
    }
    
    if (error.code === 'ECONNABORTED') {
      errorService.logWarning('Request timeout', {
        url: originalRequest?.url,
        correlationId: originalRequest?.correlationId,
        timeout: originalRequest?.timeout,
      });
    }
    
    return Promise.reject(error);
  }
);