import { rateLimitMiddleware } from './rate-limiting';

/**
 * Utility to clear rate limits - useful for development/testing
 */
export async function clearRateLimits() {
  console.log('Rate limit clearing is not available for in-memory rate limiters');
  console.log('Please restart the server to clear in-memory rate limits');
}

// For development mode, add a reset endpoint
export function addRateLimitResetEndpoint(app: any) {
  if (process.env.NODE_ENV === 'development') {
    app.post('/api/dev/reset-rate-limits', async (req: any, res: any) => {
      // In development, we can just restart the rate limiters
      console.log('Rate limit reset requested in development mode');
      res.json({ 
        message: 'Rate limits are in-memory. Restart the server to clear them.',
        note: 'This endpoint is only available in development mode'
      });
    });
  }
}