const { faker } = require('@faker-js/faker');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Test data generation functions
function generateTestUsers(count = 100) {
  const users = [];
  
  for (let i = 0; i < count; i++) {
    users.push({
      email: `loadtest${i}@example.com`,
      password: 'LoadTest123!',
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      company: faker.company.name(),
      licenseNumber: `EL${faker.number.int({ min: 100000, max: 999999 })}`,
      phone: faker.phone.number(),
    });
  }
  
  // Add some admin users
  users.push({
    email: '<EMAIL>',
    password: 'AdminTest123!',
    firstName: 'Admin',
    lastName: 'User',
    company: 'Load Test Corp',
    licenseNumber: 'EL999999',
    role: 'admin',
  });
  
  return users;
}

// Artillery processor functions
module.exports = {
  setupTestEnvironment: function(context, events, done) {
    console.log('Setting up test environment...');
    
    // Generate test users if not exists
    const usersFile = path.join(__dirname, '../data/test-users.csv');
    if (!fs.existsSync(usersFile)) {
      const users = generateTestUsers();
      const csv = 'email,password\\n' + 
        users.map(u => `${u.email},${u.password}`).join('\\n');
      
      fs.mkdirSync(path.dirname(usersFile), { recursive: true });
      fs.writeFileSync(usersFile, csv);
      
      // Also save as JSON for k6
      fs.writeFileSync(
        path.join(__dirname, '../data/test-users.json'),
        JSON.stringify(users, null, 2)
      );
    }
    
    done();
  },

  generateNewUser: function(context, events, done) {
    context.vars.newEmail = faker.internet.email();
    context.vars.newPassword = 'Test123!' + faker.string.alphanumeric(5);
    context.vars.firstName = faker.person.firstName();
    context.vars.lastName = faker.person.lastName();
    context.vars.company = faker.company.name();
    context.vars.licenseNumber = `EL${faker.number.int({ min: 100000, max: 999999 })}`;
    done();
  },

  authenticateUser: function(context, events, done) {
    if (!context.vars.authToken) {
      // Simulate login - in real scenario, this would make an API call
      context.vars.authToken = 'test-token-' + crypto.randomBytes(16).toString('hex');
      context.vars.userId = faker.string.uuid();
    }
    done();
  },

  generateAuthHeader: function(context, events, done) {
    if (!context.vars.authToken) {
      context.vars.authToken = 'test-token-' + crypto.randomBytes(16).toString('hex');
    }
    done();
  },

  generateVoltageDropData: function(context, events, done) {
    context.vars.voltageDropData = {
      voltage: faker.helpers.arrayElement([120, 208, 240, 277, 480]),
      current: faker.number.int({ min: 20, max: 400 }),
      distance: faker.number.int({ min: 50, max: 1000 }),
      conductorSize: faker.helpers.arrayElement(['14', '12', '10', '8', '6', '4', '2', '1', '1/0', '2/0', '3/0', '4/0', '250kcmil', '350kcmil', '500kcmil']),
      conductorType: faker.helpers.arrayElement(['copper', 'aluminum']),
      conduitType: faker.helpers.arrayElement(['steel', 'pvc', 'aluminum']),
      powerFactor: faker.number.float({ min: 0.7, max: 0.95, precision: 0.01 }),
      phases: faker.helpers.arrayElement([1, 3]),
      parallel: faker.number.int({ min: 1, max: 4 }),
      temperature: faker.number.int({ min: 60, max: 140 }),
    };
    done();
  },

  generateShortCircuitData: function(context, events, done) {
    context.vars.shortCircuitData = {
      transformerKVA: faker.helpers.arrayElement([500, 750, 1000, 1500, 2000, 2500]),
      transformerImpedance: faker.number.float({ min: 2, max: 8, precision: 0.1 }),
      primaryVoltage: faker.helpers.arrayElement([4160, 12470, 13800, 34500]),
      secondaryVoltage: faker.helpers.arrayElement([208, 240, 480, 600]),
      motorContribution: faker.datatype.boolean(),
      cableLength: faker.number.int({ min: 0, max: 2000 }),
      cableSize: faker.helpers.arrayElement(['1/0', '4/0', '250kcmil', '350kcmil', '500kcmil', '750kcmil']),
      conduitsPerPhase: faker.number.int({ min: 1, max: 6 }),
      cableMaterial: faker.helpers.arrayElement(['copper', 'aluminum']),
      conduitMaterial: faker.helpers.arrayElement(['steel', 'pvc', 'aluminum']),
    };
    done();
  },

  generateArcFlashData: function(context, events, done) {
    context.vars.arcFlashData = {
      systemVoltage: faker.helpers.arrayElement([208, 480, 600, 4160, 13800]),
      faultCurrent: faker.number.int({ min: 5000, max: 65000 }),
      faultClearingTime: faker.number.float({ min: 0.01, max: 2, precision: 0.01 }),
      workingDistance: faker.number.int({ min: 18, max: 60 }),
      enclosureType: faker.helpers.arrayElement(['open', 'box', 'mcc', 'panel', 'switchgear']),
      groundingType: faker.helpers.arrayElement(['solidly-grounded', 'resistance-grounded', 'ungrounded']),
      conductorGap: faker.number.int({ min: 13, max: 153 }),
      arcDuration: faker.number.float({ min: 0.01, max: 2, precision: 0.01 }),
    };
    done();
  },

  generateCircuitData: function(context, events, done) {
    context.vars.circuitData = {
      circuitNumber: faker.number.int({ min: 1, max: 42 }),
      description: faker.helpers.arrayElement([
        'Lighting Circuit',
        'Receptacle Circuit',
        'HVAC Unit',
        'Panel Feeder',
        'Motor Circuit',
        'Equipment Circuit',
        'Emergency Lighting',
        'Computer Room',
        'Kitchen Equipment',
        'Elevator',
      ]) + ' ' + faker.number.int({ min: 1, max: 99 }),
      amperage: faker.helpers.arrayElement([15, 20, 25, 30, 40, 50, 60, 70, 80, 90, 100]),
      voltage: faker.helpers.arrayElement([120, 208, 240, 277, 480]),
      phase: faker.helpers.arrayElement(['A', 'B', 'C', 'AB', 'BC', 'CA', 'ABC']),
      wireSize: faker.helpers.arrayElement(['14', '12', '10', '8', '6', '4', '2', '1']),
      breakerType: faker.helpers.arrayElement(['Standard', 'GFCI', 'AFCI', 'CAFCI', 'EPD']),
      poles: faker.helpers.arrayElement([1, 2, 3]),
    };
    done();
  },

  generatePanelData: function(context, events, done) {
    const voltage = faker.helpers.arrayElement([120, 208, 240, 480]);
    const phases = voltage === 120 ? 1 : 3;
    
    context.vars.panelData = {
      name: faker.helpers.arrayElement(['MDP', 'LP', 'PP', 'DP']) + '-' + faker.number.int({ min: 1, max: 99 }),
      location: faker.helpers.arrayElement([
        'Electrical Room',
        'Basement',
        'First Floor',
        'Second Floor',
        'Mechanical Room',
        'Kitchen',
        'Penthouse',
      ]),
      voltage: voltage,
      phases: phases,
      amperage: faker.helpers.arrayElement([100, 200, 225, 400, 600, 800, 1200, 1600, 2000]),
      mainBreakerSize: faker.helpers.arrayElement([100, 200, 225, 400, 600, 800, 1200, 1600, 2000]),
      spaces: faker.helpers.arrayElement([20, 30, 42, 54, 66, 84]),
      mounting: faker.helpers.arrayElement(['Surface', 'Flush', 'Free-standing']),
      enclosure: faker.helpers.arrayElement(['NEMA 1', 'NEMA 3R', 'NEMA 4', 'NEMA 4X']),
      manufacturer: faker.helpers.arrayElement(['Square D', 'Siemens', 'GE', 'Eaton', 'ABB']),
      modelNumber: faker.string.alphanumeric(10).toUpperCase(),
    };
    done();
  },

  generateMaterialList: function(context, events, done) {
    const items = [];
    const count = faker.number.int({ min: 5, max: 20 });
    
    for (let i = 0; i < count; i++) {
      items.push({
        materialId: 'MAT' + faker.string.alphanumeric(8).toUpperCase(),
        quantity: faker.number.int({ min: 1, max: 1000 }),
        unit: faker.helpers.arrayElement(['EA', 'FT', 'LF', 'BOX', 'ROLL', 'SET']),
      });
    }
    
    context.vars.materialList = items;
    done();
  },

  generateEstimateData: function(context, events, done) {
    context.vars.estimateData = {
      name: faker.company.catchPhrase() + ' Project',
      projectId: faker.string.uuid(),
      customerId: faker.string.uuid(),
      status: 'draft',
      validUntil: faker.date.future().toISOString(),
      terms: faker.helpers.arrayElement([
        'Net 30',
        'Net 60',
        '50% deposit, Net 30',
        '2/10 Net 30',
      ]),
      notes: faker.lorem.paragraph(),
      taxRate: faker.number.float({ min: 0, max: 0.15, precision: 0.001 }),
      overhead: faker.number.float({ min: 0.1, max: 0.3, precision: 0.01 }),
      profit: faker.number.float({ min: 0.1, max: 0.3, precision: 0.01 }),
    };
    done();
  },

  generateLineItem: function(context, events, done) {
    context.vars.lineItem = {
      description: faker.commerce.productName(),
      category: faker.helpers.arrayElement(['Material', 'Labor', 'Equipment', 'Subcontractor', 'Other']),
      quantity: faker.number.int({ min: 1, max: 1000 }),
      unit: faker.helpers.arrayElement(['EA', 'HR', 'FT', 'LS', 'DAY']),
      unitCost: faker.number.float({ min: 1, max: 1000, precision: 0.01 }),
      markup: faker.number.float({ min: 0, max: 0.5, precision: 0.01 }),
      taxable: faker.datatype.boolean(),
      notes: faker.lorem.sentence(),
    };
    done();
  },

  generateConduitFillData: function(context, events, done) {
    const conductors = [];
    const count = faker.number.int({ min: 3, max: 20 });
    
    for (let i = 0; i < count; i++) {
      conductors.push({
        size: faker.helpers.arrayElement(['14', '12', '10', '8', '6', '4', '2', '1', '1/0', '2/0']),
        type: faker.helpers.arrayElement(['THHN', 'THWN', 'XHHW', 'USE', 'RHH']),
        count: faker.number.int({ min: 1, max: 10 }),
      });
    }
    
    context.vars.conduitFillData = {
      conduitType: faker.helpers.arrayElement(['EMT', 'IMC', 'RMC', 'PVC', 'LFNC', 'LFMC']),
      conduitSize: faker.helpers.arrayElement(['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '4']),
      conductors: conductors,
    };
    done();
  },

  generateWireSizeData: function(context, events, done) {
    context.vars.wireSizeData = {
      voltage: faker.helpers.arrayElement([120, 208, 240, 277, 480, 600]),
      current: faker.number.int({ min: 10, max: 800 }),
      distance: faker.number.int({ min: 10, max: 2000 }),
      voltageDrop: faker.number.float({ min: 1, max: 5, precision: 0.1 }),
      ambientTemp: faker.number.int({ min: 60, max: 140 }),
      conductorType: faker.helpers.arrayElement(['copper', 'aluminum']),
      insulation: faker.helpers.arrayElement(['THHN', 'THWN', 'XHHW', 'USE-2']),
      conduitType: faker.helpers.arrayElement(['steel', 'pvc', 'aluminum', 'free-air']),
      conductorsPerConduit: faker.number.int({ min: 1, max: 9 }),
      powerFactor: faker.number.float({ min: 0.7, max: 1, precision: 0.01 }),
      phases: faker.helpers.arrayElement([1, 3]),
    };
    done();
  },

  generateReport: function(context, events, done) {
    const timestamp = new Date().toISOString();
    const resultDir = path.join(__dirname, '../results');
    
    if (!fs.existsSync(resultDir)) {
      fs.mkdirSync(resultDir, { recursive: true });
    }
    
    // Log test completion
    console.log(`Load test completed at ${timestamp}`);
    console.log(`Results will be saved to ${resultDir}`);
    
    done();
  },

  // Stress test specific functions
  setupStressTest: function(context, events, done) {
    console.log('Preparing for stress test...');
    context.vars.stressTestId = faker.string.uuid();
    done();
  },

  setupSpikeTest: function(context, events, done) {
    console.log('Preparing for spike test...');
    context.vars.spikeTestId = faker.string.uuid();
    done();
  },

  setupEnduranceTest: function(context, events, done) {
    console.log('Starting endurance test...');
    context.vars.enduranceTestId = faker.string.uuid();
    context.vars.startTime = Date.now();
    done();
  },

  analyzeMemoryLeaks: function(context, events, done) {
    console.log('Analyzing for memory leaks...');
    // In a real scenario, this would check memory metrics
    done();
  },

  checkResourceExhaustion: function(context, events, done) {
    console.log('Checking for resource exhaustion...');
    // In a real scenario, this would check system resources
    done();
  },
};

// Generate initial test data when module loads
if (require.main === module) {
  console.log('Generating test data...');
  
  const dataDir = path.join(__dirname, '../data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // Generate test users
  const users = generateTestUsers(200);
  const usersCsv = 'email,password\\n' + 
    users.map(u => `${u.email},${u.password}`).join('\\n');
  
  fs.writeFileSync(path.join(dataDir, 'test-users.csv'), usersCsv);
  fs.writeFileSync(path.join(dataDir, 'test-users.json'), JSON.stringify(users, null, 2));
  
  console.log(`Generated ${users.length} test users`);
  console.log('Test data generation complete!');
}