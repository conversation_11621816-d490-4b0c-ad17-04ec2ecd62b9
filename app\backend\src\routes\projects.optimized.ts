import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../index';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';
import { ProjectSchema } from '@electrical/shared';
import { emitToProject } from '../socket';
import { io } from '../index';
import { CacheService } from '../services/cache.service';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Pagination and filter schema
const projectQuerySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  search: z.string().optional(),
  status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']).optional(),
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']).optional(),
  customer_id: z.string().uuid().optional(),
  sortBy: z.enum(['name', 'created_at', 'updated_at', 'status']).default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Create project schema
const createProjectSchema = ProjectSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});

// Update project schema
const updateProjectSchema = createProjectSchema.partial();

// Get all projects - OPTIMIZED
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const params = projectQuerySchema.parse(req.query);
    
    // Check cache first
    const cacheKey = { ...params, userId: req.user?.userId };
    const cached = await CacheService.getCachedListQuery('projects', cacheKey);
    if (cached) {
      return res.json(cached);
    }
    
    const skip = (params.page - 1) * params.limit;
    
    // Build where clause
    const where = {
      ...(params.search && {
        OR: [
          { name: { contains: params.search, mode: 'insensitive' as const } },
          { address: { contains: params.search, mode: 'insensitive' as const } },
          { permit_number: { contains: params.search, mode: 'insensitive' as const } }
        ]
      }),
      ...(params.status && { status: params.status }),
      ...(params.type && { type: params.type }),
      ...(params.customer_id && { customer_id: params.customer_id })
    };
    
    // Optimized query with selective field loading
    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        skip,
        take: params.limit,
        orderBy: { [params.sortBy]: params.sortOrder },
        select: {
          id: true,
          customer_id: true,
          name: true,
          address: true,
          city: true,
          state: true,
          zip: true,
          type: true,
          status: true,
          voltage_system: true,
          service_size: true,
          square_footage: true,
          permit_number: true,
          inspection_status: true,
          created_at: true,
          updated_at: true,
          // Optimized relation loading
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              type: true
            }
          },
          // Use _count for aggregates instead of loading all relations
          _count: {
            select: {
              estimates: true,
              calculations: true,
              panels: true
            }
          }
        }
      }),
      prisma.project.count({ where })
    ]);
    
    const result = {
      data: projects,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    };
    
    // Cache the result
    await CacheService.cacheListQuery('projects', cacheKey, result);
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Get project by ID - OPTIMIZED
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    // Use select to load only needed fields
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      select: {
        id: true,
        customer_id: true,
        name: true,
        address: true,
        city: true,
        state: true,
        zip: true,
        type: true,
        status: true,
        voltage_system: true,
        service_size: true,
        square_footage: true,
        permit_number: true,
        permit_expiry: true,
        inspection_status: true,
        panel_count: true,
        main_disconnect_type: true,
        grounding_system: true,
        has_generator: true,
        has_solar: true,
        created_at: true,
        updated_at: true,
        // Optimized customer loading
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            type: true,
            credit_limit: true,
            payment_terms: true
          }
        },
        // Limited recent estimates
        estimates: {
          orderBy: { created_at: 'desc' },
          take: 5,
          select: {
            id: true,
            version: true,
            status: true,
            total_amount: true,
            created_at: true,
            valid_until: true,
            _count: {
              select: {
                material_items: true,
                labor_items: true
              }
            }
          }
        },
        // Limited recent calculations
        calculations: {
          orderBy: { created_at: 'desc' },
          take: 10,
          select: {
            id: true,
            calculation_type: true,
            created_at: true,
            user: {
              select: {
                name: true
              }
            }
          }
        },
        // Panel summary
        panels: {
          select: {
            id: true,
            name: true,
            panel_type: true,
            ampere_rating: true,
            spaces_total: true,
            spaces_used: true
          }
        }
      }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

// Create project - OPTIMIZED
router.post('/', authorize('admin', 'estimator', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const data = createProjectSchema.parse(req.body);
    
    // Verify customer exists - optimized query
    const customerExists = await prisma.customer.findFirst({
      where: {
        id: data.customer_id,
        deleted_at: null
      },
      select: { id: true } // Only select id for existence check
    });
    
    if (!customerExists) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    const project = await prisma.project.create({
      data,
      select: {
        id: true,
        customer_id: true,
        name: true,
        address: true,
        city: true,
        state: true,
        zip: true,
        type: true,
        status: true,
        voltage_system: true,
        service_size: true,
        created_at: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    // Invalidate project list cache
    await CacheService.invalidatePattern('list:projects');
    
    res.status(201).json(project);
  } catch (error) {
    next(error);
  }
});

// Update project - OPTIMIZED
router.put('/:id', authorize('admin', 'estimator', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const data = updateProjectSchema.parse(req.body);
    
    // Check if project exists - optimized
    const existing = await prisma.project.findUnique({
      where: { id: req.params.id },
      select: { id: true }
    });
    
    if (!existing) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    // If changing customer, verify new customer exists
    if (data.customer_id) {
      const customerExists = await prisma.customer.findFirst({
        where: {
          id: data.customer_id,
          deleted_at: null
        },
        select: { id: true }
      });
      
      if (!customerExists) {
        throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
      }
    }
    
    const project = await prisma.project.update({
      where: { id: req.params.id },
      data,
      select: {
        id: true,
        customer_id: true,
        name: true,
        address: true,
        city: true,
        state: true,
        zip: true,
        type: true,
        status: true,
        voltage_system: true,
        service_size: true,
        updated_at: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    // Emit update to connected clients
    emitToProject(io, project.id, 'project:updated', project);
    
    // Invalidate caches
    await CacheService.invalidatePattern('list:projects');
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

// Get project estimates - OPTIMIZED
router.get('/:id/estimates', async (req: AuthRequest, res, next) => {
  try {
    // Verify project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: req.params.id },
      select: { id: true }
    });
    
    if (!projectExists) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    const estimates = await prisma.estimate.findMany({
      where: { project_id: req.params.id },
      orderBy: { version: 'desc' },
      select: {
        id: true,
        version: true,
        status: true,
        valid_until: true,
        subtotal: true,
        tax_total: true,
        total_amount: true,
        profit_margin: true,
        contingency_percent: true,
        created_at: true,
        updated_at: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        approver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            material_items: true,
            labor_items: true
          }
        }
      }
    });
    
    res.json(estimates);
  } catch (error) {
    next(error);
  }
});

// Get project calculations - OPTIMIZED with pagination
router.get('/:id/calculations', async (req: AuthRequest, res, next) => {
  try {
    const { page = '1', limit = '50' } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;
    
    // Verify project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: req.params.id },
      select: { id: true }
    });
    
    if (!projectExists) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    const [calculations, total] = await Promise.all([
      prisma.calculationLog.findMany({
        where: { project_id: req.params.id },
        orderBy: { created_at: 'desc' },
        skip,
        take: limitNum,
        select: {
          id: true,
          calculation_type: true,
          created_at: true,
          // Parse and return only essential data from JSON
          input_data: true,
          output_data: true,
          nec_references: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.calculationLog.count({
        where: { project_id: req.params.id }
      })
    ]);
    
    res.json({
      data: calculations,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    next(error);
  }
});

export { router as projectsRouterOptimized };