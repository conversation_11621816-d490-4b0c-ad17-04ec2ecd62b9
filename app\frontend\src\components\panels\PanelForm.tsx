import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { z } from 'zod';
import { PanelSchema, Panel } from '@electrical/shared';
import { panelService } from '../../services/panelService';
import { useAuthStore } from '../../stores/auth';

// Real manufacturer data - NEC 2023 compliant
const MANUFACTURERS = [
  { value: 'Square D', label: 'Square D by Schneider Electric' },
  { value: 'Eaton', label: 'Eaton' },
  { value: 'Siemens', label: 'Siemens' },
  { value: 'GE', label: 'GE (General Electric)' },
  { value: '<PERSON>ton', label: '<PERSON><PERSON>' },
  { value: 'Cutler-Hammer', label: '<PERSON><PERSON>-<PERSON> (Eaton)' },
  { value: 'Murray', label: '<PERSON> (Siemens)' },
  { value: 'ITE', label: 'ITE (Siemens)' },
];

const VOLTAGE_SYSTEMS = [
  { value: '120/240V_1PH', label: '120/240V Single Phase' },
  { value: '208V_3PH', label: '208V Three Phase' },
  { value: '240V_3PH', label: '240V Three Phase' },
  { value: '480V_3PH', label: '480V Three Phase' },
  { value: '277/480V_3PH', label: '277/480V Three Phase' },
];

const AMPERE_RATINGS = [100, 125, 150, 200, 225, 400, 600, 800, 1000, 1200, 1600, 2000, 2500, 3000, 4000];

const ENCLOSURE_TYPES = [
  { value: 'NEMA_1', label: 'NEMA 1 - Indoor' },
  { value: 'NEMA_3R', label: 'NEMA 3R - Outdoor Rainproof' },
  { value: 'NEMA_4', label: 'NEMA 4 - Watertight' },
  { value: 'NEMA_4X', label: 'NEMA 4X - Corrosion Resistant' },
];

export const PanelForm: React.FC = () => {
  const navigate = useNavigate();
  const { projectId, panelId } = useParams<{ projectId: string; panelId?: string }>();
  const { token } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [existingPanels, setExistingPanels] = useState<Panel[]>([]);
  
  const [formData, setFormData] = useState({
    project_id: projectId || '',
    name: '',
    location: '',
    panel_type: 'SUB' as const,
    manufacturer: '',
    model_number: '',
    catalog_number: '',
    voltage_system: '120/240V_1PH' as const,
    ampere_rating: 100,
    bus_rating: 100,
    main_breaker_size: undefined as number | undefined,
    phase_config: 'SINGLE_PHASE' as const,
    mounting_type: 'SURFACE' as const,
    enclosure_type: 'NEMA_1' as const,
    spaces_total: 24,
    fed_from_panel_id: '',
    fed_from_circuit: undefined as number | undefined,
    notes: '',
  });

  useEffect(() => {
    if (panelId && token) {
      loadPanel();
    }
    if (projectId && token) {
      loadExistingPanels();
    }
  }, [panelId, projectId, token]);

  useEffect(() => {
    // Auto-update phase configuration based on voltage system
    const voltageSystem = formData.voltage_system;
    if (voltageSystem === '120/240V_1PH') {
      setFormData(prev => ({ ...prev, phase_config: 'SINGLE_PHASE' }));
    } else if (voltageSystem.includes('3PH')) {
      setFormData(prev => ({ 
        ...prev, 
        phase_config: voltageSystem === '277/480V_3PH' ? 'THREE_PHASE_4W' : 'THREE_PHASE_3W' 
      }));
    }
  }, [formData.voltage_system]);

  useEffect(() => {
    // Auto-set bus rating to match ampere rating if not manually set
    setFormData(prev => ({ ...prev, bus_rating: prev.ampere_rating }));
  }, [formData.ampere_rating]);

  const loadPanel = async () => {
    if (!panelId || !token) return;
    
    try {
      setLoading(true);
      const panel = await panelService.getPanel(panelId, token);
      setFormData({
        ...panel,
        main_breaker_size: panel.main_breaker_size || undefined,
        fed_from_circuit: panel.fed_from_circuit || undefined,
      });
    } catch (error) {
      console.error('Error loading panel:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadExistingPanels = async () => {
    if (!projectId || !token) return;
    
    try {
      const panels = await panelService.getPanelsByProject(projectId, token);
      setExistingPanels(panels.filter(p => p.id !== panelId));
    } catch (error) {
      console.error('Error loading panels:', error);
    }
  };

  const getSpaceOptions = () => {
    const baseOptions = [12, 18, 20, 24, 30, 32, 40, 42, 48, 54, 60, 66, 72, 84];
    if (formData.ampere_rating >= 400) {
      return [...baseOptions, 96, 120, 144, 168];
    }
    return baseOptions;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!token) return;

    try {
      setLoading(true);
      setErrors({});

      // Validate with Zod schema
      const validatedData = PanelSchema.parse(formData);

      if (panelId) {
        await panelService.updatePanel(panelId, validatedData, token);
      } else {
        await panelService.createPanel(validatedData, token);
      }

      navigate(`/projects/${projectId}/panels`);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(fieldErrors);
      } else {
        console.error('Error saving panel:', error);
        setErrors({ general: 'Failed to save panel' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? (value === '' ? undefined : Number(value)) : value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {panelId ? 'Edit Panel' : 'New Panel'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {errors.general && (
            <div className="rounded-md bg-red-50 p-4">
              <p className="text-sm text-red-800">{errors.general}</p>
            </div>
          )}

          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Panel Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  placeholder="Main Panel, Sub Panel A, Kitchen Panel"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Location *
                </label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  placeholder="Electrical Room, Basement, Kitchen"
                  required
                />
                {errors.location && (
                  <p className="mt-1 text-sm text-red-600">{errors.location}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Panel Type *
                </label>
                <select
                  name="panel_type"
                  value={formData.panel_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="MAIN">Main Panel</option>
                  <option value="SUB">Sub Panel</option>
                  <option value="DISTRIBUTION">Distribution Panel</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mounting Type *
                </label>
                <select
                  name="mounting_type"
                  value={formData.mounting_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="SURFACE">Surface Mount</option>
                  <option value="FLUSH">Flush Mount</option>
                  <option value="RECESSED">Recessed</option>
                </select>
              </div>
            </div>
          </div>

          {/* Electrical Specifications */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Electrical Specifications
            </h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Voltage System *
                </label>
                <select
                  name="voltage_system"
                  value={formData.voltage_system}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {VOLTAGE_SYSTEMS.map(vs => (
                    <option key={vs.value} value={vs.value}>
                      {vs.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ampere Rating *
                </label>
                <select
                  name="ampere_rating"
                  value={formData.ampere_rating}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {AMPERE_RATINGS.map(rating => (
                    <option key={rating} value={rating}>
                      {rating} Amps
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Main Breaker Size (Leave empty for MLO)
                </label>
                <input
                  type="number"
                  name="main_breaker_size"
                  value={formData.main_breaker_size || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  placeholder="Main Lug Only if empty"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Total Spaces *
                </label>
                <select
                  name="spaces_total"
                  value={formData.spaces_total}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {getSpaceOptions().map(spaces => (
                    <option key={spaces} value={spaces}>
                      {spaces} Spaces
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enclosure Type *
                </label>
                <select
                  name="enclosure_type"
                  value={formData.enclosure_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {ENCLOSURE_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Manufacturer Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Manufacturer Information
            </h3>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Manufacturer
                </label>
                <select
                  name="manufacturer"
                  value={formData.manufacturer}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="">Select manufacturer</option>
                  {MANUFACTURERS.map(mfr => (
                    <option key={mfr.value} value={mfr.value}>
                      {mfr.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Model Number
                </label>
                <input
                  type="text"
                  name="model_number"
                  value={formData.model_number}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  placeholder="QO124M100P, BR2040B100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Catalog Number
                </label>
                <input
                  type="text"
                  name="catalog_number"
                  value={formData.catalog_number}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Feeder Information (for sub panels) */}
          {formData.panel_type !== 'MAIN' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Feeder Information
              </h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Fed From Panel
                  </label>
                  <select
                    name="fed_from_panel_id"
                    value={formData.fed_from_panel_id}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  >
                    <option value="">Select feeding panel</option>
                    {existingPanels.map(panel => (
                      <option key={panel.id} value={panel.id}>
                        {panel.name} ({panel.ampere_rating}A)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Fed From Circuit Number
                  </label>
                  <input
                    type="number"
                    name="fed_from_circuit"
                    value={formData.fed_from_circuit || ''}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                    min="1"
                    disabled={!formData.fed_from_panel_id}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
              placeholder="Additional notes or special requirements"
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate(`/projects/${projectId}/panels`)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : panelId ? 'Update Panel' : 'Create Panel'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};