import { api } from './api';
import { Project } from '@electrical/shared';

export const projectService = {
  /**
   * Get all projects
   */
  async getProjects(): Promise<Project[]> {
    const response = await api.get<Project[]>('/projects');
    return response.data;
  },

  /**
   * Get a single project by ID
   */
  async getProject(projectId: string): Promise<Project> {
    const response = await api.get<Project>(`/projects/${projectId}`);
    return response.data;
  },

  /**
   * Create a new project
   */
  async createProject(project: Partial<Project>): Promise<Project> {
    const response = await api.post<Project>('/projects', project);
    return response.data;
  },

  /**
   * Update an existing project
   */
  async updateProject(projectId: string, project: Partial<Project>): Promise<Project> {
    const response = await api.put<Project>(`/projects/${projectId}`, project);
    return response.data;
  },

  /**
   * Delete a project
   */
  async deleteProject(projectId: string): Promise<void> {
    await api.delete(`/projects/${projectId}`);
  },
};