import { <PERSON><PERSON>ty, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn } from 'typeorm';

@Entity('sync_queue')
export class SyncQueue {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  entityType: string;

  @Column()
  entityId: string;

  @Column()
  action: 'create' | 'update' | 'delete';

  @Column({ type: 'text' })
  data: string; // JSON string of the data to sync

  @Column({ type: 'integer', default: 0 })
  retryCount: number;

  @Column({ type: 'integer', default: 1 })
  priority: number; // Lower number = higher priority

  @Column({ type: 'text', default: 'pending' })
  status: 'pending' | 'processing' | 'completed' | 'failed';

  @Column({ type: 'text', nullable: true })
  error: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  processedAt: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string
}