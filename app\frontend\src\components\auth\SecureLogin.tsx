import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Lock, Mail, Shield, AlertTriangle } from 'lucide-react';
import { useAuthStore } from '../../stores/auth';
import { Validators, SecureStorage, SessionManager } from '../../security';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

export function SecureLogin() {
  const navigate = useNavigate();
  const { login, loading, error } = useAuthStore();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [securityWarning, setSecurityWarning] = useState<string | null>(null);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  
  useEffect(() => {
    // Check for security warnings
    checkSecurityStatus();
    
    // Check if account is temporarily blocked
    const blockExpiry = SecureStorage.getItem('login_block_until');
    if (blockExpiry && new Date(blockExpiry) > new Date()) {
      setIsBlocked(true);
      const timeout = setTimeout(() => {
        setIsBlocked(false);
        SecureStorage.removeItem('login_block_until');
      }, new Date(blockExpiry).getTime() - Date.now());
      return () => clearTimeout(timeout);
    }
  }, []);
  
  const checkSecurityStatus = () => {
    // Check if connection is secure
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      setSecurityWarning('Warning: You are using an insecure connection. Your login credentials may be at risk.');
    }
    
    // Check for session timeout
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('timeout') === 'true') {
      setSecurityWarning('Your session has expired due to inactivity. Please log in again.');
    }
  };
  
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!Validators.email(formData.email)) {
      errors.email = 'Invalid email address';
    }
    
    if (!formData.password) {
      errors.password = 'Password is required';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isBlocked) {
      return;
    }
    
    if (!validateForm()) {
      return;
    }
    
    try {
      const result = await login(formData.email, formData.password);
      
      if (result.success) {
        // Reset login attempts
        setLoginAttempts(0);
        SecureStorage.removeItem('login_attempts');
        
        // Start session timer
        SessionManager.startSessionTimer(
          result.sessionDuration || 3600000, // 1 hour default
          () => {
            // Show warning notification
            // Session expiring in 5 minutes
          },
          () => {
            // Auto logout
            navigate('/login?timeout=true');
          }
        );
        
        navigate('/dashboard');
      }
    } catch (err) {
      const attempts = loginAttempts + 1;
      setLoginAttempts(attempts);
      SecureStorage.setItem('login_attempts', attempts);
      
      // Block after 5 failed attempts
      if (attempts >= 5) {
        const blockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        SecureStorage.setItem('login_block_until', blockUntil.toISOString());
        setIsBlocked(true);
      }
    }
  };
  
  const getRemainingBlockTime = (): string => {
    const blockExpiry = SecureStorage.getItem('login_block_until');
    if (!blockExpiry) return '';
    
    const remaining = new Date(blockExpiry).getTime() - Date.now();
    const minutes = Math.ceil(remaining / 60000);
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-center mb-4">
            <Shield className="h-12 w-12 text-blue-600" />
          </div>
          <CardTitle className="text-2xl text-center">Secure Login</CardTitle>
          <CardDescription className="text-center">
            Sign in to your electrical contracting account
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {securityWarning && (
            <Alert variant="warning" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <span>{securityWarning}</span>
            </Alert>
          )}
          
          {error && (
            <Alert variant="destructive" className="mb-4">
              <span>{error}</span>
              {loginAttempts > 0 && loginAttempts < 5 && (
                <span className="block text-sm mt-1">
                  {5 - loginAttempts} attempt{5 - loginAttempts !== 1 ? 's' : ''} remaining
                </span>
              )}
            </Alert>
          )}
          
          {isBlocked && (
            <Alert variant="destructive" className="mb-4">
              <Lock className="h-4 w-4" />
              <span>
                Too many failed login attempts. Account temporarily locked.
                Try again in {getRemainingBlockTime()}.
              </span>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  autoComplete="email"
                  required
                  disabled={isBlocked}
                  className="pl-10"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  aria-invalid={!!validationErrors.email}
                  aria-describedby={validationErrors.email ? 'email-error' : undefined}
                />
              </div>
              {validationErrors.email && (
                <p id="email-error" className="mt-1 text-sm text-red-600">
                  {validationErrors.email}
                </p>
              )}
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  disabled={isBlocked}
                  className="pl-10 pr-10"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  aria-invalid={!!validationErrors.password}
                  aria-describedby={validationErrors.password ? 'password-error' : undefined}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {validationErrors.password && (
                <p id="password-error" className="mt-1 text-sm text-red-600">
                  {validationErrors.password}
                </p>
              )}
            </div>
            
            <div className="flex items-center justify-between">
              <a
                href="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                Forgot your password?
              </a>
            </div>
            
            <Button
              type="submit"
              className="w-full"
              disabled={loading || isBlocked}
            >
              {loading ? 'Signing in...' : 'Sign In Securely'}
            </Button>
          </form>
          
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-gray-800 text-gray-500">
                  Security Features
                </span>
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-2 gap-2 text-xs text-gray-500">
              <div className="flex items-center">
                <Shield className="h-3 w-3 mr-1 text-green-500" />
                <span>Encrypted connection</span>
              </div>
              <div className="flex items-center">
                <Lock className="h-3 w-3 mr-1 text-green-500" />
                <span>Secure password storage</span>
              </div>
              <div className="flex items-center">
                <Shield className="h-3 w-3 mr-1 text-green-500" />
                <span>Session timeout protection</span>
              </div>
              <div className="flex items-center">
                <Lock className="h-3 w-3 mr-1 text-green-500" />
                <span>Brute force protection</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}