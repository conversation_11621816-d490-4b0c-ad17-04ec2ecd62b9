<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Production API domain with certificate pinning -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.electrical-contractor.com</domain>
        <pin-set expiration="2025-12-31">
            <!-- Primary certificate pin -->
            <pin digest="SHA-256">YOUR_PRIMARY_CERT_HASH_HERE=</pin>
            <!-- Backup certificate pin (for rotation) -->
            <pin digest="SHA-256">YOUR_BACKUP_CERT_HASH_HERE=</pin>
            <!-- Intermediate CA pin (as fallback) -->
            <pin digest="SHA-256">YOUR_INTERMEDIATE_CA_HASH_HERE=</pin>
        </pin-set>
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </domain-config>
    
    <!-- Development configuration (only for debug builds) -->
    <debug-overrides>
        <trust-anchors>
            <!-- Allow user-installed certificates for debugging -->
            <certificates src="user" />
            <certificates src="system" />
        </trust-anchors>
    </debug-overrides>
    
    <!-- Default configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </base-config>
</network-security-config>