import { <PERSON>tity, Column, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';
import { Circuit } from './Circuit';

@Entity('panels')
export class Panel extends BaseEntity {
  @Column()
  name: string;

  @Column()
  location: string;

  @Column({ type: 'integer' })
  voltage: number;

  @Column({ type: 'integer' })
  amperage: number;

  @Column({ type: 'integer' })
  phases: number;

  @Column({ type: 'text', default: 'main' })
  type: 'main' | 'sub' | 'distribution';

  @Column({ nullable: true })
  manufacturer: string;

  @Column({ nullable: true })
  model: string;

  @Column({ nullable: true })
  serialNumber: string;

  @Column({ nullable: true })
  installationDate: string;

  @Column({ nullable: true })
  lastInspectionDate: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  qrCode: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string

  @Column()
  projectId: string;

  @ManyToOne(() => Project, project => project.panels)
  project: Project;

  @OneToMany(() => Circuit, circuit => circuit.panel)
  circuits: Circuit[];
}