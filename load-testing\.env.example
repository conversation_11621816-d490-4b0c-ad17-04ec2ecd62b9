# Load Testing Environment Configuration

# Target API URL
API_BASE_URL=http://localhost:3000/api

# WebSocket URL
WS_URL=ws://localhost:3000

# Test User Credentials (for authenticated tests)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=LoadTest123!

# Admin User Credentials (for admin endpoints)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin123!

# Test Configuration
MAX_CONCURRENT_USERS=100
RAMP_UP_TIME=300  # seconds
TEST_DURATION=1800  # seconds (30 minutes)

# Performance Thresholds
P95_RESPONSE_TIME=1000  # milliseconds
P99_RESPONSE_TIME=2000  # milliseconds
ERROR_RATE_THRESHOLD=1  # percentage

# Database Connection (for direct queries)
DATABASE_URL=postgresql://user:password@localhost:5432/electrical_db

# Redis Connection (for monitoring)
REDIS_URL=redis://localhost:6379

# Monitoring
MONITOR_INTERVAL=5000  # milliseconds
ENABLE_REAL_TIME_MONITORING=true

# Results Storage
RESULTS_DIR=./results
KEEP_RESULTS_DAYS=30