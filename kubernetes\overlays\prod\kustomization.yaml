apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: electrical-production

bases:
  - ../../base

commonLabels:
  environment: production

patchesStrategicMerge:
  - deployment-patches.yaml
  - ingress-patch.yaml

configMapGenerator:
  - name: backend-config
    behavior: merge
    literals:
      - cors-origin=https://electrical-app.com,https://www.electrical-app.com
      - log-level=warn

replicas:
  - name: backend
    count: 3
  - name: frontend
    count: 2
  - name: agents
    count: 2
  - name: postgres
    count: 1
  - name: redis
    count: 3