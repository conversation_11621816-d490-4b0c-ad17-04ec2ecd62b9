# Contributing to Electrical Contracting Application

First off, thank you for considering contributing to our project! It's people like you that make this application a great tool for electrical contractors everywhere.

## 📋 Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [How Can I Contribute?](#how-can-i-contribute)
4. [Development Process](#development-process)
5. [Style Guidelines](#style-guidelines)
6. [Community](#community)

## 📜 Code of Conduct

### Our Pledge

We are committed to providing a welcoming and inspiring community for all. Contributors are expected to adhere to our Code of Conduct:

- **Be Respectful**: Treat everyone with respect. No harassment, discrimination, or inappropriate behavior.
- **Be Collaborative**: Work together effectively, be helpful, and share knowledge.
- **Be Professional**: Maintain professionalism in all interactions.
- **Be Inclusive**: Welcome and support people of all backgrounds and identities.

### Reporting Issues

If you experience or witness unacceptable behavior, please report <NAME_EMAIL>. All reports will be handled with discretion and confidentiality.

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:

1. Read our [Developer Documentation](./developer/README.md)
2. Set up your [Development Environment](./developer/setup.md)
3. Joined our [Discord Community](https://discord.gg/electricalapp)
4. Signed the Contributor License Agreement (CLA)

### First Contributions

Looking for a place to start? Check out issues labeled:
- `good first issue` - Great for newcomers
- `help wanted` - We need your help!
- `documentation` - Help improve our docs

## 🤝 How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check existing issues to avoid duplicates. When creating a bug report, include:

**Bug Report Template:**
```markdown
### Description
Clear and concise description of the bug

### Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

### Expected Behavior
What you expected to happen

### Actual Behavior
What actually happened

### Screenshots
If applicable, add screenshots

### Environment
- OS: [e.g., Windows 11]
- Browser: [e.g., Chrome 120]
- App Version: [e.g., 1.2.3]
- Mobile Device: [if applicable]

### Additional Context
Any other relevant information
```

### Suggesting Enhancements

Enhancement suggestions are tracked as GitHub issues. When creating an enhancement suggestion, include:

**Enhancement Template:**
```markdown
### Problem Statement
What problem does this solve?

### Proposed Solution
How should it work?

### Alternatives Considered
What other solutions did you consider?

### Additional Context
Mockups, examples, or references
```

### Pull Requests

#### Before Submitting

1. **Check Existing PRs**: Ensure your change hasn't already been submitted
2. **Discuss Major Changes**: For significant changes, open an issue first
3. **One PR = One Feature**: Keep PRs focused on a single feature or fix
4. **Update Tests**: Add/update tests for your changes
5. **Update Documentation**: Keep docs in sync with code changes

#### PR Process

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/electrical-app.git
   cd electrical-app
   git remote add upstream https://github.com/org/electrical-app.git
   ```

2. **Create Branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/bug-description
   ```

3. **Make Changes**
   - Write clean, documented code
   - Follow our [Code Style Guide](./developer/code-style.md)
   - Add tests for new functionality
   - Ensure all tests pass

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat(component): add new feature"
   ```
   Follow our commit message convention:
   - `feat`: New feature
   - `fix`: Bug fix
   - `docs`: Documentation only
   - `style`: Code style changes
   - `refactor`: Code refactoring
   - `perf`: Performance improvements
   - `test`: Test additions/changes
   - `chore`: Build/tooling changes

5. **Push Changes**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create Pull Request**
   - Use our PR template
   - Link related issues
   - Request reviews from maintainers
   - Respond to feedback promptly

#### PR Template
```markdown
### Description
Brief description of changes

### Type of Change
- [ ] Bug fix (non-breaking change)
- [ ] New feature (non-breaking change)
- [ ] Breaking change
- [ ] Documentation update

### Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

### Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Comments added for complex code
- [ ] Documentation updated
- [ ] No new warnings generated
- [ ] Tests added/updated
- [ ] All tests passing

### Related Issues
Closes #(issue number)

### Screenshots
If applicable
```

## 🔄 Development Process

### Branch Strategy

We use Git Flow:
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - New features
- `fix/*` - Bug fixes
- `hotfix/*` - Production hotfixes
- `release/*` - Release preparation

### Code Review Process

All submissions require review:

1. **Automated Checks**: CI must pass
2. **Code Review**: At least 2 approvals required
3. **Testing**: Verify functionality works
4. **Documentation**: Ensure docs are updated

#### Review Guidelines

When reviewing:
- Be constructive and respectful
- Explain your reasoning
- Suggest improvements
- Approve when satisfied

### Testing Requirements

#### Test Coverage
- Minimum 80% code coverage
- 100% coverage for critical paths
- Unit tests for all utilities
- Integration tests for APIs
- E2E tests for user flows

#### Running Tests
```bash
# All tests
pnpm test

# Unit tests only
pnpm test:unit

# Integration tests
pnpm test:integration

# E2E tests
pnpm test:e2e

# Coverage report
pnpm test:coverage
```

### Documentation

#### What to Document
- Public APIs (JSDoc)
- Complex algorithms
- Configuration options
- Architecture decisions
- User-facing features

#### Documentation Standards
- Write in clear, simple English
- Include code examples
- Keep it up-to-date
- Test code snippets

## 🎨 Style Guidelines

### Code Style
Follow our comprehensive [Code Style Guide](./developer/code-style.md). Key points:
- TypeScript strict mode
- ESLint + Prettier
- Meaningful variable names
- Comprehensive error handling

### UI/UX Guidelines
- Follow Material Design principles
- Ensure accessibility (WCAG 2.1 AA)
- Mobile-first responsive design
- Consistent interaction patterns

### Git Guidelines
- Atomic commits (one change per commit)
- Clear commit messages
- No merge commits in feature branches
- Rebase before merging

## 🌐 Community

### Communication Channels

- **Discord**: [Join our server](https://discord.gg/electricalapp)
  - `#general` - General discussion
  - `#dev-help` - Development questions
  - `#feature-requests` - Feature ideas
  - `#showcase` - Show your work

- **GitHub Discussions**: Technical discussions
- **Stack Overflow**: Tag questions with `electrical-app`

### Getting Help

- Check documentation first
- Search existing issues/discussions
- Ask in Discord #dev-help
- Create a detailed issue if needed

### Recognition

We recognize contributors in several ways:
- Contributors list in README
- Monthly contributor spotlight
- Conference ticket raffles
- Exclusive contributor swag

## 🎯 Areas Needing Help

### Current Priorities

1. **Mobile App Features**
   - Offline sync improvements
   - Camera/scanner enhancements
   - Performance optimization

2. **Calculations**
   - Additional NEC calculations
   - International standards support
   - Calculation validation

3. **Integrations**
   - Accounting software
   - Supplier catalogs
   - IoT devices

4. **Documentation**
   - Tutorial videos
   - API examples
   - Translations

5. **Testing**
   - E2E test coverage
   - Performance testing
   - Security testing

### Skills Needed

- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Node.js, PostgreSQL, Redis
- **Mobile**: React Native, iOS/Android
- **DevOps**: Docker, Kubernetes, CI/CD
- **Domain**: Electrical knowledge, NEC familiarity

## 📝 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## 🙏 Thank You!

Your contributions make this project better for electrical contractors worldwide. Whether it's fixing a typo, adding a feature, or improving documentation, every contribution matters.

Welcome to our community, and happy coding! ⚡

---

Questions? Reach <NAME_EMAIL> or ask in Discord.