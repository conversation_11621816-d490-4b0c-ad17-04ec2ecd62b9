apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-pvc
spec:
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: agents-pvc
spec:
  resources:
    requests:
      storage: 1Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
spec:
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      resources:
        requests:
          storage: 10Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      resources:
        requests:
          storage: 1Gi