import { api } from './api';

export interface InspectionChecklistItem {
  id: string;
  checklist_id: string;
  category: string;
  item_code: string;
  description: string;
  nec_reference?: string;
  inspection_criteria?: string;
  common_failures?: string;
  status: 'NOT_INSPECTED' | 'PASS' | 'FAIL' | 'NA' | 'CORRECTED';
  inspector_notes?: string;
  failure_reason?: string;
  correction_required: boolean;
  correction_description?: string;
  correction_deadline?: Date;
  correction_completed: boolean;
  correction_date?: Date;
  correction_verified_by?: string;
  photo_required: boolean;
  photos_attached: number;
  severity: string;
  is_code_violation: boolean;
  immediate_danger: boolean;
  measurement_required: boolean;
  measurement_type?: string;
  measurement_value?: string;
  measurement_unit?: string;
  measurement_range_min?: number;
  measurement_range_max?: number;
  sequence_number: number;
  is_required: boolean;
  created_at: Date;
  updated_at: Date;
  inspected_at?: Date;
}

export interface InspectionPhoto {
  id: string;
  checklist_id: string;
  item_id?: string;
  file_path: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  caption?: string;
  photo_type: 'OVERVIEW' | 'DETAIL' | 'VIOLATION' | 'CORRECTION' | 'BEFORE' | 'AFTER';
  location_tag?: string;
  latitude?: number;
  longitude?: number;
  thumbnail_path?: string;
  is_annotated: boolean;
  annotation_data?: string;
  uploaded_by: string;
  uploaded_at: Date;
}

export interface InspectionChecklist {
  id: string;
  project_id: string;
  permit_document_id?: string;
  inspection_type: string;
  inspection_subtype?: string;
  inspection_number: number;
  inspection_date?: Date;
  scheduled_date?: Date;
  inspector_name?: string;
  inspector_id?: string;
  inspector_company?: string;
  inspector_phone?: string;
  inspector_email?: string;
  status: 'PENDING' | 'SCHEDULED' | 'IN_PROGRESS' | 'PASSED' | 'FAILED' | 'PARTIAL_PASS' | 'CANCELLED';
  overall_result?: 'PASS' | 'FAIL' | 'CONDITIONAL_PASS';
  reinspection_required: boolean;
  corrections_required: boolean;
  location_details?: string;
  access_instructions?: string;
  qr_code_id?: string;
  qr_code_url?: string;
  contractor_present: boolean;
  contractor_name?: string;
  contractor_signature?: string;
  inspector_signature?: string;
  sign_off_date?: Date;
  report_generated: boolean;
  report_path?: string;
  report_sent_date?: Date;
  created_by: string;
  created_at: Date;
  updated_at: Date;
  completed_at?: Date;
  checklist_items: InspectionChecklistItem[];
  photos: InspectionPhoto[];
  project?: any;
  permit_document?: any;
}

export interface InspectionType {
  key: string;
  name: string;
  categories: Array<{
    name: string;
    itemCount: number;
  }>;
}

export interface CreateInspectionDto {
  projectId: string;
  inspectionType: string;
  inspectionSubtype?: string;
  scheduledDate?: Date;
  permitDocumentId?: string;
}

export interface UpdateInspectionStatusDto {
  status?: string;
  inspectorName?: string;
  inspectorId?: string;
  inspectorCompany?: string;
  inspectorPhone?: string;
  inspectorEmail?: string;
  inspectionDate?: Date;
  overallResult?: string;
  reinspectionRequired?: boolean;
  correctionsRequired?: boolean;
}

export interface UpdateChecklistItemDto {
  status?: string;
  inspectorNotes?: string;
  failureReason?: string;
  correctionRequired?: boolean;
  correctionDescription?: string;
  correctionDeadline?: Date;
  measurementValue?: string;
  photosAttached?: number;
}

export interface SignOffDto {
  contractorPresent: boolean;
  contractorName?: string;
  contractorSignature?: string;
  inspectorSignature?: string;
}

export interface InspectionStats {
  totalInspections: number;
  passed: number;
  failed: number;
  pending: number;
  scheduled: number;
  correctionsRequired: number;
  reinspectionsRequired: number;
  byType: Record<string, number>;
}

class InspectionService {
  async getInspectionTypes(): Promise<InspectionType[]> {
    const response = await api.get('/inspections/types');
    return response.data.types;
  }

  async createInspection(data: CreateInspectionDto): Promise<InspectionChecklist> {
    const response = await api.post('/inspections', data);
    return response.data.checklist;
  }

  async getInspection(id: string): Promise<InspectionChecklist> {
    const response = await api.get(`/inspections/${id}`);
    return response.data.checklist;
  }

  async getInspectionByQRCode(qrCodeId: string): Promise<InspectionChecklist> {
    const response = await api.get(`/inspections/qr/${qrCodeId}`);
    return response.data.checklist;
  }

  async listProjectInspections(
    projectId: string,
    filters?: {
      status?: string;
      inspectionType?: string;
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Promise<InspectionChecklist[]> {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.inspectionType) params.append('inspectionType', filters.inspectionType);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString());
    if (filters?.dateTo) params.append('dateTo', filters.dateTo.toISOString());

    const response = await api.get(`/inspections/project/${projectId}?${params.toString()}`);
    return response.data.inspections;
  }

  async updateInspectionStatus(
    id: string,
    data: UpdateInspectionStatusDto
  ): Promise<InspectionChecklist> {
    const response = await api.patch(`/inspections/${id}/status`, data);
    return response.data.checklist;
  }

  async updateChecklistItem(
    itemId: string,
    data: UpdateChecklistItemDto
  ): Promise<InspectionChecklistItem> {
    const response = await api.patch(`/inspections/item/${itemId}`, data);
    return response.data.item;
  }

  async completeCorrection(itemId: string): Promise<InspectionChecklistItem> {
    const response = await api.post(`/inspections/item/${itemId}/complete-correction`);
    return response.data.item;
  }

  async uploadPhoto(
    checklistId: string,
    file: File,
    data: {
      itemId?: string;
      caption?: string;
      photoType?: string;
      locationTag?: string;
      latitude?: number;
      longitude?: number;
    }
  ): Promise<InspectionPhoto> {
    const formData = new FormData();
    formData.append('photo', file);
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value.toString());
      }
    });

    const response = await api.post(`/inspections/${checklistId}/photos`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data.photo;
  }

  async signOffInspection(id: string, data: SignOffDto): Promise<InspectionChecklist> {
    const response = await api.post(`/inspections/${id}/sign-off`, data);
    return response.data.checklist;
  }

  async generateReport(id: string): Promise<{ reportPath: string }> {
    const response = await api.post(`/inspections/${id}/generate-report`);
    return response.data;
  }

  async getProjectInspectionStats(projectId: string): Promise<InspectionStats> {
    const response = await api.get(`/inspections/project/${projectId}/stats`);
    return response.data.stats;
  }
}

export default new InspectionService();