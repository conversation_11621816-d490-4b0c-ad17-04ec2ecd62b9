import { ConduitFillService } from '../conduit-fill';

describe('ConduitFillService', () => {
  let service: ConduitFillService;

  beforeEach(() => {
    service = new ConduitFillService();
  });

  describe('Basic Conduit Fill Calculations', () => {
    it('should calculate fill for single conductor', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1/2',
        conductors: [
          { size: '12', type: 'THHN', quantity: 1 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(1);
      expect(result.fill_percent_allowed).toBe(53); // 53% for 1 conductor
      expect(result.passes_nec).toBe(true);
      expect(result.conductor_details[0].area_each).toBe(0.0133);
      expect(result.necReferences).toContain('Chapter 9, Table 1');
    });

    it('should calculate fill for two conductors', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1/2',
        conductors: [
          { size: '12', type: 'THHN', quantity: 2 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(2);
      expect(result.fill_percent_allowed).toBe(31); // 31% for 2 conductors
      expect(result.passes_nec).toBe(true);
      expect(result.total_conductor_area).toBeCloseTo(0.0266, 4);
    });

    it('should calculate fill for multiple conductors', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '3/4',
        conductors: [
          { size: '12', type: 'THHN', quantity: 6 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(6);
      expect(result.fill_percent_allowed).toBe(40); // 40% for >2 conductors
      expect(result.passes_nec).toBe(true);
      expect(result.total_conductor_area).toBeCloseTo(0.0798, 4);
    });

    it('should calculate conduit area correctly', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '10', type: 'THHN', quantity: 3 }
        ]
      };

      const result = await service.calculate(input);

      // EMT 1" has ID of 1.049"
      // Area = π × (1.049/2)² = 0.864 sq in
      expect(result.conduit_area_sq_in).toBeCloseTo(0.864, 3);
    });
  });

  describe('Mixed Conductor Sizes', () => {
    it('should handle different wire sizes in same conduit', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 3 },
          { size: '10', type: 'THHN', quantity: 2 },
          { size: '8', type: 'THHN', quantity: 1 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(6);
      expect(result.conductor_details).toHaveLength(3);
      
      // Check individual conductor areas
      expect(result.conductor_details[0].size).toBe('12');
      expect(result.conductor_details[0].area_each).toBe(0.0133);
      expect(result.conductor_details[0].total_area).toBeCloseTo(0.0399, 4);
      
      expect(result.conductor_details[1].size).toBe('10');
      expect(result.conductor_details[1].area_each).toBe(0.0211);
      expect(result.conductor_details[1].total_area).toBeCloseTo(0.0422, 4);
      
      expect(result.conductor_details[2].size).toBe('8');
      expect(result.conductor_details[2].area_each).toBe(0.0366);
      expect(result.conductor_details[2].total_area).toBe(0.0366);
    });

    it('should calculate total area correctly for mixed sizes', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1-1/4',
        conductors: [
          { size: '6', type: 'THHN', quantity: 3 },
          { size: '4', type: 'THHN', quantity: 1 }
        ]
      };

      const result = await service.calculate(input);

      const expectedArea = (0.0507 * 3) + (0.0824 * 1);
      expect(result.total_conductor_area).toBeCloseTo(expectedArea, 4);
    });
  });

  describe('Conduit Size Variations', () => {
    const conduitSizes = ['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '3-1/2', '4'];

    conduitSizes.forEach(size => {
      it(`should calculate fill for ${size}" EMT conduit`, async () => {
        const input = {
          conduit_type: 'EMT' as const,
          conduit_size: size,
          conductors: [
            { size: '12', type: 'THHN', quantity: 3 }
          ]
        };

        const result = await service.calculate(input);

        expect(result.conduit_size).toBe(size);
        expect(result.conduit_area_sq_in).toBeGreaterThan(0);
        expect(result.fill_percent_actual).toBeGreaterThan(0);
      });
    });
  });

  describe('Wire Size Variations', () => {
    const wireSizes = ['14', '12', '10', '8', '6', '4', '3', '2', '1', '1/0', '2/0', '3/0', '4/0', '250', '300', '350', '400', '500'];

    wireSizes.forEach(size => {
      it(`should calculate area for ${size} AWG/kcmil THHN`, async () => {
        const input = {
          conduit_type: 'EMT' as const,
          conduit_size: '2',
          conductors: [
            { size, type: 'THHN', quantity: 1 }
          ]
        };

        const result = await service.calculate(input);

        expect(result.conductor_details[0].size).toBe(size);
        expect(result.conductor_details[0].area_each).toBeGreaterThan(0);
      });
    });
  });

  describe('NEC Compliance Checks', () => {
    it('should pass when fill is within limits', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 5 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.passes_nec).toBe(true);
      expect(result.fill_percent_actual).toBeLessThanOrEqual(40);
    });

    it('should fail when fill exceeds limits', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1/2',
        conductors: [
          { size: '10', type: 'THHN', quantity: 6 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.passes_nec).toBe(false);
      expect(result.fill_percent_actual).toBeGreaterThan(40);
      expect(result.recommendations).toContainEqual(
        expect.stringContaining('Conduit fill exceeds NEC limit')
      );
    });

    it('should apply correct fill percentage for conductor count', async () => {
      const testCases = [
        { count: 1, expected: 53 },
        { count: 2, expected: 31 },
        { count: 3, expected: 40 },
        { count: 10, expected: 40 }
      ];

      for (const test of testCases) {
        const input = {
          conduit_type: 'EMT' as const,
          conduit_size: '1',
          conductors: [
            { size: '14', type: 'THHN', quantity: test.count }
          ]
        };

        const result = await service.calculate(input);
        
        expect(result.fill_percent_allowed).toBe(test.expected);
      }
    });
  });

  describe('Recommendations', () => {
    it('should recommend larger conduit when overfilled', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1/2',
        conductors: [
          { size: '10', type: 'THHN', quantity: 8 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.passes_nec).toBe(false);
      expect(result.recommendations).toContainEqual(
        expect.stringContaining('Recommended conduit size:')
      );
    });

    it('should warn when approaching limit', async () => {
      // Find a configuration that gives 32-40% fill (80% of 40%)
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '3/4',
        conductors: [
          { size: '12', type: 'THHN', quantity: 9 }
        ]
      };

      const result = await service.calculate(input);

      if (result.fill_percent_actual > 32 && result.fill_percent_actual <= 40) {
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Conduit fill is approaching the limit')
        );
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Consider upsizing for easier wire pulling')
        );
      }
    });

    it('should indicate remaining capacity when within limits', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 3 }
        ]
      };

      const result = await service.calculate(input);

      if (result.passes_nec) {
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Conduit fill is within acceptable limits')
        );
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Can fit approximately')
        );
      }
    });

    it('should suggest splitting conductors for extreme overfill', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '4',
        conductors: [
          { size: '500', type: 'THHN', quantity: 20 }
        ]
      };

      const result = await service.calculate(input);

      if (!result.passes_nec) {
        const hasRecommendation = result.recommendations.some(r => 
          r.includes('Recommended conduit size:') || 
          r.includes('Consider splitting conductors')
        );
        expect(hasRecommendation).toBe(true);
      }
    });
  });

  describe('Conduit Types', () => {
    const conduitTypes: Array<'EMT' | 'RMC' | 'PVC' | 'LFMC' | 'LFNC'> = ['EMT', 'RMC', 'PVC', 'LFMC', 'LFNC'];

    conduitTypes.forEach(type => {
      it(`should handle ${type} conduit type`, async () => {
        const input = {
          conduit_type: type,
          conduit_size: '1',
          conductors: [
            { size: '12', type: 'THHN', quantity: 4 }
          ]
        };

        const result = await service.calculate(input);

        expect(result.conduit_type).toBe(type);
        expect(result.conduit_area_sq_in).toBeGreaterThan(0);
        // Note: Current implementation uses EMT values for all types
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty conductor list', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: []
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(0);
      expect(result.total_conductor_area).toBe(0);
      expect(result.fill_percent_actual).toBe(0);
      expect(result.passes_nec).toBe(true);
    });

    it('should handle single large conductor', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '4',
        conductors: [
          { size: '1000', type: 'THHN', quantity: 1 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(1);
      expect(result.fill_percent_allowed).toBe(53);
      expect(result.conductor_details[0].area_each).toBe(1.3478);
    });

    it('should handle many small conductors', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '2',
        conductors: [
          { size: '14', type: 'THHN', quantity: 50 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(50);
      expect(result.total_conductor_area).toBeCloseTo(50 * 0.0097, 4);
    });

    it('should throw error for unknown conduit size', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '99',
        conductors: [
          { size: '12', type: 'THHN', quantity: 3 }
        ]
      };

      await expect(service.calculate(input)).rejects.toThrow('Unknown conduit size: 99');
    });

    it('should throw error for unknown wire size', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '999', type: 'THHN', quantity: 3 }
        ]
      };

      await expect(service.calculate(input)).rejects.toThrow('Unknown wire size: 999');
    });

    it('should handle zero quantity conductors', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 0 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(0);
      expect(result.total_conductor_area).toBe(0);
    });
  });

  describe('Calculation Details', () => {
    it('should calculate remaining area correctly', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 5 }
        ]
      };

      const result = await service.calculate(input);

      const expectedAllowable = result.conduit_area_sq_in * 0.40;
      const expectedRemaining = expectedAllowable - result.total_conductor_area;

      expect(result.allowable_area).toBeCloseTo(expectedAllowable, 4);
      expect(result.remaining_area).toBeCloseTo(expectedRemaining, 4);
    });

    it('should calculate fill percentage accurately', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '3/4',
        conductors: [
          { size: '10', type: 'THHN', quantity: 4 }
        ]
      };

      const result = await service.calculate(input);

      const expectedFillPercent = (result.total_conductor_area / result.conduit_area_sq_in) * 100;
      expect(result.fill_percent_actual).toBeCloseTo(expectedFillPercent, 2);
    });
  });

  describe('Required Conduit Size Calculation', () => {
    it('should calculate minimum conduit size correctly', async () => {
      // Start with overfilled conduit
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1/2',
        conductors: [
          { size: '10', type: 'THHN', quantity: 10 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.passes_nec).toBe(false);
      
      // Should recommend a conduit size
      const sizeRecommendation = result.recommendations.find(r => 
        r.includes('Recommended conduit size:')
      );
      expect(sizeRecommendation).toBeDefined();
      
      if (sizeRecommendation) {
        // Verify the recommended size would work
        const match = sizeRecommendation.match(/Recommended conduit size: (.+)/);
        if (match) {
          const recommendedSize = match[1];
          
          // Test the recommended size
          const testInput = { ...input, conduit_size: recommendedSize };
          const testResult = await service.calculate(testInput);
          
          expect(testResult.passes_nec).toBe(true);
        }
      }
    });
  });

  describe('Different Conductor Types', () => {
    const conductorTypes = ['THHN', 'THWN', 'XHHW', 'RHH', 'RHW'];

    conductorTypes.forEach(type => {
      it(`should handle ${type} conductor type`, async () => {
        const input = {
          conduit_type: 'EMT' as const,
          conduit_size: '1',
          conductors: [
            { size: '12', type, quantity: 4 }
          ]
        };

        const result = await service.calculate(input);

        expect(result.conductor_details[0].type).toBe(type);
        // Note: Current implementation uses THHN areas for all types
        expect(result.conductor_details[0].area_each).toBe(0.0133);
      });
    });
  });

  describe('NEC References', () => {
    it('should include all relevant NEC references', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '1',
        conductors: [
          { size: '12', type: 'THHN', quantity: 6 }
        ]
      };

      const result = await service.calculate(input);

      expect(result.necReferences).toContain('Chapter 9, Table 1');
      expect(result.necReferences).toContain('Chapter 9, Table 4');
      expect(result.necReferences).toContain('Chapter 9, Table 5');
      expect(result.necReferences).toHaveLength(3);
    });
  });

  describe('Complex Mixed Installations', () => {
    it('should handle typical branch circuit mix', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '3/4',
        conductors: [
          { size: '12', type: 'THHN', quantity: 8 }, // Hot conductors
          { size: '12', type: 'THHN', quantity: 1 }, // Neutral
          { size: '12', type: 'THHN', quantity: 1 }  // Ground
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(10);
      expect(result.conductor_details).toHaveLength(3);
      expect(result.total_conductor_area).toBeCloseTo(10 * 0.0133, 4);
    });

    it('should handle feeder with multiple sizes', async () => {
      const input = {
        conduit_type: 'EMT' as const,
        conduit_size: '2',
        conductors: [
          { size: '3/0', type: 'THHN', quantity: 3 }, // Phase conductors
          { size: '3/0', type: 'THHN', quantity: 1 }, // Neutral
          { size: '6', type: 'THHN', quantity: 1 }    // Ground
        ]
      };

      const result = await service.calculate(input);

      expect(result.total_conductors).toBe(5);
      expect(result.fill_percent_allowed).toBe(40);
      
      // Verify individual areas
      const phaseArea = result.conductor_details.find(d => d.size === '3/0' && d.quantity === 3);
      expect(phaseArea?.total_area).toBeCloseTo(3 * 0.2679, 4);
      
      const groundArea = result.conductor_details.find(d => d.size === '6');
      expect(groundArea?.total_area).toBe(0.0507);
    });
  });
});