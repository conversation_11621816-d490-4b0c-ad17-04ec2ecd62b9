@echo off
echo Starting Electrical Contracting Application (Main Services Only)...
echo =================================================================

cd app

echo Installing dependencies with pnpm...
call pnpm install

echo.
echo Starting Backend and Frontend services...
echo.
echo Note: This starts only the main application without AI agents.
echo The application will work fully without the agents.
echo.

call pnpm run dev:main

echo.
echo Application started successfully!
echo.
echo Access the application at:
echo   Frontend: http://localhost:3000
echo   Backend API: http://localhost:3001
echo.
echo Press Ctrl+C to stop all services
pause