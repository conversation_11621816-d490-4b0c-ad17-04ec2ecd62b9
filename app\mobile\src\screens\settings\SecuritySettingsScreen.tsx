import React, { useState, useEffect } from 'react';
import {
  VStack,
  HStack,
  Text,
  Switch,
  Button,
  Divider,
  Icon,
  Box,
  ScrollView,
  Pressable,
  Modal,
  FormControl,
  Select,
  Alert,
} from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { BiometryTypes } from 'react-native-biometrics';
import { securityService, SecurityConfig } from '@services/securityService';
import { useDispatch } from 'react-redux';
import { showToast } from '@store/slices/uiSlice';
import { AppDispatch } from '@store/index';

const SecuritySettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();
  
  const [config, setConfig] = useState<SecurityConfig>({
    enableBiometrics: false,
    enablePinCode: false,
    autoLockTimeout: 5,
    requireReauthOnForeground: true,
    enableScreenshotPrevention: true,
    enableDeviceBinding: true,
  });
  
  const [biometryType, setBiometryType] = useState<BiometryTypes | null>(null);
  const [biometryAvailable, setBiometryAvailable] = useState(false);
  const [showTimeoutModal, setShowTimeoutModal] = useState(false);
  const [deviceSecurityInfo, setDeviceSecurityInfo] = useState<{
    isSecure: boolean;
    issues: string[];
  }>({ isSecure: true, issues: [] });

  useEffect(() => {
    loadSettings();
    checkBiometryAvailability();
    checkDeviceSecurity();
  }, []);

  const loadSettings = async () => {
    const savedConfig = await securityService.getSecurityConfig();
    setConfig(savedConfig);
  };

  const checkBiometryAvailability = async () => {
    const { available, biometryType } = await securityService.checkBiometricAvailability();
    setBiometryAvailable(available);
    setBiometryType(biometryType);
  };

  const checkDeviceSecurity = async () => {
    const securityInfo = await securityService.checkDeviceSecurity();
    setDeviceSecurityInfo(securityInfo);
  };

  const updateSetting = async (key: keyof SecurityConfig, value: any) => {
    try {
      const newConfig = { ...config, [key]: value };
      await securityService.setSecurityConfig({ [key]: value });
      setConfig(newConfig);
      
      dispatch(showToast({
        message: 'Security settings updated',
        type: 'success',
      }));
    } catch (error) {
      dispatch(showToast({
        message: 'Failed to update settings',
        type: 'error',
      }));
    }
  };

  const handleBiometricToggle = async (enabled: boolean) => {
    if (enabled && biometryAvailable) {
      // Authenticate first before enabling
      const authenticated = await securityService.authenticateWithBiometrics(
        'Authenticate to enable biometric login'
      );
      
      if (authenticated) {
        await updateSetting('enableBiometrics', true);
      }
    } else {
      await updateSetting('enableBiometrics', false);
    }
  };

  const handlePinToggle = async (enabled: boolean) => {
    if (enabled) {
      // Navigate to PIN setup screen
      navigation.navigate('PinSetup' as never);
    } else {
      // Confirm before disabling
      await updateSetting('enablePinCode', false);
    }
  };

  const getBiometricLabel = () => {
    switch (biometryType) {
      case BiometryTypes.TouchID:
        return 'Touch ID';
      case BiometryTypes.FaceID:
        return 'Face ID';
      case BiometryTypes.Biometrics:
        return 'Fingerprint';
      default:
        return 'Biometrics';
    }
  };

  const timeoutOptions = [
    { label: 'Immediately', value: 0 },
    { label: '1 minute', value: 1 },
    { label: '5 minutes', value: 5 },
    { label: '15 minutes', value: 15 },
    { label: '30 minutes', value: 30 },
    { label: 'Never', value: -1 },
  ];

  return (
    <ScrollView bg="gray.50" flex={1}>
      <VStack space={4} p={4}>
        {/* Device Security Status */}
        {!deviceSecurityInfo.isSecure && (
          <Alert status="warning" mb={4}>
            <VStack space={2} flexShrink={1} w="100%">
              <HStack alignItems="center" space={2}>
                <Alert.Icon />
                <Text fontSize="md" fontWeight="medium">
                  Security Issues Detected
                </Text>
              </HStack>
              {deviceSecurityInfo.issues.map((issue, index) => (
                <Text key={index} fontSize="sm" color="gray.600" ml={6}>
                  • {issue}
                </Text>
              ))}
            </VStack>
          </Alert>
        )}

        {/* Authentication Methods */}
        <Box bg="white" borderRadius="lg" shadow={1}>
          <VStack space={0} divider={<Divider />}>
            <Text fontSize="lg" fontWeight="bold" p={4} color="gray.800">
              Authentication Methods
            </Text>

            {/* Biometric Authentication */}
            <Pressable
              onPress={() => handleBiometricToggle(!config.enableBiometrics)}
              disabled={!biometryAvailable}
            >
              <HStack justifyContent="space-between" alignItems="center" p={4}>
                <HStack space={3} alignItems="center" flex={1}>
                  <Icon
                    as={MaterialIcons}
                    name="fingerprint"
                    size={6}
                    color={biometryAvailable ? 'primary.600' : 'gray.400'}
                  />
                  <VStack flex={1}>
                    <Text fontSize="md" fontWeight="medium">
                      {getBiometricLabel()}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      {biometryAvailable
                        ? 'Use biometrics for quick access'
                        : 'Not available on this device'}
                    </Text>
                  </VStack>
                </HStack>
                <Switch
                  isChecked={config.enableBiometrics}
                  isDisabled={!biometryAvailable}
                  onToggle={() => handleBiometricToggle(!config.enableBiometrics)}
                />
              </HStack>
            </Pressable>

            {/* PIN Code */}
            <Pressable onPress={() => handlePinToggle(!config.enablePinCode)}>
              <HStack justifyContent="space-between" alignItems="center" p={4}>
                <HStack space={3} alignItems="center" flex={1}>
                  <Icon
                    as={MaterialIcons}
                    name="pin"
                    size={6}
                    color="primary.600"
                  />
                  <VStack flex={1}>
                    <Text fontSize="md" fontWeight="medium">
                      PIN Code
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Use a PIN for authentication
                    </Text>
                  </VStack>
                </HStack>
                <Switch
                  isChecked={config.enablePinCode}
                  onToggle={() => handlePinToggle(!config.enablePinCode)}
                />
              </HStack>
            </Pressable>
          </VStack>
        </Box>

        {/* Security Settings */}
        <Box bg="white" borderRadius="lg" shadow={1}>
          <VStack space={0} divider={<Divider />}>
            <Text fontSize="lg" fontWeight="bold" p={4} color="gray.800">
              Security Settings
            </Text>

            {/* Auto-lock Timeout */}
            <Pressable onPress={() => setShowTimeoutModal(true)}>
              <HStack justifyContent="space-between" alignItems="center" p={4}>
                <HStack space={3} alignItems="center" flex={1}>
                  <Icon
                    as={MaterialIcons}
                    name="timer"
                    size={6}
                    color="primary.600"
                  />
                  <VStack flex={1}>
                    <Text fontSize="md" fontWeight="medium">
                      Auto-lock Timeout
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Lock app after inactivity
                    </Text>
                  </VStack>
                </HStack>
                <HStack alignItems="center" space={1}>
                  <Text color="gray.600">
                    {config.autoLockTimeout === -1
                      ? 'Never'
                      : config.autoLockTimeout === 0
                      ? 'Immediately'
                      : `${config.autoLockTimeout} min`}
                  </Text>
                  <Icon
                    as={MaterialIcons}
                    name="chevron-right"
                    size={5}
                    color="gray.400"
                  />
                </HStack>
              </HStack>
            </Pressable>

            {/* Require Re-authentication */}
            <HStack justifyContent="space-between" alignItems="center" p={4}>
              <HStack space={3} alignItems="center" flex={1}>
                <Icon
                  as={MaterialIcons}
                  name="security"
                  size={6}
                  color="primary.600"
                />
                <VStack flex={1}>
                  <Text fontSize="md" fontWeight="medium">
                    Require Re-authentication
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    When app returns to foreground
                  </Text>
                </VStack>
              </HStack>
              <Switch
                isChecked={config.requireReauthOnForeground}
                onToggle={(value) => updateSetting('requireReauthOnForeground', value)}
              />
            </HStack>

            {/* Screenshot Prevention */}
            <HStack justifyContent="space-between" alignItems="center" p={4}>
              <HStack space={3} alignItems="center" flex={1}>
                <Icon
                  as={MaterialIcons}
                  name="screenshot"
                  size={6}
                  color="primary.600"
                />
                <VStack flex={1}>
                  <Text fontSize="md" fontWeight="medium">
                    Prevent Screenshots
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Block screenshots on sensitive screens
                  </Text>
                </VStack>
              </HStack>
              <Switch
                isChecked={config.enableScreenshotPrevention}
                onToggle={(value) => updateSetting('enableScreenshotPrevention', value)}
              />
            </HStack>

            {/* Device Binding */}
            <HStack justifyContent="space-between" alignItems="center" p={4}>
              <HStack space={3} alignItems="center" flex={1}>
                <Icon
                  as={MaterialIcons}
                  name="phone-android"
                  size={6}
                  color="primary.600"
                />
                <VStack flex={1}>
                  <Text fontSize="md" fontWeight="medium">
                    Device Binding
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Lock account to this device
                  </Text>
                </VStack>
              </HStack>
              <Switch
                isChecked={config.enableDeviceBinding}
                onToggle={(value) => updateSetting('enableDeviceBinding', value)}
              />
            </HStack>
          </VStack>
        </Box>

        {/* Additional Actions */}
        <Box bg="white" borderRadius="lg" shadow={1}>
          <VStack space={0} divider={<Divider />}>
            {config.enablePinCode && (
              <Pressable onPress={() => navigation.navigate('ChangePin' as never)}>
                <HStack justifyContent="space-between" alignItems="center" p={4}>
                  <HStack space={3} alignItems="center">
                    <Icon
                      as={MaterialIcons}
                      name="edit"
                      size={6}
                      color="primary.600"
                    />
                    <Text fontSize="md" fontWeight="medium">
                      Change PIN
                    </Text>
                  </HStack>
                  <Icon
                    as={MaterialIcons}
                    name="chevron-right"
                    size={5}
                    color="gray.400"
                  />
                </HStack>
              </Pressable>
            )}

            <Pressable onPress={() => navigation.navigate('SessionManagement' as never)}>
              <HStack justifyContent="space-between" alignItems="center" p={4}>
                <HStack space={3} alignItems="center">
                  <Icon
                    as={MaterialIcons}
                    name="devices"
                    size={6}
                    color="primary.600"
                  />
                  <Text fontSize="md" fontWeight="medium">
                    Active Sessions
                  </Text>
                </HStack>
                <Icon
                  as={MaterialIcons}
                  name="chevron-right"
                  size={5}
                  color="gray.400"
                />
              </HStack>
            </Pressable>
          </VStack>
        </Box>
      </VStack>

      {/* Auto-lock Timeout Modal */}
      <Modal isOpen={showTimeoutModal} onClose={() => setShowTimeoutModal(false)}>
        <Modal.Content maxWidth="350">
          <Modal.Header>Auto-lock Timeout</Modal.Header>
          <Modal.Body>
            <FormControl>
              <Select
                selectedValue={config.autoLockTimeout.toString()}
                onValueChange={(value) => {
                  updateSetting('autoLockTimeout', parseInt(value));
                  setShowTimeoutModal(false);
                }}
                placeholder="Select timeout"
              >
                {timeoutOptions.map((option) => (
                  <Select.Item
                    key={option.value}
                    label={option.label}
                    value={option.value.toString()}
                  />
                ))}
              </Select>
            </FormControl>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </ScrollView>
  );
};

export default SecuritySettingsScreen;