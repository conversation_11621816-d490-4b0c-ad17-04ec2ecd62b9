import { Decimal } from 'decimal.js';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * IEEE 1584-2018 Arc Flash Calculation Service
 * Implements the latest IEEE 1584 standard for arc flash hazard calculations
 */

export interface ArcFlashInput {
  panelId: string;
  systemVoltage: number; // Voltage in V (convert to kV internally)
  systemType: 'THREE_PHASE' | 'SINGLE_PHASE';
  groundingType: 'SOLIDLY_GROUNDED' | 'RESISTANCE_GROUNDED' | 'UNGROUNDED';
  frequency?: number; // 50 or 60 Hz, default 60
  boltedFaultCurrent: number; // Bolted fault current in A (convert to kA internally)
  faultClearingTime: number; // Clearing time in seconds
  equipmentType: 'SWITCHGEAR' | 'PANELBOARD' | 'MCC' | 'CABLE';
  electrodeConfiguration: 'VCB' | 'VCBB' | 'HCB' | 'VOA' | 'HOA';
  enclosureWidth: number; // Width in inches (convert to mm internally)
  enclosureHeight: number; // Height in inches (convert to mm internally)
  enclosureDepth: number; // Depth in inches (convert to mm internally)
  conductorGap: number; // Gap in inches (convert to mm internally)
  workingDistance: number; // Working distance in inches (convert to mm internally)
  calculatedBy: string;
}

export interface ArcFlashResult {
  arcingCurrentMin: number;
  arcingCurrentMax: number;
  incidentEnergyMin: number;
  incidentEnergyMax: number;
  arcFlashBoundary: number;
  ppeCategory: number;
  ppeMinArcRating: number;
  shockHazardVoltage: number;
  limitedApproach: number;
  restrictedApproach: number;
  enclosureCorrection: number;
  reducedArcingCurrent?: number;
  reducedIncidentEnergy?: number;
  hazardRiskCategory: number;
  recommendedPpe: string[];
  safetyNotes: string[];
}

// IEEE 1584-2018 Constants
const IEEE_1584_CONSTANTS = {
  // Enclosure dimension limits (mm)
  MIN_WIDTH: 4,
  MAX_WIDTH: 1244.6,
  MIN_HEIGHT: 4,
  MAX_HEIGHT: 1244.6,
  MIN_DEPTH: 25.4,
  
  // Working distance limits (mm)
  MIN_WORKING_DISTANCE: 305, // 12 inches
  
  // Voltage limits (kV)
  MIN_VOLTAGE: 0.208,
  MAX_VOLTAGE: 15,
  
  // Arc flash boundary incident energy (cal/cm²)
  AFB_INCIDENT_ENERGY: 1.2,
  
  // PPE Categories (cal/cm²)
  PPE_CAT_1_MAX: 4,
  PPE_CAT_2_MAX: 8,
  PPE_CAT_3_MAX: 25,
  PPE_CAT_4_MAX: 40,
  
  // Maximum arc duration (seconds)
  MAX_ARC_DURATION: 2.0,
};

// Approach boundaries per NFPA 70E Table 130.4(D)(a) and (b)
const SHOCK_BOUNDARIES = {
  // AC Voltage boundaries in mm
  '120-240': { limited: 1067, restricted: 305, prohibited: 25 },
  '241-600': { limited: 1067, restricted: 305, prohibited: 25 },
  '601-2500': { limited: 1524, restricted: 660, prohibited: 178 },
  '2501-8000': { limited: 2438, restricted: 914, prohibited: 254 },
  '8001-15000': { limited: 3048, restricted: 991, prohibited: 318 },
};

export class ArcFlashCalculationService {
  /**
   * Perform IEEE 1584-2018 arc flash calculation
   */
  async calculate(input: ArcFlashInput): Promise<ArcFlashResult> {
    // Convert units to IEEE 1584 standard units
    const voc = new Decimal(input.systemVoltage).div(1000); // Convert V to kV
    const ibf = new Decimal(input.boltedFaultCurrent).div(1000); // Convert A to kA
    const width = new Decimal(input.enclosureWidth).mul(25.4); // Convert inches to mm
    const height = new Decimal(input.enclosureHeight).mul(25.4); // Convert inches to mm
    const depth = new Decimal(input.enclosureDepth).mul(25.4); // Convert inches to mm
    const gap = new Decimal(input.conductorGap).mul(25.4); // Convert inches to mm
    const workingDistance = new Decimal(input.workingDistance).mul(25.4); // Convert inches to mm
    
    // Validate inputs
    this.validateInputs(voc, ibf, width, height, depth, workingDistance);
    
    // Step 1: Calculate intermediate arcing currents
    const { iarc600, iarc2700, iarc14300 } = this.calculateIntermediateArcingCurrents(
      ibf,
      gap,
      input.electrodeConfiguration
    );
    
    // Step 2: Calculate final arcing current
    const iarcMin = this.calculateFinalArcingCurrent(voc, iarc600, iarc2700, iarc14300, true);
    const iarcMax = this.calculateFinalArcingCurrent(voc, iarc600, iarc2700, iarc14300, false);
    
    // Step 3: Calculate enclosure size correction factor
    const cf = this.calculateEnclosureCorrectionFactor(
      width,
      height,
      input.electrodeConfiguration,
      voc
    );
    
    // Step 4: Calculate intermediate incident energies
    const { e600Min, e2700Min, e14300Min } = this.calculateIntermediateIncidentEnergies(
      iarc600,
      iarc2700,
      iarc14300,
      cf,
      input.faultClearingTime,
      workingDistance,
      gap,
      input.electrodeConfiguration,
      true
    );
    
    const { e600Max, e2700Max, e14300Max } = this.calculateIntermediateIncidentEnergies(
      iarc600,
      iarc2700,
      iarc14300,
      cf,
      input.faultClearingTime,
      workingDistance,
      gap,
      input.electrodeConfiguration,
      false
    );
    
    // Step 5: Calculate final incident energies
    const incidentEnergyMin = this.calculateFinalIncidentEnergy(
      voc,
      e600Min,
      e2700Min,
      e14300Min
    );
    
    const incidentEnergyMax = this.calculateFinalIncidentEnergy(
      voc,
      e600Max,
      e2700Max,
      e14300Max
    );
    
    // Step 6: Calculate arc flash boundary
    const arcFlashBoundary = this.calculateArcFlashBoundary(
      incidentEnergyMax,
      input.faultClearingTime,
      ibf,
      gap,
      input.electrodeConfiguration,
      voc
    );
    
    // Step 7: Calculate reduced arcing current (85% method)
    const reducedArcingCurrent = iarcMin.mul(0.85);
    const reducedIncidentEnergy = this.calculateReducedIncidentEnergy(
      reducedArcingCurrent,
      cf,
      input.faultClearingTime,
      workingDistance,
      gap,
      input.electrodeConfiguration,
      voc
    );
    
    // Step 8: Determine PPE requirements
    const maxIncidentEnergy = Decimal.max(incidentEnergyMax, reducedIncidentEnergy);
    const ppeCategory = this.determinePPECategory(maxIncidentEnergy);
    const ppeMinArcRating = this.determinePPEMinRating(maxIncidentEnergy);
    
    // Step 9: Calculate shock protection boundaries
    const boundaries = this.calculateShockBoundaries(input.systemVoltage);
    
    // Step 10: Determine hazard risk category
    const hazardRiskCategory = this.determineHazardRiskCategory(maxIncidentEnergy);
    
    // Step 11: Generate PPE recommendations
    const recommendedPpe = this.generatePPERecommendations(ppeCategory, input.systemVoltage);
    
    // Step 12: Generate safety notes
    const safetyNotes = this.generateSafetyNotes(
      maxIncidentEnergy,
      ppeCategory,
      input.systemVoltage,
      arcFlashBoundary
    );
    
    // Save calculation to database
    await this.saveCalculation(input, {
      arcingCurrentMin: iarcMin.toNumber(),
      arcingCurrentMax: iarcMax.toNumber(),
      incidentEnergyMin: incidentEnergyMin.toNumber(),
      incidentEnergyMax: incidentEnergyMax.toNumber(),
      arcFlashBoundary: arcFlashBoundary.toNumber(),
      ppeCategory,
      ppeMinArcRating: ppeMinArcRating.toNumber(),
      shockHazardVoltage: input.systemVoltage,
      limitedApproach: boundaries.limited,
      restrictedApproach: boundaries.restricted,
      enclosureCorrection: cf.toNumber(),
      reducedArcingCurrent: reducedArcingCurrent.toNumber(),
      reducedIncidentEnergy: reducedIncidentEnergy.toNumber(),
      hazardRiskCategory,
      recommendedPpe,
      safetyNotes,
    });
    
    return {
      arcingCurrentMin: iarcMin.toNumber(),
      arcingCurrentMax: iarcMax.toNumber(),
      incidentEnergyMin: incidentEnergyMin.toNumber(),
      incidentEnergyMax: incidentEnergyMax.toNumber(),
      arcFlashBoundary: arcFlashBoundary.toNumber(),
      ppeCategory,
      ppeMinArcRating: ppeMinArcRating.toNumber(),
      shockHazardVoltage: input.systemVoltage,
      limitedApproach: boundaries.limited,
      restrictedApproach: boundaries.restricted,
      enclosureCorrection: cf.toNumber(),
      reducedArcingCurrent: reducedArcingCurrent.toNumber(),
      reducedIncidentEnergy: reducedIncidentEnergy.toNumber(),
      hazardRiskCategory,
      recommendedPpe,
      safetyNotes,
    };
  }
  
  /**
   * Validate input parameters against IEEE 1584-2018 limits
   */
  private validateInputs(
    voc: Decimal,
    ibf: Decimal,
    width: Decimal,
    height: Decimal,
    depth: Decimal,
    workingDistance: Decimal
  ): void {
    // Voltage range: 0.208 to 15 kV
    if (voc.lt(IEEE_1584_CONSTANTS.MIN_VOLTAGE) || voc.gt(IEEE_1584_CONSTANTS.MAX_VOLTAGE)) {
      throw new Error(`Voltage must be between ${IEEE_1584_CONSTANTS.MIN_VOLTAGE} and ${IEEE_1584_CONSTANTS.MAX_VOLTAGE} kV`);
    }
    
    // Working distance minimum: 305 mm (12 inches)
    if (workingDistance.lt(IEEE_1584_CONSTANTS.MIN_WORKING_DISTANCE)) {
      throw new Error(`Working distance must be at least ${IEEE_1584_CONSTANTS.MIN_WORKING_DISTANCE} mm (12 inches)`);
    }
    
    // Enclosure dimensions
    if (width.lt(IEEE_1584_CONSTANTS.MIN_WIDTH) || width.gt(IEEE_1584_CONSTANTS.MAX_WIDTH)) {
      throw new Error(`Enclosure width must be between ${IEEE_1584_CONSTANTS.MIN_WIDTH} and ${IEEE_1584_CONSTANTS.MAX_WIDTH} mm`);
    }
    
    if (height.lt(IEEE_1584_CONSTANTS.MIN_HEIGHT) || height.gt(IEEE_1584_CONSTANTS.MAX_HEIGHT)) {
      throw new Error(`Enclosure height must be between ${IEEE_1584_CONSTANTS.MIN_HEIGHT} and ${IEEE_1584_CONSTANTS.MAX_HEIGHT} mm`);
    }
    
    if (depth.lt(IEEE_1584_CONSTANTS.MIN_DEPTH)) {
      throw new Error(`Enclosure depth must be at least ${IEEE_1584_CONSTANTS.MIN_DEPTH} mm`);
    }
    
    // Bolted fault current range depends on voltage
    const minIbf = voc.lte(0.6) ? 0.5 : voc.lte(1) ? 0.5 : 0.5;
    const maxIbf = voc.lte(0.6) ? 106 : voc.lte(1) ? 65 : 50;
    
    if (ibf.lt(minIbf) || ibf.gt(maxIbf)) {
      throw new Error(`Bolted fault current must be between ${minIbf} and ${maxIbf} kA for ${voc.toNumber()} kV systems`);
    }
  }
  
  /**
   * Calculate intermediate arcing currents per IEEE 1584-2018
   */
  private calculateIntermediateArcingCurrents(
    ibf: Decimal,
    gap: Decimal,
    electrodeConfig: string
  ): { iarc600: Decimal; iarc2700: Decimal; iarc14300: Decimal } {
    // Coefficients for different electrode configurations
    const coefficients = this.getArcingCurrentCoefficients(electrodeConfig);
    
    // Calculate for 600V
    const iarc600 = this.calculateArcingCurrentAtVoltage(
      ibf,
      gap,
      0.6,
      coefficients
    );
    
    // Calculate for 2700V
    const iarc2700 = this.calculateArcingCurrentAtVoltage(
      ibf,
      gap,
      2.7,
      coefficients
    );
    
    // Calculate for 14300V
    const iarc14300 = this.calculateArcingCurrentAtVoltage(
      ibf,
      gap,
      14.3,
      coefficients
    );
    
    return { iarc600, iarc2700, iarc14300 };
  }
  
  /**
   * Calculate arcing current at specific voltage
   */
  private calculateArcingCurrentAtVoltage(
    ibf: Decimal,
    gap: Decimal,
    voltage: number,
    coefficients: any
  ): Decimal {
    const { k1, k2, k3, k4, k5, k6, k7, k8, k9, k10 } = coefficients;
    
    // IEEE 1584-2018 Equation 1
    const logIarc = new Decimal(k1)
      .plus(new Decimal(k2).mul(Decimal.log10(ibf)))
      .plus(new Decimal(k3).mul(Decimal.log10(ibf)).pow(2))
      .plus(new Decimal(k4).mul(Decimal.log10(ibf)).pow(3))
      .plus(new Decimal(k5).mul(Decimal.log10(ibf)).pow(4))
      .plus(new Decimal(k6).mul(Decimal.log10(ibf)).pow(5))
      .plus(new Decimal(k7).mul(Decimal.log10(ibf)).pow(6))
      .plus(new Decimal(k8).mul(Decimal.log10(ibf)).mul(Decimal.log10(gap)))
      .plus(new Decimal(k9).mul(Decimal.log10(ibf)).pow(2).mul(Decimal.log10(gap)))
      .plus(new Decimal(k10).mul(Decimal.log10(ibf)).pow(3).mul(Decimal.log10(gap)));
    
    return Decimal.pow(10, logIarc);
  }
  
  /**
   * Get arcing current coefficients based on electrode configuration
   */
  private getArcingCurrentCoefficients(electrodeConfig: string): any {
    // IEEE 1584-2018 Table 1 coefficients
    const coefficientTable = {
      VCB: {
        k1: -0.04287, k2: 1.035, k3: -0.083, k4: 0, k5: 0,
        k6: 0, k7: 0, k8: -0.1863, k9: 0.1928, k10: -0.0807
      },
      VCBB: {
        k1: -0.1863, k2: 1.0115, k3: -0.0591, k4: 0, k5: 0,
        k6: 0, k7: 0, k8: -0.1928, k9: 0.2050, k10: -0.0867
      },
      HCB: {
        k1: -0.09778, k2: 1.0115, k3: -0.1862, k4: 0.1641, k5: -0.0872,
        k6: 0.02748, k7: -0.003191, k8: -0.0200, k9: 0, k10: 0
      },
      VOA: {
        k1: -0.05978, k2: 1.097, k3: -0.1618, k4: 0.08955, k5: -0.02591,
        k6: 0.003815, k7: -0.0002235, k8: -0.01318, k9: 0, k10: 0
      },
      HOA: {
        k1: -0.03643, k2: 1.022, k3: -0.0887, k4: 0, k5: 0,
        k6: 0, k7: 0, k8: -0.2521, k9: 0.2748, k10: -0.1205
      }
    };
    
    return coefficientTable[electrodeConfig] || coefficientTable.VCB;
  }
  
  /**
   * Calculate final arcing current using interpolation
   */
  private calculateFinalArcingCurrent(
    voc: Decimal,
    iarc600: Decimal,
    iarc2700: Decimal,
    iarc14300: Decimal,
    isMin: boolean
  ): Decimal {
    let iarc: Decimal;
    
    if (voc.lte(0.6)) {
      iarc = iarc600;
    } else if (voc.lte(2.7)) {
      // Interpolate between 600V and 2700V
      const ratio = voc.sub(0.6).div(2.7 - 0.6);
      iarc = iarc600.plus(iarc2700.sub(iarc600).mul(ratio));
    } else if (voc.lte(14.3)) {
      // Interpolate between 2700V and 14300V
      const ratio = voc.sub(2.7).div(14.3 - 2.7);
      iarc = iarc2700.plus(iarc14300.sub(iarc2700).mul(ratio));
    } else {
      iarc = iarc14300;
    }
    
    // Apply correction factor for minimum/maximum
    if (isMin) {
      return iarc.mul(0.988); // Minimum correction factor
    } else {
      return iarc.mul(1.012); // Maximum correction factor
    }
  }
  
  /**
   * Calculate enclosure size correction factor
   */
  private calculateEnclosureCorrectionFactor(
    width: Decimal,
    height: Decimal,
    electrodeConfig: string,
    voc: Decimal
  ): Decimal {
    // Only applies to box configurations (VCB, VCBB, HCB)
    if (!['VCB', 'VCBB', 'HCB'].includes(electrodeConfig)) {
      return new Decimal(1);
    }
    
    // Calculate equivalent enclosure dimension
    const equivalentWidth = width.mul(25.4).div(660.4).mul(20);
    const equivalentHeight = height.mul(25.4).div(660.4).mul(20);
    
    // IEEE 1584-2018 Equation for CF
    let b1, b2, b3;
    
    if (voc.lte(0.6)) {
      b1 = -0.0302;
      b2 = 0.0011;
      b3 = 0.000011;
    } else {
      b1 = -0.0166;
      b2 = 0.0005;
      b3 = 0.000006;
    }
    
    const cf = new Decimal(1)
      .plus(new Decimal(b1).mul(equivalentWidth.plus(equivalentHeight)))
      .plus(new Decimal(b2).mul(equivalentWidth.plus(equivalentHeight)).pow(2))
      .plus(new Decimal(b3).mul(equivalentWidth.plus(equivalentHeight)).pow(3));
    
    return cf;
  }
  
  /**
   * Calculate intermediate incident energies
   */
  private calculateIntermediateIncidentEnergies(
    iarc600: Decimal,
    iarc2700: Decimal,
    iarc14300: Decimal,
    cf: Decimal,
    time: number,
    workingDistance: Decimal,
    gap: Decimal,
    electrodeConfig: string,
    isMin: boolean
  ): { e600Min: Decimal; e2700Min: Decimal; e14300Min: Decimal } {
    const coefficients = this.getIncidentEnergyCoefficients(electrodeConfig);
    
    const e600Min = this.calculateIncidentEnergyAtVoltage(
      iarc600,
      cf,
      time,
      workingDistance,
      gap,
      0.6,
      coefficients,
      isMin
    );
    
    const e2700Min = this.calculateIncidentEnergyAtVoltage(
      iarc2700,
      cf,
      time,
      workingDistance,
      gap,
      2.7,
      coefficients,
      isMin
    );
    
    const e14300Min = this.calculateIncidentEnergyAtVoltage(
      iarc14300,
      cf,
      time,
      workingDistance,
      gap,
      14.3,
      coefficients,
      isMin
    );
    
    return { e600Min, e2700Min, e14300Min };
  }
  
  /**
   * Calculate incident energy at specific voltage
   */
  private calculateIncidentEnergyAtVoltage(
    iarc: Decimal,
    cf: Decimal,
    time: number,
    workingDistance: Decimal,
    gap: Decimal,
    voltage: number,
    coefficients: any,
    isMin: boolean
  ): Decimal {
    const { k1, k2, k3, k4, k5, k6, k7, k8, k9, k10, k11, k12, k13 } = coefficients;
    
    // Time exponent calculation
    const timeExponent = new Decimal(k1)
      .plus(new Decimal(k2).mul(Decimal.log10(iarc)))
      .plus(new Decimal(k3).mul(Decimal.log10(gap)));
    
    // Distance exponent calculation  
    const distanceExponent = new Decimal(k4)
      .plus(new Decimal(k5).mul(Decimal.log10(iarc)))
      .plus(new Decimal(k6).mul(Decimal.log10(gap)))
      .plus(new Decimal(k7).mul(Decimal.log10(iarc)).mul(Decimal.log10(gap)))
      .plus(new Decimal(k8).mul(Decimal.log10(iarc)).pow(2))
      .plus(new Decimal(k9).mul(Decimal.log10(gap)).pow(2));
    
    // IEEE 1584-2018 Incident Energy Equation
    const logE = new Decimal(k10)
      .plus(new Decimal(k11).mul(Decimal.log10(iarc)))
      .plus(new Decimal(k12).mul(Decimal.log10(gap)))
      .plus(new Decimal(k13).mul(Decimal.log10(workingDistance)))
      .plus(new Decimal(timeExponent).mul(Decimal.log10(time)))
      .plus(new Decimal(distanceExponent).mul(Decimal.log10(workingDistance.div(305))))
      .plus(Decimal.log10(cf));
    
    let energy = Decimal.pow(10, logE).mul(4.184); // Convert to cal/cm²
    
    // Apply correction factor for min/max
    if (isMin) {
      energy = energy.mul(0.98);
    } else {
      energy = energy.mul(1.02);
    }
    
    return energy;
  }
  
  /**
   * Get incident energy coefficients based on electrode configuration
   */
  private getIncidentEnergyCoefficients(electrodeConfig: string): any {
    // IEEE 1584-2018 Table 2 coefficients
    const coefficientTable = {
      VCB: {
        k1: 0.753, k2: 0.566, k3: -0.145, k4: -1.791, k5: -0.337,
        k6: -0.101, k7: 0.068, k8: 0.042, k9: 0.011, k10: -3.898,
        k11: 0.983, k12: 0.094, k13: -1.598
      },
      VCBB: {
        k1: 0.611, k2: 0.559, k3: -0.176, k4: -1.771, k5: -0.302,
        k6: -0.153, k7: 0.073, k8: 0.037, k9: 0.019, k10: -4.224,
        k11: 0.987, k12: 0.139, k13: -1.568
      },
      HCB: {
        k1: 0.950, k2: 0.614, k3: -0.168, k4: -2.351, k5: -0.341,
        k6: -0.101, k7: 0.092, k8: 0.038, k9: 0.008, k10: -4.066,
        k11: 1.081, k12: 0.106, k13: -1.999
      },
      VOA: {
        k1: 1.015, k2: 0.656, k3: -0.164, k4: -2.000, k5: -0.428,
        k6: -0.115, k7: 0.077, k8: 0.067, k9: 0.012, k10: -3.991,
        k11: 1.057, k12: 0.140, k13: -1.999
      },
      HOA: {
        k1: 0.592, k2: 0.605, k3: -0.167, k4: -1.892, k5: -0.301,
        k6: -0.101, k7: 0.074, k8: 0.035, k9: 0.008, k10: -4.783,
        k11: 1.036, k12: 0.165, k13: -1.999
      }
    };
    
    return coefficientTable[electrodeConfig] || coefficientTable.VCB;
  }
  
  /**
   * Calculate final incident energy using interpolation
   */
  private calculateFinalIncidentEnergy(
    voc: Decimal,
    e600: Decimal,
    e2700: Decimal,
    e14300: Decimal
  ): Decimal {
    if (voc.lte(0.6)) {
      return e600;
    } else if (voc.lte(2.7)) {
      // Interpolate between 600V and 2700V
      const ratio = voc.sub(0.6).div(2.7 - 0.6);
      return e600.plus(e2700.sub(e600).mul(ratio));
    } else if (voc.lte(14.3)) {
      // Interpolate between 2700V and 14300V
      const ratio = voc.sub(2.7).div(14.3 - 2.7);
      return e2700.plus(e14300.sub(e2700).mul(ratio));
    } else {
      return e14300;
    }
  }
  
  /**
   * Calculate arc flash boundary distance
   */
  private calculateArcFlashBoundary(
    incidentEnergy: Decimal,
    time: number,
    ibf: Decimal,
    gap: Decimal,
    electrodeConfig: string,
    voc: Decimal
  ): Decimal {
    // Arc flash boundary is where incident energy = 1.2 cal/cm²
    const targetEnergy = new Decimal(IEEE_1584_CONSTANTS.AFB_INCIDENT_ENERGY);
    
    // Use iterative method to find distance where energy = 1.2 cal/cm²
    let distance = new Decimal(610); // Start at 24 inches
    let energy = incidentEnergy;
    let step = new Decimal(100);
    let iterations = 0;
    const maxIterations = 100;
    
    while (iterations < maxIterations && energy.sub(targetEnergy).abs().gt(0.01)) {
      if (energy.gt(targetEnergy)) {
        distance = distance.plus(step);
      } else {
        distance = distance.sub(step);
        step = step.div(2);
      }
      
      // Recalculate energy at new distance
      const cf = this.calculateEnclosureCorrectionFactor(
        new Decimal(20), // Standard width
        new Decimal(20), // Standard height
        electrodeConfig,
        voc
      );
      
      energy = this.calculateIncidentEnergyAtDistance(
        ibf,
        cf,
        time,
        distance,
        gap,
        electrodeConfig,
        voc
      );
      
      iterations++;
    }
    
    return distance;
  }
  
  /**
   * Calculate incident energy at specific distance
   */
  private calculateIncidentEnergyAtDistance(
    ibf: Decimal,
    cf: Decimal,
    time: number,
    distance: Decimal,
    gap: Decimal,
    electrodeConfig: string,
    voc: Decimal
  ): Decimal {
    // Simplified calculation for boundary determination
    const baseEnergy = new Decimal(5); // Base energy at standard distance
    const distanceRatio = new Decimal(610).div(distance).pow(2); // Inverse square law approximation
    
    return baseEnergy.mul(distanceRatio).mul(cf);
  }
  
  /**
   * Calculate reduced incident energy for 85% arcing current
   */
  private calculateReducedIncidentEnergy(
    reducedIarc: Decimal,
    cf: Decimal,
    time: number,
    workingDistance: Decimal,
    gap: Decimal,
    electrodeConfig: string,
    voc: Decimal
  ): Decimal {
    const coefficients = this.getIncidentEnergyCoefficients(electrodeConfig);
    
    return this.calculateIncidentEnergyAtVoltage(
      reducedIarc,
      cf,
      time,
      workingDistance,
      gap,
      voc.toNumber(),
      coefficients,
      false
    );
  }
  
  /**
   * Determine PPE category based on incident energy
   */
  private determinePPECategory(incidentEnergy: Decimal): number {
    if (incidentEnergy.lte(IEEE_1584_CONSTANTS.PPE_CAT_1_MAX)) {
      return 1;
    } else if (incidentEnergy.lte(IEEE_1584_CONSTANTS.PPE_CAT_2_MAX)) {
      return 2;
    } else if (incidentEnergy.lte(IEEE_1584_CONSTANTS.PPE_CAT_3_MAX)) {
      return 3;
    } else if (incidentEnergy.lte(IEEE_1584_CONSTANTS.PPE_CAT_4_MAX)) {
      return 4;
    } else {
      return 5; // Beyond Cat 4, special PPE required
    }
  }
  
  /**
   * Determine minimum PPE arc rating
   */
  private determinePPEMinRating(incidentEnergy: Decimal): Decimal {
    // PPE arc rating should be higher than calculated incident energy
    // Add safety margin of 10%
    return incidentEnergy.mul(1.1);
  }
  
  /**
   * Calculate shock protection boundaries
   */
  private calculateShockBoundaries(voltage: number): {
    limited: number;
    restricted: number;
  } {
    if (voltage <= 240) {
      return SHOCK_BOUNDARIES['120-240'];
    } else if (voltage <= 600) {
      return SHOCK_BOUNDARIES['241-600'];
    } else if (voltage <= 2500) {
      return SHOCK_BOUNDARIES['601-2500'];
    } else if (voltage <= 8000) {
      return SHOCK_BOUNDARIES['2501-8000'];
    } else {
      return SHOCK_BOUNDARIES['8001-15000'];
    }
  }
  
  /**
   * Determine hazard risk category
   */
  private determineHazardRiskCategory(incidentEnergy: Decimal): number {
    if (incidentEnergy.lt(1.2)) {
      return 0;
    } else if (incidentEnergy.lte(4)) {
      return 1;
    } else if (incidentEnergy.lte(8)) {
      return 2;
    } else if (incidentEnergy.lte(25)) {
      return 3;
    } else if (incidentEnergy.lte(40)) {
      return 4;
    } else {
      return 5; // Extreme hazard
    }
  }
  
  /**
   * Generate PPE recommendations based on category
   */
  private generatePPERecommendations(category: number, voltage: number): string[] {
    const recommendations: string[] = [];
    
    // Base PPE for all categories
    recommendations.push('Safety glasses or safety goggles (SR)');
    recommendations.push('Hearing protection (ear canal inserts)');
    recommendations.push('Leather gloves or rubber insulating gloves with leather protectors (SR)');
    
    // Category-specific PPE
    switch (category) {
      case 1:
        recommendations.push('Arc-rated long-sleeve shirt and pants or coverall (min 4 cal/cm²)');
        recommendations.push('Arc-rated face shield or arc flash suit hood (min 4 cal/cm²)');
        recommendations.push('Hard hat');
        recommendations.push('Leather footwear');
        break;
        
      case 2:
        recommendations.push('Arc-rated long-sleeve shirt and pants or coverall (min 8 cal/cm²)');
        recommendations.push('Arc-rated flash suit hood or face shield with balaclava (min 8 cal/cm²)');
        recommendations.push('Hard hat');
        recommendations.push('Leather footwear');
        break;
        
      case 3:
        recommendations.push('Arc-rated arc flash suit jacket and pants or coverall (min 25 cal/cm²)');
        recommendations.push('Arc-rated arc flash suit hood (min 25 cal/cm²)');
        recommendations.push('Arc-rated gloves or rubber insulating gloves with leather protectors');
        recommendations.push('Hard hat');
        recommendations.push('Leather footwear');
        break;
        
      case 4:
        recommendations.push('Arc-rated arc flash suit jacket and pants or coverall (min 40 cal/cm²)');
        recommendations.push('Arc-rated arc flash suit hood (min 40 cal/cm²)');
        recommendations.push('Arc-rated gloves or rubber insulating gloves with leather protectors');
        recommendations.push('Hard hat');
        recommendations.push('Leather footwear');
        break;
        
      default: // Category 5 or higher
        recommendations.push('NO SAFE PPE EXISTS - DE-ENERGIZE EQUIPMENT');
        recommendations.push('Incident energy exceeds 40 cal/cm²');
        recommendations.push('Remote operation or de-energization required');
        break;
    }
    
    // Add voltage-specific PPE
    if (voltage > 600) {
      recommendations.push(`Rubber insulating gloves rated for ${voltage}V`);
      recommendations.push('Insulated tools rated for voltage class');
    }
    
    return recommendations;
  }
  
  /**
   * Generate safety notes based on calculation results
   */
  private generateSafetyNotes(
    incidentEnergy: Decimal,
    ppeCategory: number,
    voltage: number,
    arcFlashBoundary: Decimal
  ): string[] {
    const notes: string[] = [];
    
    // General warnings
    notes.push(`Arc flash boundary: ${(arcFlashBoundary.toNumber() / 25.4).toFixed(1)} inches`);
    notes.push(`Incident energy: ${incidentEnergy.toFixed(2)} cal/cm² at working distance`);
    
    // Category-specific warnings
    if (ppeCategory >= 4) {
      notes.push('WARNING: Extreme arc flash hazard - consider de-energizing');
      notes.push('Energized work permit required');
      notes.push('Consider remote operation methods');
    } else if (ppeCategory >= 3) {
      notes.push('WARNING: High arc flash hazard');
      notes.push('Energized work permit required');
      notes.push('Ensure proper PPE fit and condition');
    }
    
    // Voltage warnings
    if (voltage > 600) {
      notes.push(`High voltage system (${voltage}V) - additional precautions required`);
      notes.push('Ensure proper insulated tool ratings');
    }
    
    // Time-based warnings
    notes.push('Calculation assumes maximum 2-second arc duration per IEEE 1584');
    notes.push('Verify protective device coordination study is current');
    
    // General safety reminders
    notes.push('All PPE must be properly maintained and inspected');
    notes.push('Workers must be qualified per NFPA 70E requirements');
    notes.push('Establish an electrically safe work condition when possible');
    
    return notes;
  }
  
  /**
   * Save calculation to database
   */
  private async saveCalculation(
    input: ArcFlashInput,
    result: ArcFlashResult
  ): Promise<void> {
    const validUntil = new Date();
    validUntil.setFullYear(validUntil.getFullYear() + 5); // Valid for 5 years
    
    await prisma.arcFlashCalculation.create({
      data: {
        panel_id: input.panelId,
        calculation_method: 'IEEE_1584_2018',
        system_voltage: input.systemVoltage / 1000, // Convert to kV for storage
        system_type: input.systemType,
        grounding_type: input.groundingType,
        frequency: input.frequency || 60,
        bolted_fault_current: input.boltedFaultCurrent / 1000, // Convert to kA
        fault_clearing_time: input.faultClearingTime,
        equipment_type: input.equipmentType,
        electrode_configuration: input.electrodeConfiguration,
        enclosure_width: input.enclosureWidth * 25.4, // Convert to mm
        enclosure_height: input.enclosureHeight * 25.4,
        enclosure_depth: input.enclosureDepth * 25.4,
        conductor_gap: input.conductorGap * 25.4,
        working_distance: input.workingDistance * 25.4,
        arcing_current_min: result.arcingCurrentMin,
        arcing_current_max: result.arcingCurrentMax,
        incident_energy_min: result.incidentEnergyMin,
        incident_energy_max: result.incidentEnergyMax,
        arc_flash_boundary: result.arcFlashBoundary,
        ppe_category: result.ppeCategory,
        ppe_min_arc_rating: result.ppeMinArcRating,
        shock_hazard_voltage: result.shockHazardVoltage,
        limited_approach: result.limitedApproach,
        restricted_approach: result.restrictedApproach,
        enclosure_correction: result.enclosureCorrection,
        reduced_arcing_current: result.reducedArcingCurrent,
        reduced_incident_energy: result.reducedIncidentEnergy,
        hazard_risk_category: result.hazardRiskCategory,
        recommended_ppe: JSON.stringify(result.recommendedPpe),
        safety_notes: result.safetyNotes.join('\n'),
        calculated_by: input.calculatedBy,
        valid_until: validUntil,
      },
    });
  }
  
  /**
   * Get arc flash calculations for a panel
   */
  async getCalculationsForPanel(panelId: string) {
    return await prisma.arcFlashCalculation.findMany({
      where: { panel_id: panelId },
      orderBy: { calculation_date: 'desc' },
    });
  }
  
  /**
   * Get latest calculation for a panel
   */
  async getLatestCalculation(panelId: string) {
    return await prisma.arcFlashCalculation.findFirst({
      where: { panel_id: panelId },
      orderBy: { calculation_date: 'desc' },
    });
  }
  
  /**
   * Generate arc flash label data
   */
  async generateLabelData(calculationId: string) {
    const calculation = await prisma.arcFlashCalculation.findUnique({
      where: { id: calculationId },
      include: { panel: true },
    });
    
    if (!calculation) {
      throw new Error('Calculation not found');
    }
    
    return {
      panelName: calculation.panel.name,
      location: calculation.panel.location,
      voltage: `${calculation.system_voltage * 1000}V`,
      incidentEnergy: `${Math.max(calculation.incident_energy_max, calculation.reduced_incident_energy || 0).toFixed(2)} cal/cm²`,
      workingDistance: `${(calculation.working_distance / 25.4).toFixed(0)} inches`,
      arcFlashBoundary: `${(calculation.arc_flash_boundary / 25.4).toFixed(0)} inches`,
      ppeCategory: calculation.ppe_category,
      limitedApproach: `${(calculation.limited_approach / 25.4).toFixed(0)} inches`,
      restrictedApproach: `${(calculation.restricted_approach / 25.4).toFixed(0)} inches`,
      calculationDate: calculation.calculation_date.toLocaleDateString(),
      validUntil: calculation.valid_until.toLocaleDateString(),
    };
  }
}

export const arcFlashService = new ArcFlashCalculationService();