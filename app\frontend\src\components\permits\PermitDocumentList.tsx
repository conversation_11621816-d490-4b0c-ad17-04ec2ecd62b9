import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { permitService, PermitDocument } from '../../services/permitService';
import { format } from 'date-fns';
import {
  FileText,
  Download,
  Send,
  Eye,
  Edit,
  Trash2,
  FileSignature,
  Paperclip,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface PermitDocumentListProps {
  documents: PermitDocument[];
  onUpdate: () => void;
  projectId: string;
}

export const PermitDocumentList: React.FC<PermitDocumentListProps> = ({
  documents,
  onUpdate,
  projectId
}) => {
  const [selectedDocument, setSelectedDocument] = useState<PermitDocument | null>(null);
  const [generating, setGenerating] = useState<string | null>(null);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Edit className="h-4 w-4" />;
      case 'READY':
        return <FileSignature className="h-4 w-4" />;
      case 'SUBMITTED':
        return <Send className="h-4 w-4" />;
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4" />;
      case 'EXPIRED':
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const handleGeneratePDF = async (document: PermitDocument) => {
    try {
      setGenerating(document.id);
      
      let result;
      switch (document.document_type) {
        case 'PERMIT_APPLICATION':
          result = await permitService.generatePermitApplicationPDF(document.id);
          break;
        case 'INSPECTION_REQUEST':
          result = await permitService.generateInspectionRequestPDF(document.id);
          break;
        default:
          throw new Error('PDF generation not supported for this document type');
      }

      alert(`PDF generated successfully! ${result.pageCount} pages, ${(result.sizeBytes / 1024).toFixed(2)} KB`);
      onUpdate();
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      alert('Failed to generate PDF');
    } finally {
      setGenerating(null);
    }
  };

  const handleSubmit = async (document: PermitDocument) => {
    try {
      const submissionDate = new Date().toISOString();
      await permitService.submitPermitDocument(document.id, {
        submission_date: submissionDate
      });
      alert('Document submitted successfully!');
      onUpdate();
    } catch (error) {
      console.error('Failed to submit document:', error);
      alert('Failed to submit document');
    }
  };

  const handleDownloadPDF = async (document: PermitDocument) => {
    if (!document.generated_pdf_path) {
      alert('No PDF generated yet. Please generate the PDF first.');
      return;
    }

    try {
      const blob = await permitService.downloadPDF(document.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${document.document_type.toLowerCase()}_${document.id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to download PDF:', error);
      alert('Failed to download PDF');
    }
  };

  const groupedDocuments = documents.reduce((acc, doc) => {
    const type = doc.document_type;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(doc);
    return acc;
  }, {} as Record<string, PermitDocument[]>);

  return (
    <div className="space-y-6">
      {Object.entries(groupedDocuments).map(([type, docs]) => (
        <Card key={type}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {permitService.getDocumentTypeDisplayName(type)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {docs.map((doc) => (
                <div
                  key={doc.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium">{doc.title}</h3>
                        <Badge variant={permitService.getStatusColor(doc.status) as any}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(doc.status)}
                            {doc.status}
                          </span>
                        </Badge>
                        {doc.document_version > 1 && (
                          <Badge variant="outline">v{doc.document_version}</Badge>
                        )}
                      </div>
                      {doc.description && (
                        <p className="text-sm text-gray-600 mt-1">{doc.description}</p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                        <span>Jurisdiction: {doc.jurisdiction}</span>
                        {doc.permit_number && (
                          <span>Permit #: {doc.permit_number}</span>
                        )}
                        <span>Created: {format(new Date(doc.created_at), 'MMM d, yyyy')}</span>
                      </div>
                      {doc.submission_date && (
                        <div className="text-sm text-gray-500 mt-1">
                          Submitted: {format(new Date(doc.submission_date), 'MMM d, yyyy')}
                        </div>
                      )}
                      {doc.rejection_reason && (
                        <div className="text-sm text-red-600 mt-2">
                          Rejection reason: {doc.rejection_reason}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {doc.status === 'DRAFT' && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleGeneratePDF(doc)}
                            disabled={generating === doc.id}
                          >
                            {generating === doc.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900" />
                            ) : (
                              <FileText className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedDocument(doc)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      {doc.status === 'READY' && !doc.signature_required && (
                        <Button
                          size="sm"
                          onClick={() => handleSubmit(doc)}
                        >
                          <Send className="h-4 w-4 mr-1" />
                          Submit
                        </Button>
                      )}
                      {doc.status === 'READY' && doc.signature_required && !doc.signed_by && (
                        <Button
                          size="sm"
                          variant="outline"
                        >
                          <FileSignature className="h-4 w-4 mr-1" />
                          Sign
                        </Button>
                      )}
                      {doc.generated_pdf_path && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadPDF(doc)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedDocument(doc)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  {doc.attachments && JSON.parse(doc.attachments).length > 0 && (
                    <div className="mt-3 flex items-center gap-2 text-sm text-gray-600">
                      <Paperclip className="h-4 w-4" />
                      {JSON.parse(doc.attachments).length} attachment(s)
                    </div>
                  )}
                  {doc.generated_pdf_path && (
                    <div className="mt-3 flex items-center gap-4 text-sm text-gray-600">
                      <span>PDF: {doc.page_count} pages</span>
                      <span>{((doc.file_size_bytes || 0) / 1024).toFixed(2)} KB</span>
                      {doc.generation_date && (
                        <span>Generated: {format(new Date(doc.generation_date), 'MMM d, yyyy h:mm a')}</span>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}

      {documents.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No permit documents yet</p>
            <p className="text-sm text-gray-500 mt-1">
              Create your first permit application to get started
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};