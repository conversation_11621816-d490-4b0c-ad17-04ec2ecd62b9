import { VoltageDropService } from '../voltage-drop';
import { Decimal } from 'decimal.js';

describe('VoltageDropService', () => {
  let service: VoltageDropService;

  beforeEach(() => {
    service = new VoltageDropService();
  });

  describe('Basic Voltage Drop Calculations', () => {
    it('should calculate single-phase voltage drop correctly', async () => {
      const input = {
        circuit_name: 'Lighting Circuit',
        voltage: 120,
        phase: '1PH' as const,
        amperage: 20,
        distance: 100,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.circuit_name).toBe('Lighting Circuit');
      expect(result.voltage).toBe(120);
      expect(result.phase).toBe('1PH');
      expect(result.calculated_vd_percent).toBeGreaterThan(0);
      expect(result.calculated_vd_percent).toBeLessThan(5);
      expect(result.passes_branch_limit).toBe(true);
      expect(result.necReferences).toContain('215.2(A)(1)');
      expect(result.necReferences).toContain('210.19(A)(1)');
    });

    it('should calculate three-phase voltage drop correctly', async () => {
      const input = {
        circuit_name: 'Motor Circuit',
        voltage: 480,
        phase: '3PH' as const,
        amperage: 50,
        distance: 200,
        conductor_size: '6',
        conductor_type: 'CU' as const,
        conduit_type: 'STEEL' as const,
        power_factor: 0.85,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.phase).toBe('3PH');
      expect(result.calculated_vd_percent).toBeGreaterThan(0);
      
      // Three-phase should have lower voltage drop than single-phase for same conditions
      const singlePhaseInput = { ...input, phase: '1PH' as const };
      const singlePhaseResult = await service.calculate(singlePhaseInput);
      expect(result.calculated_vd_percent).toBeLessThan(singlePhaseResult.calculated_vd_percent);
    });

    it('should handle different voltages correctly', async () => {
      const voltages = [120, 208, 240, 277, 480];
      
      for (const voltage of voltages) {
        const input = {
          circuit_name: `Test Circuit ${voltage}V`,
          voltage,
          phase: '1PH' as const,
          amperage: 20,
          distance: 100,
          conductor_size: '10',
          conductor_type: 'CU' as const,
          conduit_type: 'PVC' as const,
          power_factor: 0.9,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        
        expect(result.voltage).toBe(voltage);
        expect(result.calculated_vd_volts).toBeGreaterThan(0);
        
        // Higher voltage should have lower percentage drop
        if (voltage > 120) {
          expect(result.calculated_vd_percent).toBeLessThan(5);
        }
      }
    });
  });

  describe('Conductor Types and Sizes', () => {
    it('should handle copper conductors correctly', async () => {
      const copperSizes = ['14', '12', '10', '8', '6', '4', '2', '1', '1/0', '2/0', '3/0', '4/0', '250', '300', '350', '400', '500'];
      
      for (const size of copperSizes) {
        const input = {
          circuit_name: `Copper ${size} AWG`,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 10,
          distance: 50,
          conductor_size: size,
          conductor_type: 'CU' as const,
          conduit_type: 'PVC' as const,
          power_factor: 0.9,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        
        expect(result.conductor_size).toBe(size);
        expect(result.conductor_type).toBe('CU');
        expect(result.conductor_ampacity).toBeGreaterThan(0);
      }
    });

    it('should handle aluminum conductors correctly', async () => {
      const aluminumSizes = ['12', '10', '8', '6', '4', '2', '1', '1/0', '2/0', '3/0', '4/0', '250', '300', '350', '400', '500'];
      
      for (const size of aluminumSizes) {
        const input = {
          circuit_name: `Aluminum ${size} AWG`,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 10,
          distance: 50,
          conductor_size: size,
          conductor_type: 'AL' as const,
          conduit_type: 'AL' as const,
          power_factor: 0.9,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        
        expect(result.conductor_size).toBe(size);
        expect(result.conductor_type).toBe('AL');
        expect(result.conductor_ampacity).toBeGreaterThan(0);
      }
    });

    it('should show aluminum has higher voltage drop than copper', async () => {
      const input = {
        circuit_name: 'Comparison Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 50,
        distance: 200,
        conductor_size: '6',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const copperResult = await service.calculate(input);
      const aluminumResult = await service.calculate({ ...input, conductor_type: 'AL' as const });

      expect(aluminumResult.calculated_vd_percent).toBeGreaterThan(copperResult.calculated_vd_percent);
    });
  });

  describe('Temperature Derating', () => {
    it('should not apply derating at or below 86°F', async () => {
      const input = {
        circuit_name: 'Normal Temp Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 100,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 86
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_applied).toBe(false);
      expect(result.temperature_derating_factor).toBe(1);
      expect(result.conductor_ampacity).toBe(35); // Base ampacity for #10 copper
    });

    it('should apply 96% derating for 87-95°F', async () => {
      const input = {
        circuit_name: 'Warm Temp Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 100,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 90
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_applied).toBe(true);
      expect(result.temperature_derating_factor).toBe(0.96);
      expect(result.conductor_ampacity).toBeCloseTo(35 * 0.96, 1);
      expect(result.necReferences).toContain('310.15(B)(1)');
    });

    it('should apply 94% derating for 96-104°F', async () => {
      const input = {
        circuit_name: 'Hot Temp Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 100,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 100
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_applied).toBe(true);
      expect(result.temperature_derating_factor).toBe(0.94);
      expect(result.conductor_ampacity).toBeCloseTo(35 * 0.94, 1);
    });

    it('should apply 88% derating above 104°F', async () => {
      const input = {
        circuit_name: 'Very Hot Temp Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 100,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 110
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_applied).toBe(true);
      expect(result.temperature_derating_factor).toBe(0.88);
      expect(result.conductor_ampacity).toBeCloseTo(35 * 0.88, 1);
    });
  });

  describe('NEC Compliance Checks', () => {
    it('should pass branch circuit limit when under 3%', async () => {
      const input = {
        circuit_name: 'Compliant Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 20,
        distance: 50,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.calculated_vd_percent).toBeLessThanOrEqual(3);
      expect(result.passes_branch_limit).toBe(true);
      expect(result.nec_limit_branch).toBe(3);
    });

    it('should fail branch circuit limit when over 3%', async () => {
      const input = {
        circuit_name: 'Non-compliant Circuit',
        voltage: 120,
        phase: '1PH' as const,
        amperage: 20,
        distance: 200,
        conductor_size: '14',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.calculated_vd_percent).toBeGreaterThan(3);
      expect(result.passes_branch_limit).toBe(false);
      expect(result.recommendations).toContain('Voltage drop exceeds 3% branch circuit limit');
    });

    it('should check total limit of 5%', async () => {
      const input = {
        circuit_name: 'Total Limit Check',
        voltage: 120,
        phase: '1PH' as const,
        amperage: 30,
        distance: 300,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.nec_limit_total).toBe(5);
      expect(result.passes_total_limit).toBe(result.calculated_vd_percent <= 5);
    });

    it('should check conductor ampacity adequacy', async () => {
      const input = {
        circuit_name: 'Undersized Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30, // #14 copper is only rated for 20A
        distance: 50,
        conductor_size: '14',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.adequate_ampacity).toBe(false);
      expect(result.conductor_ampacity).toBe(20);
      expect(result.recommendations).toContain('Conductor ampacity (20.0 A) is less than circuit amperage (30 A)');
      expect(result.necReferences).toContain('310.16');
    });

    it('should pass ampacity check when conductor is adequate', async () => {
      const input = {
        circuit_name: 'Properly Sized Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 50,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.adequate_ampacity).toBe(true);
      expect(result.conductor_ampacity).toBe(35);
    });
  });

  describe('Power Factor Effects', () => {
    it('should show higher voltage drop with lower power factor', async () => {
      const baseInput = {
        circuit_name: 'Power Factor Test',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 50,
        distance: 200,
        conductor_size: '6',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 1.0,
        ambient_temp_f: 75
      };

      const pf100 = await service.calculate(baseInput);
      const pf90 = await service.calculate({ ...baseInput, power_factor: 0.9 });
      const pf80 = await service.calculate({ ...baseInput, power_factor: 0.8 });
      const pf70 = await service.calculate({ ...baseInput, power_factor: 0.7 });

      // Lower power factor should result in higher voltage drop
      expect(pf90.calculated_vd_percent).toBeGreaterThan(pf100.calculated_vd_percent);
      expect(pf80.calculated_vd_percent).toBeGreaterThan(pf90.calculated_vd_percent);
      expect(pf70.calculated_vd_percent).toBeGreaterThan(pf80.calculated_vd_percent);
    });

    it('should handle typical power factors correctly', async () => {
      const typicalPowerFactors = [
        { pf: 1.0, description: 'Resistive loads' },
        { pf: 0.9, description: 'General loads' },
        { pf: 0.85, description: 'Motors at full load' },
        { pf: 0.8, description: 'Fluorescent lighting' },
        { pf: 0.7, description: 'Motors at partial load' }
      ];

      for (const { pf, description } of typicalPowerFactors) {
        const input = {
          circuit_name: description,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 30,
          distance: 100,
          conductor_size: '10',
          conductor_type: 'CU' as const,
          conduit_type: 'PVC' as const,
          power_factor: pf,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        
        expect(result.calculated_vd_percent).toBeGreaterThan(0);
        expect(result.calculated_vd_percent).toBeLessThan(10);
      }
    });
  });

  describe('Distance Effects', () => {
    it('should show linear relationship with distance', async () => {
      const distances = [50, 100, 200, 400];
      const results: number[] = [];

      for (const distance of distances) {
        const input = {
          circuit_name: `Distance ${distance}ft`,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 30,
          distance,
          conductor_size: '10',
          conductor_type: 'CU' as const,
          conduit_type: 'PVC' as const,
          power_factor: 0.9,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        results.push(result.calculated_vd_percent);
      }

      // Voltage drop should double when distance doubles
      expect(results[1]).toBeCloseTo(results[0] * 2, 1);
      expect(results[2]).toBeCloseTo(results[0] * 4, 1);
      expect(results[3]).toBeCloseTo(results[0] * 8, 1);
    });

    it('should handle very short distances', async () => {
      const input = {
        circuit_name: 'Very Short Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 50,
        distance: 5,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.calculated_vd_percent).toBeLessThan(1);
      expect(result.passes_branch_limit).toBe(true);
    });

    it('should handle very long distances', async () => {
      const input = {
        circuit_name: 'Very Long Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 20,
        distance: 1000,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.calculated_vd_percent).toBeGreaterThan(5);
      expect(result.passes_branch_limit).toBe(false);
      expect(result.passes_total_limit).toBe(false);
    });
  });

  describe('Recommendations', () => {
    it('should recommend larger conductor for excessive voltage drop', async () => {
      const input = {
        circuit_name: 'Undersized for Distance',
        voltage: 120,
        phase: '1PH' as const,
        amperage: 20,
        distance: 150,
        conductor_size: '14',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.passes_branch_limit).toBe(false);
      expect(result.recommendations).toContainEqual(expect.stringContaining('Consider using larger conductor'));
      expect(result.recommendations).toContainEqual(expect.stringContaining('Recommended conductor size:'));
    });

    it('should warn when approaching limits', async () => {
      // Find a configuration that gives 2-3% voltage drop
      const input = {
        circuit_name: 'Approaching Limit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 20,
        distance: 100,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      if (result.calculated_vd_percent > 2 && result.calculated_vd_percent <= 3) {
        expect(result.recommendations).toContain('Voltage drop is acceptable but approaching limits');
        expect(result.recommendations).toContain('Consider upsizing conductor for future expansion');
      }
    });

    it('should indicate when everything is acceptable', async () => {
      const input = {
        circuit_name: 'Well Designed Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 20,
        distance: 50,
        conductor_size: '10',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.passes_branch_limit).toBe(true);
      expect(result.adequate_ampacity).toBe(true);
      expect(result.recommendations).toContain('Voltage drop is within acceptable limits');
      expect(result.recommendations).toContain('Conductor size is adequate for the load');
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero amperage', async () => {
      const input = {
        circuit_name: 'No Load Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 0,
        distance: 100,
        conductor_size: '12',
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.calculated_vd_volts).toBe(0);
      expect(result.calculated_vd_percent).toBe(0);
      expect(result.passes_branch_limit).toBe(true);
      expect(result.adequate_ampacity).toBe(true);
    });

    it('should handle maximum current values', async () => {
      const input = {
        circuit_name: 'High Current Circuit',
        voltage: 480,
        phase: '3PH' as const,
        amperage: 1000,
        distance: 100,
        conductor_size: '1000',
        conductor_type: 'CU' as const,
        conduit_type: 'STEEL' as const,
        power_factor: 0.85,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.amperage).toBe(1000);
      expect(result.calculated_vd_percent).toBeGreaterThan(0);
      expect(result.conductor_ampacity).toBeGreaterThan(1000);
    });

    it('should handle invalid conductor sizes', async () => {
      const input = {
        circuit_name: 'Invalid Size Circuit',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 20,
        distance: 100,
        conductor_size: '999', // Invalid size
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.conductor_ampacity).toBe(0);
      expect(result.adequate_ampacity).toBe(false);
    });

    it('should handle extreme temperatures', async () => {
      const temperatures = [-20, 0, 32, 150, 200];

      for (const temp of temperatures) {
        const input = {
          circuit_name: `Temp ${temp}°F`,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 30,
          distance: 100,
          conductor_size: '10',
          conductor_type: 'CU' as const,
          conduit_type: 'PVC' as const,
          power_factor: 0.9,
          ambient_temp_f: temp
        };

        const result = await service.calculate(input);
        
        expect(result.calculated_vd_percent).toBeGreaterThan(0);
        
        if (temp > 86) {
          expect(result.temperature_derating_applied).toBe(true);
          expect(result.temperature_derating_factor).toBeLessThan(1);
        }
      }
    });
  });

  describe('Conduit Type Variations', () => {
    it('should handle all conduit types', async () => {
      const conduitTypes: Array<'STEEL' | 'PVC' | 'AL'> = ['STEEL', 'PVC', 'AL'];

      for (const conduitType of conduitTypes) {
        const input = {
          circuit_name: `${conduitType} Conduit`,
          voltage: 240,
          phase: '1PH' as const,
          amperage: 30,
          distance: 100,
          conductor_size: '10',
          conductor_type: 'CU' as const,
          conduit_type: conduitType,
          power_factor: 0.9,
          ambient_temp_f: 75
        };

        const result = await service.calculate(input);
        
        expect(result.calculated_vd_percent).toBeGreaterThan(0);
        // Note: Current implementation doesn't differentiate by conduit type
        // but the interface supports it for future enhancements
      }
    });
  });

  describe('Required Conductor Size Calculation', () => {
    it('should recommend correct conductor size for voltage drop', async () => {
      const input = {
        circuit_name: 'Size Recommendation Test',
        voltage: 240,
        phase: '1PH' as const,
        amperage: 30,
        distance: 200,
        conductor_size: '14', // Too small
        conductor_type: 'CU' as const,
        conduit_type: 'PVC' as const,
        power_factor: 0.9,
        ambient_temp_f: 75
      };

      const result = await service.calculate(input);

      expect(result.passes_branch_limit).toBe(false);
      
      // Should recommend a conductor size in the recommendations
      const sizeRecommendation = result.recommendations.find(r => 
        r.includes('Recommended conductor size:')
      );
      expect(sizeRecommendation).toBeDefined();
      
      // The recommended size should be larger than #14
      if (sizeRecommendation) {
        // Extract the recommended size from the string
        const match = sizeRecommendation.match(/Recommended conductor size: (.+)/);
        if (match) {
          const recommendedSize = match[1];
          // Verify it's a valid conductor size
          expect(['12', '10', '8', '6', '4', '3', '2', '1', '1/0', '2/0', '3/0', '4/0']).toContain(recommendedSize);
        }
      }
    });
  });
});