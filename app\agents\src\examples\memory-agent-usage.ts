import { MemoryAgent, KnowledgeCategory } from '../agents/memory-agent';
import { MessageBus } from '../infrastructure/message-bus';

/**
 * Example usage of the Memory Agent in an electrical calculation system
 * 
 * This demonstrates how other agents can leverage the Memory Agent's capabilities
 * for learning, pattern recognition, and knowledge retrieval.
 */

async function demonstrateMemoryAgentUsage() {
  // Initialize the Memory Agent
  const memoryAgent = new MemoryAgent({
    id: 'main-memory-agent',
    name: 'Electrical Memory Agent',
    description: 'Central memory and learning system for electrical calculations',
  });

  console.log('Memory Agent initialized successfully\n');

  // Example 1: Store NEC violation patterns
  console.log('=== Example 1: Storing NEC Violation Patterns ===');
  
  const necViolation = {
    content: {
      code: 'NEC 210.52(C)',
      violation: 'Kitchen countertop receptacles not spaced properly',
      details: 'Receptacles must be installed so that no point along the wall line is more than 24 inches from a receptacle outlet',
      severity: 'high',
      commonLocations: ['Kitchen', 'Bathroom countertops'],
      correctiveAction: 'Install additional receptacles to meet spacing requirements',
    },
    category: KnowledgeCategory.NEC_VIOLATIONS,
    tags: ['NEC', '210.52', 'receptacles', 'kitchen', 'spacing'],
    importance: 0.9,
    relatedConcepts: ['countertop receptacles', 'GFCI protection', 'small appliance circuits'],
  };

  const storeResult = await memoryAgent['processTask']('store-knowledge', necViolation);
  console.log('Stored NEC violation:', storeResult);

  // Example 2: Learn from calculation errors
  console.log('\n=== Example 2: Learning from Calculation Errors ===');
  
  const calculationError = {
    errorType: 'voltage-drop-exceeded',
    context: {
      circuitLength: 150,
      wireSize: '14 AWG',
      load: 15,
      voltageDropCalculated: 5.2,
      maxAllowed: 3,
      units: { length: 'feet', load: 'amps', voltageDrop: 'percent' },
    },
    correctSolution: {
      recommendedWireSize: '12 AWG',
      resultingVoltageDrop: 2.8,
      calculation: 'VD = (2 × L × I × R) / 1000',
    },
    category: KnowledgeCategory.COMMON_MISTAKES,
  };

  const errorLearning = await memoryAgent['processTask']('learn-from-errors', calculationError);
  console.log('Learned from error:', errorLearning);

  // Example 3: Store calculation patterns
  console.log('\n=== Example 3: Storing Calculation Patterns ===');
  
  const calculationPatterns = [
    {
      content: {
        pattern: 'Residential Service Load Calculation',
        method: 'NEC 220.82 Optional Method',
        steps: [
          'Calculate general lighting load: 3 VA/sq ft',
          'Add small appliance circuits: 1,500 VA each (min 2)',
          'Add laundry circuit: 1,500 VA',
          'Add fixed appliances at nameplate rating',
          'Apply demand factors per NEC 220.82',
        ],
        example: {
          squareFootage: 2000,
          generalLighting: 6000,
          smallAppliance: 3000,
          laundry: 1500,
          totalBeforeDemand: 10500,
        },
      },
      category: KnowledgeCategory.CALCULATION_PATTERNS,
      tags: ['service-sizing', 'residential', 'NEC-220.82', 'load-calculation'],
      importance: 0.95,
    },
    {
      content: {
        pattern: 'Conduit Fill Calculation',
        method: 'NEC Chapter 9 Tables',
        steps: [
          'Identify conductor type and size',
          'Look up conductor area in Chapter 9, Table 5',
          'Determine conduit type',
          'Apply 40% fill for 3+ conductors',
          'Select conduit size from Chapter 9, Table 4',
        ],
        commonMistakes: [
          'Using wrong table for conductor type',
          'Forgetting to account for equipment grounding conductor',
          'Applying wrong fill percentage',
        ],
      },
      category: KnowledgeCategory.CALCULATION_PATTERNS,
      tags: ['conduit-fill', 'Chapter-9', 'conductor-sizing'],
      importance: 0.88,
    },
  ];

  for (const pattern of calculationPatterns) {
    await memoryAgent['processTask']('store-knowledge', pattern);
  }
  console.log(`Stored ${calculationPatterns.length} calculation patterns`);

  // Example 4: Retrieve relevant knowledge
  console.log('\n=== Example 4: Retrieving Relevant Knowledge ===');
  
  const query = 'kitchen receptacle spacing requirements';
  const retrievedKnowledge = await memoryAgent['processTask']('retrieve-knowledge', {
    query: query,
    category: KnowledgeCategory.NEC_VIOLATIONS,
    limit: 5,
  });

  console.log(`Found ${retrievedKnowledge.length} relevant memories for query: "${query}"`);
  if (retrievedKnowledge.length > 0) {
    console.log('Top result:', retrievedKnowledge[0].content);
  }

  // Example 5: Find patterns in stored knowledge
  console.log('\n=== Example 5: Finding Patterns ===');
  
  const patterns = await memoryAgent['processTask']('find-patterns', {
    category: KnowledgeCategory.CALCULATION_PATTERNS,
    minConfidence: 0.5,
    includeRelated: true,
  });

  console.log(`Found ${patterns.length} patterns`);
  patterns.forEach(pattern => {
    console.log(`- ${pattern.pattern} (confidence: ${(pattern.confidence * 100).toFixed(0)}%)`);
  });

  // Example 6: Store material pricing trends
  console.log('\n=== Example 6: Material Pricing Tracking ===');
  
  const pricingData = {
    content: {
      material: '12-2 NM-B Cable',
      manufacturer: 'Southwire',
      price: 0.85,
      unit: 'per foot',
      date: new Date(),
      supplier: 'Local Supply House',
      region: 'Northeast US',
      trend: 'increasing',
      percentChange: 5.2,
    },
    category: KnowledgeCategory.MATERIAL_PRICING,
    tags: ['cable', '12-2', 'NM-B', 'pricing', 'Northeast'],
    importance: 0.7,
  };

  await memoryAgent['processTask']('store-knowledge', pricingData);
  console.log('Stored material pricing data');

  // Example 7: Get insights on a topic
  console.log('\n=== Example 7: Getting Insights ===');
  
  const insights = await memoryAgent['processTask']('get-insights', {
    topic: 'voltage drop calculations',
    category: KnowledgeCategory.CALCULATION_PATTERNS,
  });

  console.log(`Insights on "voltage drop calculations":`);
  console.log(`Confidence: ${(insights.confidence * 100).toFixed(0)}%`);
  console.log('Key insights:');
  insights.insights.forEach(insight => {
    console.log(`- ${insight}`);
  });
  console.log('Related concepts:', insights.relatedConcepts);

  // Example 8: Build knowledge graph
  console.log('\n=== Example 8: Building Knowledge Graph ===');
  
  const graphResult = await memoryAgent['processTask']('build-knowledge-graph', {
    category: KnowledgeCategory.CALCULATION_PATTERNS,
  });

  console.log(`Knowledge graph built with ${graphResult.nodes} nodes and ${graphResult.connections} connections`);

  // Example 9: Memory consolidation
  console.log('\n=== Example 9: Memory Consolidation ===');
  
  const consolidationResult = await memoryAgent['processTask']('consolidate-memory', {
    threshold: 0.7,
    minAccessCount: 2,
    timeWindow: 24 * 60 * 60 * 1000, // 24 hours
  });

  console.log(`Memory consolidation: ${consolidationResult.consolidated} consolidated, ${consolidationResult.pruned} pruned`);

  // Example 10: Memory optimization
  console.log('\n=== Example 10: Memory Optimization ===');
  
  const optimizationResult = await memoryAgent['processTask']('optimize-memory', {
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    minImportance: 0.3,
    preserveCategories: [KnowledgeCategory.NEC_VIOLATIONS, KnowledgeCategory.BEST_PRACTICES],
  });

  console.log(`Memory optimization: ${optimizationResult.removed} removed, ${optimizationResult.optimized} optimized`);

  // Example 11: Integration with other agents via MessageBus
  console.log('\n=== Example 11: MessageBus Integration ===');
  
  const messageBus = MessageBus.getInstance();
  
  // Simulate another agent requesting knowledge
  const taskRequest = MessageBus.createTaskRequest(
    'calculation-agent',
    'main-memory-agent',
    'retrieve-knowledge',
    {
      query: 'wire sizing for 30 amp circuit',
      category: KnowledgeCategory.CALCULATION_PATTERNS,
      limit: 3,
    },
    'HIGH'
  );

  console.log('Simulating knowledge request from another agent...');
  // In a real scenario, the response would be handled via the message bus

  // Cleanup
  await memoryAgent.shutdown();
  console.log('\nMemory Agent shutdown complete');
}

// Advanced usage example: Creating a learning feedback loop
async function demonstrateLearningFeedbackLoop() {
  console.log('\n=== Advanced Example: Learning Feedback Loop ===');
  
  const memoryAgent = new MemoryAgent({
    id: 'learning-memory-agent',
    name: 'Learning Memory Agent',
    description: 'Memory agent with continuous learning capabilities',
  });

  // Simulate multiple calculations with outcomes
  const calculations = [
    {
      type: 'wire-sizing',
      input: { load: 25, distance: 100, voltage: 240 },
      output: { wireSize: '10 AWG', voltageDrop: 2.1 },
      success: true,
    },
    {
      type: 'wire-sizing',
      input: { load: 25, distance: 200, voltage: 240 },
      output: { wireSize: '10 AWG', voltageDrop: 4.2 },
      success: false, // Voltage drop too high
      corrected: { wireSize: '8 AWG', voltageDrop: 2.6 },
    },
    {
      type: 'wire-sizing',
      input: { load: 40, distance: 150, voltage: 240 },
      output: { wireSize: '8 AWG', voltageDrop: 3.1 },
      success: false, // Slightly over 3%
      corrected: { wireSize: '6 AWG', voltageDrop: 2.0 },
    },
  ];

  // Process calculations and learn from outcomes
  for (const calc of calculations) {
    if (calc.success) {
      // Store successful pattern
      await memoryAgent['processTask']('store-knowledge', {
        content: {
          calculationType: calc.type,
          input: calc.input,
          output: calc.output,
          outcome: 'success',
        },
        category: KnowledgeCategory.CALCULATION_PATTERNS,
        tags: [calc.type, 'successful'],
        importance: 0.7,
      });
    } else {
      // Learn from error
      await memoryAgent['processTask']('learn-from-errors', {
        errorType: `${calc.type}-incorrect`,
        context: {
          input: calc.input,
          incorrectOutput: calc.output,
          issue: 'Voltage drop exceeded 3% limit',
        },
        correctSolution: calc.corrected,
        category: KnowledgeCategory.COMMON_MISTAKES,
      });
    }
  }

  // After learning, find patterns
  const learnedPatterns = await memoryAgent['processTask']('find-patterns', {
    category: KnowledgeCategory.COMMON_MISTAKES,
    minConfidence: 0.1,
    includeRelated: true,
  });

  console.log('Learned patterns from feedback loop:');
  learnedPatterns.forEach(pattern => {
    console.log(`- ${pattern.pattern}`);
    console.log(`  Occurrences: ${pattern.occurrences}`);
    console.log(`  Confidence: ${(pattern.confidence * 100).toFixed(0)}%`);
  });

  // Get insights based on learned patterns
  const wireSizingInsights = await memoryAgent['processTask']('get-insights', {
    topic: 'wire sizing voltage drop',
  });

  console.log('\nInsights from learning:');
  wireSizingInsights.insights.forEach(insight => {
    console.log(`- ${insight}`);
  });

  await memoryAgent.shutdown();
}

// Run the demonstrations
async function main() {
  try {
    await demonstrateMemoryAgentUsage();
    await demonstrateLearningFeedbackLoop();
  } catch (error) {
    console.error('Error in demonstration:', error);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}