import { Decimal } from 'decimal.js';
import { z } from 'zod';

// Input validation schema
export const ShortCircuitInputSchema = z.object({
  // Source/Utility Data
  utilityVoltage: z.number().min(120).max(15000),
  utilityFaultCurrent: z.number().min(1).max(200), // kA
  utilityXRRatio: z.number().min(1).max(50).default(10),
  
  // Transformer Data (optional)
  transformerKva: z.number().min(10).max(5000).optional(),
  transformerImpedance: z.number().min(1).max(15).optional(), // percent
  transformerXRRatio: z.number().min(1).max(50).optional(),
  transformerPrimaryV: z.number().min(120).max(35000).optional(),
  transformerSecondaryV: z.number().min(120).max(600).optional(),
  transformerType: z.enum(['DRY_TYPE', 'OIL_FILLED', 'CAST_RESIN']).optional(),
  
  // Conductor Data
  conductorLength: z.number().min(0).max(5000), // feet
  conductorSize: z.string(),
  conductorMaterial: z.enum(['COPPER', 'ALUMINUM']).default('COPPER'),
  conductorType: z.string(),
  conductorsPerPhase: z.number().min(1).max(10).default(1),
  conduitType: z.enum(['STEEL', 'ALUMINUM', 'PVC', 'CABLE_TRAY']),
  
  // Motor Contribution
  includeMotorContribution: z.boolean().default(false),
  motorHpTotal: z.number().min(0).max(10000).optional(),
  motorContributionMultiplier: z.number().min(3).max(6).default(4),
  
  // System Configuration
  systemType: z.enum(['THREE_PHASE', 'SINGLE_PHASE']).default('THREE_PHASE'),
  calculationMethod: z.enum(['POINT_TO_POINT', 'IMPEDANCE_METHOD', 'PER_UNIT']).default('POINT_TO_POINT'),
});

export type ShortCircuitInput = z.infer<typeof ShortCircuitInputSchema>;

// Conductor resistance and reactance values per 1000 ft at 75°C
// Based on NEC Chapter 9 Table 8 & 9
const CONDUCTOR_DATA: Record<string, { r_copper: number; x_steel: number; x_pvc: number; r_aluminum: number }> = {
  '14_AWG': { r_copper: 3.14, x_steel: 0.058, x_pvc: 0.073, r_aluminum: 5.17 },
  '12_AWG': { r_copper: 2.00, x_steel: 0.054, x_pvc: 0.068, r_aluminum: 3.28 },
  '10_AWG': { r_copper: 1.24, x_steel: 0.050, x_pvc: 0.063, r_aluminum: 2.04 },
  '8_AWG': { r_copper: 0.778, x_steel: 0.052, x_pvc: 0.065, r_aluminum: 1.28 },
  '6_AWG': { r_copper: 0.491, x_steel: 0.051, x_pvc: 0.064, r_aluminum: 0.808 },
  '4_AWG': { r_copper: 0.308, x_steel: 0.048, x_pvc: 0.060, r_aluminum: 0.508 },
  '3_AWG': { r_copper: 0.245, x_steel: 0.047, x_pvc: 0.059, r_aluminum: 0.403 },
  '2_AWG': { r_copper: 0.194, x_steel: 0.045, x_pvc: 0.057, r_aluminum: 0.319 },
  '1_AWG': { r_copper: 0.154, x_steel: 0.046, x_pvc: 0.057, r_aluminum: 0.253 },
  '1/0_AWG': { r_copper: 0.122, x_steel: 0.044, x_pvc: 0.055, r_aluminum: 0.201 },
  '2/0_AWG': { r_copper: 0.0967, x_steel: 0.043, x_pvc: 0.054, r_aluminum: 0.159 },
  '3/0_AWG': { r_copper: 0.0766, x_steel: 0.042, x_pvc: 0.052, r_aluminum: 0.126 },
  '4/0_AWG': { r_copper: 0.0608, x_steel: 0.041, x_pvc: 0.051, r_aluminum: 0.100 },
  '250_kcmil': { r_copper: 0.0515, x_steel: 0.041, x_pvc: 0.052, r_aluminum: 0.0847 },
  '300_kcmil': { r_copper: 0.0429, x_steel: 0.041, x_pvc: 0.051, r_aluminum: 0.0707 },
  '350_kcmil': { r_copper: 0.0367, x_steel: 0.040, x_pvc: 0.050, r_aluminum: 0.0605 },
  '400_kcmil': { r_copper: 0.0321, x_steel: 0.040, x_pvc: 0.049, r_aluminum: 0.0529 },
  '500_kcmil': { r_copper: 0.0258, x_steel: 0.039, x_pvc: 0.048, r_aluminum: 0.0424 },
  '600_kcmil': { r_copper: 0.0214, x_steel: 0.039, x_pvc: 0.048, r_aluminum: 0.0353 },
  '750_kcmil': { r_copper: 0.0171, x_steel: 0.038, x_pvc: 0.048, r_aluminum: 0.0282 },
  '1000_kcmil': { r_copper: 0.0129, x_steel: 0.037, x_pvc: 0.046, r_aluminum: 0.0212 },
};

export interface ShortCircuitResult {
  // Input echo
  input: ShortCircuitInput;
  
  // Impedances
  sourceImpedance: { r: number; x: number; z: number };
  transformerImpedance?: { r: number; x: number; z: number };
  conductorImpedance: { r: number; x: number; z: number };
  totalImpedance: { r: number; x: number; z: number; xrRatio: number };
  
  // Fault currents
  symmetricalFault3ph: number; // kA
  symmetricalFaultLG: number; // kA
  symmetricalFaultLL: number; // kA
  asymmetricalFault3ph: number; // kA
  peakFaultCurrent: number; // kA
  motorContribution?: number; // kA
  
  // Equipment adequacy
  requiredAicRating: number; // kA
  
  // Calculation details
  calculationSteps: string[];
  warnings: string[];
  necReferences: string[];
}

export class ShortCircuitCalculationService {
  /**
   * Perform short circuit calculation using point-to-point method
   * Based on IEEE standards and NEC requirements
   */
  static calculate(input: ShortCircuitInput): ShortCircuitResult {
    const steps: string[] = [];
    const warnings: string[] = [];
    const necReferences: string[] = [
      'NEC 110.9 - Interrupting Rating',
      'NEC 110.10 - Circuit Impedance and Short-Circuit Current Ratings',
      'NEC 240.86 - Series Ratings',
      'IEEE 141 - Industrial Power System Analysis',
    ];
    
    // Step 1: Calculate source impedance
    steps.push('Step 1: Calculate source impedance from utility fault current');
    const voltageLL = new Decimal(input.utilityVoltage);
    const voltagePhase = voltageLL.div(Math.sqrt(3));
    const sourceZ = voltagePhase.div(new Decimal(input.utilityFaultCurrent).times(1000)); // Convert kA to A
    const sourceXRRatio = new Decimal(input.utilityXRRatio);
    
    // Using X/R ratio to find R and X
    // Z² = R² + X² and X/R = ratio
    // R = Z / sqrt(1 + (X/R)²)
    let sourceR = sourceZ.div(new Decimal(1).plus(sourceXRRatio.pow(2)).sqrt());
    let sourceX = sourceR.times(sourceXRRatio);
    
    steps.push(`Source impedance: R=${sourceR.toFixed(6)}Ω, X=${sourceX.toFixed(6)}Ω, Z=${sourceZ.toFixed(6)}Ω`);
    
    // Step 2: Calculate transformer impedance if present
    let transformerR = new Decimal(0);
    let transformerX = new Decimal(0);
    let transformerZ = new Decimal(0);
    let transformerSecondaryVoltage = voltageLL;
    
    if (input.transformerKva && input.transformerImpedance) {
      steps.push('Step 2: Calculate transformer impedance');
      necReferences.push('IEEE C57.12.00 - Transformer Standards');
      
      if (input.transformerSecondaryV) {
        transformerSecondaryVoltage = new Decimal(input.transformerSecondaryV);
      }
      
      // Transformer impedance: Zt = (kV²/MVA) × (%Z/100)
      const kV = transformerSecondaryVoltage.div(1000);
      const MVA = new Decimal(input.transformerKva).div(1000);
      transformerZ = kV.pow(2).div(MVA).times(input.transformerImpedance).div(100);
      
      // Calculate R and X from X/R ratio
      const xrRatio = new Decimal(input.transformerXRRatio || 8); // Default X/R for transformers
      transformerR = transformerZ.div(new Decimal(1).plus(xrRatio.pow(2)).sqrt());
      transformerX = transformerR.times(xrRatio);
      
      steps.push(`Transformer impedance: R=${transformerR.toFixed(6)}Ω, X=${transformerX.toFixed(6)}Ω, Z=${transformerZ.toFixed(6)}Ω`);
      
      // If transformer is present, we need to reflect source impedance to secondary
      if (input.transformerPrimaryV && input.transformerSecondaryV) {
        const turnsRatio = new Decimal(input.transformerSecondaryV).div(input.transformerPrimaryV);
        sourceR = sourceR.times(turnsRatio.pow(2));
        sourceX = sourceX.times(turnsRatio.pow(2));
        steps.push(`Source impedance reflected to secondary: R=${sourceR.toFixed(6)}Ω, X=${sourceX.toFixed(6)}Ω`);
      }
    }
    
    // Step 3: Calculate conductor impedance
    steps.push('Step 3: Calculate conductor impedance');
    necReferences.push('NEC Chapter 9 Table 8 - Conductor Properties');
    necReferences.push('NEC Chapter 9 Table 9 - AC Resistance and Reactance');
    
    const conductorData = CONDUCTOR_DATA[input.conductorSize];
    if (!conductorData) {
      throw new Error(`Unknown conductor size: ${input.conductorSize}`);
    }
    
    // Get resistance based on material
    const rPer1000ft = input.conductorMaterial === 'COPPER' 
      ? conductorData.r_copper 
      : conductorData.r_aluminum;
    
    // Get reactance based on conduit type
    const xPer1000ft = input.conduitType === 'STEEL' || input.conduitType === 'ALUMINUM'
      ? conductorData.x_steel
      : conductorData.x_pvc;
    
    // Calculate total conductor impedance
    const lengthMultiplier = new Decimal(input.conductorLength).div(1000);
    const parallelDivisor = new Decimal(input.conductorsPerPhase);
    
    const conductorR = new Decimal(rPer1000ft).times(lengthMultiplier).div(parallelDivisor);
    const conductorX = new Decimal(xPer1000ft).times(lengthMultiplier).div(parallelDivisor);
    const conductorZ = conductorR.pow(2).plus(conductorX.pow(2)).sqrt();
    
    steps.push(`Conductor impedance: R=${conductorR.toFixed(6)}Ω, X=${conductorX.toFixed(6)}Ω, Z=${conductorZ.toFixed(6)}Ω`);
    
    if (input.conductorsPerPhase > 1) {
      steps.push(`Parallel conductors: ${input.conductorsPerPhase} per phase`);
      necReferences.push('NEC 310.10(G) - Parallel Conductors');
    }
    
    // Step 4: Calculate total impedance
    steps.push('Step 4: Calculate total impedance to fault point');
    const totalR = sourceR.plus(transformerR).plus(conductorR);
    const totalX = sourceX.plus(transformerX).plus(conductorX);
    const totalZ = totalR.pow(2).plus(totalX.pow(2)).sqrt();
    const totalXR = totalX.div(totalR);
    
    steps.push(`Total impedance: R=${totalR.toFixed(6)}Ω, X=${totalX.toFixed(6)}Ω, Z=${totalZ.toFixed(6)}Ω`);
    steps.push(`System X/R ratio at fault point: ${totalXR.toFixed(2)}`);
    
    // Step 5: Calculate fault currents
    steps.push('Step 5: Calculate fault currents');
    const workingVoltage = input.transformerKva ? transformerSecondaryVoltage : voltageLL;
    
    // Three-phase fault current: I = V / (√3 × Z)
    const I3ph = workingVoltage.div(Math.sqrt(3)).div(totalZ).div(1000); // Convert to kA
    
    // Line-to-ground fault (approximate as 87% of 3-phase for solidly grounded systems)
    const ILG = I3ph.times(0.87);
    
    // Line-to-line fault (87% of 3-phase fault)
    const ILL = I3ph.times(0.87);
    
    steps.push(`Symmetrical fault currents:`);
    steps.push(`- Three-phase: ${I3ph.toFixed(2)} kA`);
    steps.push(`- Line-to-ground: ${ILG.toFixed(2)} kA`);
    steps.push(`- Line-to-line: ${ILL.toFixed(2)} kA`);
    
    // Calculate asymmetrical fault current
    // Multiplying factor based on X/R ratio (simplified from IEEE tables)
    let mf = new Decimal(1);
    if (totalXR.lt(5)) {
      mf = new Decimal(1.25);
    } else if (totalXR.lt(10)) {
      mf = new Decimal(1.35);
    } else if (totalXR.lt(15)) {
      mf = new Decimal(1.45);
    } else {
      mf = new Decimal(1.55);
    }
    
    const I3phAsym = I3ph.times(mf);
    steps.push(`Asymmetrical RMS fault current: ${I3phAsym.toFixed(2)} kA (MF=${mf})`);
    
    // Peak fault current: Ipeak = √2 × Isym × (1 + e^(-π/X/R))
    const peakMultiplier = new Decimal(Math.sqrt(2)).times(
      new Decimal(1).plus(new Decimal(Math.E).pow(new Decimal(-Math.PI).div(totalXR)))
    );
    const Ipeak = I3ph.times(peakMultiplier);
    steps.push(`Peak instantaneous fault current: ${Ipeak.toFixed(2)} kA`);
    
    // Step 6: Motor contribution if applicable
    let motorContributionkA: number | undefined;
    if (input.includeMotorContribution && input.motorHpTotal) {
      steps.push('Step 6: Calculate motor contribution');
      necReferences.push('IEEE 141 Section 4.5 - Motor Contribution');
      
      // Approximate motor FLA: 1.5A per HP at 480V, adjust for other voltages
      const motorFLA = new Decimal(input.motorHpTotal).times(1.5).times(480).div(workingVoltage);
      motorContributionkA = motorFLA.times(input.motorContributionMultiplier).div(1000).toNumber();
      
      steps.push(`Motor contribution: ${motorContributionkA.toFixed(2)} kA`);
      steps.push(`Total fault current with motors: ${I3ph.plus(motorContributionkA).toFixed(2)} kA`);
      
      if (motorContributionkA > I3ph.times(0.25).toNumber()) {
        warnings.push('Motor contribution exceeds 25% of utility fault current - consider in equipment ratings');
      }
    }
    
    // Determine required AIC rating
    const requiredAic = I3phAsym.toNumber();
    
    // Add warnings
    if (requiredAic > 65) {
      warnings.push(`High fault current (${requiredAic.toFixed(1)} kA) - special equipment may be required`);
      warnings.push('Consider current-limiting devices or series ratings');
    }
    
    if (totalXR.gt(15)) {
      warnings.push('High X/R ratio - consider DC component in protective device selection');
    }
    
    if (input.conductorLength > 500) {
      warnings.push('Long conductor run - verify voltage drop calculations');
    }
    
    return {
      input,
      sourceImpedance: {
        r: sourceR.toNumber(),
        x: sourceX.toNumber(),
        z: sourceZ.toNumber(),
      },
      transformerImpedance: input.transformerKva ? {
        r: transformerR.toNumber(),
        x: transformerX.toNumber(),
        z: transformerZ.toNumber(),
      } : undefined,
      conductorImpedance: {
        r: conductorR.toNumber(),
        x: conductorX.toNumber(),
        z: conductorZ.toNumber(),
      },
      totalImpedance: {
        r: totalR.toNumber(),
        x: totalX.toNumber(),
        z: totalZ.toNumber(),
        xrRatio: totalXR.toNumber(),
      },
      symmetricalFault3ph: I3ph.toNumber(),
      symmetricalFaultLG: ILG.toNumber(),
      symmetricalFaultLL: ILL.toNumber(),
      asymmetricalFault3ph: I3phAsym.toNumber(),
      peakFaultCurrent: Ipeak.toNumber(),
      motorContribution: motorContributionkA,
      requiredAicRating: requiredAic,
      calculationSteps: steps,
      warnings,
      necReferences,
    };
  }
  
  /**
   * Verify equipment ratings against calculated fault currents
   */
  static verifyEquipmentRatings(
    faultCurrent: number,
    equipmentRatings: {
      busBracingRating?: number;
      mainBreakerAic?: number;
      branchBreakerAic?: number;
    }
  ): {
    busBracingAdequate: boolean;
    mainBreakerAdequate: boolean;
    branchBreakerAdequate: boolean;
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    
    const busBracingAdequate = equipmentRatings.busBracingRating 
      ? equipmentRatings.busBracingRating >= faultCurrent 
      : false;
      
    const mainBreakerAdequate = equipmentRatings.mainBreakerAic
      ? equipmentRatings.mainBreakerAic >= faultCurrent
      : false;
      
    const branchBreakerAdequate = equipmentRatings.branchBreakerAic
      ? equipmentRatings.branchBreakerAic >= faultCurrent
      : false;
    
    if (!busBracingAdequate && equipmentRatings.busBracingRating) {
      recommendations.push(
        `Bus bracing inadequate: ${equipmentRatings.busBracingRating} kA < ${faultCurrent.toFixed(1)} kA required`
      );
    }
    
    if (!mainBreakerAdequate && equipmentRatings.mainBreakerAic) {
      recommendations.push(
        `Main breaker AIC inadequate: ${equipmentRatings.mainBreakerAic} kA < ${faultCurrent.toFixed(1)} kA required`
      );
      recommendations.push('Consider current-limiting fuses or series-rated combination');
    }
    
    if (!branchBreakerAdequate && equipmentRatings.branchBreakerAic) {
      recommendations.push(
        `Branch breaker AIC inadequate: ${equipmentRatings.branchBreakerAic} kA < ${faultCurrent.toFixed(1)} kA required`
      );
      recommendations.push('Verify series ratings per NEC 240.86');
    }
    
    return {
      busBracingAdequate,
      mainBreakerAdequate,
      branchBreakerAdequate,
      recommendations,
    };
  }
  
  /**
   * Get standard AIC ratings for equipment selection
   */
  static getStandardAicRatings(): number[] {
    return [10, 14, 18, 22, 25, 35, 42, 50, 65, 85, 100, 150, 200];
  }
  
  /**
   * Get next higher standard AIC rating
   */
  static getRequiredAicRating(faultCurrent: number): number {
    const standardRatings = this.getStandardAicRatings();
    for (const rating of standardRatings) {
      if (rating >= faultCurrent) {
        return rating;
      }
    }
    return 200; // Maximum standard rating
  }
}