import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { ArcFlashLabel, PPERequirement } from '../../types/electrical';
import { PPE_CATEGORIES } from '../../constants/electrical';

interface Props {
  arcFlashLabel: ArcFlashLabel;
  onPPEInfo?: () => void;
}

export const ArcFlashWarning: React.FC<Props> = ({ arcFlashLabel, onPPEInfo }) => {
  const ppeCategory = PPE_CATEGORIES.find(cat => cat.category === arcFlashLabel.ppeCategory);
  
  const getHazardLevel = () => {
    if (arcFlashLabel.incidentEnergy < 1.2) return { level: 'Low', color: '#4CAF50' };
    if (arcFlashLabel.incidentEnergy < 4) return { level: 'Moderate', color: '#FF9800' };
    if (arcFlashLabel.incidentEnergy < 8) return { level: 'High', color: '#ff6b35' };
    return { level: 'Extreme', color: '#f44336' };
  };

  const hazard = getHazardLevel();

  const showBoundaryInfo = () => {
    Alert.alert(
      'Approach Boundaries',
      `Limited Approach: ${arcFlashLabel.limitedApproachBoundary} inches\n` +
      `Restricted Approach: ${arcFlashLabel.restrictedApproachBoundary} inches\n` +
      `Prohibited Approach: ${arcFlashLabel.prohibitedApproachBoundary} inches\n\n` +
      'Only qualified personnel should cross these boundaries.',
      [{ text: 'OK' }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={[styles.warningHeader, { backgroundColor: hazard.color }]}>
        <Text style={styles.warningIcon}>⚡</Text>
        <Text style={styles.warningTitle}>ARC FLASH HAZARD</Text>
        <Text style={styles.hazardLevel}>{hazard.level} Risk</Text>
      </View>

      <View style={styles.mainInfo}>
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Incident Energy</Text>
          <Text style={[styles.cardValue, { color: hazard.color }]}>
            {arcFlashLabel.incidentEnergy} cal/cm²
          </Text>
          <Text style={styles.cardSubtext}>at {arcFlashLabel.workingDistance} inches</Text>
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Arc Flash Boundary</Text>
          <Text style={[styles.cardValue, { color: hazard.color }]}>
            {arcFlashLabel.arcFlashBoundary} inches
          </Text>
          <TouchableOpacity onPress={showBoundaryInfo}>
            <Text style={styles.moreInfoLink}>View all boundaries →</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.ppeSection}>
        <View style={styles.ppeSectionHeader}>
          <Text style={styles.ppeSectionTitle}>Required PPE</Text>
          <View style={[styles.ppeCategoryBadge, { backgroundColor: hazard.color }]}>
            <Text style={styles.ppeCategoryText}>Category {arcFlashLabel.ppeCategory}</Text>
          </View>
        </View>

        {ppeCategory && (
          <>
            <Text style={styles.ppeDescription}>{ppeCategory.description}</Text>
            
            <View style={styles.ppeRequirements}>
              {ppeCategory.requirements.map((requirement, index) => (
                <View key={index} style={styles.requirementItem}>
                  <Text style={styles.requirementBullet}>•</Text>
                  <Text style={styles.requirementText}>{requirement}</Text>
                </View>
              ))}
            </View>

            <View style={styles.minRatingBox}>
              <Text style={styles.minRatingLabel}>Minimum Arc Rating:</Text>
              <Text style={styles.minRatingValue}>{ppeCategory.minCalCm2} cal/cm²</Text>
            </View>
          </>
        )}
      </View>

      <View style={styles.equipmentInfo}>
        <Text style={styles.infoTitle}>Equipment Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Voltage:</Text>
          <Text style={styles.infoValue}>{arcFlashLabel.voltage} V</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Equipment ID:</Text>
          <Text style={styles.infoValue}>{arcFlashLabel.equipmentId}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Last Calculated:</Text>
          <Text style={styles.infoValue}>
            {new Date(arcFlashLabel.lastCalculated).toLocaleDateString()}
          </Text>
        </View>
      </View>

      <View style={styles.safetyNotice}>
        <Text style={styles.safetyNoticeTitle}>⚠️ SAFETY NOTICE</Text>
        <Text style={styles.safetyNoticeText}>
          De-energize equipment when possible. Follow all lockout/tagout procedures.
          Only qualified personnel should work on or near energized equipment.
        </Text>
      </View>

      {onPPEInfo && (
        <TouchableOpacity style={styles.ppeInfoButton} onPress={onPPEInfo}>
          <Text style={styles.ppeInfoButtonText}>View PPE Requirements Guide</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  warningHeader: {
    padding: 20,
    alignItems: 'center',
  },
  warningIcon: {
    fontSize: 48,
    color: 'white',
    marginBottom: 10,
  },
  warningTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  hazardLevel: {
    fontSize: 18,
    color: 'white',
    fontWeight: '500',
  },
  mainInfo: {
    flexDirection: 'row',
    padding: 15,
  },
  infoCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    marginHorizontal: 5,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  cardSubtext: {
    fontSize: 12,
    color: '#999',
  },
  moreInfoLink: {
    fontSize: 12,
    color: '#2196F3',
    marginTop: 5,
  },
  ppeSection: {
    backgroundColor: 'white',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ppeSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  ppeSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  ppeCategoryBadge: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  ppeCategoryText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  ppeDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  ppeRequirements: {
    marginBottom: 15,
  },
  requirementItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  requirementBullet: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
  },
  requirementText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  minRatingBox: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  minRatingLabel: {
    fontSize: 16,
    color: '#E65100',
    fontWeight: '500',
  },
  minRatingValue: {
    fontSize: 20,
    color: '#E65100',
    fontWeight: 'bold',
  },
  equipmentInfo: {
    backgroundColor: 'white',
    margin: 15,
    marginTop: 0,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  safetyNotice: {
    backgroundColor: '#FFF3E0',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  safetyNoticeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E65100',
    marginBottom: 10,
  },
  safetyNoticeText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  ppeInfoButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  ppeInfoButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});