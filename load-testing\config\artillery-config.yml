config:
  target: "{{ $processEnvironment.API_BASE_URL }}"
  processor: "./scripts/test-data-generator.js"
  payload:
    - path: "./data/test-users.csv"
      fields:
        - "email"
        - "password"
      order: "sequence"
  variables:
    baseUrl: "{{ $processEnvironment.API_BASE_URL }}"
    wsUrl: "{{ $processEnvironment.WS_URL }}"
  
  # HTTP Settings
  http:
    timeout: 30
    pool: 50
    maxSockets: 100
  
  # WebSocket Settings
  ws:
    rejectUnauthorized: false
    
  # Plugins
  plugins:
    metrics-by-endpoint: {}
    ensure:
      thresholds:
        - http.response_time.p95: 1000
        - http.response_time.p99: 2000
        - http.codes.2xx: 99
  
  # Default headers
  defaults:
    headers:
      Content-Type: "application/json"
      Accept: "application/json"

# Reusable scenarios
scenarios:
  - name: "User Authentication Flow"
    weight: 10
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"
              as: "userId"
      - think: 2
      
  - name: "Electrical Calculations"
    weight: 30
    flow:
      - function: "generateAuthHeader"
      - post:
          url: "/calculations/voltage-drop"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            voltage: 480
            current: 100
            distance: 200
            conductorSize: "1/0"
            conductorType: "copper"
            conduitType: "steel"
            powerFactor: 0.85
            phases: 3
          expect:
            - statusCode: 200
            - contentType: json
      - think: 1
      - post:
          url: "/calculations/wire-size"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            voltage: 480
            current: 150
            distance: 150
            voltageDrop: 3
            ambientTemp: 30
            conductorType: "copper"
            insulation: "THWN"
          expect:
            - statusCode: 200
      - think: 1
      - post:
          url: "/calculations/conduit-fill"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            conduitType: "EMT"
            conduitSize: "2"
            conductors: [
              { size: "12", type: "THWN", count: 9 },
              { size: "10", type: "THWN", count: 6 }
            ]
          expect:
            - statusCode: 200
            
  - name: "Panel Operations"
    weight: 25
    flow:
      - function: "generateAuthHeader"
      - get:
          url: "/panels"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$[0].id"
              as: "panelId"
      - think: 1
      - get:
          url: "/panels/{{ panelId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 2
      - post:
          url: "/panels/{{ panelId }}/circuits"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            circuitNumber: "{{ $randomNumber(1, 42) }}"
            description: "Load Test Circuit"
            amperage: "{{ $randomNumber(15, 50) }}"
            voltage: 120
            phase: "A"
          
  - name: "Material Search"
    weight: 20
    flow:
      - function: "generateAuthHeader"
      - get:
          url: "/materials/search?query=wire&category=electrical"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      - think: 1
      - get:
          url: "/materials/search?query=breaker&manufacturer=Square D"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 1
      - get:
          url: "/materials/pricing/bulk"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            items: [
              { materialId: "MAT001", quantity: 100 },
              { materialId: "MAT002", quantity: 50 }
            ]
            
  - name: "WebSocket Connection"
    weight: 10
    engine: "ws"
    flow:
      - function: "generateAuthHeader"
      - connect:
          url: "{{ wsUrl }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 5
      - send:
          data:
            type: "subscribe"
            channel: "project_updates"
            projectId: "{{ $randomString(10) }}"
      - think: 10
      - send:
          data:
            type: "estimate_collaboration"
            estimateId: "{{ $randomString(10) }}"
            action: "update_line_item"
      - think: 5
      
  - name: "File Operations"
    weight: 5
    flow:
      - function: "generateAuthHeader"
      - post:
          url: "/exports/panel-schedule/{{ panelId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            format: "pdf"
          capture:
            - json: "$.exportId"
              as: "exportId"
      - think: 2
      - loop:
          - get:
              url: "/exports/status/{{ exportId }}"
              headers:
                Authorization: "Bearer {{ authToken }}"
          count: 5
          think: 1