import React, { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@store/index';
import { showToast } from '@store/slices/uiSlice';
import PinCodeInput from '@components/auth/PinCodeInput';
import { enhancedAuthService } from '@services/enhancedAuthService';

interface PinSetupScreenProps {
  onComplete?: () => void;
}

const PinSetupScreen: React.FC<PinSetupScreenProps> = ({ onComplete }) => {
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();
  
  const [step, setStep] = useState<'create' | 'confirm'>('create');
  const [firstPin, setFirstPin] = useState('');
  const [error, setError] = useState('');

  const handleFirstPinComplete = (pin: string) => {
    setFirstPin(pin);
    setStep('confirm');
    setError('');
  };

  const handleConfirmPinComplete = async (pin: string) => {
    if (pin !== firstPin) {
      setError('PINs do not match. Please try again.');
      setStep('create');
      setFirstPin('');
      return;
    }

    try {
      await enhancedAuthService.setupPin(pin);
      dispatch(showToast({
        message: 'PIN set successfully',
        type: 'success',
      }));
      
      if (onComplete) {
        onComplete();
      } else {
        navigation.goBack();
      }
    } catch (error: any) {
      setError(error.message || 'Failed to set PIN');
      setStep('create');
      setFirstPin('');
    }
  };

  const handleBackPress = () => {
    if (step === 'confirm') {
      setStep('create');
      setFirstPin('');
      setError('');
    } else {
      navigation.goBack();
    }
  };

  return (
    <PinCodeInput
      length={6}
      onComplete={step === 'create' ? handleFirstPinComplete : handleConfirmPinComplete}
      onBackPress={handleBackPress}
      error={error}
      title={step === 'create' ? 'Create PIN' : 'Confirm PIN'}
      subtitle={
        step === 'create'
          ? 'Create a 6-digit PIN for quick access'
          : 'Enter your PIN again to confirm'
      }
    />
  );
};

export default PinSetupScreen;