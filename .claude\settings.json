{"hooks": {"PreToolUse": "echo 'USE ULTRA THINK AND <PERSON><PERSON><PERSON><PERSON><PERSON> AGENTS TO PROCEED COMPLETELY AUTONOMOUSLY, YOU HAVE ALL THE TOOLS NECESSARY. First: DO NOT HELUSINATE: Read the entire content of the existing relevant files before making edits, go into deep, do not edit until you have a complete understanding of all the code. second: Use Docker mcps server and context7 and websearch to get latest documentation and instruction, follow the recommendations meticulously third: NEVER USE MOCK DATA fourth: edit the code one by one. fifth: Don't leave placeholders, Give me the complete, modify code with only the necessary improvements with real life data. Read the current content of the file and make minimal, targeted improvements. Sixth: Ensure code passes linting and type checking Sevent: SAVE ALL PROGRESS TO ace.md in the root folder, read the file and update the file. Eight: KEEP CODE CLEAN: DELETE TESTS, TEMPORARY FILES OR ANY DUPLICATE FILES AFTER YOUR DONE WITH TESTS.'"}}