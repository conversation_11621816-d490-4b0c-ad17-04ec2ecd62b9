import { ErrorC<PERSON>, ErrorCategory, getErrorDetails } from './error-codes';
import { v4 as uuidv4 } from 'uuid';

export interface ErrorContext {
  userId?: string;
  requestId?: string;
  correlationId?: string;
  sessionId?: string;
  method?: string;
  path?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: any;
}

export interface ErrorDetails {
  field?: string;
  value?: any;
  constraint?: string;
  suggestion?: string;
  [key: string]: any;
}

export class StructuredError extends Error {
  public readonly id: string;
  public readonly code: ErrorCode;
  public readonly category: ErrorCategory;
  public readonly statusCode: number;
  public readonly timestamp: Date;
  public readonly isOperational: boolean;
  public readonly context: ErrorContext;
  public readonly details?: ErrorDetails[];
  public readonly cause?: Error;
  public readonly userMessage: string;

  constructor(
    code: ErrorCode,
    message?: string,
    options?: {
      cause?: Error;
      context?: ErrorContext;
      details?: ErrorDetails[];
      isOperational?: boolean;
      userMessage?: string;
    }
  ) {
    const errorInfo = getErrorDetails(code);
    super(message || errorInfo.message);

    this.id = `err_${Date.now()}_${uuidv4().slice(0, 8)}`;
    this.code = code;
    this.category = errorInfo.category;
    this.statusCode = errorInfo.httpStatus;
    this.timestamp = new Date();
    this.isOperational = options?.isOperational ?? true;
    this.context = options?.context || {};
    this.details = options?.details;
    this.cause = options?.cause;
    this.userMessage = options?.userMessage || errorInfo.message;

    // Ensure prototype chain is properly set
    Object.setPrototypeOf(this, StructuredError.prototype);

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert error to JSON for logging
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      code: this.code,
      category: this.category,
      message: this.message,
      userMessage: this.userMessage,
      statusCode: this.statusCode,
      timestamp: this.timestamp.toISOString(),
      isOperational: this.isOperational,
      context: this.context,
      details: this.details,
      cause: this.cause ? {
        message: this.cause.message,
        stack: this.cause.stack,
      } : undefined,
      stack: this.stack,
    };
  }

  /**
   * Convert to user-safe response
   */
  toUserResponse(): Record<string, any> {
    return {
      error: {
        id: this.id,
        code: this.code,
        message: this.userMessage,
        details: this.details?.map(d => ({
          field: d.field,
          message: d.suggestion || 'Invalid value',
        })),
      },
    };
  }

  /**
   * Check if error is retriable
   */
  isRetriable(): boolean {
    // Network and temporary database errors are retriable
    return [
      ErrorCode.DATABASE_CONNECTION_FAILED,
      ErrorCode.QUERY_TIMEOUT,
      ErrorCode.SERVICE_UNAVAILABLE,
      ErrorCode.NETWORK_ERROR,
      ErrorCode.EXTERNAL_SERVICE_ERROR,
    ].includes(this.code);
  }

  /**
   * Check if error should trigger an alert
   */
  shouldAlert(): boolean {
    // Critical errors that need immediate attention
    return !this.isOperational || [
      ErrorCode.DATABASE_CONNECTION_FAILED,
      ErrorCode.CONFIGURATION_ERROR,
      ErrorCode.MEMORY_ERROR,
      ErrorCode.DEPENDENCY_ERROR,
    ].includes(this.code);
  }
}

// Factory functions for common errors
export class ErrorFactory {
  static validation(
    message: string,
    details?: ErrorDetails[],
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(ErrorCode.INVALID_INPUT, message, {
      details,
      context,
      userMessage: 'Validation failed. Please check your input.',
    });
  }

  static notFound(
    resource: string,
    id?: string,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.RECORD_NOT_FOUND,
      `${resource} not found${id ? `: ${id}` : ''}`,
      {
        context,
        userMessage: `The requested ${resource.toLowerCase()} was not found.`,
      }
    );
  }

  static unauthorized(
    message = 'Unauthorized access',
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(ErrorCode.UNAUTHORIZED, message, {
      context,
      userMessage: 'Please authenticate to access this resource.',
    });
  }

  static forbidden(
    message = 'Access forbidden',
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(ErrorCode.FORBIDDEN, message, {
      context,
      userMessage: 'You do not have permission to access this resource.',
    });
  }

  static duplicate(
    resource: string,
    field: string,
    value: any,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.DUPLICATE_RECORD,
      `${resource} with ${field} '${value}' already exists`,
      {
        details: [{ field, value }],
        context,
        userMessage: `A ${resource.toLowerCase()} with this ${field} already exists.`,
      }
    );
  }

  static database(
    error: Error,
    operation: string,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.DATABASE_CONNECTION_FAILED,
      `Database error during ${operation}`,
      {
        cause: error,
        context,
        isOperational: false,
        userMessage: 'A database error occurred. Please try again later.',
      }
    );
  }

  static calculation(
    message: string,
    details?: ErrorDetails[],
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(ErrorCode.CALCULATION_ERROR, message, {
      details,
      context,
      userMessage: 'Calculation error. Please check your inputs.',
    });
  }

  static necViolation(
    violation: string,
    article: string,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.NEC_VIOLATION,
      `NEC Article ${article} violation: ${violation}`,
      {
        details: [{ field: 'article', value: article, constraint: violation }],
        context,
        userMessage: `This configuration violates NEC Article ${article}.`,
      }
    );
  }

  static rateLimit(
    retryAfter?: number,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.API_RATE_LIMIT,
      'Rate limit exceeded',
      {
        details: retryAfter ? [{ field: 'retryAfter', value: retryAfter }] : undefined,
        context,
        userMessage: `Too many requests. Please try again${retryAfter ? ` in ${retryAfter} seconds` : ' later'}.`,
      }
    );
  }

  static internal(
    error: Error,
    operation: string,
    context?: ErrorContext
  ): StructuredError {
    return new StructuredError(
      ErrorCode.INTERNAL_ERROR,
      `Internal error during ${operation}`,
      {
        cause: error,
        context,
        isOperational: false,
        userMessage: 'An unexpected error occurred. Please try again later.',
      }
    );
  }
}