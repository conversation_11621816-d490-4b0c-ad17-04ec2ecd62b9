import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';
import { randomItem, randomIntBetween, randomString } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';
import { SharedArray } from 'k6/data';

// Custom metrics
const loginErrorRate = new Rate('login_errors');
const calculationDuration = new Trend('calculation_duration');
const wsConnectionErrors = new Counter('ws_connection_errors');
const activePanels = new Gauge('active_panels');
const dbQueryDuration = new Trend('db_query_duration');

// Load test data
const testUsers = new SharedArray('users', function() {
  return JSON.parse(open('../data/test-users.json'));
});

// Configuration
export const options = {
  scenarios: {
    // Gradual ramp-up scenario
    gradual_load: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 10 },   // Warm-up
        { duration: '5m', target: 50 },   // Ramp to 50 users
        { duration: '10m', target: 100 }, // Ramp to 100 users
        { duration: '20m', target: 100 }, // Stay at 100 users
        { duration: '5m', target: 0 },    // Ramp down
      ],
      gracefulRampDown: '30s',
    },
    // Constant arrival rate for specific endpoints
    constant_calculations: {
      executor: 'constant-arrival-rate',
      rate: 30,
      timeUnit: '1s',
      duration: '30m',
      preAllocatedVUs: 50,
      maxVUs: 100,
      exec: 'calculationScenario',
      startTime: '5m', // Start after initial ramp-up
    },
    // WebSocket connections
    websocket_load: {
      executor: 'constant-vus',
      vus: 20,
      duration: '35m',
      exec: 'websocketScenario',
      startTime: '3m',
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<1000', 'p(99)<2000'],
    http_req_failed: ['rate<0.05'],
    login_errors: ['rate<0.01'],
    calculation_duration: ['p(95)<1500'],
    ws_connection_errors: ['count<10'],
    'http_req_duration{type:calculation}': ['p(95)<2000'],
    'http_req_duration{type:panel}': ['p(95)<1000'],
    'http_req_duration{type:material}': ['p(95)<800'],
  },
  summaryTrendStats: ['avg', 'min', 'med', 'max', 'p(90)', 'p(95)', 'p(99)'],
};

const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:3000/api';
const WS_URL = __ENV.WS_URL || 'ws://localhost:3000';

// Helper functions
function authenticate() {
  const user = randomItem(testUsers);
  const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
    email: user.email,
    password: user.password,
  }), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const success = check(loginRes, {
    'login successful': (r) => r.status === 200,
    'has auth token': (r) => r.json('token') !== undefined,
  });
  
  loginErrorRate.add(!success);
  
  if (success) {
    return {
      token: loginRes.json('token'),
      userId: loginRes.json('user.id'),
    };
  }
  return null;
}

function makeAuthRequest(url, token, params = {}) {
  return http.get(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    ...params,
  });
}

function makeAuthPost(url, token, payload, params = {}) {
  return http.post(url, JSON.stringify(payload), {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    ...params,
  });
}

// Main scenario
export default function() {
  const auth = authenticate();
  if (!auth) return;
  
  group('User Dashboard Flow', () => {
    // Load dashboard data
    const projectsRes = makeAuthRequest(
      `${BASE_URL}/projects?limit=10`,
      auth.token,
      { tags: { type: 'dashboard' } }
    );
    
    check(projectsRes, {
      'projects loaded': (r) => r.status === 200,
    });
    
    sleep(randomIntBetween(1, 3));
    
    // Get recent estimates
    const estimatesRes = makeAuthRequest(
      `${BASE_URL}/estimates/recent?limit=5`,
      auth.token,
      { tags: { type: 'dashboard' } }
    );
    
    check(estimatesRes, {
      'estimates loaded': (r) => r.status === 200,
    });
  });
  
  sleep(randomIntBetween(2, 5));
  
  group('Panel Operations', () => {
    const panelsRes = makeAuthRequest(
      `${BASE_URL}/panels?limit=20`,
      auth.token,
      { tags: { type: 'panel' } }
    );
    
    if (check(panelsRes, { 'panels loaded': (r) => r.status === 200 })) {
      const panels = panelsRes.json();
      activePanels.add(panels.length);
      
      if (panels.length > 0) {
        const panel = randomItem(panels);
        
        // Get panel schedule
        const scheduleRes = makeAuthRequest(
          `${BASE_URL}/panels/${panel.id}/schedule`,
          auth.token,
          { tags: { type: 'panel' } }
        );
        
        check(scheduleRes, {
          'panel schedule loaded': (r) => r.status === 200,
        });
        
        sleep(1);
        
        // Add a circuit
        const circuitRes = makeAuthPost(
          `${BASE_URL}/panels/${panel.id}/circuits`,
          auth.token,
          {
            circuitNumber: randomIntBetween(1, 42),
            description: `Test Circuit ${randomString(5)}`,
            amperage: randomIntBetween(15, 50),
            voltage: randomItem([120, 208, 240]),
            phase: randomItem(['A', 'B', 'C']),
          },
          { tags: { type: 'panel' } }
        );
        
        check(circuitRes, {
          'circuit added': (r) => r.status === 201 || r.status === 200,
        });
      }
    }
  });
  
  sleep(randomIntBetween(2, 4));
  
  group('Material Search', () => {
    const categories = ['electrical', 'wire', 'conduit', 'devices', 'panels'];
    const searchQuery = randomItem(['wire', 'breaker', 'panel', 'box', 'conduit']);
    
    const searchRes = makeAuthRequest(
      `${BASE_URL}/materials/search?query=${searchQuery}&category=${randomItem(categories)}`,
      auth.token,
      { tags: { type: 'material' } }
    );
    
    check(searchRes, {
      'material search successful': (r) => r.status === 200,
    });
    
    sleep(1);
    
    // Price calculation
    if (searchRes.status === 200) {
      const materials = searchRes.json();
      if (materials.length > 0) {
        const items = materials.slice(0, 5).map(m => ({
          materialId: m.id,
          quantity: randomIntBetween(1, 100),
        }));
        
        const priceRes = makeAuthPost(
          `${BASE_URL}/materials/pricing/calculate`,
          auth.token,
          {
            items: items,
            location: randomItem(['CA', 'TX', 'NY', 'FL']),
            taxRate: randomIntBetween(5, 10),
          },
          { tags: { type: 'material' } }
        );
        
        check(priceRes, {
          'pricing calculated': (r) => r.status === 200,
        });
      }
    }
  });
  
  sleep(randomIntBetween(3, 6));
}

// Calculation-focused scenario
export function calculationScenario() {
  const auth = authenticate();
  if (!auth) return;
  
  const startTime = Date.now();
  
  group('Electrical Calculations', () => {
    // Voltage drop calculation
    const voltageDropRes = makeAuthPost(
      `${BASE_URL}/calculations/voltage-drop`,
      auth.token,
      {
        voltage: randomItem([120, 208, 240, 480]),
        current: randomIntBetween(50, 200),
        distance: randomIntBetween(50, 500),
        conductorSize: randomItem(['12', '10', '8', '6', '4', '2', '1/0', '2/0']),
        conductorType: 'copper',
        conduitType: randomItem(['steel', 'pvc', 'aluminum']),
        powerFactor: 0.85 + (randomIntBetween(0, 15) / 100),
        phases: randomItem([1, 3]),
      },
      { tags: { type: 'calculation' } }
    );
    
    check(voltageDropRes, {
      'voltage drop calculated': (r) => r.status === 200,
    });
    
    sleep(0.5);
    
    // Short circuit calculation
    const shortCircuitRes = makeAuthPost(
      `${BASE_URL}/calculations/short-circuit`,
      auth.token,
      {
        transformerKVA: randomItem([500, 750, 1000, 1500, 2000]),
        transformerImpedance: 5 + (randomIntBetween(0, 20) / 10),
        primaryVoltage: randomItem([4160, 13800, 34500]),
        secondaryVoltage: randomItem([208, 480]),
        motorContribution: Math.random() > 0.5,
        cableLength: randomIntBetween(100, 1000),
        cableSize: randomItem(['250kcmil', '350kcmil', '500kcmil']),
        conduitsPerPhase: randomIntBetween(1, 4),
      },
      { tags: { type: 'calculation' } }
    );
    
    check(shortCircuitRes, {
      'short circuit calculated': (r) => r.status === 200,
    });
    
    sleep(0.5);
    
    // Arc flash calculation
    const arcFlashRes = makeAuthPost(
      `${BASE_URL}/calculations/arc-flash`,
      auth.token,
      {
        systemVoltage: randomItem([208, 480, 4160]),
        faultCurrent: randomIntBetween(10000, 65000),
        faultClearingTime: 0.1 + (randomIntBetween(0, 40) / 100),
        workingDistance: randomIntBetween(18, 36),
        enclosureType: randomItem(['open', 'box', 'mcc', 'panel', 'switchgear']),
        groundingType: randomItem(['solidly-grounded', 'resistance-grounded', 'ungrounded']),
        conductorGap: randomIntBetween(13, 153),
      },
      { tags: { type: 'calculation' } }
    );
    
    check(arcFlashRes, {
      'arc flash calculated': (r) => r.status === 200,
    });
  });
  
  calculationDuration.add(Date.now() - startTime);
  
  sleep(randomIntBetween(1, 3));
}

// WebSocket scenario
export function websocketScenario() {
  const auth = authenticate();
  if (!auth) return;
  
  const url = `${WS_URL}?token=${auth.token}`;
  
  const res = ws.connect(url, {}, function(socket) {
    socket.on('open', () => {
      console.log('WebSocket connected');
      
      // Subscribe to channels
      socket.send(JSON.stringify({
        type: 'subscribe',
        channels: ['project_updates', 'estimates', 'notifications'],
      }));
      
      // Send periodic updates
      socket.setInterval(() => {
        socket.send(JSON.stringify({
          type: 'heartbeat',
          timestamp: Date.now(),
        }));
      }, 30000);
      
      // Simulate collaboration
      socket.setInterval(() => {
        socket.send(JSON.stringify({
          type: 'estimate_update',
          estimateId: randomString(10),
          changes: {
            lineItem: randomIntBetween(1, 50),
            quantity: randomIntBetween(1, 100),
            price: randomIntBetween(10, 1000),
          },
        }));
      }, randomIntBetween(5000, 15000));
    });
    
    socket.on('message', (data) => {
      const message = JSON.parse(data);
      check(message, {
        'valid message format': (m) => m.type !== undefined,
      });
    });
    
    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      wsConnectionErrors.add(1);
    });
    
    // Keep connection open for the duration
    socket.setTimeout(() => {
      socket.close();
    }, 30 * 60 * 1000); // 30 minutes
  });
  
  check(res, {
    'WebSocket connection established': (r) => r && r.status === 101,
  });
}

// Lifecycle hooks
export function setup() {
  console.log('Setting up load test environment...');
  
  // Verify API is accessible
  const healthCheck = http.get(`${BASE_URL.replace('/api', '')}/health`);
  check(healthCheck, {
    'API is healthy': (r) => r.status === 200,
  });
  
  return {
    startTime: Date.now(),
  };
}

export function teardown(data) {
  console.log(`Load test completed. Duration: ${(Date.now() - data.startTime) / 1000}s`);
}

// Custom summary
export function handleSummary(data) {
  return {
    'summary.json': JSON.stringify(data),
    'summary.txt': textSummary(data, { indent: ' ', enableColors: false }),
    '../results/k6-load-test-results.json': JSON.stringify(data),
  };
}

function textSummary(data, options) {
  // Custom text summary implementation
  let summary = 'Load Test Summary\\n';
  summary += '================\\n\\n';
  
  // Add key metrics
  const metrics = data.metrics;
  if (metrics.http_req_duration) {
    summary += `Response Times:\\n`;
    summary += `  p95: ${metrics.http_req_duration.values['p(95)']}ms\\n`;
    summary += `  p99: ${metrics.http_req_duration.values['p(99)']}ms\\n\\n`;
  }
  
  if (metrics.http_req_failed) {
    summary += `Error Rate: ${(metrics.http_req_failed.values.rate * 100).toFixed(2)}%\\n`;
  }
  
  return summary;
}