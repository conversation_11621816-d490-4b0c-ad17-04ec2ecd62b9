# Electrical Contracting App - Infrastructure Overview

## 🏗️ Infrastructure Components

### Container Images
- **Backend API**: Node.js 20 Alpine-based image with multi-stage build
- **Frontend**: Nginx Alpine serving optimized React build
- **Agents**: Node.js 20 Alpine for AI agent services

### Databases
- **PostgreSQL 16**: Primary relational database with replication
- **Redis 7**: Caching, session storage, and pub/sub messaging
- **Neo4j 5**: Graph database for relationship modeling
- **ChromaDB**: Vector database for AI embeddings
- **InfluxDB 2**: Time-series data for metrics and analytics

### Orchestration
- **Docker Compose**: Local development and simple deployments
- **Kubernetes**: Production-grade container orchestration
- **Helm**: Package manager for Kubernetes applications

### CI/CD
- **GitHub Actions**: Automated testing, building, and deployment
- **Container Registry**: GitHub Container Registry (ghcr.io)
- **Environments**: Development, Staging, Production

### Monitoring & Observability
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **ELK Stack**: Centralized logging (Elasticsearch, Logstash, Kibana)
- **Sentry**: Error tracking and performance monitoring

### Security
- **TLS/SSL**: Let's Encrypt certificates via cert-manager
- **Network Policies**: Kubernetes-native traffic control
- **Secrets Management**: Kubernetes secrets, HashiCorp Vault compatible
- **RBAC**: Role-based access control for Kubernetes resources

## 🚀 Quick Start

### Local Development
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Access services
- Frontend: http://localhost:5173
- Backend: http://localhost:3000
- PostgreSQL: localhost:5432
- Redis: localhost:6379
```

### Production Deployment
```bash
# Deploy to Kubernetes
./scripts/deploy.sh production v1.0.0

# Or use Helm
helm install electrical-app helm/electrical-app -n production
```

## 📊 Architecture Decisions

### Why Multi-Stage Docker Builds?
- Smaller production images (up to 70% reduction)
- Better security (no build tools in production)
- Faster deployments
- Clear separation of concerns

### Why Kubernetes?
- Auto-scaling based on load
- Self-healing capabilities
- Rolling updates with zero downtime
- Service discovery and load balancing
- Secrets and configuration management

### Why Multiple Databases?
- **PostgreSQL**: ACID compliance for critical business data
- **Redis**: Sub-millisecond response for caching
- **Neo4j**: Efficient graph traversal for relationships
- **ChromaDB**: Vector similarity search for AI features
- **InfluxDB**: Optimized time-series queries for metrics

## 🔧 Configuration

### Environment Variables
All services are configured via environment variables. See `.env.example` for a complete list.

### Resource Limits
Production resource allocations:
- Backend: 2 CPU cores, 2GB RAM
- Frontend: 0.5 CPU cores, 512MB RAM  
- Agents: 1 CPU core, 2GB RAM
- PostgreSQL: 4 CPU cores, 4GB RAM
- Redis: 2 CPU cores, 2GB RAM

### Scaling Policies
- Backend: 3-10 replicas, 70% CPU threshold
- Frontend: 2-6 replicas, 80% CPU threshold
- Agents: 2-8 replicas, 75% CPU threshold

## 📈 Performance Optimizations

### Caching Strategy
1. **Browser Cache**: 1 year for static assets
2. **CDN Cache**: CloudFlare or AWS CloudFront
3. **Redis Cache**: Database queries, API responses
4. **Application Cache**: In-memory caching for hot data

### Database Optimizations
- Connection pooling
- Query optimization with indexes
- Read replicas for analytics
- Materialized views for reports
- Partitioning for large tables

### Load Balancing
- Nginx reverse proxy with least connections
- Kubernetes service mesh (optional Istio)
- Sticky sessions for WebSocket connections

## 🛡️ Security Measures

### Network Security
- All traffic encrypted with TLS 1.2+
- Network policies restrict pod-to-pod communication
- WAF rules for common attacks
- Rate limiting per IP and API key

### Application Security
- JWT tokens with refresh mechanism
- CSRF protection
- SQL injection prevention
- XSS protection headers
- Content Security Policy

### Infrastructure Security
- Regular security scanning with Trivy
- OWASP dependency checks
- Container image signing
- Audit logging for all changes

## 🔄 Backup & Disaster Recovery

### Backup Schedule
- **Continuous**: Database transaction logs
- **Hourly**: Redis snapshots
- **Daily**: Full database backups
- **Weekly**: Full system backups

### Recovery Objectives
- **RPO** (Recovery Point Objective): < 1 hour
- **RTO** (Recovery Time Objective): < 4 hours

### Backup Storage
- Primary: S3 with lifecycle policies
- Secondary: Cross-region replication
- Archive: Glacier for long-term storage

## 📚 Additional Resources

- [Deployment Guide](./DEPLOYMENT.md)
- [Security Documentation](./SECURITY.md)
- [API Documentation](./API.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

## 🤝 Support

For infrastructure issues:
1. Check monitoring dashboards
2. Review logs in Kibana
3. Check GitHub Issues
4. Contact DevOps team

---

Last Updated: January 2024
Version: 1.0.0