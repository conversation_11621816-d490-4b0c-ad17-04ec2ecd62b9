import { getDatabaseConnection } from '../database/connection';
import { SyncQueue } from '../database/entities';
import { ConflictResolver } from './ConflictResolver';
import { SyncQueue as SyncQueueService } from './SyncQueue';
import { NetworkMonitor } from './NetworkMonitor';
import { SyncStatus, SyncResult, SyncOptions } from './types';
import api from '@services/api';
import { v4 as uuidv4 } from 'uuid';

export class SyncEngine {
  private static instance: SyncEngine;
  private conflictResolver: ConflictResolver;
  private syncQueue: SyncQueueService;
  private networkMonitor: NetworkMonitor;
  private isSyncing: boolean = false;
  private syncListeners: ((status: SyncStatus) => void)[] = [];

  private constructor() {
    this.conflictResolver = new ConflictResolver();
    this.syncQueue = new SyncQueueService();
    this.networkMonitor = new NetworkMonitor();
    this.setupNetworkListener();
  }

  static getInstance(): SyncEngine {
    if (!SyncEngine.instance) {
      SyncEngine.instance = new SyncEngine();
    }
    return SyncEngine.instance;
  }

  private setupNetworkListener() {
    this.networkMonitor.addListener((isConnected) => {
      if (isConnected && !this.isSyncing) {
        this.sync({ automatic: true });
      }
    });
  }

  addSyncListener(listener: (status: SyncStatus) => void) {
    this.syncListeners.push(listener);
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(status: SyncStatus) {
    this.syncListeners.forEach(listener => listener(status));
  }

  async sync(options: SyncOptions = {}): Promise<SyncResult> {
    if (this.isSyncing) {
      return { success: false, error: 'Sync already in progress' };
    }

    if (!this.networkMonitor.isConnected() && !options.forceOffline) {
      return { success: false, error: 'No network connection' };
    }

    this.isSyncing = true;
    const startTime = Date.now();
    const result: SyncResult = {
      success: true,
      syncedItems: 0,
      failedItems: 0,
      conflicts: [],
    };

    try {
      this.notifyListeners({
        isSyncing: true,
        progress: 0,
        message: 'Starting sync...',
      });

      // Get pending sync items
      const pendingItems = await this.syncQueue.getPendingItems(options);
      const totalItems = pendingItems.length;

      for (let i = 0; i < pendingItems.length; i++) {
        const item = pendingItems[i];
        
        try {
          await this.syncItem(item, options);
          result.syncedItems++;
        } catch (error) {
          console.error(`Failed to sync item ${item.id}:`, error);
          result.failedItems++;
          
          if (error.message?.includes('conflict')) {
            result.conflicts?.push({
              entityType: item.entityType,
              entityId: item.entityId,
              localData: JSON.parse(item.data),
              remoteData: error.remoteData,
            });
          }
        }

        this.notifyListeners({
          isSyncing: true,
          progress: ((i + 1) / totalItems) * 100,
          message: `Syncing ${i + 1} of ${totalItems}...`,
        });
      }

      // Pull remote changes
      if (!options.pushOnly) {
        await this.pullRemoteChanges(options);
      }

      result.duration = Date.now() - startTime;
      
      this.notifyListeners({
        isSyncing: false,
        progress: 100,
        message: 'Sync completed',
        lastSyncTime: Date.now(),
      });

    } catch (error) {
      result.success = false;
      result.error = error.message;
      
      this.notifyListeners({
        isSyncing: false,
        progress: 0,
        message: 'Sync failed',
        error: error.message,
      });
    } finally {
      this.isSyncing = false;
    }

    return result;
  }

  private async syncItem(item: SyncQueue, options: SyncOptions) {
    const maxRetries = options.maxRetries || 3;
    
    if (item.retryCount >= maxRetries) {
      await this.syncQueue.markAsFailed(item.id, 'Max retries exceeded');
      throw new Error('Max retries exceeded');
    }

    try {
      await this.syncQueue.markAsProcessing(item.id);

      const response = await this.sendToServer(item);
      
      if (response.conflict) {
        const resolution = await this.conflictResolver.resolve({
          entityType: item.entityType,
          entityId: item.entityId,
          localData: JSON.parse(item.data),
          remoteData: response.remoteData,
          strategy: options.conflictStrategy || 'last-write-wins',
        });

        if (resolution.action === 'retry') {
          await this.syncQueue.updateData(item.id, resolution.data);
          return this.syncItem(item, options);
        }
      }

      await this.syncQueue.markAsCompleted(item.id);
      await this.updateLocalEntity(item.entityType, item.entityId, response.data);

    } catch (error) {
      await this.syncQueue.incrementRetryCount(item.id);
      throw error;
    }
  }

  private async sendToServer(item: SyncQueue): Promise<any> {
    const endpoint = `/offline/sync/${item.entityType}`;
    const method = item.action === 'delete' ? 'DELETE' : 'POST';
    
    const response = await api.request({
      method,
      url: endpoint,
      data: {
        action: item.action,
        entityId: item.entityId,
        data: JSON.parse(item.data),
        clientId: await this.getClientId(),
        timestamp: item.createdAt,
      },
    });

    return response.data;
  }

  private async pullRemoteChanges(options: SyncOptions) {
    const lastSyncTime = await this.getLastSyncTime();
    const response = await api.get('/offline/changes', {
      params: {
        since: lastSyncTime,
        clientId: await this.getClientId(),
      },
    });

    const changes = response.data.changes;
    
    for (const change of changes) {
      await this.applyRemoteChange(change, options);
    }

    await this.setLastSyncTime(Date.now());
  }

  private async applyRemoteChange(change: any, options: SyncOptions) {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(change.entityType);
    
    const localEntity = await repository.findOne({
      where: { remoteId: change.remoteId },
    });

    if (localEntity && localEntity.version >= change.version) {
      // Local version is newer or same, check for conflicts
      if (localEntity.syncStatus === 'pending') {
        // We have local changes that haven't been synced
        const resolution = await this.conflictResolver.resolve({
          entityType: change.entityType,
          entityId: localEntity.id,
          localData: localEntity,
          remoteData: change.data,
          strategy: options.conflictStrategy || 'last-write-wins',
        });

        if (resolution.action === 'use-remote') {
          await repository.save({
            ...localEntity,
            ...change.data,
            version: change.version,
            syncStatus: 'synced',
          });
        }
      }
    } else {
      // Apply remote change
      if (localEntity) {
        await repository.save({
          ...localEntity,
          ...change.data,
          version: change.version,
          syncStatus: 'synced',
        });
      } else {
        await repository.save({
          ...change.data,
          remoteId: change.remoteId,
          version: change.version,
          syncStatus: 'synced',
        });
      }
    }
  }

  private async updateLocalEntity(entityType: string, entityId: string, remoteData: any) {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(entityType);
    
    await repository.update(entityId, {
      remoteId: remoteData.id,
      syncStatus: 'synced',
      lastSyncedAt: new Date().toISOString(),
      version: remoteData.version,
    });
  }

  private async getClientId(): Promise<string> {
    // TODO: Get from secure storage
    return 'client-' + uuidv4();
  }

  private async getLastSyncTime(): Promise<number> {
    // TODO: Get from persistent storage
    return 0;
  }

  private async setLastSyncTime(timestamp: number): Promise<void> {
    // TODO: Save to persistent storage
  }

  async forceSyncEntity(entityType: string, entityId: string): Promise<void> {
    await this.syncQueue.addToQueue({
      entityType,
      entityId,
      action: 'update',
      priority: 0, // High priority
    });
    
    await this.sync({ 
      filter: { entityId },
      maxRetries: 5,
    });
  }

  async getConflicts(): Promise<any[]> {
    const db = await getDatabaseConnection();
    const query = `
      SELECT * FROM projects WHERE syncStatus = 'conflict'
      UNION ALL
      SELECT * FROM panels WHERE syncStatus = 'conflict'
      UNION ALL
      SELECT * FROM materials WHERE syncStatus = 'conflict'
    `;
    
    return db.query(query);
  }

  async resolveConflict(entityType: string, entityId: string, resolution: 'local' | 'remote' | 'merge', mergeData?: any) {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(entityType);
    
    const entity = await repository.findOne({ where: { id: entityId } });
    if (!entity || entity.syncStatus !== 'conflict') {
      throw new Error('No conflict found for this entity');
    }

    const conflictData = JSON.parse(entity.conflictData || '{}');
    
    let resolvedData: any;
    switch (resolution) {
      case 'local':
        resolvedData = entity;
        break;
      case 'remote':
        resolvedData = conflictData.remote;
        break;
      case 'merge':
        resolvedData = mergeData || { ...entity, ...conflictData.remote };
        break;
    }

    await repository.save({
      ...entity,
      ...resolvedData,
      syncStatus: 'pending',
      conflictData: null,
      version: entity.version + 1,
    });

    await this.syncQueue.addToQueue({
      entityType,
      entityId,
      action: 'update',
      priority: 0,
    });
  }
}