export interface Project {
  id: string;
  name: string;
  description?: string;
  clientId: string;
  clientName: string;
  address: string;
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';
  startDate: string;
  endDate?: string;
  estimatedCost: number;
  actualCost?: number;
  progress: number;
  electricianIds: string[];
  createdAt: string;
  updatedAt: string;
  permits?: Permit[];
  inspections?: Inspection[];
  materials?: Material[];
  notes?: Note[];
}

export interface Permit {
  id: string;
  projectId: string;
  permitNumber: string;
  type: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  submittedDate: string;
  approvedDate?: string;
  expiryDate?: string;
  documents: Document[];
}

export interface Inspection {
  id: string;
  projectId: string;
  type: 'rough_in' | 'final' | 'service' | 'other';
  scheduledDate: string;
  completedDate?: string;
  status: 'scheduled' | 'passed' | 'failed' | 'pending';
  inspectorName?: string;
  notes?: string;
  failureReasons?: string[];
  photos?: string[];
}

export interface Material {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  supplier?: string;
  status: 'ordered' | 'delivered' | 'installed';
  orderedDate?: string;
  deliveredDate?: string;
}

export interface Note {
  id: string;
  projectId: string;
  authorId: string;
  authorName: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  attachments?: string[];
}

export interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  size: number;
  uploadedAt: string;
}

export interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  filters: {
    status: 'all' | Project['status'];
    dateRange: { start: string; end: string } | null;
  };
}