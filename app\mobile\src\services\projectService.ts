import api from './api';
import { Project, Permit, Inspection, Material, Note } from '@types/project';
import { PaginatedResponse } from '@types/index';

class ProjectService {
  async getProjects(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<Project[]> {
    const response = await api.get<PaginatedResponse<Project>>('/projects', { params });
    return response.data.data;
  }

  async getProjectById(id: string): Promise<Project> {
    const response = await api.get<Project>(`/projects/${id}`);
    return response.data;
  }

  async createProject(data: Partial<Project>): Promise<Project> {
    const response = await api.post<Project>('/projects', data);
    return response.data;
  }

  async updateProject(id: string, data: Partial<Project>): Promise<Project> {
    const response = await api.put<Project>(`/projects/${id}`, data);
    return response.data;
  }

  async deleteProject(id: string): Promise<void> {
    await api.delete(`/projects/${id}`);
  }

  // Permits
  async getProjectPermits(projectId: string): Promise<Permit[]> {
    const response = await api.get<Permit[]>(`/projects/${projectId}/permits`);
    return response.data;
  }

  async createPermit(projectId: string, data: Partial<Permit>): Promise<Permit> {
    const response = await api.post<Permit>(`/projects/${projectId}/permits`, data);
    return response.data;
  }

  async updatePermit(projectId: string, permitId: string, data: Partial<Permit>): Promise<Permit> {
    const response = await api.put<Permit>(`/projects/${projectId}/permits/${permitId}`, data);
    return response.data;
  }

  // Inspections
  async getProjectInspections(projectId: string): Promise<Inspection[]> {
    const response = await api.get<Inspection[]>(`/projects/${projectId}/inspections`);
    return response.data;
  }

  async createInspection(projectId: string, data: Partial<Inspection>): Promise<Inspection> {
    const response = await api.post<Inspection>(`/projects/${projectId}/inspections`, data);
    return response.data;
  }

  async updateInspection(
    projectId: string,
    inspectionId: string,
    data: Partial<Inspection>
  ): Promise<Inspection> {
    const response = await api.put<Inspection>(
      `/projects/${projectId}/inspections/${inspectionId}`,
      data
    );
    return response.data;
  }

  async uploadInspectionPhoto(
    projectId: string,
    inspectionId: string,
    photo: FormData
  ): Promise<string> {
    const response = await api.post<{ url: string }>(
      `/projects/${projectId}/inspections/${inspectionId}/photos`,
      photo,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data.url;
  }

  // Materials
  async getProjectMaterials(projectId: string): Promise<Material[]> {
    const response = await api.get<Material[]>(`/projects/${projectId}/materials`);
    return response.data;
  }

  async createMaterial(projectId: string, data: Partial<Material>): Promise<Material> {
    const response = await api.post<Material>(`/projects/${projectId}/materials`, data);
    return response.data;
  }

  async updateMaterial(
    projectId: string,
    materialId: string,
    data: Partial<Material>
  ): Promise<Material> {
    const response = await api.put<Material>(
      `/projects/${projectId}/materials/${materialId}`,
      data
    );
    return response.data;
  }

  async deleteMaterial(projectId: string, materialId: string): Promise<void> {
    await api.delete(`/projects/${projectId}/materials/${materialId}`);
  }

  // Notes
  async getProjectNotes(projectId: string): Promise<Note[]> {
    const response = await api.get<Note[]>(`/projects/${projectId}/notes`);
    return response.data;
  }

  async createNote(projectId: string, data: Partial<Note>): Promise<Note> {
    const response = await api.post<Note>(`/projects/${projectId}/notes`, data);
    return response.data;
  }

  async updateNote(projectId: string, noteId: string, data: Partial<Note>): Promise<Note> {
    const response = await api.put<Note>(`/projects/${projectId}/notes/${noteId}`, data);
    return response.data;
  }

  async deleteNote(projectId: string, noteId: string): Promise<void> {
    await api.delete(`/projects/${projectId}/notes/${noteId}`);
  }

  // Generate project report
  async generateReport(projectId: string, format: 'pdf' | 'excel' = 'pdf'): Promise<Blob> {
    const response = await api.get(`/projects/${projectId}/report`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  }
}

export const projectService = new ProjectService();