#!/bin/bash

# Run Calculation Tests with Coverage
# This script runs all electrical calculation tests and generates a coverage report

echo "========================================="
echo "Running Electrical Calculation Test Suite"
echo "========================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Change to backend directory
cd "$(dirname "$0")"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing dependencies...${NC}"
    pnpm install
fi

# Clear previous coverage
rm -rf coverage

echo -e "\n${GREEN}Running Load Calculation Tests...${NC}"
npm run test:load-calculation -- --verbose

echo -e "\n${GREEN}Running Voltage Drop Tests...${NC}"
npm run test:voltage-drop -- --verbose

echo -e "\n${GREEN}Running Wire Size Tests...${NC}"
npm run test:wire-size -- --verbose

echo -e "\n${GREEN}Running Conduit Fill Tests...${NC}"
npm run test:conduit-fill -- --verbose

echo -e "\n${GREEN}Running All Tests with Coverage...${NC}"
npm run test:coverage

# Check if coverage meets requirements
echo -e "\n${YELLOW}Checking Coverage Requirements...${NC}"
npm run test:coverage -- --coverageThreshold='{"global":{"branches":100,"functions":100,"lines":100,"statements":100}}'

if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}✓ All tests passed with 100% coverage!${NC}"
else
    echo -e "\n${RED}✗ Coverage requirements not met${NC}"
    echo -e "${YELLOW}Run 'npm run test:coverage:html' to see detailed coverage report${NC}"
    exit 1
fi

# Generate HTML coverage report
echo -e "\n${GREEN}Generating HTML Coverage Report...${NC}"
npm run test:coverage:html

echo -e "\n${GREEN}Test Summary:${NC}"
echo "- Load Calculation Service: ✓"
echo "- Voltage Drop Service: ✓"
echo "- Wire Size Service: ✓"
echo "- Conduit Fill Service: ✓"
echo -e "\nCoverage report available at: ${YELLOW}coverage/index.html${NC}"