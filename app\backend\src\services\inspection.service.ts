import { PrismaClient, InspectionChecklist, InspectionChecklistItem, Prisma } from '@prisma/client';
import { AppError } from '../utils/errors';

const prisma = new PrismaClient();

// NEC 2023 inspection types and their corresponding checklist items
const INSPECTION_TEMPLATES = {
  ROUGH_IN: {
    name: 'Rough-In Inspection',
    categories: [
      {
        category: 'WIRING',
        items: [
          {
            item_code: 'WIR-01',
            description: 'Cable sheathing extends at least 1/4" into boxes',
            nec_reference: '314.17(C)',
            inspection_criteria: 'Cable sheathing must extend through the cable clamp and be visible inside the box',
            photo_required: true
          },
          {
            item_code: 'WIR-02',
            description: 'Minimum 6" of free conductor at outlets and switches, 3" beyond box face',
            nec_reference: '300.14',
            inspection_criteria: 'Measure conductor length from where it emerges from cable sheath',
            photo_required: false
          },
          {
            item_code: 'WIR-03',
            description: 'Cables secured within 8" of boxes without cable clamps',
            nec_reference: '334.30',
            inspection_criteria: 'First staple must be within 8" of box entry for NM cable',
            photo_required: true
          },
          {
            item_code: 'WIR-04',
            description: 'Cables secured at maximum 4.5\' intervals',
            nec_reference: '334.30',
            inspection_criteria: 'Measure spacing between cable staples or supports',
            photo_required: false
          },
          {
            item_code: 'WIR-05',
            description: 'Cables protected where passing through metal studs',
            nec_reference: '300.4(B)(1)',
            inspection_criteria: 'Bushings or grommets installed in all metal stud penetrations',
            photo_required: true
          },
          {
            item_code: 'WIR-06',
            description: 'Protection plates installed where cables are less than 1.25" from stud edge',
            nec_reference: '300.4(A)(1)',
            inspection_criteria: 'Steel plates at least 1/16" thick covering the area of wiring',
            photo_required: true
          }
        ]
      },
      {
        category: 'BOXES',
        items: [
          {
            item_code: 'BOX-01',
            description: 'Boxes secured to structural members',
            nec_reference: '314.23',
            inspection_criteria: 'Boxes must be rigidly secured with appropriate fasteners',
            photo_required: false
          },
          {
            item_code: 'BOX-02',
            description: 'Box fill calculations correct',
            nec_reference: '314.16',
            inspection_criteria: 'Count conductors, devices, clamps per Table 314.16(A)',
            photo_required: false
          },
          {
            item_code: 'BOX-03',
            description: 'Boxes set to proper depth for wall finish',
            nec_reference: '314.20',
            inspection_criteria: 'Boxes flush with combustible surfaces, max 1/4" recess in non-combustible',
            photo_required: false
          },
          {
            item_code: 'BOX-04',
            description: 'Junction boxes accessible',
            nec_reference: '314.29',
            inspection_criteria: 'All junction boxes must remain accessible without removing building finish',
            photo_required: true
          }
        ]
      },
      {
        category: 'GROUNDING',
        items: [
          {
            item_code: 'GND-01',
            description: 'Equipment grounding conductors properly connected',
            nec_reference: '250.148',
            inspection_criteria: 'All EGCs connected together and to box if metal',
            photo_required: true
          },
          {
            item_code: 'GND-02',
            description: 'Grounding electrode conductor properly sized',
            nec_reference: 'Table 250.66',
            inspection_criteria: 'Size based on largest ungrounded service conductor',
            measurement_required: true,
            measurement_type: 'WIRE_SIZE'
          },
          {
            item_code: 'GND-03',
            description: 'Bonding jumpers installed at service equipment',
            nec_reference: '250.92',
            inspection_criteria: 'All service raceways and enclosures bonded together',
            photo_required: true
          }
        ]
      },
      {
        category: 'PANELS',
        items: [
          {
            item_code: 'PNL-01',
            description: 'Working clearance in front of panels',
            nec_reference: '110.26(A)',
            inspection_criteria: 'Minimum 36" depth, 30" width, 6.5\' height',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'INCHES'
          },
          {
            item_code: 'PNL-02',
            description: 'Panel mounting height appropriate',
            nec_reference: '408.41',
            inspection_criteria: 'No breaker center higher than 6\'7" above floor',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'INCHES'
          },
          {
            item_code: 'PNL-03',
            description: 'Directory card present',
            nec_reference: '408.4',
            inspection_criteria: 'Circuit directory provided and located on panel face or inside door',
            photo_required: false
          }
        ]
      }
    ]
  },
  FINAL: {
    name: 'Final Inspection',
    categories: [
      {
        category: 'DEVICES',
        items: [
          {
            item_code: 'DEV-01',
            description: 'GFCI protection installed in required locations',
            nec_reference: '210.8',
            inspection_criteria: 'Kitchen, bathroom, garage, outdoor, basement outlets protected',
            photo_required: false
          },
          {
            item_code: 'DEV-02',
            description: 'AFCI protection installed for required circuits',
            nec_reference: '210.12',
            inspection_criteria: 'All dwelling unit circuits as specified in 210.12(A)',
            photo_required: false
          },
          {
            item_code: 'DEV-03',
            description: 'Receptacles properly grounded',
            nec_reference: '406.4(C)',
            inspection_criteria: 'Test all receptacles for proper grounding',
            measurement_required: true,
            measurement_type: 'GROUND_TEST'
          },
          {
            item_code: 'DEV-04',
            description: 'Proper polarity at all receptacles',
            nec_reference: '200.11',
            inspection_criteria: 'Hot and neutral conductors correctly terminated',
            measurement_required: true,
            measurement_type: 'POLARITY_TEST'
          },
          {
            item_code: 'DEV-05',
            description: 'Tamper-resistant receptacles in dwelling units',
            nec_reference: '406.12',
            inspection_criteria: 'All 125V, 15 & 20A receptacles in areas specified',
            photo_required: false
          }
        ]
      },
      {
        category: 'LIGHTING',
        items: [
          {
            item_code: 'LGT-01',
            description: 'Required lighting outlets installed',
            nec_reference: '210.70',
            inspection_criteria: 'Verify all habitable rooms, bathrooms, hallways, stairways, garages',
            photo_required: false
          },
          {
            item_code: 'LGT-02',
            description: 'Recessed lights suitable for insulation contact',
            nec_reference: '410.116',
            inspection_criteria: 'IC-rated fixtures where insulation present',
            photo_required: true
          },
          {
            item_code: 'LGT-03',
            description: 'Bathroom fixtures suitable for damp/wet locations',
            nec_reference: '410.10(D)',
            inspection_criteria: 'Fixtures within tub/shower zones properly rated',
            photo_required: false
          }
        ]
      },
      {
        category: 'PANELS',
        items: [
          {
            item_code: 'PNL-04',
            description: 'All circuits properly labeled',
            nec_reference: '408.4(A)',
            inspection_criteria: 'Clear, specific identification of each circuit',
            photo_required: true
          },
          {
            item_code: 'PNL-05',
            description: 'Unused openings covered with identified blanks',
            nec_reference: '408.7',
            inspection_criteria: 'All unused spaces have approved blank covers',
            photo_required: false
          },
          {
            item_code: 'PNL-06',
            description: 'Arc-flash label present on service equipment',
            nec_reference: '110.16',
            inspection_criteria: 'Label must be present on service disconnect',
            photo_required: true
          }
        ]
      },
      {
        category: 'SAFETY',
        items: [
          {
            item_code: 'SAF-01',
            description: 'Smoke detectors interconnected and powered correctly',
            nec_reference: '210.12(B)',
            inspection_criteria: 'All smoke detectors on AFCI protected circuit',
            photo_required: false
          },
          {
            item_code: 'SAF-02',
            description: 'Emergency disconnect labeled at service',
            nec_reference: '230.85',
            inspection_criteria: 'EMERGENCY DISCONNECT - NOT SERVICE EQUIPMENT label if applicable',
            photo_required: true
          }
        ]
      }
    ]
  },
  SERVICE: {
    name: 'Service Inspection',
    categories: [
      {
        category: 'SERVICE_ENTRANCE',
        items: [
          {
            item_code: 'SVC-01',
            description: 'Service conductors properly sized',
            nec_reference: '230.42',
            inspection_criteria: 'Size per calculated load, minimum per 230.79',
            measurement_required: true,
            measurement_type: 'WIRE_SIZE'
          },
          {
            item_code: 'SVC-02',
            description: 'Service mast properly supported',
            nec_reference: '230.28',
            inspection_criteria: 'Adequate bracing for service drop tension',
            photo_required: true
          },
          {
            item_code: 'SVC-03',
            description: 'Weatherhead above point of attachment',
            nec_reference: '230.54(C)',
            inspection_criteria: 'Service head above service-drop attachment',
            photo_required: true
          },
          {
            item_code: 'SVC-04',
            description: 'Service grounding and bonding complete',
            nec_reference: '250.24',
            inspection_criteria: 'GEC connected, main bonding jumper installed',
            photo_required: true
          }
        ]
      },
      {
        category: 'METER',
        items: [
          {
            item_code: 'MTR-01',
            description: 'Meter socket properly grounded',
            nec_reference: '250.8',
            inspection_criteria: 'Meter enclosure bonded to grounding system',
            photo_required: false
          },
          {
            item_code: 'MTR-02',
            description: 'Working clearance at meter',
            nec_reference: '110.26',
            inspection_criteria: 'Proper clearances maintained',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'INCHES'
          }
        ]
      },
      {
        category: 'GROUNDING_ELECTRODE',
        items: [
          {
            item_code: 'GES-01',
            description: 'Grounding electrode system installed',
            nec_reference: '250.50',
            inspection_criteria: 'All present electrodes bonded together',
            photo_required: true
          },
          {
            item_code: 'GES-02',
            description: 'Ground rod installation depth',
            nec_reference: '250.53(G)',
            inspection_criteria: 'Minimum 8 feet in contact with soil',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'FEET'
          },
          {
            item_code: 'GES-03',
            description: 'Ground resistance test if required',
            nec_reference: '250.53(A)(2)',
            inspection_criteria: 'Single rod must be 25 ohms or less, or supplemented',
            measurement_required: true,
            measurement_type: 'RESISTANCE',
            measurement_unit: 'OHMS',
            measurement_range_max: 25
          }
        ]
      }
    ]
  },
  POOL_SPA: {
    name: 'Pool/Spa Inspection',
    categories: [
      {
        category: 'BONDING',
        items: [
          {
            item_code: 'POOL-01',
            description: 'Equipotential bonding grid installed',
            nec_reference: '680.26(B)',
            inspection_criteria: '#8 AWG solid copper grid, 12"x12" max spacing',
            photo_required: true
          },
          {
            item_code: 'POOL-02',
            description: 'Perimeter surfaces bonded',
            nec_reference: '680.26(B)(2)',
            inspection_criteria: '3 feet from pool edge, 18-24" below surface',
            photo_required: true
          },
          {
            item_code: 'POOL-03',
            description: 'Metal parts bonded to grid',
            nec_reference: '680.26(B)(7)',
            inspection_criteria: 'Ladder, handrails, diving boards, slides connected',
            photo_required: true
          },
          {
            item_code: 'POOL-04',
            description: 'Pool pump motor bonded',
            nec_reference: '680.26(B)(6)',
            inspection_criteria: 'Bonding conductor connected to motor',
            photo_required: true
          }
        ]
      },
      {
        category: 'GFCI_PROTECTION',
        items: [
          {
            item_code: 'POOL-05',
            description: 'Pool pump GFCI protected',
            nec_reference: '680.21(C)',
            inspection_criteria: 'GFCI protection for pump motor circuit',
            photo_required: false
          },
          {
            item_code: 'POOL-06',
            description: 'Underwater lights GFCI protected',
            nec_reference: '680.23(A)(3)',
            inspection_criteria: 'GFCI protection required for all underwater luminaires',
            photo_required: false
          },
          {
            item_code: 'POOL-07',
            description: 'Receptacles within 20\' GFCI protected',
            nec_reference: '680.22(A)(1)',
            inspection_criteria: 'All 125V receptacles within 20 feet of pool',
            photo_required: false
          }
        ]
      },
      {
        category: 'CLEARANCES',
        items: [
          {
            item_code: 'POOL-08',
            description: 'Receptacle distances from pool',
            nec_reference: '680.22(A)(1)',
            inspection_criteria: 'No receptacles within 6 feet of pool edge',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'FEET',
            measurement_range_min: 6
          },
          {
            item_code: 'POOL-09',
            description: 'Overhead conductor clearances',
            nec_reference: 'Table 680.9(A)',
            inspection_criteria: 'Verify clearances per table for voltage and location',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'FEET'
          }
        ]
      }
    ]
  },
  SOLAR: {
    name: 'Solar PV System Inspection',
    categories: [
      {
        category: 'DC_SYSTEM',
        items: [
          {
            item_code: 'SOL-01',
            description: 'DC combiner box properly labeled',
            nec_reference: '690.53',
            inspection_criteria: 'Warning labels for DC voltage present',
            photo_required: true
          },
          {
            item_code: 'SOL-02',
            description: 'DC conductors properly sized',
            nec_reference: '690.8',
            inspection_criteria: '125% of Isc for current, temperature corrected',
            measurement_required: true,
            measurement_type: 'WIRE_SIZE'
          },
          {
            item_code: 'SOL-03',
            description: 'DC arc-fault protection installed',
            nec_reference: '690.11',
            inspection_criteria: 'Arc-fault circuit protection for DC circuits',
            photo_required: false
          },
          {
            item_code: 'SOL-04',
            description: 'Rapid shutdown devices installed',
            nec_reference: '690.12',
            inspection_criteria: 'Rapid shutdown function verified',
            photo_required: true
          }
        ]
      },
      {
        category: 'GROUNDING',
        items: [
          {
            item_code: 'SOL-05',
            description: 'Equipment grounding conductors installed',
            nec_reference: '690.43',
            inspection_criteria: 'All metallic parts bonded to EGC',
            photo_required: true
          },
          {
            item_code: 'SOL-06',
            description: 'DC grounding electrode if required',
            nec_reference: '690.47',
            inspection_criteria: 'DC GEC installed per system configuration',
            photo_required: true
          }
        ]
      },
      {
        category: 'INVERTER',
        items: [
          {
            item_code: 'SOL-07',
            description: 'Inverter disconnect accessible',
            nec_reference: '690.15',
            inspection_criteria: 'Disconnecting means within sight or lockable',
            photo_required: true
          },
          {
            item_code: 'SOL-08',
            description: 'AC and DC disconnect labeling',
            nec_reference: '690.53',
            inspection_criteria: 'Proper labeling of all disconnects',
            photo_required: true
          }
        ]
      }
    ]
  },
  GENERATOR: {
    name: 'Generator Installation Inspection',
    categories: [
      {
        category: 'TRANSFER_EQUIPMENT',
        items: [
          {
            item_code: 'GEN-01',
            description: 'Transfer switch properly rated',
            nec_reference: '702.5',
            inspection_criteria: 'Rating adequate for calculated load',
            photo_required: true
          },
          {
            item_code: 'GEN-02',
            description: 'Transfer switch prevents backfeed',
            nec_reference: '702.5',
            inspection_criteria: 'Mechanical or electrical interlock functioning',
            photo_required: false
          },
          {
            item_code: 'GEN-03',
            description: 'Neutral switching if required',
            nec_reference: '250.20(B)',
            inspection_criteria: 'Separately derived system requirements met',
            photo_required: false
          }
        ]
      },
      {
        category: 'GENERATOR',
        items: [
          {
            item_code: 'GEN-04',
            description: 'Generator grounding system',
            nec_reference: '250.34',
            inspection_criteria: 'Portable or permanent generator properly grounded',
            photo_required: true
          },
          {
            item_code: 'GEN-05',
            description: 'Generator disconnect location',
            nec_reference: '445.18',
            inspection_criteria: 'Disconnect readily accessible',
            photo_required: true
          },
          {
            item_code: 'GEN-06',
            description: 'Load management system if used',
            nec_reference: '702.4(B)',
            inspection_criteria: 'Automatic load management functioning',
            photo_required: false
          }
        ]
      },
      {
        category: 'INSTALLATION',
        items: [
          {
            item_code: 'GEN-07',
            description: 'Minimum clearances maintained',
            nec_reference: '110.26',
            inspection_criteria: 'Working space and ventilation clearances',
            measurement_required: true,
            measurement_type: 'DISTANCE',
            measurement_unit: 'INCHES'
          },
          {
            item_code: 'GEN-08',
            description: 'Flexible connections for vibration',
            nec_reference: '445.17',
            inspection_criteria: 'Flexible conduit at generator connection',
            photo_required: true
          }
        ]
      }
    ]
  },
  EMERGENCY_SYSTEM: {
    name: 'Emergency System Inspection',
    categories: [
      {
        category: 'WIRING',
        items: [
          {
            item_code: 'EMER-01',
            description: 'Emergency circuit identification',
            nec_reference: '700.10(A)',
            inspection_criteria: 'All boxes and enclosures properly marked',
            photo_required: true
          },
          {
            item_code: 'EMER-02',
            description: 'Wiring kept separate from normal circuits',
            nec_reference: '700.10(B)',
            inspection_criteria: 'Emergency wiring in separate raceways',
            photo_required: true
          },
          {
            item_code: 'EMER-03',
            description: 'Fire protection for emergency feeders',
            nec_reference: '700.10(D)',
            inspection_criteria: '2-hour fire rating or circuit protective system',
            photo_required: true
          }
        ]
      },
      {
        category: 'TRANSFER_EQUIPMENT',
        items: [
          {
            item_code: 'EMER-04',
            description: 'Automatic transfer within 10 seconds',
            nec_reference: '700.12',
            inspection_criteria: 'Test automatic transfer timing',
            measurement_required: true,
            measurement_type: 'TIME',
            measurement_unit: 'SECONDS',
            measurement_range_max: 10
          },
          {
            item_code: 'EMER-05',
            description: 'Transfer switch exercising timer',
            nec_reference: 'NFPA 110',
            inspection_criteria: 'Monthly exercising programmed',
            photo_required: false
          }
        ]
      },
      {
        category: 'TESTING',
        items: [
          {
            item_code: 'EMER-06',
            description: 'Emergency lighting test switch',
            nec_reference: '700.3',
            inspection_criteria: 'Test switch installed and functional',
            photo_required: true
          },
          {
            item_code: 'EMER-07',
            description: 'Battery systems functional test',
            nec_reference: '700.3(F)',
            inspection_criteria: 'Unit equipment tested for proper operation',
            photo_required: false
          }
        ]
      }
    ]
  }
};

export class InspectionService {
  /**
   * Create a new inspection checklist from template
   */
  async createInspectionChecklist(data: {
    projectId: string;
    inspectionType: string;
    inspectionSubtype?: string;
    scheduledDate?: Date;
    createdBy: string;
    permitDocumentId?: string;
  }): Promise<InspectionChecklist> {
    const template = INSPECTION_TEMPLATES[data.inspectionType as keyof typeof INSPECTION_TEMPLATES];
    if (!template) {
      throw new AppError(`Invalid inspection type: ${data.inspectionType}`, 400);
    }

    // Check if project exists
    const project = await prisma.project.findUnique({
      where: { id: data.projectId }
    });

    if (!project) {
      throw new AppError('Project not found', 404);
    }

    // Generate QR code ID
    const qrCodeId = `INS-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Create the inspection checklist
    const checklist = await prisma.inspectionChecklist.create({
      data: {
        project_id: data.projectId,
        permit_document_id: data.permitDocumentId,
        inspection_type: data.inspectionType,
        inspection_subtype: data.inspectionSubtype,
        scheduled_date: data.scheduledDate,
        qr_code_id: qrCodeId,
        qr_code_url: `/inspection/mobile/${qrCodeId}`,
        created_by: data.createdBy,
        checklist_items: {
          create: template.categories.flatMap((category, categoryIndex) =>
            category.items.map((item, itemIndex) => ({
              category: category.category,
              item_code: item.item_code,
              description: item.description,
              nec_reference: item.nec_reference,
              inspection_criteria: item.inspection_criteria,
              photo_required: item.photo_required || false,
              measurement_required: item.measurement_required || false,
              measurement_type: item.measurement_type,
              measurement_unit: item.measurement_unit,
              measurement_range_min: item.measurement_range_min,
              measurement_range_max: item.measurement_range_max,
              sequence_number: categoryIndex * 100 + itemIndex,
              severity: item.severity || 'STANDARD'
            }))
          )
        }
      },
      include: {
        checklist_items: {
          orderBy: { sequence_number: 'asc' }
        },
        project: {
          include: {
            customer: true
          }
        }
      }
    });

    return checklist;
  }

  /**
   * Get inspection checklist by ID
   */
  async getInspectionChecklist(id: string): Promise<InspectionChecklist | null> {
    return prisma.inspectionChecklist.findUnique({
      where: { id },
      include: {
        checklist_items: {
          orderBy: { sequence_number: 'asc' }
        },
        photos: {
          orderBy: { uploaded_at: 'desc' }
        },
        project: {
          include: {
            customer: true
          }
        },
        permit_document: true
      }
    });
  }

  /**
   * Get inspection checklist by QR code
   */
  async getInspectionByQRCode(qrCodeId: string): Promise<InspectionChecklist | null> {
    return prisma.inspectionChecklist.findUnique({
      where: { qr_code_id: qrCodeId },
      include: {
        checklist_items: {
          orderBy: { sequence_number: 'asc' }
        },
        photos: {
          orderBy: { uploaded_at: 'desc' }
        },
        project: {
          include: {
            customer: true
          }
        }
      }
    });
  }

  /**
   * List inspection checklists for a project
   */
  async listProjectInspections(
    projectId: string,
    filters?: {
      status?: string;
      inspectionType?: string;
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Promise<InspectionChecklist[]> {
    const where: Prisma.InspectionChecklistWhereInput = {
      project_id: projectId,
      ...(filters?.status && { status: filters.status }),
      ...(filters?.inspectionType && { inspection_type: filters.inspectionType }),
      ...(filters?.dateFrom || filters?.dateTo ? {
        inspection_date: {
          ...(filters?.dateFrom && { gte: filters.dateFrom }),
          ...(filters?.dateTo && { lte: filters.dateTo })
        }
      } : {})
    };

    return prisma.inspectionChecklist.findMany({
      where,
      include: {
        checklist_items: {
          where: {
            status: { not: 'NOT_INSPECTED' }
          },
          orderBy: { sequence_number: 'asc' }
        },
        photos: {
          select: {
            id: true,
            photo_type: true
          }
        }
      },
      orderBy: [
        { inspection_date: 'desc' },
        { created_at: 'desc' }
      ]
    });
  }

  /**
   * Update inspection checklist status
   */
  async updateInspectionStatus(
    id: string,
    data: {
      status?: string;
      inspectorName?: string;
      inspectorId?: string;
      inspectorCompany?: string;
      inspectorPhone?: string;
      inspectorEmail?: string;
      inspectionDate?: Date;
      overallResult?: string;
      reinspectionRequired?: boolean;
      correctionsRequired?: boolean;
    }
  ): Promise<InspectionChecklist> {
    // If marking as completed, check all required items
    if (data.status === 'PASSED' || data.status === 'FAILED') {
      const checklist = await prisma.inspectionChecklist.findUnique({
        where: { id },
        include: {
          checklist_items: {
            where: { is_required: true }
          }
        }
      });

      if (!checklist) {
        throw new AppError('Inspection checklist not found', 404);
      }

      const uninspectedItems = checklist.checklist_items.filter(
        item => item.status === 'NOT_INSPECTED'
      );

      if (uninspectedItems.length > 0) {
        throw new AppError(
          `Cannot complete inspection: ${uninspectedItems.length} required items not inspected`,
          400
        );
      }
    }

    return prisma.inspectionChecklist.update({
      where: { id },
      data: {
        ...(data.status && { status: data.status }),
        ...(data.inspectorName && { inspector_name: data.inspectorName }),
        ...(data.inspectorId && { inspector_id: data.inspectorId }),
        ...(data.inspectorCompany && { inspector_company: data.inspectorCompany }),
        ...(data.inspectorPhone && { inspector_phone: data.inspectorPhone }),
        ...(data.inspectorEmail && { inspector_email: data.inspectorEmail }),
        ...(data.inspectionDate && { inspection_date: data.inspectionDate }),
        ...(data.overallResult && { overall_result: data.overallResult }),
        ...(data.reinspectionRequired !== undefined && { 
          reinspection_required: data.reinspectionRequired 
        }),
        ...(data.correctionsRequired !== undefined && { 
          corrections_required: data.correctionsRequired 
        }),
        ...(data.status === 'PASSED' || data.status === 'FAILED' ? {
          completed_at: new Date()
        } : {})
      },
      include: {
        checklist_items: {
          orderBy: { sequence_number: 'asc' }
        },
        photos: true
      }
    });
  }

  /**
   * Update individual checklist item
   */
  async updateChecklistItem(
    id: string,
    data: {
      status?: string;
      inspectorNotes?: string;
      failureReason?: string;
      correctionRequired?: boolean;
      correctionDescription?: string;
      correctionDeadline?: Date;
      measurementValue?: string;
      photosAttached?: number;
    }
  ): Promise<InspectionChecklistItem> {
    return prisma.inspectionChecklistItem.update({
      where: { id },
      data: {
        ...(data.status && { 
          status: data.status,
          inspected_at: new Date()
        }),
        ...(data.inspectorNotes !== undefined && { inspector_notes: data.inspectorNotes }),
        ...(data.failureReason !== undefined && { failure_reason: data.failureReason }),
        ...(data.correctionRequired !== undefined && { 
          correction_required: data.correctionRequired 
        }),
        ...(data.correctionDescription !== undefined && { 
          correction_description: data.correctionDescription 
        }),
        ...(data.correctionDeadline && { correction_deadline: data.correctionDeadline }),
        ...(data.measurementValue !== undefined && { measurement_value: data.measurementValue }),
        ...(data.photosAttached !== undefined && { photos_attached: data.photosAttached })
      }
    });
  }

  /**
   * Mark correction as completed
   */
  async completeCorrection(
    itemId: string,
    verifiedBy: string
  ): Promise<InspectionChecklistItem> {
    return prisma.inspectionChecklistItem.update({
      where: { id: itemId },
      data: {
        correction_completed: true,
        correction_date: new Date(),
        correction_verified_by: verifiedBy,
        status: 'CORRECTED'
      }
    });
  }

  /**
   * Add photo to inspection
   */
  async addInspectionPhoto(data: {
    checklistId: string;
    itemId?: string;
    filePath: string;
    fileName: string;
    fileSize: number;
    mimeType?: string;
    caption?: string;
    photoType: string;
    locationTag?: string;
    uploadedBy: string;
    latitude?: number;
    longitude?: number;
  }) {
    return prisma.inspectionPhoto.create({
      data: {
        checklist_id: data.checklistId,
        item_id: data.itemId,
        file_path: data.filePath,
        file_name: data.fileName,
        file_size: data.fileSize,
        mime_type: data.mimeType || 'image/jpeg',
        caption: data.caption,
        photo_type: data.photoType,
        location_tag: data.locationTag,
        uploaded_by: data.uploadedBy,
        latitude: data.latitude,
        longitude: data.longitude
      }
    });
  }

  /**
   * Sign off inspection
   */
  async signOffInspection(
    id: string,
    data: {
      contractorPresent: boolean;
      contractorName?: string;
      contractorSignature?: string;
      inspectorSignature?: string;
    }
  ): Promise<InspectionChecklist> {
    return prisma.inspectionChecklist.update({
      where: { id },
      data: {
        contractor_present: data.contractorPresent,
        contractor_name: data.contractorName,
        contractor_signature: data.contractorSignature,
        inspector_signature: data.inspectorSignature,
        sign_off_date: new Date()
      }
    });
  }

  /**
   * Generate inspection report
   */
  async generateInspectionReport(id: string): Promise<{ reportPath: string }> {
    const checklist = await this.getInspectionChecklist(id);
    if (!checklist) {
      throw new AppError('Inspection checklist not found', 404);
    }

    // Group items by category
    const itemsByCategory = checklist.checklist_items.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    }, {} as Record<string, typeof checklist.checklist_items>);

    // Calculate statistics
    const totalItems = checklist.checklist_items.length;
    const passedItems = checklist.checklist_items.filter(i => i.status === 'PASS').length;
    const failedItems = checklist.checklist_items.filter(i => i.status === 'FAIL').length;
    const naItems = checklist.checklist_items.filter(i => i.status === 'NA').length;
    const correctedItems = checklist.checklist_items.filter(i => i.status === 'CORRECTED').length;

    // TODO: Implement actual PDF generation
    // For now, return a placeholder path
    const reportPath = `/reports/inspection/${checklist.id}_${Date.now()}.pdf`;

    // Update the checklist to indicate report was generated
    await prisma.inspectionChecklist.update({
      where: { id },
      data: {
        report_generated: true,
        report_path: reportPath
      }
    });

    return { reportPath };
  }

  /**
   * Get inspection statistics for a project
   */
  async getProjectInspectionStats(projectId: string) {
    const inspections = await prisma.inspectionChecklist.findMany({
      where: { project_id: projectId },
      include: {
        checklist_items: {
          select: {
            status: true,
            correction_required: true,
            correction_completed: true
          }
        }
      }
    });

    const stats = {
      totalInspections: inspections.length,
      passed: inspections.filter(i => i.overall_result === 'PASS').length,
      failed: inspections.filter(i => i.overall_result === 'FAIL').length,
      pending: inspections.filter(i => i.status === 'PENDING').length,
      scheduled: inspections.filter(i => i.status === 'SCHEDULED').length,
      correctionsRequired: inspections.filter(i => i.corrections_required).length,
      reinspectionsRequired: inspections.filter(i => i.reinspection_required).length,
      byType: {} as Record<string, number>
    };

    // Count by type
    inspections.forEach(inspection => {
      if (!stats.byType[inspection.inspection_type]) {
        stats.byType[inspection.inspection_type] = 0;
      }
      stats.byType[inspection.inspection_type]++;
    });

    return stats;
  }

  /**
   * Get available inspection types
   */
  getAvailableInspectionTypes() {
    return Object.entries(INSPECTION_TEMPLATES).map(([key, template]) => ({
      key,
      name: template.name,
      categories: template.categories.map(c => ({
        name: c.category,
        itemCount: c.items.length
      }))
    }));
  }
}