import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@store/index';
import {
  setOnlineStatus,
  setSyncInProgress,
  setLastSyncTime,
  removePendingAction,
} from '@store/slices/offlineSlice';
import { showToast } from '@store/slices/uiSlice';
import api from '@services/api';

export const useOfflineSync = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isOnline, pendingActions, syncInProgress } = useSelector(
    (state: RootState) => state.offline
  );
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    // Monitor network connectivity
    const unsubscribeNetInfo = NetInfo.addEventListener(state => {
      dispatch(setOnlineStatus(state.isConnected ?? false));
      
      if (state.isConnected && pendingActions.length > 0 && !syncInProgress) {
        syncPendingActions();
      }
    });

    // Monitor app state
    const appStateSubscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        isOnline &&
        pendingActions.length > 0 &&
        !syncInProgress
      ) {
        syncPendingActions();
      }
      appState.current = nextAppState;
    });

    return () => {
      unsubscribeNetInfo();
      appStateSubscription.remove();
    };
  }, [dispatch, isOnline, pendingActions, syncInProgress]);

  const syncPendingActions = async () => {
    dispatch(setSyncInProgress(true));
    dispatch(showToast({ message: 'Syncing offline data...', type: 'info' }));

    let successCount = 0;
    let errorCount = 0;

    for (const action of pendingActions) {
      try {
        if (action.type === 'API_REQUEST') {
          const { url, method, data } = action.payload;
          await api.request({
            url,
            method,
            data,
          });
          successCount++;
          dispatch(removePendingAction(action.id));
        }
      } catch (error) {
        errorCount++;
        console.error('Failed to sync action:', action, error);
      }
    }

    dispatch(setSyncInProgress(false));
    dispatch(setLastSyncTime(Date.now()));

    if (successCount > 0) {
      dispatch(
        showToast({
          message: `Successfully synced ${successCount} offline actions`,
          type: 'success',
        })
      );
    }

    if (errorCount > 0) {
      dispatch(
        showToast({
          message: `Failed to sync ${errorCount} actions. Will retry later.`,
          type: 'error',
        })
      );
    }
  };

  return {
    isOnline,
    pendingActionsCount: pendingActions.length,
    syncInProgress,
    syncPendingActions,
  };
};