import { PrismaClient } from '@prisma/client';
import { arcFlashService, ArcFlashInput } from './calculations/arc-flash';
import { getTransactionService } from './transaction.service';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

export class ArcFlashService {
  /**
   * Create a new arc flash calculation for a panel with transaction support
   */
  async createCalculation(input: ArcFlashInput & { userId: string }) {
    const transactionResult = await transactionService.executeTransaction(async (tx) => {
      // Verify panel exists and user has access
      const panel = await tx.panel.findFirst({
        where: {
          id: input.panelId,
          project: {
            OR: [
              { customer: { id: { not: undefined } } }, // User has access to customer
              // Add more access control logic as needed
            ],
          },
        },
        include: {
          project: true,
        },
      });
      
      if (!panel) {
        throw new Error('Panel not found or access denied');
      }
      
      // Perform calculation
      const result = await arcFlashService.calculate({
        ...input,
        calculatedBy: input.userId,
      });
      
      // Create arc flash calculation record
      const calculation = await tx.arcFlashCalculation.create({
        data: {
          panel_id: input.panelId,
          calculation_date: new Date(),
          system_voltage: input.systemVoltage,
          system_type: input.systemType,
          grounding_type: input.groundingType,
          bolted_fault_current: input.boltedFaultCurrent,
          fault_clearing_time: input.faultClearingTime,
          equipment_type: input.equipmentType,
          electrode_configuration: input.electrodeConfiguration,
          enclosure_width: input.enclosureWidth,
          enclosure_height: input.enclosureHeight,
          enclosure_depth: input.enclosureDepth,
          conductor_gap: input.conductorGap,
          working_distance: input.workingDistance,
          arc_current: result.arcingCurrentMax,
          incident_energy: result.incidentEnergyMax,
          arc_flash_boundary: result.arcFlashBoundary,
          ppe_category: result.ppeCategory,
          shock_hazard_voltage: result.shockHazardVoltage,
          limited_approach: result.limitedApproach,
          restricted_approach: result.restrictedApproach,
          prohibited_approach: result.restrictedApproach, // Using restricted as prohibited not available
          equipment_label_required: true, // Default to true for safety
          notes: input.notes || '',
          calculated_by: input.userId,
          valid_until: new Date(Date.now() + 5 * 365 * 24 * 60 * 60 * 1000), // 5 years
          created_at: new Date(),
          updated_at: new Date()
        }
      });
      
      // Log calculation
      await tx.calculationLog.create({
        data: {
          calculation_type: 'ARC_FLASH',
          input_data: JSON.stringify(input),
          output_data: JSON.stringify(result),
          nec_references: JSON.stringify([
            'IEEE 1584-2018',
            'NFPA 70E-2021 Article 130',
            'NEC 110.16',
          ]),
          performed_by: input.userId,
          project_id: panel.project_id,
        },
      });
      
      // Update panel with latest arc flash data
      await tx.panel.update({
        where: { id: input.panelId },
        data: {
          arc_flash_incident_energy: result.incidentEnergy,
          arc_flash_boundary: result.arcFlashBoundary,
          arc_flash_ppe_category: result.ppeCategory,
          arc_flash_last_calculated: new Date(),
          updated_at: new Date()
        }
      });
      
      // Create audit log
      await createAuditLog({
        action: AUDIT_ACTIONS.CALCULATION_PERFORM,
        userId: input.userId,
        resourceType: 'PANEL',
        resourceId: input.panelId,
        details: {
          calculation_id: calculation.id,
          calculation_type: 'ARC_FLASH',
          incident_energy: result.incidentEnergy,
          ppe_category: result.ppeCategory,
          arc_flash_boundary: result.arcFlashBoundary
        }
      });
      
      return { calculation, result };
    });
    
    if (!transactionResult.success) {
      throw transactionResult.error || new Error('Failed to create arc flash calculation');
    }
    
    return transactionResult.data!.result;
  }
  
  /**
   * Get all calculations for a panel
   */
  async getCalculationsForPanel(panelId: string, userId: string) {
    // Verify access
    const panel = await prisma.panel.findFirst({
      where: {
        id: panelId,
        project: {
          OR: [
            { customer: { id: { not: undefined } } }, // User has access
          ],
        },
      },
    });
    
    if (!panel) {
      throw new Error('Panel not found or access denied');
    }
    
    return await arcFlashService.getCalculationsForPanel(panelId);
  }
  
  /**
   * Get latest calculation for a panel
   */
  async getLatestCalculation(panelId: string, userId: string) {
    // Verify access
    const panel = await prisma.panel.findFirst({
      where: {
        id: panelId,
        project: {
          OR: [
            { customer: { id: { not: undefined } } }, // User has access
          ],
        },
      },
    });
    
    if (!panel) {
      throw new Error('Panel not found or access denied');
    }
    
    return await arcFlashService.getLatestCalculation(panelId);
  }
  
  /**
   * Generate arc flash label for a calculation
   */
  async generateLabel(calculationId: string, userId: string) {
    // Verify access
    const calculation = await prisma.arcFlashCalculation.findFirst({
      where: {
        id: calculationId,
        panel: {
          project: {
            OR: [
              { customer: { id: { not: undefined } } }, // User has access
            ],
          },
        },
      },
    });
    
    if (!calculation) {
      throw new Error('Calculation not found or access denied');
    }
    
    return await arcFlashService.generateLabelData(calculationId);
  }
  
  /**
   * Get panels requiring arc flash analysis
   */
  async getPanelsRequiringAnalysis(projectId: string, userId: string) {
    // Get all panels for project without recent arc flash calculations
    const panels = await prisma.panel.findMany({
      where: {
        project_id: projectId,
        project: {
          OR: [
            { customer: { id: { not: undefined } } }, // User has access
          ],
        },
      },
      include: {
        arc_flash_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
      },
    });
    
    const requiresAnalysis = panels.filter(panel => {
      if (panel.arc_flash_calculations.length === 0) {
        return true; // No calculations exist
      }
      
      const latestCalc = panel.arc_flash_calculations[0];
      const fiveYearsAgo = new Date();
      fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);
      
      // Requires new analysis if older than 5 years
      return latestCalc.calculation_date < fiveYearsAgo;
    });
    
    return requiresAnalysis;
  }
  
  /**
   * Validate arc flash input parameters
   */
  validateInput(input: any): ArcFlashInput {
    // Validate required fields
    if (!input.panelId) throw new Error('Panel ID is required');
    if (!input.systemVoltage) throw new Error('System voltage is required');
    if (!input.systemType) throw new Error('System type is required');
    if (!input.groundingType) throw new Error('Grounding type is required');
    if (!input.boltedFaultCurrent) throw new Error('Bolted fault current is required');
    if (!input.faultClearingTime) throw new Error('Fault clearing time is required');
    if (!input.equipmentType) throw new Error('Equipment type is required');
    if (!input.electrodeConfiguration) throw new Error('Electrode configuration is required');
    if (!input.enclosureWidth) throw new Error('Enclosure width is required');
    if (!input.enclosureHeight) throw new Error('Enclosure height is required');
    if (!input.enclosureDepth) throw new Error('Enclosure depth is required');
    if (!input.conductorGap) throw new Error('Conductor gap is required');
    if (!input.workingDistance) throw new Error('Working distance is required');
    
    // Validate system type
    if (!['THREE_PHASE', 'SINGLE_PHASE'].includes(input.systemType)) {
      throw new Error('Invalid system type');
    }
    
    // Validate grounding type
    if (!['SOLIDLY_GROUNDED', 'RESISTANCE_GROUNDED', 'UNGROUNDED'].includes(input.groundingType)) {
      throw new Error('Invalid grounding type');
    }
    
    // Validate equipment type
    if (!['SWITCHGEAR', 'PANELBOARD', 'MCC', 'CABLE'].includes(input.equipmentType)) {
      throw new Error('Invalid equipment type');
    }
    
    // Validate electrode configuration
    if (!['VCB', 'VCBB', 'HCB', 'VOA', 'HOA'].includes(input.electrodeConfiguration)) {
      throw new Error('Invalid electrode configuration');
    }
    
    // Validate frequency
    if (input.frequency && ![50, 60].includes(input.frequency)) {
      throw new Error('Frequency must be 50 or 60 Hz');
    }
    
    // Validate numeric values
    if (input.systemVoltage < 208 || input.systemVoltage > 15000) {
      throw new Error('System voltage must be between 208V and 15kV');
    }
    
    if (input.boltedFaultCurrent < 500 || input.boltedFaultCurrent > 106000) {
      throw new Error('Bolted fault current must be between 500A and 106kA');
    }
    
    if (input.faultClearingTime <= 0 || input.faultClearingTime > 2) {
      throw new Error('Fault clearing time must be between 0 and 2 seconds');
    }
    
    if (input.workingDistance < 12) {
      throw new Error('Working distance must be at least 12 inches');
    }
    
    return input as ArcFlashInput;
  }
  
  /**
   * Get recommended PPE based on incident energy
   */
  getRecommendedPPE(incidentEnergy: number): {
    category: number;
    description: string;
    requirements: string[];
  } {
    if (incidentEnergy <= 1.2) {
      return {
        category: 0,
        description: 'Minimal PPE Required',
        requirements: [
          'Safety glasses',
          'Hearing protection',
          'Leather gloves',
          'Natural fiber clothing',
        ],
      };
    } else if (incidentEnergy <= 4) {
      return {
        category: 1,
        description: 'PPE Category 1',
        requirements: [
          'Arc-rated clothing, minimum 4 cal/cm²',
          'Arc-rated face shield or hood',
          'Safety glasses',
          'Hearing protection',
          'Leather gloves',
          'Leather footwear',
        ],
      };
    } else if (incidentEnergy <= 8) {
      return {
        category: 2,
        description: 'PPE Category 2',
        requirements: [
          'Arc-rated clothing, minimum 8 cal/cm²',
          'Arc-rated hood and face shield',
          'Safety glasses',
          'Hearing protection',
          'Leather gloves',
          'Leather footwear',
        ],
      };
    } else if (incidentEnergy <= 25) {
      return {
        category: 3,
        description: 'PPE Category 3',
        requirements: [
          'Arc-rated clothing, minimum 25 cal/cm²',
          'Arc-rated arc flash suit hood',
          'Arc-rated gloves',
          'Safety glasses',
          'Hearing protection',
          'Leather footwear',
        ],
      };
    } else if (incidentEnergy <= 40) {
      return {
        category: 4,
        description: 'PPE Category 4',
        requirements: [
          'Arc-rated clothing, minimum 40 cal/cm²',
          'Arc-rated arc flash suit hood',
          'Arc-rated gloves',
          'Safety glasses',
          'Hearing protection',
          'Leather footwear',
        ],
      };
    } else {
      return {
        category: 5,
        description: 'NO SAFE PPE EXISTS',
        requirements: [
          'DO NOT WORK ON ENERGIZED EQUIPMENT',
          'De-energize before work',
          'Use remote operation methods',
          'Incident energy exceeds 40 cal/cm²',
        ],
      };
    }
  }
}

export const arcFlashServiceInstance = new ArcFlashService();