import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useSyncStatus } from '@offline/hooks/useSyncStatus';
import { formatDistanceToNow } from 'date-fns';

export const SyncStatusBar: React.FC = () => {
  const {
    isOnline,
    isSyncing,
    syncProgress,
    syncMessage,
    lastSyncTime,
    pendingChanges,
    connectionType,
    isWifi,
    syncError,
    manualSync,
    clearSyncError,
  } = useSyncStatus();

  const getStatusColor = () => {
    if (!isOnline) return '#FF5252';
    if (syncError) return '#FFA726';
    if (isSyncing) return '#42A5F5';
    if (pendingChanges > 0) return '#FFA726';
    return '#66BB6A';
  };

  const getStatusIcon = () => {
    if (!isOnline) return 'cloud-off';
    if (syncError) return 'sync-problem';
    if (isSyncing) return 'sync';
    if (pendingChanges > 0) return 'cloud-upload';
    return 'cloud-done';
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    if (syncError) return 'Sync Error';
    if (isSyncing) return syncMessage || 'Syncing...';
    if (pendingChanges > 0) return `${pendingChanges} pending`;
    if (lastSyncTime) {
      return `Synced ${formatDistanceToNow(lastSyncTime, { addSuffix: true })}`;
    }
    return 'All synced';
  };

  return (
    <View style={[styles.container, { backgroundColor: getStatusColor() }]}>
      <View style={styles.leftSection}>
        <Icon name={getStatusIcon()} size={20} color="white" />
        <Text style={styles.statusText}>{getStatusText()}</Text>
        
        {isOnline && connectionType !== 'unknown' && (
          <View style={styles.connectionInfo}>
            <Icon 
              name={isWifi ? 'wifi' : 'signal-cellular-alt'} 
              size={16} 
              color="white" 
            />
            <Text style={styles.connectionText}>
              {connectionType.toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.rightSection}>
        {isSyncing && (
          <View style={styles.progressContainer}>
            <ActivityIndicator size="small" color="white" />
            {syncProgress > 0 && (
              <Text style={styles.progressText}>{Math.round(syncProgress)}%</Text>
            )}
          </View>
        )}

        {!isSyncing && isOnline && (pendingChanges > 0 || syncError) && (
          <TouchableOpacity 
            onPress={syncError ? clearSyncError : manualSync}
            style={styles.actionButton}
          >
            <Text style={styles.actionText}>
              {syncError ? 'Dismiss' : 'Sync Now'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 36,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
  connectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    paddingLeft: 12,
    borderLeftWidth: 1,
    borderLeftColor: 'rgba(255, 255, 255, 0.3)',
  },
  connectionText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
  },
  actionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});