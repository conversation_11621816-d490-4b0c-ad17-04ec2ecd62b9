import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

interface MaterialSeed {
  sku: string;
  name: string;
  description?: string;
  category: string;
  manufacturer: string;
  model_number?: string;
  unit: string;
  current_price?: number;
  wire_size?: string;
  wire_type?: string;
  conduit_size?: string;
  conduit_type?: string;
  voltage_rating?: number;
  amperage_rating?: number;
  phase?: string;
  color?: string;
}

const materials: MaterialSeed[] = [
  // Square D / Schneider Electric Panels and Breakers
  {
    sku: 'QO142M200PC',
    name: 'Square D QO 200A 42-Space Main Breaker Load Center',
    category: 'PANELS',
    manufacturer: 'Square D',
    model_number: 'QO142M200PC',
    unit: 'EA',
    current_price: 389.99,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    sku: 'QO120',
    name: 'Square D QO 20A Single-Pole Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Square D',
    model_number: 'QO120',
    unit: 'EA',
    current_price: 8.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    sku: 'QO220',
    name: 'Square D QO 20A Two-Pole Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Square D',
    model_number: 'QO220',
    unit: 'EA',
    current_price: 22.99,
    voltage_rating: 240,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    sku: 'QO120CAFI',
    name: 'Square D QO 20A Arc-Fault Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Square D',
    model_number: 'QO120CAFI',
    unit: 'EA',
    current_price: 44.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    sku: 'QO120GFCI',
    name: 'Square D QO 20A GFCI Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Square D',
    model_number: 'QO120GFCI',
    unit: 'EA',
    current_price: 89.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },

  // Eaton Panels and Breakers
  {
    sku: 'BR4040B200',
    name: 'Eaton BR 200A 40-Space Main Breaker Load Center',
    category: 'PANELS',
    manufacturer: 'Eaton',
    model_number: 'BR4040B200',
    unit: 'EA',
    current_price: 349.99,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    sku: 'BR120',
    name: 'Eaton BR 20A Single-Pole Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Eaton',
    model_number: 'BR120',
    unit: 'EA',
    current_price: 6.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    sku: 'BR120AF',
    name: 'Eaton BR 20A Arc-Fault Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Eaton',
    model_number: 'BR120AF',
    unit: 'EA',
    current_price: 39.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },

  // Leviton Devices
  {
    sku: '5320-W',
    name: 'Leviton 20A Duplex Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Leviton',
    model_number: '5320-W',
    unit: 'EA',
    current_price: 3.99,
    voltage_rating: 125,
    amperage_rating: 20
  },
  {
    sku: 'GFNT2-W',
    name: 'Leviton 20A GFCI Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Leviton',
    model_number: 'GFNT2-W',
    unit: 'EA',
    current_price: 19.99,
    voltage_rating: 125,
    amperage_rating: 20
  },
  {
    sku: '1451-2W',
    name: 'Leviton 15A Single-Pole Switch - White',
    category: 'DEVICES',
    manufacturer: 'Leviton',
    model_number: '1451-2W',
    unit: 'EA',
    current_price: 1.99,
    voltage_rating: 120,
    amperage_rating: 15
  },
  {
    sku: '6674-P0W',
    name: 'Leviton SureSlide Dimmer - White',
    category: 'DEVICES',
    manufacturer: 'Leviton',
    model_number: '6674-P0W',
    unit: 'EA',
    current_price: 12.99,
    voltage_rating: 120,
    amperage_rating: 5
  },
  {
    sku: 'T5632-W',
    name: 'Leviton USB Charger/Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Leviton',
    model_number: 'T5632-W',
    unit: 'EA',
    current_price: 24.99,
    voltage_rating: 125,
    amperage_rating: 15
  },

  // Southwire Wire
  {
    sku: '22955901',
    name: 'Southwire 12 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '22955901',
    unit: 'FT',
    current_price: 0.32,
    wire_size: '12 AWG',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'BLACK'
  },
  {
    sku: '22957101',
    name: 'Southwire 12 AWG THHN White Wire - 500ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '22957101',
    unit: 'FT',
    current_price: 0.32,
    wire_size: '12 AWG',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'WHITE'
  },
  {
    sku: '22959901',
    name: 'Southwire 12 AWG THHN Green Wire - 500ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '22959901',
    unit: 'FT',
    current_price: 0.32,
    wire_size: '12 AWG',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'GREEN'
  },
  {
    sku: '22966301',
    name: 'Southwire 10 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '22966301',
    unit: 'FT',
    current_price: 0.48,
    wire_size: '10 AWG',
    wire_type: 'THHN',
    voltage_rating: 600,
    color: 'BLACK'
  },
  {
    sku: '63950021',
    name: 'Southwire 12-2 NM-B Wire - 250ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '63950021',
    unit: 'FT',
    current_price: 0.89,
    wire_size: '12 AWG',
    wire_type: 'NM',
    voltage_rating: 600
  },
  {
    sku: '63948421',
    name: 'Southwire 14-2 NM-B Wire - 250ft',
    category: 'WIRE',
    manufacturer: 'Southwire',
    model_number: '63948421',
    unit: 'FT',
    current_price: 0.59,
    wire_size: '14 AWG',
    wire_type: 'NM',
    voltage_rating: 600
  },

  // Allied Tube & Conduit
  {
    sku: 'EMT-050-10',
    name: '1/2" EMT Conduit - 10ft',
    category: 'CONDUIT',
    manufacturer: 'Allied Tube & Conduit',
    model_number: '101543',
    unit: 'EA',
    current_price: 7.99,
    conduit_size: '1/2',
    conduit_type: 'EMT'
  },
  {
    sku: 'EMT-075-10',
    name: '3/4" EMT Conduit - 10ft',
    category: 'CONDUIT',
    manufacturer: 'Allied Tube & Conduit',
    model_number: '101544',
    unit: 'EA',
    current_price: 10.99,
    conduit_size: '3/4',
    conduit_type: 'EMT'
  },
  {
    sku: 'EMT-100-10',
    name: '1" EMT Conduit - 10ft',
    category: 'CONDUIT',
    manufacturer: 'Allied Tube & Conduit',
    model_number: '101545',
    unit: 'EA',
    current_price: 14.99,
    conduit_size: '1',
    conduit_type: 'EMT'
  },

  // Carlon PVC Products
  {
    sku: 'E943D',
    name: 'Carlon 1-Gang Old Work Box',
    category: 'BOXES',
    manufacturer: 'Carlon',
    model_number: 'E943D',
    unit: 'EA',
    current_price: 2.49
  },
  {
    sku: 'E943E',
    name: 'Carlon 2-Gang Old Work Box',
    category: 'BOXES',
    manufacturer: 'Carlon',
    model_number: 'E943E',
    unit: 'EA',
    current_price: 3.99
  },
  {
    sku: 'E940',
    name: 'Carlon Schedule 40 PVC Conduit 1/2" - 10ft',
    category: 'CONDUIT',
    manufacturer: 'Carlon',
    model_number: 'E940',
    unit: 'EA',
    current_price: 5.99,
    conduit_size: '1/2',
    conduit_type: 'PVC'
  },

  // Lutron Lighting Controls
  {
    sku: 'DVCL-153P-WH',
    name: 'Lutron Diva CL Dimmer - White',
    category: 'DEVICES',
    manufacturer: 'Lutron',
    model_number: 'DVCL-153P-WH',
    unit: 'EA',
    current_price: 19.99,
    voltage_rating: 120,
    amperage_rating: 5
  },
  {
    sku: 'MACL-153M-WH',
    name: 'Lutron Maestro CL Dimmer - White',
    category: 'DEVICES',
    manufacturer: 'Lutron',
    model_number: 'MACL-153M-WH',
    unit: 'EA',
    current_price: 24.99,
    voltage_rating: 120,
    amperage_rating: 5
  },

  // Pass & Seymour Devices
  {
    sku: '3232TRWR',
    name: 'Pass & Seymour 20A Weather Resistant GFCI',
    category: 'DEVICES',
    manufacturer: 'Pass & Seymour',
    model_number: '3232TRWR',
    unit: 'EA',
    current_price: 22.99,
    voltage_rating: 125,
    amperage_rating: 20
  },
  {
    sku: 'CR20W',
    name: 'Pass & Seymour 20A Commercial Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Pass & Seymour',
    model_number: 'CR20W',
    unit: 'EA',
    current_price: 4.99,
    voltage_rating: 125,
    amperage_rating: 20
  },

  // Klein Tools
  {
    sku: 'VDV226-110',
    name: 'Klein Tools Ratcheting Cable Crimper',
    category: 'TOOLS',
    manufacturer: 'Klein Tools',
    model_number: 'VDV226-110',
    unit: 'EA',
    current_price: 59.99
  },
  {
    sku: '11055',
    name: 'Klein Tools Wire Stripper/Cutter',
    category: 'TOOLS',
    manufacturer: 'Klein Tools',
    model_number: '11055',
    unit: 'EA',
    current_price: 24.99
  },

  // 3M Electrical Tape
  {
    sku: '88-SUPER',
    name: '3M Super 88 Electrical Tape',
    category: 'MISC',
    manufacturer: '3M',
    model_number: '88-SUPER',
    unit: 'ROLL',
    current_price: 7.99
  },
  {
    sku: '35-BLUE',
    name: '3M Scotch 35 Blue Electrical Tape',
    category: 'MISC',
    manufacturer: '3M',
    model_number: '35-BLUE',
    unit: 'ROLL',
    current_price: 4.99,
    color: 'BLUE'
  },

  // Ideal Wire Nuts
  {
    sku: '30-073',
    name: 'Ideal Wire-Nut 73B Orange - Jar of 100',
    category: 'MISC',
    manufacturer: 'Ideal',
    model_number: '30-073',
    unit: 'JAR',
    current_price: 12.99
  },
  {
    sku: '30-076',
    name: 'Ideal Wire-Nut 76B Gray - Jar of 50',
    category: 'MISC',
    manufacturer: 'Ideal',
    model_number: '30-076',
    unit: 'JAR',
    current_price: 14.99
  },

  // Arlington Industries
  {
    sku: 'NM94',
    name: 'Arlington 1/2" Non-Metallic Cable Connector',
    category: 'FITTINGS',
    manufacturer: 'Arlington Industries',
    model_number: 'NM94',
    unit: 'BAG',
    current_price: 8.99
  },
  {
    sku: 'NM95',
    name: 'Arlington 3/4" Non-Metallic Cable Connector',
    category: 'FITTINGS',
    manufacturer: 'Arlington Industries',
    model_number: 'NM95',
    unit: 'BAG',
    current_price: 10.99
  },

  // Siemens Breakers
  {
    sku: 'Q120',
    name: 'Siemens 20A Single-Pole Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Siemens',
    model_number: 'Q120',
    unit: 'EA',
    current_price: 5.99,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    sku: 'Q220',
    name: 'Siemens 20A Double-Pole Circuit Breaker',
    category: 'BREAKERS',
    manufacturer: 'Siemens',
    model_number: 'Q220',
    unit: 'EA',
    current_price: 19.99,
    voltage_rating: 240,
    amperage_rating: 20,
    phase: '1PH'
  },

  // Cooper Wiring Devices
  {
    sku: 'TR270W',
    name: 'Cooper 15A Tamper Resistant Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Cooper Wiring',
    model_number: 'TR270W',
    unit: 'EA',
    current_price: 2.49,
    voltage_rating: 125,
    amperage_rating: 15
  },
  {
    sku: 'TRVGF20W',
    name: 'Cooper 20A GFCI Receptacle - White',
    category: 'DEVICES',
    manufacturer: 'Cooper Wiring',
    model_number: 'TRVGF20W',
    unit: 'EA',
    current_price: 18.99,
    voltage_rating: 125,
    amperage_rating: 20
  },

  // Hubbell Devices
  {
    sku: 'HBL5362I',
    name: 'Hubbell 20A Hospital Grade Receptacle - Ivory',
    category: 'DEVICES',
    manufacturer: 'Hubbell',
    model_number: 'HBL5362I',
    unit: 'EA',
    current_price: 12.99,
    voltage_rating: 125,
    amperage_rating: 20
  },
  {
    sku: 'HBL2310',
    name: 'Hubbell 20A Twist-Lock Receptacle L5-20R',
    category: 'DEVICES',
    manufacturer: 'Hubbell',
    model_number: 'HBL2310',
    unit: 'EA',
    current_price: 29.99,
    voltage_rating: 125,
    amperage_rating: 20
  }
];

export async function seedMaterials(): Promise<void> {
  logger.info('🔌 Seeding electrical materials...');

  for (const material of materials) {
    try {
      await prisma.material.upsert({
        where: { sku: material.sku },
        update: {
          name: material.name,
          description: material.description,
          category: material.category,
          manufacturer: material.manufacturer,
          model_number: material.model_number,
          unit: material.unit,
          current_price: material.current_price,
          wire_size: material.wire_size,
          wire_type: material.wire_type,
          conduit_size: material.conduit_size,
          conduit_type: material.conduit_type,
          voltage_rating: material.voltage_rating,
          amperage_rating: material.amperage_rating,
          phase: material.phase,
          color: material.color,
          updated_at: new Date()
        },
        create: material as any
      });

      // Also create price history entry
      if (material.current_price) {
        await prisma.materialPriceHistory.create({
          data: {
            catalog_number: material.sku,
            supplier: material.manufacturer,
            unit_cost: material.current_price,
            effective_date: new Date()
          }
        });
      }

      logger.info(`✓ Seeded material: ${material.name}`);
    } catch (error) {
      logger.error({ message: `✗ Error seeding material ${material.sku}`, error });
    }
  }

  logger.info(`✓ Seeded ${materials.length} materials`);
}

// Add barcode mappings for common UPC/EAN codes
export const barcodeMappings: Record<string, string> = {
  // Square D
  '785901403234': 'QO120',
  '785901403241': 'QO220',
  '785901403258': 'QO120CAFI',
  '785901403265': 'QO120GFCI',
  
  // Leviton
  '078477814369': '5320-W',
  '078477814376': 'GFNT2-W',
  '078477814383': '1451-2W',
  '078477814390': '6674-P0W',
  '078477814406': 'T5632-W',
  
  // Southwire
  '737576229559': '22955901',
  '737576229573': '22957101',
  '737576229597': '22959901',
  '737576229665': '22966301',
  '737576639506': '63950021',
  
  // Eaton
  '786689270206': 'BR120',
  '786689270213': 'BR120AF',
  
  // Add more mappings as needed
};

// Function to look up material by barcode
export async function lookupMaterialByBarcode(barcode: string): Promise<any> {
  // First check if it's a direct SKU
  let material = await prisma.material.findUnique({
    where: { sku: barcode }
  });

  if (material) return material;

  // Check barcode mappings
  const sku = barcodeMappings[barcode];
  if (sku) {
    material = await prisma.material.findUnique({
      where: { sku }
    });
  }

  return material;
}