import React, { useState, useEffect } from 'react';
import {
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  Box,
  ScrollView,
  <PERSON>able,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Center,
} from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { RefreshControl } from 'react-native';
import { format } from 'date-fns';
import api from '@services/api';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@store/index';
import { showToast } from '@store/slices/uiSlice';
import DeviceInfo from 'react-native-device-info';

interface Session {
  id: string;
  deviceId: string;
  deviceName: string;
  platform: string;
  location?: string;
  ipAddress: string;
  lastActive: Date;
  createdAt: Date;
  isCurrent: boolean;
}

const SessionManagementScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentDeviceId, setCurrentDeviceId] = useState<string>('');

  useEffect(() => {
    getCurrentDeviceId();
    loadSessions();
  }, []);

  const getCurrentDeviceId = async () => {
    const deviceId = await DeviceInfo.getUniqueId();
    setCurrentDeviceId(deviceId);
  };

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<Session[]>('/auth/sessions');
      setSessions(response.data);
    } catch (error) {
      dispatch(showToast({
        message: 'Failed to load sessions',
        type: 'error',
      }));
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    loadSessions();
  };

  const terminateSession = async (sessionId: string) => {
    try {
      await api.delete(`/auth/sessions/${sessionId}`);
      setSessions(sessions.filter(s => s.id !== sessionId));
      dispatch(showToast({
        message: 'Session terminated successfully',
        type: 'success',
      }));
    } catch (error) {
      dispatch(showToast({
        message: 'Failed to terminate session',
        type: 'error',
      }));
    }
  };

  const terminateAllOtherSessions = async () => {
    try {
      await api.post('/auth/sessions/terminate-others');
      loadSessions();
      dispatch(showToast({
        message: 'All other sessions terminated',
        type: 'success',
      }));
    } catch (error) {
      dispatch(showToast({
        message: 'Failed to terminate sessions',
        type: 'error',
      }));
    }
  };

  const getDeviceIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'ios':
        return 'phone-iphone';
      case 'android':
        return 'phone-android';
      case 'web':
        return 'computer';
      default:
        return 'devices';
    }
  };

  const renderSession = (session: Session) => {
    const isCurrentDevice = session.deviceId === currentDeviceId || session.isCurrent;
    
    return (
      <Box
        key={session.id}
        bg="white"
        borderRadius="lg"
        shadow={1}
        p={4}
        mb={3}
      >
        <HStack justifyContent="space-between" alignItems="flex-start">
          <HStack space={3} flex={1}>
            <Icon
              as={MaterialIcons}
              name={getDeviceIcon(session.platform)}
              size={8}
              color={isCurrentDevice ? 'primary.600' : 'gray.600'}
            />
            <VStack flex={1} space={1}>
              <HStack alignItems="center" space={2}>
                <Text fontSize="md" fontWeight="medium">
                  {session.deviceName}
                </Text>
                {isCurrentDevice && (
                  <Badge colorScheme="success" variant="subtle">
                    This Device
                  </Badge>
                )}
              </HStack>
              <Text fontSize="sm" color="gray.600">
                {session.platform} • {session.ipAddress}
              </Text>
              {session.location && (
                <Text fontSize="sm" color="gray.600">
                  {session.location}
                </Text>
              )}
              <Text fontSize="xs" color="gray.500">
                Last active: {format(new Date(session.lastActive), 'MMM d, yyyy h:mm a')}
              </Text>
              <Text fontSize="xs" color="gray.500">
                Logged in: {format(new Date(session.createdAt), 'MMM d, yyyy')}
              </Text>
            </VStack>
          </HStack>
          
          {!isCurrentDevice && (
            <Button
              size="sm"
              variant="ghost"
              colorScheme="danger"
              onPress={() => terminateSession(session.id)}
              leftIcon={<Icon as={MaterialIcons} name="logout" size={4} />}
            >
              End
            </Button>
          )}
        </HStack>
      </Box>
    );
  };

  if (isLoading) {
    return (
      <Center flex={1} bg="gray.50">
        <Spinner size="lg" />
        <Text mt={4} color="gray.600">Loading sessions...</Text>
      </Center>
    );
  }

  const currentSession = sessions.find(s => s.deviceId === currentDeviceId || s.isCurrent);
  const otherSessions = sessions.filter(s => s.deviceId !== currentDeviceId && !s.isCurrent);

  return (
    <ScrollView
      bg="gray.50"
      flex={1}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <VStack space={4} p={4}>
        {/* Security Alert */}
        <Alert status="info">
          <VStack space={2} flexShrink={1} w="100%">
            <HStack alignItems="center" space={2}>
              <Alert.Icon />
              <Text fontSize="md" fontWeight="medium">
                Active Sessions
              </Text>
            </HStack>
            <Text fontSize="sm" color="gray.600">
              These devices are currently logged into your account. If you see an unfamiliar device, terminate its session immediately.
            </Text>
          </VStack>
        </Alert>

        {/* Current Device */}
        {currentSession && (
          <VStack space={2}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              Current Device
            </Text>
            {renderSession(currentSession)}
          </VStack>
        )}

        {/* Other Sessions */}
        {otherSessions.length > 0 && (
          <VStack space={2}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="lg" fontWeight="bold" color="gray.800">
                Other Sessions ({otherSessions.length})
              </Text>
              <Button
                size="sm"
                variant="outline"
                colorScheme="danger"
                onPress={terminateAllOtherSessions}
              >
                End All
              </Button>
            </HStack>
            {otherSessions.map(renderSession)}
          </VStack>
        )}

        {/* No Other Sessions */}
        {otherSessions.length === 0 && (
          <Box bg="white" borderRadius="lg" shadow={1} p={6}>
            <VStack space={2} alignItems="center">
              <Icon
                as={MaterialIcons}
                name="security"
                size={12}
                color="success.600"
              />
              <Text fontSize="md" fontWeight="medium" textAlign="center">
                No other active sessions
              </Text>
              <Text fontSize="sm" color="gray.600" textAlign="center">
                Your account is only logged in on this device
              </Text>
            </VStack>
          </Box>
        )}

        {/* Security Tips */}
        <Box bg="blue.50" borderRadius="lg" p={4} mt={4}>
          <VStack space={2}>
            <HStack alignItems="center" space={2}>
              <Icon as={MaterialIcons} name="lightbulb" size={5} color="blue.600" />
              <Text fontSize="md" fontWeight="medium" color="blue.800">
                Security Tips
              </Text>
            </HStack>
            <VStack space={1} ml={7}>
              <Text fontSize="sm" color="blue.700">
                • Always log out from shared devices
              </Text>
              <Text fontSize="sm" color="blue.700">
                • Review active sessions regularly
              </Text>
              <Text fontSize="sm" color="blue.700">
                • Enable two-factor authentication
              </Text>
              <Text fontSize="sm" color="blue.700">
                • Use strong, unique passwords
              </Text>
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </ScrollView>
  );
};

export default SessionManagementScreen;