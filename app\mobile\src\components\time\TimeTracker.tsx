import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  AppState,
  TextInput,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BackgroundTimer from 'react-native-background-timer';
import { TimeEntry } from '../../types/electrical';
import { TASK_TYPES } from '../../constants/electrical';

interface Props {
  projectId: string;
  userId: string;
  onTimeEntrySaved?: (entry: TimeEntry) => void;
}

export const TimeTracker: React.FC<Props> = ({ projectId, userId, onTimeEntrySaved }) => {
  const [isTracking, setIsTracking] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<TimeEntry | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [taskType, setTaskType] = useState<TimeEntry['taskType']>('installation');
  const [description, setDescription] = useState('');
  const [todayEntries, setTodayEntries] = useState<TimeEntry[]>([]);
  
  const appState = useRef(AppState.currentState);
  const timerRef = useRef<number | null>(null);

  useEffect(() => {
    loadTodayEntries();
    checkActiveTimer();

    // Handle app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      subscription.remove();
      if (timerRef.current) {
        BackgroundTimer.clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleAppStateChange = (nextAppState: any) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // App came to foreground
      checkActiveTimer();
    }
    appState.current = nextAppState;
  };

  const checkActiveTimer = async () => {
    const activeTimer = await AsyncStorage.getItem('active_time_entry');
    if (activeTimer) {
      const entry = JSON.parse(activeTimer);
      setCurrentEntry(entry);
      setIsTracking(true);
      
      // Calculate elapsed time
      const startTime = new Date(entry.startTime).getTime();
      const elapsed = Math.floor((Date.now() - startTime) / 1000);
      setElapsedTime(elapsed);
      
      startTimer();
    }
  };

  const loadTodayEntries = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const entriesKey = `time_entries_${projectId}_${today}`;
      const entries = await AsyncStorage.getItem(entriesKey);
      if (entries) {
        setTodayEntries(JSON.parse(entries));
      }
    } catch (error) {
      console.error('Error loading entries:', error);
    }
  };

  const startTimer = () => {
    if (timerRef.current) {
      BackgroundTimer.clearInterval(timerRef.current);
    }

    timerRef.current = BackgroundTimer.setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
  };

  const handleStartStop = async () => {
    if (isTracking) {
      // Stop tracking
      Alert.alert(
        'Stop Timer',
        'Are you sure you want to stop tracking time?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Stop', onPress: stopTracking },
        ]
      );
    } else {
      // Start tracking
      if (!description.trim()) {
        Alert.alert('Error', 'Please enter a task description');
        return;
      }

      const entry: TimeEntry = {
        id: Date.now().toString(),
        projectId,
        userId,
        startTime: new Date(),
        taskType,
        description: description.trim(),
        approved: false,
      };

      setCurrentEntry(entry);
      setIsTracking(true);
      setElapsedTime(0);
      
      // Save to AsyncStorage for persistence
      await AsyncStorage.setItem('active_time_entry', JSON.stringify(entry));
      
      startTimer();
    }
  };

  const stopTracking = async () => {
    if (!currentEntry || timerRef.current === null) return;

    BackgroundTimer.clearInterval(timerRef.current);
    timerRef.current = null;

    const endTime = new Date();
    const duration = Math.floor((endTime.getTime() - new Date(currentEntry.startTime).getTime()) / 1000);

    const completedEntry: TimeEntry = {
      ...currentEntry,
      endTime,
      duration,
    };

    // Save completed entry
    await saveTimeEntry(completedEntry);

    // Clear active timer
    await AsyncStorage.removeItem('active_time_entry');

    // Reset state
    setIsTracking(false);
    setCurrentEntry(null);
    setElapsedTime(0);
    setDescription('');

    // Reload entries
    loadTodayEntries();

    if (onTimeEntrySaved) {
      onTimeEntrySaved(completedEntry);
    }

    Alert.alert('Success', 'Time entry saved successfully');
  };

  const saveTimeEntry = async (entry: TimeEntry) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const entriesKey = `time_entries_${projectId}_${today}`;
      
      const existingEntries = await AsyncStorage.getItem(entriesKey);
      const entries = existingEntries ? JSON.parse(existingEntries) : [];
      
      entries.push(entry);
      await AsyncStorage.setItem(entriesKey, JSON.stringify(entries));

      // Queue for sync with server
      const queueKey = 'time_entries_sync_queue';
      const queue = await AsyncStorage.getItem(queueKey);
      const queuedEntries = queue ? JSON.parse(queue) : [];
      queuedEntries.push(entry);
      await AsyncStorage.setItem(queueKey, JSON.stringify(queuedEntries));
    } catch (error) {
      console.error('Error saving time entry:', error);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalHoursToday = (): number => {
    return todayEntries.reduce((total, entry) => total + (entry.duration || 0), 0) / 3600;
  };

  const deleteEntry = async (entryId: string) => {
    Alert.alert(
      'Delete Entry',
      'Are you sure you want to delete this time entry?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const filtered = todayEntries.filter(e => e.id !== entryId);
            setTodayEntries(filtered);
            
            const today = new Date().toISOString().split('T')[0];
            const entriesKey = `time_entries_${projectId}_${today}`;
            await AsyncStorage.setItem(entriesKey, JSON.stringify(filtered));
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.timerSection}>
        <Text style={styles.timerDisplay}>{formatTime(elapsedTime)}</Text>
        
        {!isTracking && (
          <>
            <View style={styles.inputSection}>
              <Text style={styles.label}>Task Type</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={taskType}
                  onValueChange={setTaskType}
                  style={styles.picker}
                >
                  {TASK_TYPES.map(type => (
                    <Picker.Item key={type.value} label={type.label} value={type.value} />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputSection}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={styles.input}
                value={description}
                onChangeText={setDescription}
                placeholder="What are you working on?"
                multiline
              />
            </View>
          </>
        )}

        {isTracking && currentEntry && (
          <View style={styles.activeTaskInfo}>
            <Text style={styles.activeTaskType}>
              {TASK_TYPES.find(t => t.value === currentEntry.taskType)?.label}
            </Text>
            <Text style={styles.activeTaskDescription}>{currentEntry.description}</Text>
          </View>
        )}

        <TouchableOpacity
          style={[styles.timerButton, isTracking && styles.stopButton]}
          onPress={handleStartStop}
        >
          <Text style={styles.timerButtonText}>
            {isTracking ? 'Stop Timer' : 'Start Timer'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.summarySection}>
        <Text style={styles.sectionTitle}>Today's Summary</Text>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Total Hours</Text>
          <Text style={styles.summaryValue}>{getTotalHoursToday().toFixed(2)} hrs</Text>
        </View>
      </View>

      <View style={styles.entriesSection}>
        <Text style={styles.sectionTitle}>Today's Entries</Text>
        
        {todayEntries.length === 0 ? (
          <Text style={styles.noEntriesText}>No time entries for today</Text>
        ) : (
          todayEntries.map(entry => (
            <View key={entry.id} style={styles.entryCard}>
              <View style={styles.entryHeader}>
                <Text style={styles.entryType}>
                  {TASK_TYPES.find(t => t.value === entry.taskType)?.label}
                </Text>
                <Text style={styles.entryDuration}>
                  {entry.duration ? formatTime(entry.duration) : 'In Progress'}
                </Text>
              </View>
              
              <Text style={styles.entryDescription}>{entry.description}</Text>
              
              <View style={styles.entryFooter}>
                <Text style={styles.entryTime}>
                  {new Date(entry.startTime).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                  {entry.endTime && ` - ${new Date(entry.endTime).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}`}
                </Text>
                
                <TouchableOpacity onPress={() => deleteEntry(entry.id)}>
                  <Text style={styles.deleteText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  timerSection: {
    backgroundColor: 'white',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  timerDisplay: {
    fontSize: 48,
    fontWeight: '200',
    color: '#333',
    marginBottom: 20,
  },
  inputSection: {
    width: '100%',
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    minHeight: 60,
    textAlignVertical: 'top',
  },
  activeTaskInfo: {
    width: '100%',
    padding: 15,
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    marginBottom: 20,
  },
  activeTaskType: {
    fontSize: 14,
    color: '#1976D2',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  activeTaskDescription: {
    fontSize: 16,
    color: '#333',
  },
  timerButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginTop: 10,
  },
  stopButton: {
    backgroundColor: '#f44336',
  },
  timerButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  summarySection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  summaryCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  summaryValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  entriesSection: {
    padding: 20,
    paddingTop: 0,
  },
  noEntriesText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
  entryCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  entryType: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  entryDuration: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  entryDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  entryTime: {
    fontSize: 12,
    color: '#999',
  },
  deleteText: {
    fontSize: 14,
    color: '#f44336',
  },
});