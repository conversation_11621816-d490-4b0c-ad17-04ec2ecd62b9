# Frequently Asked Questions (FAQ)

## General Questions

### What is the Electrical Contracting Application?
It's a comprehensive software solution designed specifically for electrical contractors to manage projects, perform NEC-compliant calculations, track materials, handle inspections, and run their business more efficiently.

### Who is this application for?
- Electrical contractors (residential, commercial, industrial)
- Master electricians and journeymen
- Electrical estimators
- Project managers
- Electrical inspectors
- Apprentices and students

### What makes this different from general construction software?
Our application is built specifically for electrical work with:
- NEC-compliant calculators
- Electrical-specific materials database
- Panel schedule creation
- Arc flash and short circuit analysis
- Wire pulling calculations
- Electrical code references

### Is this available in my area?
Yes! The application works anywhere. While calculations default to NEC standards, you can configure local amendments and requirements in the settings.

## Account & Billing

### How do I create an account?
1. Visit app.electricalcontractor.com
2. Click "Sign Up"
3. Enter your information
4. Verify your email
5. Complete setup

### What are the pricing plans?
- **Starter**: $49/month - 1 user, basic features
- **Professional**: $99/month - 5 users, all features
- **Enterprise**: $199/month - Unlimited users, API access
- **Custom**: Contact sales for large organizations

### Can I try it before buying?
Yes! We offer a 30-day free trial with full access to all features. No credit card required to start.

### How do I add more users?
1. Go to Settings → Team
2. Click "Invite User"
3. Enter email address
4. Assign role and permissions
5. They'll receive an invitation email

### Can I change plans?
Yes, you can upgrade or downgrade anytime. Changes take effect at the next billing cycle. Downgrades may limit some features.

### What payment methods do you accept?
- Credit cards (Visa, MasterCard, Amex)
- ACH/bank transfer (for annual plans)
- Purchase orders (Enterprise only)
- Checks (contact accounting)

## Features & Functionality

### Can I use this offline?
Yes! The mobile app has full offline functionality. Select projects to download, and all features work without internet. Data syncs when reconnected.

### Which calculations are included?
- Wire size calculator
- Voltage drop calculator
- Conduit fill calculator
- Load calculations (residential & commercial)
- Motor calculations
- Short circuit analysis
- Arc flash analysis
- Grounding calculations
- Power factor correction
- Transformer calculations

### Can I create custom forms?
Yes, Professional and Enterprise plans include a form builder for creating custom inspection forms, checklists, and reports.

### Does it integrate with other software?
Yes, we integrate with:
- QuickBooks (accounting)
- Procore (construction management)
- Bluebeam (PDF markup)
- Microsoft 365 (documents)
- Google Workspace (documents)
- Various supplier catalogs

### Can I import existing data?
Yes, we support importing:
- Customer lists (CSV, Excel)
- Material databases (CSV, Excel)
- Project data (various formats)
- Contact our support team for help

### How do I generate reports?
1. Navigate to Reports section
2. Select report type
3. Choose filters/date range
4. Click "Generate"
5. Export as PDF, Excel, or email

## Technical Questions

### What browsers are supported?
- Chrome 90+ (recommended)
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile: Safari (iOS), Chrome (Android)

### What are the system requirements?
**Web Application:**
- Any modern browser
- 2GB RAM minimum
- Stable internet connection

**Mobile Application:**
- iOS 13+ or Android 8+
- 500MB storage
- Camera for scanning

### Is my data secure?
Yes, we implement:
- 256-bit SSL encryption
- SOC 2 Type II compliance
- Daily automated backups
- Role-based access control
- Two-factor authentication
- GDPR compliance

### Where is data stored?
Data is stored in secure AWS data centers with:
- Geographic redundancy
- 99.9% uptime SLA
- Automatic backups
- Disaster recovery

### Can I export my data?
Yes, you own your data and can export it anytime:
- Full database export (JSON/CSV)
- Individual project exports
- Report generation
- API access (Enterprise)

## Mobile App Questions

### Is the mobile app free?
The mobile app is free to download but requires an active subscription to use.

### What features work offline?
- All calculators
- Viewing downloaded projects
- Taking photos
- Time tracking
- Creating notes/inspections
- Barcode scanning
- Reference materials

### How much storage does it use?
- Base app: ~100MB
- Each project: ~5-50MB (depending on photos)
- Offline data: User configurable
- Photos: Compressed automatically

### Can multiple people use the same device?
Yes, the app supports multiple user profiles with quick switching and separate data storage.

## Calculation Questions

### Are calculations NEC compliant?
Yes, all calculations follow the latest NEC standards (currently 2023). You can select older code years if needed for existing installations.

### Can I save calculation results?
Yes, all calculations can be:
- Saved to projects
- Exported as PDF
- Emailed directly
- Printed for records

### Do calculations include local amendments?
You can configure local amendments in Settings → Code Requirements. Common amendments are pre-loaded for major jurisdictions.

### How accurate are the calculations?
Calculations use industry-standard formulas and NEC tables. Results include safety margins. Always verify critical calculations and use professional judgment.

## Support Questions

### How do I get help?
- In-app help system
- Email: <EMAIL>
- Phone: 1-800-ELECTRICAL
- Live chat (business hours)
- Community forum
- Video tutorials

### What are support hours?
- Email: 24/7 (response within 24 hours)
- Phone: Mon-Fri 7AM-7PM EST
- Live chat: Mon-Fri 8AM-6PM EST
- Emergency: 24/7 for critical issues

### Is training available?
Yes, we offer:
- Free onboarding session
- Weekly webinars
- On-demand video tutorials
- On-site training (Enterprise)
- Certification program

### How do I report a bug?
1. Click the feedback button in-app
2. Describe the issue
3. Include screenshots if possible
4. Submit the report
5. Track status in support portal

## Data & Backup Questions

### How often is data backed up?
- Real-time sync to cloud
- Hourly incremental backups
- Daily full backups
- 30-day retention
- Point-in-time recovery

### Can I backup locally?
Yes, you can:
- Export full backups
- Save to local storage
- Automated local backups (mobile)
- Sync to company servers (Enterprise)

### What happens if I cancel?
- Data retained for 90 days
- Full export available
- Read-only access for 30 days
- Permanent deletion after 90 days

## Compliance Questions

### Does this meet inspection requirements?
Yes, reports are designed to meet standard inspection requirements. Many inspectors prefer our digital reports for clarity and completeness.

### Can inspectors access my projects?
You can:
- Share read-only links
- Generate inspection packets
- Email reports directly
- Provide QR codes for access

### Are permits tracked?
Yes, the system tracks:
- Permit applications
- Approval status
- Inspection schedules
- Permit documents
- Expiration dates

## Advanced Features

### What is the AI Assistant?
An intelligent helper that can:
- Answer code questions
- Suggest solutions
- Review calculations
- Generate reports
- Troubleshoot issues

### How do panel schedules work?
- Visual panel builder
- Drag-and-drop circuits
- Automatic load balancing
- Code compliance checking
- Professional printouts

### Can I track inventory?
Yes, with features for:
- Barcode scanning
- Min/max levels
- Automatic reordering
- Multiple warehouses
- Usage tracking

### Does it handle prevailing wage?
Yes, Enterprise plans include:
- Wage rate tables
- Certified payroll reports
- Davis-Bacon compliance
- Union rate tracking

## Troubleshooting FAQs

### Why can't I log in?
1. Check caps lock
2. Verify email address
3. Try password reset
4. Clear browser cache
5. Contact support

### Why is it running slowly?
1. Check internet speed
2. Clear cache
3. Close other tabs
4. Update browser
5. Archive old data

### Why won't photos upload?
1. Check file size (<10MB)
2. Verify internet connection
3. Check storage space
4. Try WiFi instead of cellular

### Why don't calculations match my manual math?
1. Verify all inputs
2. Check code year setting
3. Include all correction factors
4. Review assumptions
5. Contact support with details

Remember: If you can't find an answer here, our support team is always ready to help!