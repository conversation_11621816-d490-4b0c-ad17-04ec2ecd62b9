import React, { useState, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Icon,
  Fab,
  Input,
  Select,
  CheckIcon,
  Pressable,
  Badge,
  FlatList,
} from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { RefreshControl } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import { ProjectStackScreenProps } from '@types/navigation';
import { AppDispatch, RootState } from '@store/index';
import { fetchProjects, setFilters, setSearchQuery } from '@store/slices/projectSlice';
import { Project } from '@types/project';

type Props = ProjectStackScreenProps<'ProjectList'>;

const ProjectListScreen: React.FC<Props> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { projects, isLoading, searchQuery, filters } = useSelector(
    (state: RootState) => state.projects
  );
  const [refreshing, setRefreshing] = useState(false);

  const loadProjects = useCallback(async () => {
    await dispatch(fetchProjects({ search: searchQuery })).unwrap();
  }, [dispatch, searchQuery]);

  useFocusEffect(
    useCallback(() => {
      loadProjects();
    }, [loadProjects])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProjects();
    setRefreshing(false);
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'planning':
        return 'info';
      case 'in_progress':
        return 'warning';
      case 'completed':
        return 'success';
      case 'on_hold':
        return 'danger';
      default:
        return 'gray';
    }
  };

  const getStatusLabel = (status: Project['status']) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const filteredProjects = projects.filter(project => {
    if (filters.status !== 'all' && project.status !== filters.status) {
      return false;
    }
    return true;
  });

  const renderProjectItem = ({ item }: { item: Project }) => (
    <Pressable
      onPress={() => navigation.navigate('ProjectDetails', { projectId: item.id })}
      mb={3}
    >
      <Box bg="white" rounded="lg" shadow={2} p={4}>
        <VStack space={3}>
          <HStack justifyContent="space-between" alignItems="flex-start">
            <VStack flex={1} mr={2}>
              <Heading size="sm" numberOfLines={1}>
                {item.name}
              </Heading>
              <Text fontSize="sm" color="gray.600" numberOfLines={1}>
                {item.clientName}
              </Text>
            </VStack>
            <Badge colorScheme={getStatusColor(item.status)} rounded="full">
              {getStatusLabel(item.status)}
            </Badge>
          </HStack>

          <HStack space={4}>
            <HStack space={1} alignItems="center">
              <Icon as={MaterialIcons} name="location-on" size={4} color="gray.500" />
              <Text fontSize="xs" color="gray.600" numberOfLines={1} flex={1}>
                {item.address}
              </Text>
            </HStack>
          </HStack>

          <HStack justifyContent="space-between" alignItems="center">
            <HStack space={4}>
              <VStack alignItems="center">
                <Text fontSize="xs" color="gray.500">
                  Progress
                </Text>
                <Text fontSize="sm" fontWeight="medium">
                  {item.progress}%
                </Text>
              </VStack>
              <VStack alignItems="center">
                <Text fontSize="xs" color="gray.500">
                  Budget
                </Text>
                <Text fontSize="sm" fontWeight="medium">
                  ${item.estimatedCost.toLocaleString()}
                </Text>
              </VStack>
            </HStack>
            <Icon as={MaterialIcons} name="chevron-right" size={5} color="gray.400" />
          </HStack>
        </VStack>
      </Box>
    </Pressable>
  );

  return (
    <Box flex={1} bg="gray.50" safeArea>
      <VStack flex={1}>
        {/* Search and Filter */}
        <Box bg="white" px={4} py={3} shadow={1}>
          <VStack space={3}>
            <Input
              placeholder="Search projects..."
              value={searchQuery}
              onChangeText={text => dispatch(setSearchQuery(text))}
              InputLeftElement={
                <Icon as={MaterialIcons} name="search" size={5} ml={2} color="gray.400" />
              }
            />
            <HStack space={3}>
              <Select
                flex={1}
                selectedValue={filters.status}
                placeholder="Status"
                onValueChange={value => dispatch(setFilters({ status: value as any }))}
                _selectedItem={{
                  bg: 'primary.100',
                  endIcon: <CheckIcon size={4} />,
                }}
              >
                <Select.Item label="All Status" value="all" />
                <Select.Item label="Planning" value="planning" />
                <Select.Item label="In Progress" value="in_progress" />
                <Select.Item label="Completed" value="completed" />
                <Select.Item label="On Hold" value="on_hold" />
              </Select>
            </HStack>
          </VStack>
        </Box>

        {/* Projects List */}
        <FlatList
          data={filteredProjects}
          renderItem={renderProjectItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{ padding: 16 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={
            <Center py={10}>
              <Icon as={MaterialIcons} name="work-off" size={16} color="gray.300" />
              <Text color="gray.500" mt={2}>
                No projects found
              </Text>
            </Center>
          }
        />
      </VStack>

      {/* FAB */}
      <Fab
        renderInPortal={false}
        shadow={2}
        size="sm"
        icon={<Icon as={MaterialIcons} name="add" size={6} color="white" />}
        onPress={() => navigation.navigate('ProjectCreate')}
      />
    </Box>
  );
};

export default ProjectListScreen;