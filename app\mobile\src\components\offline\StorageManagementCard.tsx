import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StorageManager } from '@offline/storage/StorageManager';
import { StorageStats } from '@offline/sync/types';

interface StorageManagementCardProps {
  onExport?: () => void;
  onImport?: () => void;
}

export const StorageManagementCard: React.FC<StorageManagementCardProps> = ({
  onExport,
  onImport,
}) => {
  const [stats, setStats] = useState<StorageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [cleaning, setCleaning] = useState(false);

  const storageManager = StorageManager.getInstance();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    setLoading(true);
    try {
      const storageStats = await storageManager.getStorageStats();
      setStats(storageStats);
    } catch (error) {
      console.error('Failed to load storage stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCleanup = async () => {
    Alert.alert(
      'Clean Up Storage',
      'This will remove old cache files and temporary data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clean Up',
          style: 'destructive',
          onPress: async () => {
            setCleaning(true);
            try {
              const freedSpace = await storageManager.cleanupCache();
              Alert.alert(
                'Cleanup Complete',
                `Freed ${formatBytes(freedSpace)} of storage space.`
              );
              await loadStats();
            } catch (error) {
              Alert.alert('Error', 'Failed to clean up storage');
            } finally {
              setCleaning(false);
            }
          },
        },
      ]
    );
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getUsagePercentage = (): number => {
    if (!stats) return 0;
    return (stats.usedSpace / stats.totalSpace) * 100;
  };

  const getUsageColor = (): string => {
    const percentage = getUsagePercentage();
    if (percentage > 90) return '#F44336';
    if (percentage > 75) return '#FF9800';
    return '#4CAF50';
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  if (!stats) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Failed to load storage information</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Icon name="storage" size={24} color="#333" />
        <Text style={styles.title}>Storage Management</Text>
      </View>

      <View style={styles.storageBar}>
        <View 
          style={[
            styles.storageUsed,
            { 
              width: `${getUsagePercentage()}%`,
              backgroundColor: getUsageColor(),
            },
          ]} 
        />
      </View>

      <Text style={styles.storageText}>
        {formatBytes(stats.usedSpace)} of {formatBytes(stats.totalSpace)} used
        ({getUsagePercentage().toFixed(1)}%)
      </Text>

      <View style={styles.breakdown}>
        <View style={styles.breakdownItem}>
          <Icon name="image" size={20} color="#666" />
          <Text style={styles.breakdownLabel}>Photos</Text>
          <Text style={styles.breakdownValue}>{formatBytes(stats.photosSize)}</Text>
        </View>
        <View style={styles.breakdownItem}>
          <Icon name="storage" size={20} color="#666" />
          <Text style={styles.breakdownLabel}>Database</Text>
          <Text style={styles.breakdownValue}>{formatBytes(stats.databaseSize)}</Text>
        </View>
        <View style={styles.breakdownItem}>
          <Icon name="cached" size={20} color="#666" />
          <Text style={styles.breakdownLabel}>Cache</Text>
          <Text style={styles.breakdownValue}>{formatBytes(stats.cacheSize)}</Text>
        </View>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity
          style={[styles.button, styles.cleanupButton]}
          onPress={handleCleanup}
          disabled={cleaning}
        >
          {cleaning ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Icon name="cleaning-services" size={20} color="white" />
              <Text style={styles.buttonText}>Clean Up</Text>
            </>
          )}
        </TouchableOpacity>

        {onExport && (
          <TouchableOpacity
            style={[styles.button, styles.exportButton]}
            onPress={onExport}
          >
            <Icon name="file-upload" size={20} color="white" />
            <Text style={styles.buttonText}>Export</Text>
          </TouchableOpacity>
        )}

        {onImport && (
          <TouchableOpacity
            style={[styles.button, styles.importButton]}
            onPress={onImport}
          >
            <Icon name="file-download" size={20} color="white" />
            <Text style={styles.buttonText}>Import</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  storageBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  storageUsed: {
    height: '100%',
    borderRadius: 4,
  },
  storageText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  breakdown: {
    marginBottom: 20,
  },
  breakdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  breakdownLabel: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  breakdownValue: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  cleanupButton: {
    backgroundColor: '#FF6B6B',
  },
  exportButton: {
    backgroundColor: '#4CAF50',
  },
  importButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 14,
  },
});