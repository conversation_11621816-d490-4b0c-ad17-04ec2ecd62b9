import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Create a test admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password_hash: hashedPassword
      },
      create: {
        email: '<EMAIL>',
        password_hash: hashedPassword,
        name: 'Test Admin',
        role: 'admin'
      }
    });
    
    console.log('Created/updated test user:', user.email);
    
    // Also create a test customer and project if they don't exist
    const customer = await prisma.customer.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '5551234567',
        address: '123 Test St',
        city: 'Test City',
        state: 'TX',
        zip: '12345'
      }
    });
    
    console.log('Created/updated test customer:', customer.name);
    
    const project = await prisma.project.create({
      data: {
        customer_id: customer.id,
        name: 'Test Electrical Project',
        address: '123 Test Street',
        city: 'Test City',
        state: 'TX',
        zip: '12345',
        type: 'COMMERCIAL',
        status: 'PLANNING',
        voltage_system: '208Y/120V',
        service_size: 400,
        square_footage: 5000
      }
    });
    
    console.log('Created test project:', project.name);
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();