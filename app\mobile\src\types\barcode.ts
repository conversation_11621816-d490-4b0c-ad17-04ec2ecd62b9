// Barcode Scanner Types

export interface BarcodeResult {
  type: 'upc' | 'ean' | 'qr' | 'code128' | 'code39' | 'datamatrix' | 'aztec' | 'pdf417';
  value: string;
  format: string;
  rawBytes?: number[];
}

export interface ScanSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  scansCount: number;
  mode: 'single' | 'batch' | 'continuous';
  scans: ScannedItem[];
}

export interface ScannedItem {
  id: string;
  barcode: string;
  type: BarcodeResult['type'];
  timestamp: Date;
  material?: MaterialInfo;
  location?: string;
  quantity: number;
  notes?: string;
  photoUri?: string;
}

export interface MaterialInfo {
  id: string;
  sku: string;
  upc?: string;
  ean?: string;
  name: string;
  description: string;
  category: string;
  manufacturer: string;
  manufacturerPartNumber?: string;
  unit: string;
  price: number;
  inStock: boolean;
  stockLevel?: number;
  imageUrl?: string;
  specifications?: Record<string, any>;
  alternatives?: AlternativeMaterial[];
  bundleItems?: BundleItem[];
}

export interface AlternativeMaterial {
  id: string;
  sku: string;
  name: string;
  manufacturer: string;
  price: number;
  availability: 'in-stock' | 'limited' | 'out-of-stock';
  reason: string; // Why it's an alternative
}

export interface BundleItem {
  materialId: string;
  name: string;
  quantity: number;
  unit: string;
}

export interface InventoryLocation {
  id: string;
  name: string;
  type: 'truck' | 'warehouse' | 'job-site' | 'shop';
  address?: string;
  manager?: string;
  phone?: string;
}

export interface InventoryTransaction {
  id: string;
  type: 'add' | 'remove' | 'transfer' | 'adjust';
  materialId: string;
  quantity: number;
  fromLocation?: string;
  toLocation?: string;
  reason?: string;
  performedBy: string;
  timestamp: Date;
  scanSessionId?: string;
}

export interface MaterialCatalog {
  id: string;
  version: string;
  lastUpdated: Date;
  totalItems: number;
  categories: CategoryInfo[];
  manufacturers: ManufacturerInfo[];
}

export interface CategoryInfo {
  code: string;
  name: string;
  itemCount: number;
  subcategories?: CategoryInfo[];
}

export interface ManufacturerInfo {
  id: string;
  name: string;
  code: string;
  website?: string;
  itemCount: number;
}

export interface PriceLookupResult {
  materialId: string;
  prices: SupplierPrice[];
  lastUpdated: Date;
  averagePrice: number;
  lowestPrice: number;
  highestPrice: number;
}

export interface SupplierPrice {
  supplierId: string;
  supplierName: string;
  price: number;
  availability: 'in-stock' | 'limited' | 'out-of-stock' | 'special-order';
  leadTime?: string;
  minimumOrder?: number;
  breakPrices?: BreakPrice[];
}

export interface BreakPrice {
  quantity: number;
  price: number;
}

export interface ScanAnalytics {
  totalScans: number;
  uniqueMaterials: number;
  topScannedMaterials: MaterialScanCount[];
  scansByCategory: Record<string, number>;
  scansByLocation: Record<string, number>;
  scansByUser: Record<string, number>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

export interface MaterialScanCount {
  materialId: string;
  name: string;
  sku: string;
  scanCount: number;
  lastScanned: Date;
}

export interface QRCodeData {
  type: 'material' | 'job' | 'location' | 'bundle' | 'custom';
  version: string;
  data: any;
  created: Date;
  createdBy: string;
}

export interface CustomQRCode {
  id: string;
  type: QRCodeData['type'];
  label: string;
  data: any;
  projectId?: string;
  created: Date;
  createdBy: string;
  printCount: number;
  lastPrinted?: Date;
}

export interface OCRResult {
  text: string;
  confidence: number;
  blocks: TextBlock[];
}

export interface TextBlock {
  text: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface BulkImportJob {
  id: string;
  fileName: string;
  totalItems: number;
  processedItems: number;
  successCount: number;
  errorCount: number;
  errors: ImportError[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
}

export interface ImportError {
  line: number;
  barcode: string;
  error: string;
}