import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL, API_TIMEOUT } from '@env';
import { store } from '@store/index';
import { clearAuth, refreshToken as refreshAuthToken } from '@store/slices/authSlice';
import { addPendingAction } from '@store/slices/offlineSlice';
import { showToast } from '@store/slices/uiSlice';
import NetInfo from '@react-native-community/netinfo';

const api: AxiosInstance = axios.create({
  baseURL: API_URL || 'http://localhost:3000/api',
  timeout: parseInt(API_TIMEOUT || '30000', 10),
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async config => {
    const token = await AsyncStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and token refresh
api.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle network errors
    if (!error.response) {
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        // Store the action for later sync if it's a write operation
        if (originalRequest.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(originalRequest.method)) {
          store.dispatch(
            addPendingAction({
              type: 'API_REQUEST',
              payload: {
                url: originalRequest.url,
                method: originalRequest.method,
                data: originalRequest.data,
              },
            })
          );
          store.dispatch(
            showToast({
              message: 'Action saved offline. Will sync when connection is restored.',
              type: 'info',
            })
          );
        }
      }
      throw error;
    }

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        await store.dispatch(refreshAuthToken()).unwrap();
        const newToken = store.getState().auth.token;
        if (newToken && originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
        }
        return api(originalRequest);
      } catch (refreshError) {
        store.dispatch(clearAuth());
        store.dispatch(
          showToast({
            message: 'Session expired. Please login again.',
            type: 'error',
          })
        );
        throw refreshError;
      }
    }

    // Handle other errors
    const message = error.response?.data?.message || error.message || 'An error occurred';
    store.dispatch(
      showToast({
        message,
        type: 'error',
      })
    );

    throw error;
  }
);

export const setupAxiosInterceptors = () => {
  // Additional setup if needed
};

export default api;