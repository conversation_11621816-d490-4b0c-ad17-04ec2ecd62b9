config:
  target: "{{ $processEnvironment.API_BASE_URL }}"
  phases:
    # Aggressive ramp-up to find breaking point
    - duration: 60
      arrivalRate: 5
      name: "Initial load"
    
    - duration: 300
      arrivalRate: 5
      rampTo: 50
      name: "Ramp to 250 users"
    
    - duration: 300
      arrivalRate: 50
      rampTo: 100
      name: "Ramp to 500 users"
    
    - duration: 300
      arrivalRate: 100
      rampTo: 200
      name: "Ramp to 1000 users"
    
    - duration: 600
      arrivalRate: 200
      name: "Sustain maximum load"
  
  processor: "../scripts/test-data-generator.js"
  
  http:
    timeout: 60
    pool: 100
    maxSockets: 500
  
  plugins:
    metrics-by-endpoint:
      percentilesOutputFileType: "csv"
    ensure:
      thresholds:
        - http.response_time.p95: 5000
        - http.response_time.p99: 10000
        - http.codes.5xx: 5
  
  statsInterval: 5

before:
  flow:
    - log: "Starting stress test - finding system breaking point"
    - function: "setupStressTest"

scenarios:
  # Focus on most resource-intensive operations
  
  - name: "Heavy Calculation Stress"
    weight: 40
    flow:
      - function: "authenticateUser"
      - loop:
          - post:
              url: "/calculations/arc-flash"
              headers:
                Authorization: "Bearer {{ authToken }}"
              json:
                systemVoltage: 480
                faultCurrent: 50000
                faultClearingTime: 0.5
                workingDistance: 18
                enclosureType: "open"
                groundingType: "solidly-grounded"
                conductorGap: 32
              expect:
                - statusCode: 200
          - post:
              url: "/calculations/short-circuit"
              headers:
                Authorization: "Bearer {{ authToken }}"
              json:
                transformerKVA: 1500
                transformerImpedance: 5.75
                primaryVoltage: 13800
                secondaryVoltage: 480
                motorContribution: true
                cableLength: 500
                cableSize: "500kcmil"
                conduitsPerPhase: 2
              expect:
                - statusCode: 200
          count: 5
  
  - name: "Database Heavy Operations"
    weight: 30
    flow:
      - function: "authenticateUser"
      - get:
          url: "/panels?limit=100&includeCircuits=true&includeCalculations=true"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/materials/search?query=a&limit=1000"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/projects?includeEstimates=true&includeInspections=true&limit=50"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  - name: "Concurrent Write Operations"
    weight: 20
    flow:
      - function: "authenticateUser"
      - loop:
          - post:
              url: "/panels"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generatePanelData"
              json: "{{ panelData }}"
              capture:
                - json: "$.id"
                  as: "newPanelId"
          - loop:
              - post:
                  url: "/panels/{{ newPanelId }}/circuits"
                  headers:
                    Authorization: "Bearer {{ authToken }}"
                  beforeRequest: "generateCircuitData"
                  json: "{{ circuitData }}"
              count: 42
          count: 2
  
  - name: "WebSocket Flood"
    weight: 10
    engine: "ws"
    flow:
      - function: "authenticateUser"
      - connect:
          url: "{{ wsUrl }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - loop:
          - send:
              data:
                type: "broadcast"
                channel: "stress_test"
                payload:
                  timestamp: "{{ $timestamp() }}"
                  data: "{{ $randomString(1000) }}"
          - think: 0.1
          count: 100