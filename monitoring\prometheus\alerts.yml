groups:
  - name: electrical_app_alerts
    interval: 30s
    rules:
      # Backend alerts
      - alert: BackendDown
        expr: up{job="backend"} == 0
        for: 2m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "Backend service is down"
          description: "Backend instance {{ $labels.instance }} has been down for more than 2 minutes."

      - alert: BackendHighCPU
        expr: rate(process_cpu_seconds_total{job="backend"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High CPU usage on backend"
          description: "Backend instance {{ $labels.instance }} has CPU usage above 80% for 5 minutes."

      - alert: BackendHighMemory
        expr: process_resident_memory_bytes{job="backend"} / 1024 / 1024 / 1024 > 1.5
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High memory usage on backend"
          description: "Backend instance {{ $labels.instance }} is using more than 1.5GB of memory."

      - alert: BackendHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High response time on backend"
          description: "95th percentile response time is above 1 second for 5 minutes."

      - alert: BackendHighErrorRate
        expr: rate(http_requests_total{job="backend",status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "High error rate on backend"
          description: "Error rate is above 5% for 5 minutes."

      # Database alerts
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL instance {{ $labels.instance }} is down."

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends{job="postgres"} / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "PostgreSQL high connection usage"
          description: "PostgreSQL is using more than 80% of available connections."

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_statements_mean_time_seconds{job="postgres"}[5m]) > 1
        for: 10m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Average query time is above 1 second for 10 minutes."

      - alert: PostgreSQLDiskUsage
        expr: pg_database_size_bytes{job="postgres"} / 1024 / 1024 / 1024 > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "PostgreSQL high disk usage"
          description: "Database size is above 80GB."

      # Redis alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis instance {{ $labels.instance }} is down."

      - alert: RedisHighMemory
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis is using more than 80% of allocated memory."

      - alert: RedisHighKeyEviction
        expr: rate(redis_evicted_keys_total{job="redis"}[5m]) > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high key eviction rate"
          description: "Redis is evicting more than 100 keys per second."

      # Frontend alerts
      - alert: FrontendDown
        expr: up{job="frontend"} == 0
        for: 2m
        labels:
          severity: critical
          service: frontend
        annotations:
          summary: "Frontend service is down"
          description: "Frontend instance {{ $labels.instance }} has been down for more than 2 minutes."

      - alert: FrontendHighLoadTime
        expr: histogram_quantile(0.95, rate(frontend_page_load_duration_seconds_bucket[5m])) > 3
        for: 5m
        labels:
          severity: warning
          service: frontend
        annotations:
          summary: "Frontend high page load time"
          description: "95th percentile page load time is above 3 seconds."

      # Kubernetes alerts
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[5m]) > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping."

      - alert: PodNotReady
        expr: kube_pod_status_ready{condition="false"} == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod is not ready"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is not ready."

      - alert: PersistentVolumeUsage
        expr: kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Persistent volume high usage"
          description: "PV {{ $labels.persistentvolumeclaim }} is using more than 80% of capacity."

      - alert: NodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes node is not ready"
          description: "Node {{ $labels.node }} is not ready."

      # Business metrics alerts
      - alert: LowDailyActiveUsers
        expr: daily_active_users < 100
        for: 1h
        labels:
          severity: info
          service: business
        annotations:
          summary: "Low daily active users"
          description: "Daily active users count is below 100."

      - alert: HighFailedCalculations
        expr: rate(electrical_calculation_failures_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "High rate of failed calculations"
          description: "More than 10 calculation failures per second."

      - alert: LowAPIUsage
        expr: rate(api_requests_total[1h]) < 100
        for: 1h
        labels:
          severity: info
          service: business
        annotations:
          summary: "Low API usage"
          description: "API request rate is below 100 requests per hour."