import { PrismaClient } from '@prisma/client';
import { AppError } from '../utils/errors';
import winston from 'winston';

// Generate a simple unique ID without external dependency
const uuidv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Create a logger instance for this service
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  defaultMeta: { service: 'transaction' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Transaction options interface
export interface TransactionOptions {
  maxWait?: number; // Maximum time to wait for transaction to start (ms)
  timeout?: number; // Maximum time for transaction to complete (ms)
  isolationLevel?: 'ReadUncommitted' | 'ReadCommitted' | 'RepeatableRead' | 'Serializable';
}

// Transaction context for nested transaction support
export interface TransactionContext {
  id: string;
  startTime: Date;
  operations: string[];
  isNested: boolean;
  parentId?: string;
}

// Result type for transaction operations
export type TransactionResult<T> = {
  success: boolean;
  data?: T;
  error?: Error;
  rollbackReason?: string;
  context: TransactionContext;
};

export class TransactionService {
  private static instance: TransactionService;
  private prisma: PrismaClient;
  private activeTransactions: Map<string, TransactionContext> = new Map();

  private constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  public static getInstance(prisma: PrismaClient): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService(prisma);
    }
    return TransactionService.instance;
  }

  /**
   * Execute operations within a database transaction
   * @param operations Function containing database operations
   * @param options Transaction options
   * @returns Transaction result with data or error
   */
  async executeTransaction<T>(
    operations: (tx: PrismaClient) => Promise<T>,
    options: TransactionOptions = {}
  ): Promise<TransactionResult<T>> {
    const transactionId = uuidv4();
    const startTime = new Date();
    const context: TransactionContext = {
      id: transactionId,
      startTime,
      operations: [],
      isNested: false
    };

    // Register transaction
    this.activeTransactions.set(transactionId, context);

    logger.info(`Starting transaction ${transactionId}`, {
      options,
      timestamp: startTime
    });

    try {
      // Execute transaction with Prisma
      const result = await this.prisma.$transaction(
        async (tx) => {
          // Log transaction boundary
          context.operations.push('TRANSACTION_START');
          
          try {
            // Execute user operations
            const data = await operations(tx as PrismaClient);
            
            // Log successful completion
            context.operations.push('TRANSACTION_COMMIT');
            
            return data;
          } catch (error) {
            // Log rollback
            context.operations.push('TRANSACTION_ROLLBACK');
            throw error;
          }
        },
        {
          maxWait: options.maxWait || 5000,
          timeout: options.timeout || 10000,
          isolationLevel: options.isolationLevel || 'ReadCommitted'
        }
      );

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      logger.info(`Transaction ${transactionId} completed successfully`, {
        duration,
        operationCount: context.operations.length
      });

      return {
        success: true,
        data: result,
        context
      };
    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      logger.error(`Transaction ${transactionId} failed`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        operations: context.operations
      });

      return {
        success: false,
        error: error instanceof Error ? error : new Error('Transaction failed'),
        rollbackReason: error instanceof Error ? error.message : 'Unknown error',
        context
      };
    } finally {
      // Clean up transaction tracking
      this.activeTransactions.delete(transactionId);
    }
  }

  /**
   * Execute operations with automatic retry on deadlock
   * @param operations Function containing database operations
   * @param maxRetries Maximum number of retry attempts
   * @param options Transaction options
   */
  async executeWithRetry<T>(
    operations: (tx: PrismaClient) => Promise<T>,
    maxRetries: number = 3,
    options: TransactionOptions = {}
  ): Promise<TransactionResult<T>> {
    let lastError: Error | undefined;
    let attempt = 0;

    while (attempt < maxRetries) {
      attempt++;
      
      const result = await this.executeTransaction(operations, options);
      
      if (result.success) {
        return result;
      }

      lastError = result.error;

      // Check if error is retryable (deadlock, timeout, etc.)
      if (this.isRetryableError(result.error)) {
        logger.warn(`Retrying transaction (attempt ${attempt}/${maxRetries})`, {
          error: result.error?.message,
          transactionId: result.context.id
        });

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
        continue;
      }

      // Non-retryable error, return immediately
      return result;
    }

    // Max retries exceeded
    return {
      success: false,
      error: lastError || new Error('Max retries exceeded'),
      rollbackReason: 'Max retries exceeded',
      context: {
        id: uuidv4(),
        startTime: new Date(),
        operations: ['MAX_RETRIES_EXCEEDED'],
        isNested: false
      }
    };
  }

  /**
   * Execute a batch of operations with all-or-nothing semantics
   * @param batchOperations Array of operations to execute
   * @param options Transaction options
   */
  async executeBatch<T>(
    batchOperations: Array<(tx: PrismaClient) => Promise<T>>,
    options: TransactionOptions = {}
  ): Promise<TransactionResult<T[]>> {
    return this.executeTransaction(async (tx) => {
      const results: T[] = [];
      
      for (let i = 0; i < batchOperations.length; i++) {
        logger.debug(`Executing batch operation ${i + 1}/${batchOperations.length}`);
        
        try {
          const result = await batchOperations[i](tx);
          results.push(result);
        } catch (error) {
          logger.error(`Batch operation ${i + 1} failed`, { error });
          throw new AppError(
            `Batch operation ${i + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            500
          );
        }
      }
      
      return results as T[];
    }, options);
  }

  /**
   * Check if an error is retryable
   * @param error Error to check
   */
  private isRetryableError(error?: Error): boolean {
    if (!error) return false;

    const retryableMessages = [
      'deadlock',
      'timeout',
      'database is locked',
      'concurrent update',
      'could not serialize'
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * Get currently active transactions (for monitoring)
   */
  getActiveTransactions(): TransactionContext[] {
    return Array.from(this.activeTransactions.values());
  }

  /**
   * Create a savepoint within a transaction (for nested transaction-like behavior)
   * Note: SQLite doesn't support true nested transactions, this simulates the behavior
   */
  async executeNested<T>(
    parentTx: PrismaClient,
    operations: (tx: PrismaClient) => Promise<T>,
    parentContext?: TransactionContext
  ): Promise<T> {
    const nestedId = uuidv4();
    const context: TransactionContext = {
      id: nestedId,
      startTime: new Date(),
      operations: [],
      isNested: true,
      parentId: parentContext?.id
    };

    logger.debug(`Starting nested transaction ${nestedId}`, {
      parentId: parentContext?.id
    });

    try {
      // In SQLite, we can't have true nested transactions
      // So we just execute the operations within the parent transaction
      const result = await operations(parentTx);
      
      logger.debug(`Nested transaction ${nestedId} completed`);
      return result;
    } catch (error) {
      logger.error(`Nested transaction ${nestedId} failed`, { error });
      throw error;
    }
  }
}

// Transaction utility functions for common patterns

/**
 * Update parent-child relationships within a transaction
 */
export async function updateParentChildRelation<T>(
  prisma: PrismaClient,
  parentUpdate: (tx: PrismaClient) => Promise<any>,
  childUpdates: Array<(tx: PrismaClient) => Promise<any>>
): Promise<TransactionResult<T>> {
  const service = TransactionService.getInstance(prisma);
  
  return service.executeTransaction(async (tx) => {
    // Update parent first
    const parent = await parentUpdate(tx);
    
    // Update all children
    const children = await Promise.all(
      childUpdates.map(update => update(tx))
    );
    
    return { parent, children } as T;
  });
}

/**
 * Execute a state transition with audit logging
 */
export async function executeStateTransition<T>(
  prisma: PrismaClient,
  entityType: string,
  entityId: string,
  fromState: string,
  toState: string,
  updateOperation: (tx: PrismaClient) => Promise<T>,
  userId: string
): Promise<TransactionResult<T>> {
  const service = TransactionService.getInstance(prisma);
  
  return service.executeTransaction(async (tx) => {
    // Perform the update
    const result = await updateOperation(tx);
    
    // TODO: Create audit log entry
    // await createAuditLog({
    //   action: 'STATE_TRANSITION',
    //   userId: userId,
    //   resourceType: entityType.toLowerCase(),
    //   resourceId: entityId,
    //   details: {
    //     from: fromState,
    //     to: toState
    //   }
    // });
    
    logger.info(`State transition completed`, {
      entityType,
      entityId,
      fromState,
      toState,
      userId
    });
    
    return result;
  });
}

/**
 * Batch create with validation
 */
export async function batchCreateWithValidation<T>(
  prisma: PrismaClient,
  items: any[],
  validateFn: (item: any) => Promise<boolean>,
  createFn: (tx: PrismaClient, item: any) => Promise<T>
): Promise<TransactionResult<T[]>> {
  const service = TransactionService.getInstance(prisma);
  
  return service.executeTransaction(async (tx) => {
    const results: T[] = [];
    
    // Validate all items first
    for (const item of items) {
      const isValid = await validateFn(item);
      if (!isValid) {
        throw new AppError(`Validation failed for item: ${JSON.stringify(item)}`, 400);
      }
    }
    
    // Create all items
    for (const item of items) {
      const created = await createFn(tx, item);
      results.push(created);
    }
    
    return results;
  });
}

// Export singleton getter for convenience
export const getTransactionService = (prisma: PrismaClient) => 
  TransactionService.getInstance(prisma);