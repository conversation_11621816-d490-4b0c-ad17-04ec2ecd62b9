import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
  BaseEntity as TypeORMBaseEntity,
} from 'typeorm';

export abstract class BaseEntity extends TypeORMBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'integer', default: 1 })
  version: number;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({ type: 'text', nullable: true })
  syncStatus: 'pending' | 'synced' | 'conflict' | null;

  @Column({ type: 'text', nullable: true })
  lastSyncedAt: string;

  @Column({ type: 'text', nullable: true })
  remoteId: string;

  @Column({ type: 'text', nullable: true })
  conflictData: string;
}