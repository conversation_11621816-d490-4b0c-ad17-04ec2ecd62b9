# Additional security configurations for nginx

# Hide nginx version
server_tokens off;

# Prevent clickjacking
add_header X-Frame-Options DENY always;

# Prevent MIME type sniffing
add_header X-Content-Type-Options nosniff always;

# Enable XSS filter
add_header X-XSS-Protection "1; mode=block" always;

# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:;" always;

# Referrer Policy
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Feature Policy
add_header Feature-Policy "camera 'none'; microphone 'none'; geolocation 'self';" always;

# Permissions Policy
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(self)" always;

# HSTS (HTTP Strict Transport Security)
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

# Block access to sensitive files
location ~* \.(git|env|yml|yaml|conf|ini|log|bak|backup|sql)$ {
    deny all;
    return 404;
}

# Block access to hidden files and directories
location ~ /\. {
    deny all;
    return 404;
}

# Disable unnecessary HTTP methods
if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
    return 405;
}

# Prevent host header injection
if ($host !~* ^(localhost|electrical-app\.com|www\.electrical-app\.com)$) {
    return 444;
}