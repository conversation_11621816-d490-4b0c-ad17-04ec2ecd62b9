# Error Handling Guide

This guide documents the comprehensive error handling and logging system implemented in the Electrical Contracting Application.

## Table of Contents

1. [Overview](#overview)
2. [Backend Error Handling](#backend-error-handling)
3. [Frontend Error Handling](#frontend-error-handling)
4. [Error Codes Reference](#error-codes-reference)
5. [Logging System](#logging-system)
6. [Health Monitoring](#health-monitoring)
7. [Best Practices](#best-practices)

## Overview

The application implements a robust error handling system with:

- **Structured Errors**: Consistent error format with unique IDs and correlation tracking
- **Comprehensive Logging**: Multi-level logging with context and performance metrics
- **Automatic Recovery**: Retry mechanisms for transient failures
- **Health Monitoring**: Real-time health checks and performance tracking
- **User-Friendly Messages**: Clear error messages without exposing sensitive details

## Backend Error Handling

### Structured Error Class

```typescript
import { StructuredError, ErrorFactory } from '@/utils/structured-error';

// Throwing a structured error
throw new StructuredError(
  ErrorCode.INVALID_INPUT,
  'Detailed error message',
  {
    details: [{ field: 'email', message: 'Invalid format' }],
    context: { userId: req.user.id },
    userMessage: 'Please provide a valid email address'
  }
);

// Using error factory methods
throw ErrorFactory.notFound('Project', projectId);
throw ErrorFactory.validation('Invalid input', details);
throw ErrorFactory.necViolation('Load exceeds panel capacity', 'Article 220');
```

### Error Middleware

The enhanced error handler middleware automatically:

- Logs errors with correlation IDs
- Converts various error types to structured format
- Returns consistent error responses
- Tracks error patterns for monitoring

### Database Connection Management

```typescript
import { dbConnectionManager } from '@/database/connection-manager';

// Execute with automatic retry
const result = await dbConnectionManager.executeWithRetry(
  async (prisma) => {
    return await prisma.project.findMany();
  },
  {
    maxRetries: 3,
    operationName: 'fetchProjects'
  }
);
```

### Request Logging

All requests are automatically logged with:

- Correlation ID for tracking across services
- Request/response timing
- User context
- Performance metrics

## Frontend Error Handling

### Error Boundaries

```typescript
import { EnhancedErrorBoundary } from '@/components/EnhancedErrorBoundary';

// Wrap components with error boundaries
<EnhancedErrorBoundary
  showDetails={process.env.NODE_ENV === 'development'}
  onError={(error, errorInfo) => {
    // Custom error handling
  }}
>
  <YourComponent />
</EnhancedErrorBoundary>

// For isolated components
<EnhancedErrorBoundary isolate>
  <RiskyComponent />
</EnhancedErrorBoundary>
```

### API Error Handling

```typescript
import { ErrorHandler, retryRequest } from '@/utils/error-handler';

// Handle API errors
try {
  const data = await api.get('/projects');
} catch (error) {
  const apiError = ErrorHandler.handleApiError(error, {
    showNotification: true,
    context: { operation: 'fetchProjects' }
  });
  
  // Extract validation errors
  const fieldErrors = ErrorHandler.handleValidationErrors(apiError);
}

// Retry failed requests
const data = await retryRequest(
  () => api.get('/projects'),
  {
    maxAttempts: 3,
    onRetry: (attempt, delay) => {
      console.log(`Retrying in ${delay}ms...`);
    }
  }
);
```

### Performance Monitoring

```typescript
import { performanceMonitor, usePerformanceMonitor } from '@/services/performance-monitor';

// In React components
function MyComponent() {
  const perf = usePerformanceMonitor('MyComponent');
  
  const fetchData = async () => {
    await perf.measureAsync('fetchData', async () => {
      // Async operation
    });
  };
  
  perf.measureRender(() => {
    // Render logic
  });
}

// Custom metrics
performanceMonitor.recordMetric('customMetric', 100, 'ms', {
  context: 'additional data'
});
```

## Error Codes Reference

### Validation Errors (4000-4099)
- `ERR_4000`: Invalid input provided
- `ERR_4001`: Missing required field
- `ERR_4002`: Invalid format
- `ERR_4003`: Value out of range
- `ERR_4004`: Invalid email
- `ERR_4005`: Invalid phone
- `ERR_4006`: Invalid date

### Authentication Errors (4100-4199)
- `ERR_4100`: Invalid credentials
- `ERR_4101`: Token expired
- `ERR_4102`: Token invalid
- `ERR_4103`: Session expired
- `ERR_4104`: Account locked
- `ERR_4105`: Account disabled
- `ERR_4106`: MFA required
- `ERR_4107`: MFA invalid

### Authorization Errors (4200-4299)
- `ERR_4200`: Unauthorized access
- `ERR_4201`: Access forbidden
- `ERR_4202`: Insufficient permissions
- `ERR_4203`: Resource access denied

### Database Errors (4300-4399)
- `ERR_4300`: Record not found
- `ERR_4301`: Duplicate record
- `ERR_4302`: Constraint violation
- `ERR_4303`: Database connection failed
- `ERR_4304`: Transaction failed
- `ERR_4305`: Query timeout
- `ERR_4306`: Data integrity error

### Calculation Errors (4500-4599)
- `ERR_4500`: Calculation error
- `ERR_4501`: Invalid calculation input
- `ERR_4502`: Calculation overflow
- `ERR_4503`: NEC violation
- `ERR_4504`: Load exceeds capacity
- `ERR_4505`: Voltage drop exceeded
- `ERR_4506`: Arc flash hazard

## Logging System

### Log Levels

1. **Fatal**: Application-breaking errors requiring immediate attention
2. **Error**: Errors that prevented an operation from completing
3. **Warn**: Warning conditions that should be investigated
4. **Info**: General informational messages
5. **HTTP**: HTTP request/response logging
6. **Debug**: Detailed debugging information
7. **Trace**: Very detailed tracing information

### Enhanced Logger Usage

```typescript
import { enhancedLogger } from '@/utils/enhanced-logger';

// Create logger with context
const logger = enhancedLogger.child({
  service: 'ProjectService',
  userId: req.user.id
});

// Log with structured data
logger.info('Project created', {
  projectId: project.id,
  duration: 150
});

// Log errors with full context
logger.error('Failed to create project', error, {
  input: projectData,
  validationErrors: errors
});

// Performance logging
logger.performance('database.query', 250, {
  query: 'findProjects',
  resultCount: 10
});

// Audit logging
logger.audit('PROJECT_CREATED', {
  projectId: project.id,
  createdBy: user.id
});
```

### Log Files

Production logs are stored in:
- `logs/fatal.log` - Critical errors only
- `logs/error.log` - All errors
- `logs/combined.log` - All logs
- `logs/audit.log` - Security audit trail
- `logs/performance.log` - Performance metrics

## Health Monitoring

### Health Check Endpoints

- `GET /api/health` - Comprehensive health check
- `GET /api/health/live` - Kubernetes liveness probe
- `GET /api/health/ready` - Kubernetes readiness probe
- `GET /api/health/metrics` - Performance metrics

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2025-01-09T10:30:00Z",
  "uptime": 3600000,
  "checks": {
    "database": {
      "status": "up",
      "responseTime": 5
    },
    "redis": {
      "status": "up",
      "responseTime": 2
    },
    "memory": {
      "status": "up",
      "details": {
        "heapUsedPercent": "45.23",
        "heapUsed": "92.45 MB"
      }
    }
  }
}
```

## Best Practices

### 1. Always Use Structured Errors

```typescript
// ❌ Bad
throw new Error('Not found');

// ✅ Good
throw ErrorFactory.notFound('Project', projectId);
```

### 2. Include Context in Logs

```typescript
// ❌ Bad
logger.error('Operation failed');

// ✅ Good
logger.error('Failed to update project', error, {
  projectId,
  userId,
  changes: updateData
});
```

### 3. Handle Errors at the Right Level

```typescript
// Service layer - throw structured errors
if (!project) {
  throw ErrorFactory.notFound('Project', projectId);
}

// Route layer - let middleware handle
router.get('/projects/:id', asyncErrorHandler(async (req, res) => {
  const project = await projectService.findById(req.params.id);
  res.json(project);
}));
```

### 4. Use Correlation IDs

```typescript
// Backend
app.use(correlationIdMiddleware);

// Frontend
ApiInterceptors.resetCorrelationId(); // On route change
```

### 5. Monitor Performance

```typescript
// Backend
await measurePerformance('complexCalculation', async () => {
  // Heavy computation
});

// Frontend
const perf = usePerformanceMonitor('Dashboard');
await perf.measureAsync('loadData', fetchDashboardData);
```

### 6. Implement Retry Logic

```typescript
// Backend
await dbConnectionManager.executeWithRetry(
  async (prisma) => prisma.project.create({ data }),
  { maxRetries: 3 }
);

// Frontend
await retryRequest(() => api.post('/projects', data), {
  maxAttempts: 3,
  onRetry: (attempt) => console.log(`Retry attempt ${attempt}`)
});
```

### 7. Test Error Scenarios

```typescript
// Test error handling
it('should handle database connection errors', async () => {
  jest.spyOn(prisma, '$queryRaw').mockRejectedValue(
    new Error('Connection refused')
  );
  
  await expect(service.findAll()).rejects.toThrow(
    StructuredError
  );
});
```

## Error Recovery Strategies

### 1. Circuit Breaker Pattern

The error boundaries implement a circuit breaker that auto-resets after multiple failures.

### 2. Exponential Backoff

Retry delays increase exponentially to avoid overwhelming failed services.

### 3. Request Queuing

Failed requests due to auth issues are queued and retried after token refresh.

### 4. Graceful Degradation

Components wrapped in error boundaries show fallback UI instead of crashing the app.

## Monitoring and Alerting

### Critical Errors

Errors with `shouldAlert()` returning true trigger immediate notifications:
- Database connection failures
- Configuration errors
- Memory errors
- Dependency failures

### Performance Alerts

Automatic alerts when thresholds are exceeded:
- API response time > 1s
- Page load time > 2.5s
- Component render > 16ms
- Memory usage > 90%

### Error Patterns

The system tracks error patterns to identify:
- Repeated failures
- Error spikes
- Unusual error types
- Performance degradation

## Integration with External Services

While the error handling system is designed to integrate with external monitoring services, the integration points are clearly marked with TODO comments:

1. **Sentry Integration** - `frontend/src/components/ErrorBoundary.tsx`
2. **LogRocket Integration** - `frontend/src/services/errorService.ts`
3. **APM Integration** - `backend/src/utils/enhanced-logger.ts`

These can be implemented based on your monitoring service preferences.