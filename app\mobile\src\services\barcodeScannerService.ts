import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  Barcode<PERSON><PERSON>ult, 
  ScanSession, 
  ScannedItem, 
  MaterialInfo,
  InventoryTransaction,
  ScanAnalytics,
  QRCodeData,
  CustomQRCode,
  OCRResult,
  BulkImportJob
} from '../types/barcode';
import { secureApi } from './secureApi';
import { materialService } from './materialService';
import uuid from 'uuid';

const SCAN_HISTORY_KEY = 'barcode_scan_history';
const SCAN_SESSION_KEY = 'barcode_scan_session_';
const MATERIAL_CATALOG_KEY = 'material_catalog_offline';
const CUSTOM_QR_KEY = 'custom_qr_codes';

class BarcodeScannerService {
  private currentSession: ScanSession | null = null;
  private scanHistory: ScannedItem[] = [];

  // Initialize service
  async initialize(): Promise<void> {
    await this.loadScanHistory();
    await this.checkAndUpdateOfflineCatalog();
  }

  // Start a new scan session
  async startScanSession(mode: 'single' | 'batch' | 'continuous'): Promise<ScanSession> {
    this.currentSession = {
      id: uuid.v4(),
      startTime: new Date(),
      scansCount: 0,
      mode,
      scans: []
    };

    await this.saveScanSession();
    return this.currentSession;
  }

  // End current scan session
  async endScanSession(): Promise<ScanSession | null> {
    if (!this.currentSession) return null;

    this.currentSession.endTime = new Date();
    await this.saveScanSession();
    
    const session = this.currentSession;
    this.currentSession = null;
    
    // Sync session to backend
    this.syncScanSessionToBackend(session);
    
    return session;
  }

  // Process a scanned barcode
  async processScan(barcode: BarcodeResult, quantity: number = 1): Promise<ScannedItem> {
    // Look up material information
    const material = await this.lookupMaterial(barcode.value);
    
    // Create scanned item
    const scannedItem: ScannedItem = {
      id: uuid.v4(),
      barcode: barcode.value,
      type: barcode.type,
      timestamp: new Date(),
      material,
      quantity,
    };

    // Add to current session if active
    if (this.currentSession) {
      this.currentSession.scans.push(scannedItem);
      this.currentSession.scansCount++;
      await this.saveScanSession();
    }

    // Add to history
    this.scanHistory.unshift(scannedItem);
    if (this.scanHistory.length > 1000) {
      this.scanHistory = this.scanHistory.slice(0, 1000);
    }
    await this.saveScanHistory();

    // Emit scan event for real-time updates
    this.emitScanEvent(scannedItem);

    return scannedItem;
  }

  // Look up material by barcode
  async lookupMaterial(barcode: string): Promise<MaterialInfo | undefined> {
    try {
      // Try online lookup first
      const response = await secureApi.get(`/materials/barcode/${barcode}`);
      const material = response.data;
      
      // Cache for offline use
      await this.cacheMaterialInfo(barcode, material);
      
      // Check for bundles
      if (material.bundleItems && material.bundleItems.length > 0) {
        this.notifyBundleDetected(material);
      }
      
      return material;
    } catch (error) {
      // Fallback to offline catalog
      return this.lookupOfflineMaterial(barcode);
    }
  }

  // Offline material lookup
  private async lookupOfflineMaterial(barcode: string): Promise<MaterialInfo | undefined> {
    // Check cached materials
    const cachedKey = `material_barcode_${barcode}`;
    const cached = await AsyncStorage.getItem(cachedKey);
    if (cached) {
      return JSON.parse(cached);
    }

    // Check offline catalog
    const catalog = await this.getOfflineCatalog();
    return catalog[barcode];
  }

  // Get offline material catalog
  private async getOfflineCatalog(): Promise<Record<string, MaterialInfo>> {
    const catalogStr = await AsyncStorage.getItem(MATERIAL_CATALOG_KEY);
    return catalogStr ? JSON.parse(catalogStr) : {};
  }

  // Update offline catalog
  async updateOfflineCatalog(): Promise<void> {
    try {
      const response = await secureApi.get('/materials/catalog/download');
      const catalog = response.data;
      
      await AsyncStorage.setItem(MATERIAL_CATALOG_KEY, JSON.stringify(catalog));
      await AsyncStorage.setItem('catalog_last_updated', new Date().toISOString());
    } catch (error) {
      console.error('Failed to update offline catalog:', error);
    }
  }

  // Check if catalog needs update
  private async checkAndUpdateOfflineCatalog(): Promise<void> {
    const lastUpdated = await AsyncStorage.getItem('catalog_last_updated');
    if (!lastUpdated) {
      await this.updateOfflineCatalog();
      return;
    }

    const lastUpdateDate = new Date(lastUpdated);
    const daysSinceUpdate = (Date.now() - lastUpdateDate.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceUpdate > 7) {
      // Update weekly
      this.updateOfflineCatalog(); // Don't await, let it run in background
    }
  }

  // Add scanned items to inventory
  async addToInventory(
    items: ScannedItem[], 
    locationId: string,
    notes?: string
  ): Promise<InventoryTransaction[]> {
    const transactions: InventoryTransaction[] = [];

    for (const item of items) {
      if (!item.material) continue;

      const transaction: InventoryTransaction = {
        id: uuid.v4(),
        type: 'add',
        materialId: item.material.id,
        quantity: item.quantity,
        toLocation: locationId,
        reason: notes,
        performedBy: 'current_user', // TODO: Get from auth
        timestamp: new Date(),
        scanSessionId: this.currentSession?.id
      };

      transactions.push(transaction);
    }

    // Save transactions
    try {
      const response = await secureApi.post('/inventory/transactions/bulk', {
        transactions
      });
      return response.data;
    } catch (error) {
      // Queue for offline sync
      await this.queueOfflineTransactions(transactions);
      return transactions;
    }
  }

  // Transfer inventory between locations
  async transferInventory(
    items: ScannedItem[],
    fromLocation: string,
    toLocation: string,
    notes?: string
  ): Promise<InventoryTransaction[]> {
    const transactions: InventoryTransaction[] = [];

    for (const item of items) {
      if (!item.material) continue;

      const transaction: InventoryTransaction = {
        id: uuid.v4(),
        type: 'transfer',
        materialId: item.material.id,
        quantity: item.quantity,
        fromLocation,
        toLocation,
        reason: notes,
        performedBy: 'current_user',
        timestamp: new Date(),
        scanSessionId: this.currentSession?.id
      };

      transactions.push(transaction);
    }

    try {
      const response = await secureApi.post('/inventory/transactions/bulk', {
        transactions
      });
      return response.data;
    } catch (error) {
      await this.queueOfflineTransactions(transactions);
      return transactions;
    }
  }

  // Add items to estimate
  async addToEstimate(estimateId: string, items: ScannedItem[]): Promise<void> {
    const materialItems = items
      .filter(item => item.material)
      .map(item => ({
        materialId: item.material!.id,
        quantity: item.quantity,
        notes: item.notes
      }));

    try {
      await secureApi.post(`/estimates/${estimateId}/materials/bulk`, {
        items: materialItems
      });
    } catch (error) {
      // Queue for offline sync
      await this.queueOfflineEstimateItems(estimateId, materialItems);
    }
  }

  // Generate custom QR code
  async generateCustomQRCode(
    type: QRCodeData['type'],
    label: string,
    data: any,
    projectId?: string
  ): Promise<CustomQRCode> {
    const qrCode: CustomQRCode = {
      id: uuid.v4(),
      type,
      label,
      data,
      projectId,
      created: new Date(),
      createdBy: 'current_user',
      printCount: 0
    };

    // Save locally
    const customQRCodes = await this.getCustomQRCodes();
    customQRCodes.push(qrCode);
    await AsyncStorage.setItem(CUSTOM_QR_KEY, JSON.stringify(customQRCodes));

    // Sync to backend
    try {
      await secureApi.post('/qr-codes/custom', qrCode);
    } catch (error) {
      // Will sync later
    }

    return qrCode;
  }

  // Parse QR code data
  parseQRCode(value: string): QRCodeData | null {
    try {
      const data = JSON.parse(value);
      if (data.type && data.version && data.data) {
        return data as QRCodeData;
      }
    } catch {
      // Not a structured QR code
    }
    return null;
  }

  // Get scan analytics
  async getScanAnalytics(
    startDate: Date,
    endDate: Date
  ): Promise<ScanAnalytics> {
    try {
      const response = await secureApi.get('/analytics/scans', {
        params: { startDate, endDate }
      });
      return response.data;
    } catch (error) {
      // Calculate from local data
      return this.calculateLocalAnalytics(startDate, endDate);
    }
  }

  // OCR text extraction from images
  async extractTextFromImage(imageUri: string): Promise<OCRResult> {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'scan.jpg'
      } as any);

      const response = await secureApi.post('/ocr/extract', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      throw new Error('OCR extraction failed');
    }
  }

  // Bulk import from CSV
  async bulkImportFromCSV(fileUri: string): Promise<BulkImportJob> {
    const jobId = uuid.v4();
    
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: fileUri,
        type: 'text/csv',
        name: 'import.csv'
      } as any);

      const response = await secureApi.post('/materials/import/csv', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      throw new Error('Bulk import failed');
    }
  }

  // Get scan history
  async getScanHistory(limit: number = 100): Promise<ScannedItem[]> {
    await this.loadScanHistory();
    return this.scanHistory.slice(0, limit);
  }

  // Clear scan history
  async clearScanHistory(): Promise<void> {
    this.scanHistory = [];
    await AsyncStorage.removeItem(SCAN_HISTORY_KEY);
  }

  // Search scan history
  searchScanHistory(query: string): ScannedItem[] {
    const searchTerm = query.toLowerCase();
    return this.scanHistory.filter(item => 
      item.barcode.toLowerCase().includes(searchTerm) ||
      item.material?.name.toLowerCase().includes(searchTerm) ||
      item.material?.sku.toLowerCase().includes(searchTerm)
    );
  }

  // Private helper methods

  private async saveScanSession(): Promise<void> {
    if (!this.currentSession) return;
    await AsyncStorage.setItem(
      `${SCAN_SESSION_KEY}${this.currentSession.id}`,
      JSON.stringify(this.currentSession)
    );
  }

  private async saveScanHistory(): Promise<void> {
    await AsyncStorage.setItem(SCAN_HISTORY_KEY, JSON.stringify(this.scanHistory));
  }

  private async loadScanHistory(): Promise<void> {
    const history = await AsyncStorage.getItem(SCAN_HISTORY_KEY);
    if (history) {
      this.scanHistory = JSON.parse(history);
    }
  }

  private async cacheMaterialInfo(barcode: string, material: MaterialInfo): Promise<void> {
    const key = `material_barcode_${barcode}`;
    await AsyncStorage.setItem(key, JSON.stringify(material));
  }

  private async getCustomQRCodes(): Promise<CustomQRCode[]> {
    const codes = await AsyncStorage.getItem(CUSTOM_QR_KEY);
    return codes ? JSON.parse(codes) : [];
  }

  private async queueOfflineTransactions(transactions: InventoryTransaction[]): Promise<void> {
    const queueKey = 'offline_inventory_transactions';
    const queue = await AsyncStorage.getItem(queueKey);
    const existingQueue = queue ? JSON.parse(queue) : [];
    
    existingQueue.push(...transactions);
    await AsyncStorage.setItem(queueKey, JSON.stringify(existingQueue));
  }

  private async queueOfflineEstimateItems(estimateId: string, items: any[]): Promise<void> {
    const queueKey = 'offline_estimate_items';
    const queue = await AsyncStorage.getItem(queueKey);
    const existingQueue = queue ? JSON.parse(queue) : {};
    
    if (!existingQueue[estimateId]) {
      existingQueue[estimateId] = [];
    }
    existingQueue[estimateId].push(...items);
    
    await AsyncStorage.setItem(queueKey, JSON.stringify(existingQueue));
  }

  private async syncScanSessionToBackend(session: ScanSession): Promise<void> {
    try {
      await secureApi.post('/scan-sessions', session);
      // Remove from local storage after successful sync
      await AsyncStorage.removeItem(`${SCAN_SESSION_KEY}${session.id}`);
    } catch (error) {
      // Will retry later
    }
  }

  private notifyBundleDetected(material: MaterialInfo): void {
    // Emit event for UI to handle bundle notification
    // This would integrate with your event system
  }

  private emitScanEvent(item: ScannedItem): void {
    // Emit event for real-time updates
    // This would integrate with your event system
  }

  private async calculateLocalAnalytics(
    startDate: Date,
    endDate: Date
  ): Promise<ScanAnalytics> {
    const relevantScans = this.scanHistory.filter(scan => 
      scan.timestamp >= startDate && scan.timestamp <= endDate
    );

    const materialCounts = new Map<string, MaterialScanCount>();
    const categoryCount: Record<string, number> = {};
    const uniqueMaterials = new Set<string>();

    relevantScans.forEach(scan => {
      if (scan.material) {
        uniqueMaterials.add(scan.material.id);
        
        // Count by material
        const key = scan.material.id;
        if (!materialCounts.has(key)) {
          materialCounts.set(key, {
            materialId: scan.material.id,
            name: scan.material.name,
            sku: scan.material.sku,
            scanCount: 0,
            lastScanned: scan.timestamp
          });
        }
        const count = materialCounts.get(key)!;
        count.scanCount++;
        count.lastScanned = scan.timestamp;

        // Count by category
        categoryCount[scan.material.category] = (categoryCount[scan.material.category] || 0) + 1;
      }
    });

    // Get top scanned materials
    const topScanned = Array.from(materialCounts.values())
      .sort((a, b) => b.scanCount - a.scanCount)
      .slice(0, 10);

    return {
      totalScans: relevantScans.length,
      uniqueMaterials: uniqueMaterials.size,
      topScannedMaterials: topScanned,
      scansByCategory: categoryCount,
      scansByLocation: {}, // Would need location tracking
      scansByUser: {}, // Would need user tracking
      timeRange: { start: startDate, end: endDate }
    };
  }

  // Sync all offline data
  async syncOfflineData(): Promise<void> {
    // Sync scan sessions
    const allKeys = await AsyncStorage.getAllKeys();
    const sessionKeys = allKeys.filter(key => key.startsWith(SCAN_SESSION_KEY));
    
    for (const key of sessionKeys) {
      const sessionStr = await AsyncStorage.getItem(key);
      if (sessionStr) {
        const session = JSON.parse(sessionStr);
        await this.syncScanSessionToBackend(session);
      }
    }

    // Sync inventory transactions
    const transactionQueue = await AsyncStorage.getItem('offline_inventory_transactions');
    if (transactionQueue) {
      const transactions = JSON.parse(transactionQueue);
      try {
        await secureApi.post('/inventory/transactions/bulk', { transactions });
        await AsyncStorage.removeItem('offline_inventory_transactions');
      } catch (error) {
        // Will retry later
      }
    }

    // Sync estimate items
    const estimateQueue = await AsyncStorage.getItem('offline_estimate_items');
    if (estimateQueue) {
      const estimates = JSON.parse(estimateQueue);
      for (const [estimateId, items] of Object.entries(estimates)) {
        try {
          await secureApi.post(`/estimates/${estimateId}/materials/bulk`, { items });
          delete estimates[estimateId];
        } catch (error) {
          // Will retry later
        }
      }
      await AsyncStorage.setItem('offline_estimate_items', JSON.stringify(estimates));
    }
  }
}

export const barcodeScannerService = new BarcodeScannerService();