import { Router, Request, Response } from 'express';
import { ShortCircuitService } from '../services/short-circuit.service';
import { ShortCircuitInputSchema } from '../services/calculations/short-circuit';
import { authenticate } from '../middleware/auth';
import { z } from 'zod';

const router: Router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * POST /api/short-circuit/calculate
 * Perform a new short circuit calculation
 */
router.post('/calculate', async (req: Request, res: Response) => {
  try {
    const { panelId, ...calculationInput } = req.body;
    
    if (!panelId) {
      return res.status(400).json({ error: 'Panel ID is required' });
    }
    
    // Validate input
    const validatedInput = ShortCircuitInputSchema.parse(calculationInput);
    
    // Create calculation
    const calculation = await ShortCircuitService.createCalculation(
      panelId,
      validatedInput,
      req.user!.id
    );
    
    res.json(calculation);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Invalid input', 
        details: error.errors 
      });
    }
    
    console.error('Short circuit calculation error:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Calculation failed' 
    });
  }
});

/**
 * GET /api/short-circuit/panel/:panelId
 * Get all short circuit calculations for a panel
 */
router.get('/panel/:panelId', async (req: Request, res: Response) => {
  try {
    const { panelId } = req.params;
    const calculations = await ShortCircuitService.getCalculationsByPanel(panelId);
    res.json(calculations);
  } catch (error) {
    console.error('Error fetching calculations:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch calculations' 
    });
  }
});

/**
 * GET /api/short-circuit/panel/:panelId/latest
 * Get the latest short circuit calculation for a panel
 */
router.get('/panel/:panelId/latest', async (req: Request, res: Response) => {
  try {
    const { panelId } = req.params;
    const calculation = await ShortCircuitService.getLatestCalculation(panelId);
    
    if (!calculation) {
      return res.status(404).json({ error: 'No calculations found for this panel' });
    }
    
    res.json(calculation);
  } catch (error) {
    console.error('Error fetching latest calculation:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch calculation' 
    });
  }
});

/**
 * GET /api/short-circuit/:calculationId
 * Get a specific calculation by ID
 */
router.get('/:calculationId', async (req: Request, res: Response) => {
  try {
    const { calculationId } = req.params;
    const calculation = await ShortCircuitService.getCalculationById(calculationId);
    
    if (!calculation) {
      return res.status(404).json({ error: 'Calculation not found' });
    }
    
    res.json(calculation);
  } catch (error) {
    // Log error through proper logging service if configured
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch calculation' 
    });
  }
});

/**
 * GET /api/short-circuit/project/:projectId/required
 * Get all panels in a project that need short circuit analysis
 */
router.get('/project/:projectId/required', async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const panels = await ShortCircuitService.getPanelsRequiringAnalysis(projectId);
    res.json(panels);
  } catch (error) {
    // Log error through proper logging service if configured
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch panels' 
    });
  }
});

/**
 * GET /api/short-circuit/:calculationId/equipment
 * Get equipment recommendations based on fault current
 */
router.get('/:calculationId/equipment', async (req: Request, res: Response) => {
  try {
    const { calculationId } = req.params;
    const recommendations = await ShortCircuitService.generateEquipmentRecommendations(
      calculationId
    );
    res.json(recommendations);
  } catch (error) {
    console.error('Error generating recommendations:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to generate recommendations' 
    });
  }
});

/**
 * GET /api/short-circuit/:calculationId/report
 * Generate report data for a calculation
 */
router.get('/:calculationId/report', async (req: Request, res: Response) => {
  try {
    const { calculationId } = req.params;
    const reportData = await ShortCircuitService.generateReportData(calculationId);
    res.json(reportData);
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to generate report' 
    });
  }
});

/**
 * PUT /api/short-circuit/:calculationId/review
 * Mark a calculation as reviewed
 */
router.put('/:calculationId/review', async (req: Request, res: Response) => {
  try {
    const { calculationId } = req.params;
    const calculation = await ShortCircuitService.updateCalculation(
      calculationId,
      req.user!.id
    );
    res.json(calculation);
  } catch (error) {
    console.error('Error updating calculation:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to update calculation' 
    });
  }
});

/**
 * DELETE /api/short-circuit/:calculationId
 * Delete a calculation (admin only)
 */
router.delete('/:calculationId', async (req: Request, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const { calculationId } = req.params;
    await ShortCircuitService.deleteCalculation(calculationId);
    res.json({ message: 'Calculation deleted successfully' });
  } catch (error) {
    console.error('Error deleting calculation:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Failed to delete calculation' 
    });
  }
});

/**
 * POST /api/short-circuit/validate
 * Validate input parameters without saving
 */
router.post('/validate', async (req: Request, res: Response) => {
  try {
    const validatedInput = ShortCircuitInputSchema.parse(req.body);
    res.json({ 
      valid: true, 
      data: validatedInput 
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        valid: false,
        error: 'Invalid input', 
        details: error.errors 
      });
    }
    
    res.status(500).json({ 
      valid: false,
      error: error instanceof Error ? error.message : 'Validation failed' 
    });
  }
});

export default router;