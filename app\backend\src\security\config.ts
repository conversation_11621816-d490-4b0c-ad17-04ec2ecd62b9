// Security configuration and constants

export const SECURITY_CONFIG = {
  // Password Policy
  password: {
    minLength: 12,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    preventCommon: true,
    preventReuse: 5, // Prevent reusing last 5 passwords
    maxAge: 90, // Days before password expires
  },
  
  // Session Configuration
  session: {
    duration: 3600, // 1 hour
    maxConcurrent: 5,
    extendOnActivity: true,
    idleTimeout: 1800, // 30 minutes
    requireFreshForSensitive: true,
    freshSessionMaxAge: 300, // 5 minutes
  },
  
  // Two-Factor Authentication
  twoFactor: {
    issuer: 'Electrical Contracting App',
    window: 1, // Accept codes from 1 window before/after
    backupCodesCount: 10,
    codeLength: 6,
  },
  
  // API Security
  api: {
    keyPrefix: 'ec_',
    keyLength: 32,
    maxKeysPerUser: 10,
    defaultExpiry: 365, // Days
  },
  
  // File Upload Security
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ],
    allowedExtensions: [
      '.jpg', '.jpeg', '.png', '.gif',
      '.pdf', '.xls', '.xlsx', '.csv'
    ],
    scanForViruses: true,
    quarantinePath: './quarantine',
  },
  
  // Data Retention
  retention: {
    auditLogs: 2 * 365, // 2 years
    securityEvents: 365, // 1 year
    sessionData: 30, // 30 days
    exportedData: 30, // 30 days
    deletedRecords: 90, // 90 days soft delete
  },
  
  // Security Headers
  headers: {
    frameOptions: 'DENY',
    xssProtection: '1; mode=block',
    contentTypeOptions: 'nosniff',
    referrerPolicy: 'strict-origin-when-cross-origin',
    permissionsPolicy: {
      camera: 'none',
      microphone: 'none',
      geolocation: 'none',
      payment: 'none',
    },
  },
  
  // Encryption
  encryption: {
    algorithm: 'aes-256-gcm',
    keyDerivationIterations: 100000,
    saltLength: 32,
    ivLength: 16,
    tagLength: 16,
  },
  
  // Monitoring
  monitoring: {
    failedLoginThreshold: 5,
    failedLoginWindow: 900, // 15 minutes
    suspiciousActivityThreshold: 10,
    alertOnAnomalousLocation: true,
    alertOnMultipleDevices: true,
  },
  
  // GDPR Compliance
  gdpr: {
    dataExportFormats: ['json', 'csv', 'pdf'],
    anonymizationFields: [
      'email', 'name', 'phone', 'address', 
      'ssn', 'tax_id', 'bank_account'
    ],
    consentRequired: true,
    rightToBeForgotten: true,
  },
};

// Security regex patterns
export const SECURITY_PATTERNS = {
  password: {
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    number: /[0-9]/,
    special: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/,
  },
  
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  phone: /^\+?1?\d{10,14}$/,
  
  creditCard: {
    visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
    mastercard: /^5[1-5][0-9]{14}$/,
    amex: /^3[47][0-9]{13}$/,
    discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
  },
  
  ssn: /^\d{3}-?\d{2}-?\d{4}$/,
  
  strongSecret: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{32,}$/,
};

// Common weak passwords to prevent
export const WEAK_PASSWORDS = [
  'password', 'password123', '********', '*********', 'qwerty',
  'abc123', 'Password123', 'password1', '123456', 'password!',
  'qwerty123', 'Aa123456', '*********0', 'QWERTY', 'Password1',
  'password123!', 'Aa123456!', 'electrical', 'electric123',
  'contractor', 'admin123', 'welcome123', 'changeme',
];

// Electrical industry specific security requirements
export const ELECTRICAL_SECURITY = {
  // NEC compliance tracking
  necCompliance: {
    requireDigitalSignature: true,
    auditAllCalculations: true,
    trackCodeReferences: true,
    versionControl: true,
  },
  
  // Permit security
  permits: {
    requireAuthentication: true,
    requireSignature: true,
    trackSubmissions: true,
    encryptSensitiveData: true,
  },
  
  // Inspection security
  inspections: {
    requirePhotoVerification: true,
    geotagPhotos: true,
    preventTampering: true,
    digitalSignatures: true,
  },
  
  // Calculation security
  calculations: {
    preventManipulation: true,
    auditAllChanges: true,
    requireVerification: true,
    lockAfterApproval: true,
  },
};