@echo off
echo Cleaning up old startup files...
echo ================================

REM Remove old startup scripts that don't work
if exist start-app.sh (
    del start-app.sh
    echo Removed: start-app.sh
)

if exist start-app.bat (
    del start-app.bat  
    echo Removed: start-app.bat
)

if exist start-main-app.bat (
    del start-main-app.bat
    echo Removed: start-main-app.bat (replaced with START_HERE.bat)
)

REM Remove individual service startup scripts (we use the main one now)
if exist app\backend\start-backend.bat (
    del app\backend\start-backend.bat
    echo Removed: app\backend\start-backend.bat
)

if exist app\frontend\start-frontend.bat (
    del app\frontend\start-frontend.bat
    echo Removed: app\frontend\start-frontend.bat
)

REM Remove old installation guides that might confuse
if exist install-and-start.md (
    del install-and-start.md
    echo Removed: install-and-start.md
)

if exist fix-startup-issues.md (
    del fix-startup-issues.md
    echo Removed: fix-startup-issues.md
)

echo.
echo Cleanup complete!
echo.
echo The only startup file you need is: start-main-app.bat
echo Documentation is in: QUICK_START.md
echo.
pause