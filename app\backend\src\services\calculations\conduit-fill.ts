import { Decimal } from 'decimal.js';
import { 
  EMT_CONDUIT_ID,
  THHN_WIRE_AREA,
  CONDUIT_FILL_PERCENT
} from '@electrical/shared';

interface ConduitFillInput {
  conduit_type: 'EMT' | 'RMC' | 'PVC' | 'LFMC' | 'LFNC';
  conduit_size: string;
  conductors: Array<{
    size: string;
    type: string;
    quantity: number;
  }>;
}

interface ConduitFillResult {
  conduit_type: string;
  conduit_size: string;
  conduit_area_sq_in: number;
  total_conductors: number;
  conductor_details: Array<{
    size: string;
    type: string;
    quantity: number;
    area_each: number;
    total_area: number;
  }>;
  total_conductor_area: number;
  fill_percent_allowed: number;
  fill_percent_actual: number;
  allowable_area: number;
  passes_nec: boolean;
  remaining_area: number;
  necReferences: string[];
  recommendations: string[];
}

export class ConduitFillService {
  async calculate(input: ConduitFillInput): Promise<ConduitFillResult> {
    const necRefs: string[] = ['Chapter 9, Table 1', 'Chapter 9, Table 4', 'Chapter 9, Table 5'];
    const recommendations: string[] = [];
    
    // Get conduit area (using EMT as example - in real app, would have all types)
    const conduitArea = this.getConduitArea(input.conduit_type, input.conduit_size);
    
    if (!conduitArea) {
      throw new Error(`Unknown conduit size: ${input.conduit_size}`);
    }
    
    // Calculate total conductors and area
    let totalConductors = 0;
    let totalConductorArea = new Decimal(0);
    const conductorDetails: ConduitFillResult['conductor_details'] = [];
    
    for (const conductor of input.conductors) {
      const wireArea = THHN_WIRE_AREA[conductor.size as keyof typeof THHN_WIRE_AREA];
      
      if (!wireArea) {
        throw new Error(`Unknown wire size: ${conductor.size}`);
      }
      
      const area = new Decimal(wireArea);
      const totalArea = area.times(conductor.quantity);
      
      totalConductors += conductor.quantity;
      totalConductorArea = totalConductorArea.plus(totalArea);
      
      conductorDetails.push({
        size: conductor.size,
        type: conductor.type,
        quantity: conductor.quantity,
        area_each: area.toNumber(),
        total_area: totalArea.toNumber()
      });
    }
    
    // Determine fill percentage allowed
    let fillPercentAllowed: number;
    if (totalConductors === 1) {
      fillPercentAllowed = CONDUIT_FILL_PERCENT[1];
    } else if (totalConductors === 2) {
      fillPercentAllowed = CONDUIT_FILL_PERCENT[2];
    } else {
      fillPercentAllowed = CONDUIT_FILL_PERCENT.OVER_2;
    }
    
    // Calculate allowable area and actual fill
    const allowableArea = new Decimal(conduitArea).times(fillPercentAllowed);
    const fillPercentActual = totalConductorArea.dividedBy(conduitArea).times(100);
    const remainingArea = allowableArea.minus(totalConductorArea);
    const passesNEC = fillPercentActual.lessThanOrEqualTo(fillPercentAllowed * 100);
    
    // Generate recommendations
    if (!passesNEC) {
      recommendations.push(`Conduit fill exceeds NEC limit of ${(fillPercentAllowed * 100).toFixed(0)}%`);
      
      // Calculate required conduit size
      const requiredSize = this.calculateRequiredConduitSize(
        input.conduit_type,
        totalConductorArea.toNumber(),
        totalConductors
      );
      
      if (requiredSize) {
        recommendations.push(`Recommended conduit size: ${requiredSize}`);
      } else {
        recommendations.push('Consider splitting conductors into multiple conduits');
      }
    } else if (fillPercentActual.greaterThan(fillPercentAllowed * 80)) {
      recommendations.push('Conduit fill is approaching the limit');
      recommendations.push('Consider upsizing for easier wire pulling');
    } else {
      recommendations.push('Conduit fill is within acceptable limits');
      
      // Calculate how many more conductors could fit
      if (conductorDetails.length > 0) {
        const avgConductorArea = totalConductorArea.dividedBy(totalConductors);
        const additionalConductors = remainingArea.dividedBy(avgConductorArea).floor();
        
        if (additionalConductors.greaterThan(0)) {
          recommendations.push(`Can fit approximately ${additionalConductors.toNumber()} more conductors of average size`);
        }
      }
    }
    
    return {
      conduit_type: input.conduit_type,
      conduit_size: input.conduit_size,
      conduit_area_sq_in: conduitArea,
      total_conductors: totalConductors,
      conductor_details: conductorDetails,
      total_conductor_area: totalConductorArea.toNumber(),
      fill_percent_allowed: fillPercentAllowed * 100,
      fill_percent_actual: fillPercentActual.toNumber(),
      allowable_area: allowableArea.toNumber(),
      passes_nec: passesNEC,
      remaining_area: remainingArea.toNumber(),
      necReferences: necRefs,
      recommendations
    };
  }
  
  private getConduitArea(type: string, size: string): number | null {
    // In real app, would have tables for all conduit types
    if (type === 'EMT') {
      return EMT_CONDUIT_ID[size as keyof typeof EMT_CONDUIT_ID] 
        ? Math.PI * Math.pow(EMT_CONDUIT_ID[size as keyof typeof EMT_CONDUIT_ID] / 2, 2)
        : null;
    }
    
    // Simplified - using EMT values for all types
    return EMT_CONDUIT_ID[size as keyof typeof EMT_CONDUIT_ID]
      ? Math.PI * Math.pow(EMT_CONDUIT_ID[size as keyof typeof EMT_CONDUIT_ID] / 2, 2)
      : null;
  }
  
  private calculateRequiredConduitSize(
    type: string,
    totalConductorArea: number,
    conductorCount: number
  ): string | null {
    const fillPercent = conductorCount === 1 ? CONDUIT_FILL_PERCENT[1] :
                       conductorCount === 2 ? CONDUIT_FILL_PERCENT[2] :
                       CONDUIT_FILL_PERCENT.OVER_2;
    
    const requiredConduitArea = totalConductorArea / fillPercent;
    
    // Find smallest conduit that works
    const sizes = Object.keys(EMT_CONDUIT_ID);
    for (const size of sizes) {
      const area = this.getConduitArea(type, size);
      if (area && area >= requiredConduitArea) {
        return size;
      }
    }
    
    return null;
  }
}