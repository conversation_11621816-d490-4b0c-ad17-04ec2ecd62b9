import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Environment schema
const envSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  FRONTEND_URL: z.string().default('http://localhost:3000'),
  
  // Database
  DATABASE_URL: z.string().default('file:./database/electrical.db'),
  DATABASE_WAL_MODE: z.string().transform(val => val === 'true').default('true'),
  
  // Redis
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  
  // Authentication
  JWT_SECRET: z.string().min(32),
  JWT_REFRESH_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  
  // External APIs
  GEMINI_API_KEY: z.string().optional(),
  CLAUDE_API_KEY: z.string().optional(),
  
  // File Storage
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // Security
  ENCRYPTION_KEY: z.string().min(32),
  CORS_ORIGINS: z.string().transform(val => val.split(',')).default('http://localhost:3000'),
  API_SIGNING_SECRET: z.string().min(32).optional(),
  USER_SIGNING_SECRET: z.string().min(32).optional(),
  
  // Agent System
  AGENT_REDIS_CHANNEL: z.string().default('electrical:agents'),
  AGENT_MEMORY_TTL: z.string().transform(Number).default('86400'), // 24 hours
  AGENT_MESSAGE_RETENTION: z.string().transform(Number).default('604800'), // 7 days
  
  // Neo4j (for Memory Agent)
  NEO4J_URI: z.string().default('bolt://localhost:7687'),
  NEO4J_USER: z.string().default('neo4j'),
  NEO4J_PASSWORD: z.string().default('electrical123'),
  
  // ChromaDB (for Memory Agent)
  CHROMADB_URL: z.string().default('http://localhost:8000'),
  
  // InfluxDB (for metrics)
  INFLUXDB_URL: z.string().default('http://localhost:8086'),
  INFLUXDB_TOKEN: z.string().optional(),
  INFLUXDB_ORG: z.string().default('electrical_contracting'),
  INFLUXDB_BUCKET: z.string().default('metrics')
});

// Parse and validate environment
const env = envSchema.parse(process.env);

// Export configuration
export const config = {
  // Application
  nodeEnv: env.NODE_ENV,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  port: env.PORT,
  frontendUrl: env.FRONTEND_URL,
  
  // Database
  databaseUrl: env.DATABASE_URL,
  databaseWalMode: env.DATABASE_WAL_MODE,
  
  // Redis
  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD
  },
  
  // Authentication
  jwt: {
    secret: env.JWT_SECRET,
    refreshSecret: env.JWT_REFRESH_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN
  },
  
  // External APIs
  apis: {
    geminiKey: env.GEMINI_API_KEY,
    claudeKey: env.CLAUDE_API_KEY
  },
  
  // File Storage
  upload: {
    dir: env.UPLOAD_DIR,
    maxFileSize: env.MAX_FILE_SIZE
  },
  
  // Rate Limiting
  rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
  rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  
  // Logging
  logLevel: env.LOG_LEVEL,
  logFile: env.LOG_FILE,
  
  // Security
  encryptionKey: env.ENCRYPTION_KEY,
  corsOrigins: env.CORS_ORIGINS,
  security: {
    apiSigningSecret: env.API_SIGNING_SECRET,
    userSigningSecret: env.USER_SIGNING_SECRET
  },
  
  // Agent System
  agents: {
    redisChannel: env.AGENT_REDIS_CHANNEL,
    memoryTtl: env.AGENT_MEMORY_TTL,
    messageRetention: env.AGENT_MESSAGE_RETENTION
  },
  
  // Memory Agent databases
  neo4j: {
    uri: env.NEO4J_URI,
    user: env.NEO4J_USER,
    password: env.NEO4J_PASSWORD
  },
  chromadb: {
    url: env.CHROMADB_URL
  },
  influxdb: {
    url: env.INFLUXDB_URL,
    token: env.INFLUXDB_TOKEN,
    org: env.INFLUXDB_ORG,
    bucket: env.INFLUXDB_BUCKET
  }
} as const;