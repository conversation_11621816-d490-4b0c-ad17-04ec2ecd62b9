import { PrismaClient } from '@prisma/client';
import { enhancedLogger } from '../utils/enhanced-logger';
import { StructuredError } from '../utils/structured-error';
import { ErrorCode } from '../utils/error-codes';

export interface ConnectionManagerConfig {
  maxRetries?: number;
  retryDelay?: number;
  connectionTimeout?: number;
  queryTimeout?: number;
  poolSize?: number;
}

export class DatabaseConnectionManager {
  private prisma?: PrismaClient;
  private config: Required<ConnectionManagerConfig>;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;
  private lastConnectionError?: Error;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(config: ConnectionManagerConfig = {}) {
    this.config = {
      maxRetries: config.maxRetries ?? 5,
      retryDelay: config.retryDelay ?? 5000,
      connectionTimeout: config.connectionTimeout ?? 30000,
      queryTimeout: config.queryTimeout ?? 10000,
      poolSize: config.poolSize ?? 10,
    };
  }

  /**
   * Get Prisma client instance with automatic connection management
   */
  async getClient(): Promise<PrismaClient> {
    if (!this.prisma) {
      await this.connect();
    }

    if (!this.isConnected) {
      throw new StructuredError(
        ErrorCode.DATABASE_CONNECTION_FAILED,
        'Database is not connected',
        {
          context: {
            attempts: this.connectionAttempts,
            lastError: this.lastConnectionError?.message,
          },
        }
      );
    }

    return this.prisma!;
  }

  /**
   * Connect to database with retry logic
   */
  async connect(): Promise<void> {
    if (this.isConnected && this.prisma) {
      return;
    }

    enhancedLogger.info('Attempting to connect to database');

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      this.connectionAttempts = attempt;

      try {
        // Create Prisma client with connection pool settings
        this.prisma = new PrismaClient({
          datasources: {
            db: {
              url: process.env.DATABASE_URL,
            },
          },
          log: [
            { level: 'error', emit: 'event' },
            { level: 'warn', emit: 'event' },
            { level: 'info', emit: 'event' },
            { level: 'query', emit: 'event' },
          ],
        });

        // Setup logging
        this.setupLogging();

        // Test connection with timeout
        await this.testConnection();

        this.isConnected = true;
        this.lastConnectionError = undefined;
        
        enhancedLogger.info('Database connected successfully', {
          attempt,
          totalAttempts: this.config.maxRetries,
        });

        // Start health monitoring
        this.startHealthMonitoring();

        return;
      } catch (error) {
        this.lastConnectionError = error as Error;
        this.isConnected = false;

        enhancedLogger.error(`Database connection attempt ${attempt} failed`, error as Error, {
          attempt,
          maxRetries: this.config.maxRetries,
          willRetry: attempt < this.config.maxRetries,
        });

        if (attempt < this.config.maxRetries) {
          // Calculate exponential backoff delay
          const delay = Math.min(
            this.config.retryDelay * Math.pow(2, attempt - 1),
            30000 // Max 30 seconds
          );

          enhancedLogger.info(`Retrying database connection in ${delay}ms`);
          await this.delay(delay);
        } else {
          // Final attempt failed
          throw new StructuredError(
            ErrorCode.DATABASE_CONNECTION_FAILED,
            'Failed to connect to database after multiple attempts',
            {
              cause: error as Error,
              context: {
                attempts: attempt,
                maxRetries: this.config.maxRetries,
              },
              isOperational: false,
            }
          );
        }
      }
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Connection test timed out after ${this.config.connectionTimeout}ms`));
      }, this.config.connectionTimeout);
    });

    const connectPromise = this.prisma!.$queryRaw`SELECT 1 as connected`;

    await Promise.race([connectPromise, timeoutPromise]);
  }

  /**
   * Setup Prisma event logging
   */
  private setupLogging(): void {
    if (!this.prisma) return;

    this.prisma.$on('error' as any, (e: any) => {
      enhancedLogger.error('Prisma error', new Error(e.message), {
        target: e.target,
        timestamp: e.timestamp,
      });
    });

    this.prisma.$on('warn' as any, (e: any) => {
      enhancedLogger.warn('Prisma warning', {
        message: e.message,
        target: e.target,
        timestamp: e.timestamp,
      });
    });

    if (process.env.NODE_ENV === 'development') {
      this.prisma.$on('info' as any, (e: any) => {
        enhancedLogger.info('Prisma info', {
          message: e.message,
          target: e.target,
          timestamp: e.timestamp,
        });
      });

      this.prisma.$on('query' as any, (e: any) => {
        enhancedLogger.debug('Prisma query', {
          query: e.query,
          params: e.params,
          duration: e.duration,
          target: e.target,
        });
      });
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Check connection health every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.prisma!.$queryRaw`SELECT 1`;
      } catch (error) {
        enhancedLogger.error('Database health check failed', error as Error);
        this.isConnected = false;

        // Attempt to reconnect
        try {
          await this.reconnect();
        } catch (reconnectError) {
          enhancedLogger.fatal('Failed to reconnect to database', {
            error: reconnectError,
          });
        }
      }
    }, 30000);
  }

  /**
   * Reconnect to database
   */
  async reconnect(): Promise<void> {
    enhancedLogger.info('Attempting to reconnect to database');

    // Disconnect first
    await this.disconnect();

    // Reset state
    this.isConnected = false;
    this.connectionAttempts = 0;

    // Reconnect
    await this.connect();
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }

    if (this.prisma) {
      await this.prisma.$disconnect();
      this.prisma = undefined;
      this.isConnected = false;
      enhancedLogger.info('Database disconnected');
    }
  }

  /**
   * Execute a database operation with retry logic
   */
  async executeWithRetry<T>(
    operation: (prisma: PrismaClient) => Promise<T>,
    options: {
      maxRetries?: number;
      retryDelay?: number;
      operationName?: string;
    } = {}
  ): Promise<T> {
    const maxRetries = options.maxRetries ?? 3;
    const retryDelay = options.retryDelay ?? 1000;
    const operationName = options.operationName ?? 'database operation';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const client = await this.getClient();
        return await operation(client);
      } catch (error) {
        const isRetriable = this.isRetriableError(error);

        enhancedLogger.warn(`${operationName} failed (attempt ${attempt}/${maxRetries})`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          attempt,
          maxRetries,
          willRetry: isRetriable && attempt < maxRetries,
        });

        if (!isRetriable || attempt === maxRetries) {
          throw error;
        }

        // Wait before retrying
        await this.delay(retryDelay * attempt);
      }
    }

    // This should never be reached
    throw new Error(`${operationName} failed after ${maxRetries} attempts`);
  }

  /**
   * Check if an error is retriable
   */
  private isRetriableError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      // Connection errors
      if (message.includes('connection') || 
          message.includes('timeout') ||
          message.includes('econnrefused') ||
          message.includes('enotfound')) {
        return true;
      }

      // Deadlock errors
      if (message.includes('deadlock') || 
          message.includes('lock timeout')) {
        return true;
      }
    }

    // Prisma specific errors
    if (error && typeof error === 'object' && 'code' in error) {
      const code = (error as any).code;
      // P1001: Can't reach database server
      // P1002: Database server timeout
      // P2024: Timed out fetching a new connection from the pool
      // P2034: Transaction failed due to a write conflict or deadlock
      return ['P1001', 'P1002', 'P2024', 'P2034'].includes(code);
    }

    return false;
  }

  /**
   * Helper to delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get connection status
   */
  getStatus(): {
    isConnected: boolean;
    connectionAttempts: number;
    lastError?: string;
  } {
    return {
      isConnected: this.isConnected,
      connectionAttempts: this.connectionAttempts,
      lastError: this.lastConnectionError?.message,
    };
  }
}

// Export singleton instance
export const dbConnectionManager = new DatabaseConnectionManager();

// Export helper function to get Prisma client
export async function getPrismaClient(): Promise<PrismaClient> {
  return dbConnectionManager.getClient();
}