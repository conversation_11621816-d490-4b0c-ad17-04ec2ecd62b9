#!/usr/bin/env node

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * Generate cryptographically secure random secrets
 */

// Generate a random hex string of specified length
function generateSecret(bytes = 32) {
  return crypto.randomBytes(bytes).toString('hex');
}

// Generate all required secrets
const secrets = {
  // JWT Secrets (32 bytes = 64 hex chars)
  JWT_SECRET: generateSecret(32),
  JWT_REFRESH_SECRET: generateSecret(32),
  
  // Encryption Key (32 bytes minimum)
  ENCRYPTION_KEY: generateSecret(32),
  
  // API Signing Secrets (32 bytes = 64 hex chars)
  API_SIGNING_SECRET: generateSecret(32),
  USER_SIGNING_SECRET: generateSecret(32),
  
  // Database passwords (16 bytes = 32 hex chars)
  REDIS_PASSWORD: generateSecret(16),
  NEO4J_PASSWORD: generateSecret(16),
  
  // InfluxDB Token (32 bytes)
  INFLUXDB_TOKEN: generateSecret(32),
};

// Output format
console.log('=== Generated Secrets ===\n');
console.log('Add these to your .env file:\n');

Object.entries(secrets).forEach(([key, value]) => {
  console.log(`${key}=${value}`);
});

console.log('\n=== Security Notes ===');
console.log('1. Never commit these secrets to version control');
console.log('2. Use different secrets for each environment (dev, staging, prod)');
console.log('3. Rotate secrets periodically (recommended: every 90 days)');
console.log('4. Store production secrets in a secure key management service');
console.log('5. Ensure .env files have restrictive permissions (chmod 600)');

// Option to save to file
const args = process.argv.slice(2);
if (args.includes('--save')) {
  const envPath = path.join(__dirname, '..', '.env.generated');
  const envContent = Object.entries(secrets)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync(envPath, envContent, { mode: 0o600 });
  console.log(`\nSecrets saved to: ${envPath}`);
  console.log('Remember to review and move relevant secrets to your .env file');
}

console.log('\n=== Certificate Pinning Reminder ===');
console.log('Don\'t forget to update SSL certificate hashes in:');
console.log('- /app/mobile/src/services/secureApi.ts');
console.log('- /app/mobile/android/app/src/main/res/xml/network_security_config.xml');
console.log('\nUse the commands in SECURITY_IMPLEMENTATION.md to obtain certificate hashes');