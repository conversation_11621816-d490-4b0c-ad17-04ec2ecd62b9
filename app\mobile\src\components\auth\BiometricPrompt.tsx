import React, { useEffect, useState } from 'react';
import {
  Modal,
  VStack,
  Text,
  Button,
  Icon,
  Center,
  Box,
  HStack,
} from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { BiometryTypes } from 'react-native-biometrics';
import { securityService } from '@services/securityService';

interface BiometricPromptProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  onFallback?: () => void;
  title?: string;
  subtitle?: string;
  fallbackLabel?: string;
}

const BiometricPrompt: React.FC<BiometricPromptProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onFallback,
  title = 'Authenticate',
  subtitle = 'Use biometrics to continue',
  fallbackLabel = 'Use PIN instead',
}) => {
  const [biometryType, setBiometryType] = useState<BiometryTypes | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkBiometryType();
  }, []);

  useEffect(() => {
    if (isOpen && !isAuthenticating) {
      // Auto-trigger biometric authentication when modal opens
      handleBiometricAuth();
    }
  }, [isOpen]);

  const checkBiometryType = async () => {
    const { biometryType } = await securityService.checkBiometricAvailability();
    setBiometryType(biometryType);
  };

  const handleBiometricAuth = async () => {
    setIsAuthenticating(true);
    setError(null);

    try {
      const success = await securityService.authenticateWithBiometrics(subtitle);
      if (success) {
        onSuccess();
        onClose();
      } else {
        setError('Authentication failed. Please try again.');
      }
    } catch (err) {
      setError(err.message || 'Biometric authentication failed');
    } finally {
      setIsAuthenticating(false);
    }
  };

  const getBiometricIcon = () => {
    switch (biometryType) {
      case BiometryTypes.TouchID:
      case BiometryTypes.Biometrics:
        return 'fingerprint';
      case BiometryTypes.FaceID:
        return 'face';
      default:
        return 'lock';
    }
  };

  const getBiometricLabel = () => {
    switch (biometryType) {
      case BiometryTypes.TouchID:
        return 'Touch ID';
      case BiometryTypes.FaceID:
        return 'Face ID';
      case BiometryTypes.Biometrics:
        return 'Fingerprint';
      default:
        return 'Biometrics';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <Modal.Content maxH="300">
        <Modal.Body>
          <VStack space={6} alignItems="center" py={4}>
            <Box
              bg="primary.100"
              p={4}
              borderRadius="full"
              shadow={2}
            >
              <Icon
                as={MaterialIcons}
                name={getBiometricIcon()}
                size={12}
                color="primary.600"
              />
            </Box>

            <VStack space={2} alignItems="center">
              <Text fontSize="xl" fontWeight="bold" color="gray.800">
                {title}
              </Text>
              <Text fontSize="md" color="gray.600" textAlign="center">
                {subtitle}
              </Text>
              {error && (
                <Text fontSize="sm" color="error.500" textAlign="center" mt={2}>
                  {error}
                </Text>
              )}
            </VStack>

            <VStack space={3} w="100%">
              <Button
                onPress={handleBiometricAuth}
                isLoading={isAuthenticating}
                isLoadingText="Authenticating..."
                size="lg"
                startIcon={
                  <Icon
                    as={MaterialIcons}
                    name={getBiometricIcon()}
                    size={5}
                  />
                }
              >
                Use {getBiometricLabel()}
              </Button>

              {onFallback && (
                <Button
                  variant="outline"
                  onPress={() => {
                    onClose();
                    onFallback();
                  }}
                  size="lg"
                >
                  {fallbackLabel}
                </Button>
              )}

              <Button
                variant="ghost"
                onPress={onClose}
                size="lg"
              >
                Cancel
              </Button>
            </VStack>
          </VStack>
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
};

export default BiometricPrompt;