import { AxiosError, AxiosResponse } from 'axios';
import { errorService } from '../services/errorService';

export interface ApiError {
  id: string;
  code: string;
  message: string;
  details?: Array<{
    field?: string;
    message: string;
  }>;
  correlationId?: string;
  requestId?: string;
}

export interface ErrorHandlerOptions {
  showNotification?: boolean;
  logError?: boolean;
  context?: Record<string, any>;
  onError?: (error: ApiError) => void;
}

export class ErrorHandler {
  private static notificationCallback?: (message: string, type: 'error' | 'warning' | 'info') => void;
  private static retryQueue: Map<string, { fn: () => Promise<any>; attempts: number }> = new Map();

  /**
   * Set notification callback for displaying errors to users
   */
  static setNotificationCallback(callback: (message: string, type: 'error' | 'warning' | 'info') => void) {
    this.notificationCallback = callback;
  }

  /**
   * Handle API errors
   */
  static handleApiError(error: unknown, options: ErrorHandlerOptions = {}): ApiError {
    const {
      showNotification = true,
      logError = true,
      context,
      onError,
    } = options;

    let apiError: ApiError;

    if (error instanceof AxiosError && error.response) {
      // Extract error from API response
      apiError = this.extractApiError(error.response);
    } else if (error instanceof Error) {
      // Handle generic errors
      apiError = {
        id: `err_${Date.now()}`,
        code: 'CLIENT_ERROR',
        message: error.message || 'An unexpected error occurred',
      };
    } else {
      // Handle unknown errors
      apiError = {
        id: `err_${Date.now()}`,
        code: 'UNKNOWN_ERROR',
        message: 'An unknown error occurred',
      };
    }

    // Log error
    if (logError) {
      const errorId = errorService.logError(error, {
        ...context,
        apiError,
      });
      apiError.id = errorId;
    }

    // Show notification
    if (showNotification && this.notificationCallback) {
      this.notificationCallback(apiError.message, 'error');
    }

    // Call custom error handler
    if (onError) {
      onError(apiError);
    }

    return apiError;
  }

  /**
   * Extract API error from Axios response
   */
  private static extractApiError(response: AxiosResponse): ApiError {
    const data = response.data;

    if (data?.error && typeof data.error === 'object') {
      return {
        id: data.error.id || `err_${Date.now()}`,
        code: data.error.code || 'API_ERROR',
        message: data.error.message || 'An error occurred',
        details: data.error.details,
        correlationId: data.error.correlationId,
        requestId: data.error.requestId,
      };
    }

    // Fallback for non-standard error responses
    return {
      id: `err_${Date.now()}`,
      code: `HTTP_${response.status}`,
      message: this.getHttpErrorMessage(response.status),
    };
  }

  /**
   * Get user-friendly HTTP error message
   */
  private static getHttpErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: 'Invalid request. Please check your input.',
      401: 'Please log in to continue.',
      403: 'You do not have permission to perform this action.',
      404: 'The requested resource was not found.',
      409: 'This action conflicts with existing data.',
      422: 'The provided data could not be processed.',
      429: 'Too many requests. Please try again later.',
      500: 'An internal server error occurred.',
      502: 'The server is temporarily unavailable.',
      503: 'The service is currently unavailable.',
    };

    return messages[status] || `An error occurred (HTTP ${status})`;
  }

  /**
   * Check if error is retriable
   */
  static isRetriableError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      // Network errors
      if (!error.response) {
        return true;
      }

      // Server errors that might be temporary
      const retriableStatuses = [408, 429, 500, 502, 503, 504];
      return retriableStatuses.includes(error.response.status);
    }

    return false;
  }

  /**
   * Retry failed request with exponential backoff
   */
  static async retryRequest<T>(
    fn: () => Promise<T>,
    options: {
      maxAttempts?: number;
      initialDelay?: number;
      maxDelay?: number;
      onRetry?: (attempt: number, delay: number) => void;
    } = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      initialDelay = 1000,
      maxDelay = 10000,
      onRetry,
    } = options;

    let lastError: unknown;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;

        if (!this.isRetriableError(error) || attempt === maxAttempts) {
          throw error;
        }

        // Calculate exponential backoff delay
        const delay = Math.min(initialDelay * Math.pow(2, attempt - 1), maxDelay);

        if (onRetry) {
          onRetry(attempt, delay);
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * Create error boundary wrapper for async operations
   */
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    options: ErrorHandlerOptions = {}
  ): Promise<T | null> {
    try {
      return await fn();
    } catch (error) {
      this.handleApiError(error, options);
      return null;
    }
  }

  /**
   * Handle form validation errors
   */
  static handleValidationErrors(error: ApiError): Record<string, string> {
    const fieldErrors: Record<string, string> = {};

    if (error.details && Array.isArray(error.details)) {
      for (const detail of error.details) {
        if (detail.field) {
          fieldErrors[detail.field] = detail.message;
        }
      }
    }

    return fieldErrors;
  }

  /**
   * Check if error is due to network issues
   */
  static isNetworkError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return !error.response && error.code === 'ERR_NETWORK';
    }
    return false;
  }

  /**
   * Check if error is due to timeout
   */
  static isTimeoutError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return error.code === 'ECONNABORTED' || error.code === 'ERR_TIMEOUT';
    }
    return false;
  }

  /**
   * Get retry after value from error response
   */
  static getRetryAfter(error: unknown): number | null {
    if (error instanceof AxiosError && error.response) {
      const retryAfter = error.response.headers['retry-after'];
      if (retryAfter) {
        const seconds = parseInt(retryAfter, 10);
        return isNaN(seconds) ? null : seconds * 1000;
      }
    }
    return null;
  }
}

// Export convenience functions
export const handleApiError = ErrorHandler.handleApiError.bind(ErrorHandler);
export const retryRequest = ErrorHandler.retryRequest.bind(ErrorHandler);
export const wrapAsync = ErrorHandler.wrapAsync.bind(ErrorHandler);
export const isNetworkError = ErrorHandler.isNetworkError.bind(ErrorHandler);
export const isTimeoutError = ErrorHandler.isTimeoutError.bind(ErrorHandler);