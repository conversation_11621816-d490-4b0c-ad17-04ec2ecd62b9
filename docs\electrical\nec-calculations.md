# NEC Calculations Documentation

This document provides detailed explanations of the electrical calculations implemented in the application, following the 2023 National Electrical Code (NEC) standards.

## 📐 Wire Size Calculations

### Overview
Wire size calculations determine the appropriate conductor size based on load requirements while ensuring safety and code compliance.

### Key Factors

1. **Ampacity** (NEC Article 310)
   - Base ampacity from Table 310.16
   - Temperature corrections (Table 310.15(B)(1))
   - Adjustment factors for multiple conductors (Table 310.15(C)(1))

2. **Voltage Drop** (NEC 210.19(A) Informational Note 4)
   - Branch circuits: 3% maximum recommended
   - Feeders: 3% maximum recommended
   - Combined: 5% maximum total

3. **Continuous Loads** (NEC 210.19(A)(1))
   - Conductors must be sized at 125% of continuous loads

### Calculation Method

```typescript
// Core wire sizing algorithm
function calculateWireSize(params: WireSizeParams): WireSizeResult {
  const {
    current,
    voltage,
    distance,
    phase,
    wireType,
    temperature,
    ambientTemp,
    conductorCount,
    loadType
  } = params;

  // Step 1: Adjust for continuous load
  const adjustedCurrent = loadType === 'continuous' 
    ? current * 1.25 
    : current;

  // Step 2: Get base ampacity from NEC tables
  let wireSize = getMinimumWireSize(adjustedCurrent, wireType, temperature);
  
  // Step 3: Apply temperature correction
  const tempCorrection = getTemperatureCorrection(temperature, ambientTemp);
  
  // Step 4: Apply adjustment for multiple conductors
  const conductorAdjustment = getConductorAdjustment(conductorCount);
  
  // Step 5: Calculate derated ampacity
  const deratedAmpacity = baseAmpacity * tempCorrection * conductorAdjustment;
  
  // Step 6: Verify wire size meets derated requirements
  while (deratedAmpacity < adjustedCurrent) {
    wireSize = getNextLargerSize(wireSize);
    baseAmpacity = getAmpacity(wireSize, wireType, temperature);
    deratedAmpacity = baseAmpacity * tempCorrection * conductorAdjustment;
  }
  
  // Step 7: Check voltage drop
  const voltageDrop = calculateVoltageDrop({
    current,
    wireSize,
    distance,
    voltage,
    phase,
    powerFactor: 0.9
  });
  
  // Step 8: Upsize if voltage drop exceeds limits
  while (voltageDrop.percentage > 3) {
    wireSize = getNextLargerSize(wireSize);
    voltageDrop = calculateVoltageDrop({ ...params, wireSize });
  }
  
  return {
    wireSize,
    ampacity: deratedAmpacity,
    voltageDrop,
    necReferences: ['310.16', '310.15(B)(1)', '210.19(A)']
  };
}
```

### Temperature Correction Factors (Table 310.15(B)(1))

| Ambient Temp °C | 60°C | 75°C | 90°C |
|-----------------|------|------|------|
| 26-30          | 1.00 | 1.00 | 1.00 |
| 31-35          | 0.91 | 0.94 | 0.96 |
| 36-40          | 0.82 | 0.88 | 0.91 |
| 41-45          | 0.71 | 0.82 | 0.87 |
| 46-50          | 0.58 | 0.75 | 0.82 |

### Adjustment Factors (Table 310.15(C)(1))

| Number of Conductors | Adjustment Factor |
|---------------------|-------------------|
| 4-6                 | 80%               |
| 7-9                 | 70%               |
| 10-20               | 50%               |
| 21-30               | 45%               |
| 31-40               | 40%               |
| 41+                 | 35%               |

## ⚡ Voltage Drop Calculations

### Formula for Single-Phase Circuits
```
VD = (2 × L × R × I) / 1000
VD% = (VD / V) × 100

Where:
- VD = Voltage drop in volts
- L = One-way length in feet
- R = Resistance in ohms per 1000 feet
- I = Current in amperes
- V = Source voltage
```

### Formula for Three-Phase Circuits
```
VD = (√3 × L × R × I × cos(θ)) / 1000
VD% = (VD / V) × 100

Where:
- cos(θ) = Power factor
- Other variables same as single-phase
```

### Implementation
```typescript
function calculateVoltageDrop(params: VoltageDropParams): VoltageDropResult {
  const { current, wireSize, distance, voltage, phase, powerFactor = 0.9 } = params;
  
  // Get conductor properties
  const resistance = getConductorResistance(wireSize, params.wireType);
  const reactance = getConductorReactance(wireSize, params.conduitType);
  
  // Calculate impedance
  const impedance = Math.sqrt(
    Math.pow(resistance * powerFactor, 2) + 
    Math.pow(reactance * Math.sqrt(1 - Math.pow(powerFactor, 2)), 2)
  );
  
  // Calculate voltage drop
  let voltageDrop: number;
  
  if (phase === 'single') {
    voltageDrop = (2 * distance * impedance * current) / 1000;
  } else {
    voltageDrop = (Math.sqrt(3) * distance * impedance * current) / 1000;
  }
  
  const percentage = (voltageDrop / voltage) * 100;
  
  return {
    voltageDrop,
    percentage,
    voltageAtLoad: voltage - voltageDrop,
    powerLoss: voltageDrop * current,
    recommendation: percentage > 3 ? 'Consider larger wire size' : 'Acceptable'
  };
}
```

## 🏗️ Conduit Fill Calculations

### NEC Chapter 9 Requirements

Maximum fill percentages:
- 1 conductor: 53%
- 2 conductors: 31%
- 3+ conductors: 40%

### Calculation Process

```typescript
function calculateConduitFill(params: ConduitFillParams): ConduitFillResult {
  const { conduitType, conduitSize, conductors } = params;
  
  // Get conduit area from NEC Table 4
  const conduitArea = getConduitArea(conduitType, conduitSize);
  
  // Calculate total conductor area
  let totalConductorArea = 0;
  const conductorDetails = [];
  
  for (const conductor of conductors) {
    const area = getConductorArea(conductor.size, conductor.type);
    const totalArea = area * conductor.quantity;
    totalConductorArea += totalArea;
    
    conductorDetails.push({
      ...conductor,
      unitArea: area,
      totalArea
    });
  }
  
  // Determine maximum fill percentage
  const totalConductors = conductors.reduce((sum, c) => sum + c.quantity, 0);
  const maxFillPercent = getMaxFillPercent(totalConductors);
  const maxFillArea = conduitArea * (maxFillPercent / 100);
  
  // Calculate actual fill percentage
  const actualFillPercent = (totalConductorArea / conduitArea) * 100;
  
  return {
    conduitArea,
    totalConductorArea,
    actualFillPercent,
    maxFillPercent,
    maxFillArea,
    isAcceptable: actualFillPercent <= maxFillPercent,
    conductorDetails,
    recommendation: actualFillPercent > maxFillPercent 
      ? `Use ${getNextLargerConduit(conduitSize)} conduit`
      : 'Fill is acceptable',
    necReference: 'Chapter 9, Table 1'
  };
}
```

### Conductor Areas (Table 5)

Example areas for THHN conductors:
| Wire Size | Area (sq in) |
|-----------|--------------|
| 14 AWG    | 0.0097       |
| 12 AWG    | 0.0133       |
| 10 AWG    | 0.0211       |
| 8 AWG     | 0.0366       |
| 6 AWG     | 0.0507       |
| 4 AWG     | 0.0824       |

## 📊 Load Calculations (Article 220)

### Residential Load Calculation - Standard Method

```typescript
function calculateResidentialLoad(params: ResidentialLoadParams): LoadCalculationResult {
  const { squareFootage, smallAppliances, laundry, fixedAppliances } = params;
  
  // Step 1: General lighting load (220.12)
  const lightingLoad = squareFootage * 3; // 3 VA per sq ft
  
  // Step 2: Small appliance circuits (220.52(A))
  const smallApplianceLoad = smallAppliances * 1500; // minimum 2 circuits
  
  // Step 3: Laundry circuit (220.52(B))
  const laundryLoad = laundry ? 1500 : 0;
  
  // Step 4: Total general load
  let generalLoad = lightingLoad + smallApplianceLoad + laundryLoad;
  
  // Step 5: Apply demand factors (Table 220.42)
  let demandLoad = 0;
  if (generalLoad <= 3000) {
    demandLoad = generalLoad;
  } else {
    demandLoad = 3000 + (generalLoad - 3000) * 0.35;
  }
  
  // Step 6: Fixed appliances (220.53)
  const applianceLoad = fixedAppliances.reduce((sum, app) => sum + app.watts, 0);
  if (fixedAppliances.length >= 4) {
    demandLoad += applianceLoad * 0.75; // 75% demand factor
  } else {
    demandLoad += applianceLoad;
  }
  
  // Step 7: Heating and cooling (220.60)
  const hvacLoad = Math.max(params.heatingLoad || 0, params.coolingLoad || 0);
  demandLoad += hvacLoad;
  
  // Step 8: Calculate service size
  const voltage = params.voltage || 240;
  const serviceAmps = demandLoad / voltage;
  const serviceSize = getStandardServiceSize(serviceAmps);
  
  return {
    generalLightingLoad: lightingLoad,
    smallApplianceLoad,
    laundryLoad,
    totalGeneralLoad: generalLoad,
    demandFactoredLoad: demandLoad,
    applianceLoad,
    hvacLoad,
    totalDemandLoad: demandLoad,
    calculatedAmps: serviceAmps,
    recommendedServiceSize: serviceSize,
    necReferences: ['220.12', '220.42', '220.52', '220.53', '220.60']
  };
}
```

### Commercial Load Calculation

```typescript
function calculateCommercialLoad(params: CommercialLoadParams): LoadCalculationResult {
  const { 
    occupancyType, 
    squareFootage, 
    receptacles, 
    continuousLoads,
    nonContinuousLoads,
    motorLoads,
    specialLoads
  } = params;
  
  // Step 1: Lighting load from Table 220.12
  const lightingVAperSqFt = getLightingLoad(occupancyType);
  const lightingLoad = squareFootage * lightingVAperSqFt;
  
  // Step 2: Receptacle loads (220.14)
  const receptacleLoad = receptacles * 180; // 180 VA per receptacle
  
  // Step 3: Apply demand factors for receptacles (Table 220.44)
  let receptacleDemand = 0;
  if (receptacleLoad <= 10000) {
    receptacleDemand = receptacleLoad;
  } else {
    receptacleDemand = 10000 + (receptacleLoad - 10000) * 0.5;
  }
  
  // Step 4: Continuous and non-continuous loads
  const continuousTotal = continuousLoads.reduce((sum, load) => sum + load.watts, 0);
  const nonContinuousTotal = nonContinuousLoads.reduce((sum, load) => sum + load.watts, 0);
  
  // Step 5: Motor loads (430.24)
  const largestMotor = Math.max(...motorLoads.map(m => m.flc));
  const motorLoadTotal = motorLoads.reduce((sum, motor) => sum + motor.flc * motor.voltage, 0);
  const motorDemand = motorLoadTotal + (largestMotor * 0.25 * 240); // 125% of largest motor
  
  // Step 6: Calculate total load
  const totalLoad = lightingLoad + receptacleDemand + 
                   (continuousTotal * 1.25) + nonContinuousTotal + 
                   motorDemand + specialLoads;
  
  return {
    lightingLoad,
    receptacleLoad: receptacleDemand,
    continuousLoads: continuousTotal * 1.25,
    nonContinuousLoads: nonContinuousTotal,
    motorLoads: motorDemand,
    specialLoads,
    totalCalculatedLoad: totalLoad,
    serviceSize: getCommercialServiceSize(totalLoad, params.voltage),
    necReferences: ['220.12', '220.14', '220.44', '430.24']
  };
}
```

## ⚙️ Motor Calculations (Article 430)

### Motor Circuit Components

1. **Branch Circuit Conductors**: 125% of motor FLC (430.22)
2. **Overload Protection**: 115-140% of motor FLC (430.32)
3. **Short Circuit Protection**: Per Table 430.52
4. **Disconnecting Means**: 115% of FLC (430.110)

### Implementation

```typescript
function calculateMotorCircuit(params: MotorParams): MotorCircuitResult {
  const { hp, voltage, phase, designLetter, serviceFactor, startingMethod } = params;
  
  // Step 1: Get FLC from Table 430.248/250
  const flc = getMotorFLC(hp, voltage, phase);
  
  // Step 2: Size conductors (430.22)
  const conductorAmps = flc * 1.25;
  const conductorSize = calculateWireSize({
    current: conductorAmps,
    voltage,
    phase,
    loadType: 'motor'
  });
  
  // Step 3: Overload protection (430.32)
  const overloadMin = flc * 1.15;
  const overloadMax = flc * 1.25; // for SF 1.15 or greater
  const overloadSetting = selectOverloadDevice(overloadMin, overloadMax);
  
  // Step 4: Short circuit protection (Table 430.52)
  const maxBreakerPercent = getMaxBreakerPercent(startingMethod, designLetter);
  const maxBreakerSize = flc * maxBreakerPercent;
  const breakerSize = selectStandardBreaker(maxBreakerSize, 'down');
  
  // Step 5: Starter size
  const starterSize = getStarterSize(hp, voltage);
  
  // Step 6: Disconnect sizing (430.110)
  const disconnectAmps = flc * 1.15;
  const disconnectSize = selectDisconnect(disconnectAmps, voltage);
  
  return {
    motorFLC: flc,
    conductorSize: conductorSize.wireSize,
    conductorAmps,
    overloadRange: { min: overloadMin, max: overloadMax },
    overloadSetting,
    breakerSize,
    starterSize,
    disconnectSize,
    necReferences: ['430.22', '430.32', '430.52', '430.110', 'Table 430.248/250']
  };
}
```

## 🌩️ Short Circuit Calculations

### Available Fault Current

```typescript
function calculateShortCircuit(params: ShortCircuitParams): ShortCircuitResult {
  const { 
    transformerKVA,
    transformerImpedance,
    voltage,
    conductorLength,
    conductorSize,
    conductorType
  } = params;
  
  // Step 1: Calculate transformer let-through current
  const transformerFLC = (transformerKVA * 1000) / (voltage * Math.sqrt(3));
  const infiniteBusFault = transformerFLC / (transformerImpedance / 100);
  
  // Step 2: Calculate conductor impedance
  const resistance = getConductorResistance(conductorSize, conductorType);
  const reactance = getConductorReactance(conductorSize);
  
  // Step 3: Calculate total impedance
  const rTotal = (resistance * conductorLength) / 1000;
  const xTotal = (reactance * conductorLength) / 1000;
  const zTotal = Math.sqrt(Math.pow(rTotal, 2) + Math.pow(xTotal, 2));
  
  // Step 4: Calculate fault current
  const faultCurrent = voltage / (Math.sqrt(3) * zTotal);
  
  // Step 5: Apply multiplier for asymmetrical current
  const xrRatio = xTotal / rTotal;
  const asymmetricalFactor = getAsymmetricalFactor(xrRatio);
  const asymmetricalFault = faultCurrent * asymmetricalFactor;
  
  // Step 6: Check equipment ratings
  const equipment = params.equipment || [];
  const adequacyCheck = equipment.map(item => ({
    name: item.name,
    rating: item.aicRating,
    adequate: item.aicRating >= asymmetricalFault,
    margin: ((item.aicRating - asymmetricalFault) / item.aicRating) * 100
  }));
  
  return {
    availableFaultCurrent: faultCurrent,
    asymmetricalFaultCurrent: asymmetricalFault,
    xrRatio,
    transformerContribution: infiniteBusFault,
    adequacyCheck,
    recommendations: adequacyCheck
      .filter(item => !item.adequate)
      .map(item => `${item.name} requires higher AIC rating`),
    necReferences: ['110.9', '110.10']
  };
}
```

## ⚡ Arc Flash Calculations (IEEE 1584)

### Incident Energy Calculation

```typescript
function calculateArcFlash(params: ArcFlashParams): ArcFlashResult {
  const {
    voltage,
    faultCurrent,
    arcDuration,
    workingDistance,
    enclosureType,
    electrodeCfg
  } = params;
  
  // Step 1: Calculate arcing current
  const arcingCurrent = calculateArcingCurrent(faultCurrent, voltage);
  
  // Step 2: Calculate incident energy
  let En: number; // Normalized incident energy
  
  if (voltage <= 1000) {
    // Low voltage equation
    const K1 = getK1(electrodeCfg);
    const K2 = getK2(enclosureType);
    
    En = Math.pow(10, 
      K1 + K2 + 1.081 * Math.log10(arcingCurrent) + 0.0011 * gap
    );
  } else {
    // Medium voltage equation
    En = 4.184 * 0.1 * (0.792 + 0.0792 * Math.log10(arcingCurrent));
  }
  
  // Step 3: Apply distance and time factors
  const incidentEnergy = En * (Math.pow(610 / workingDistance, 2)) * (arcDuration / 0.2);
  
  // Step 4: Determine PPE category
  const ppeCategory = determinePPECategory(incidentEnergy);
  
  // Step 5: Calculate arc flash boundary
  const arcFlashBoundary = calculateArcFlashBoundary(En, arcDuration);
  
  return {
    incidentEnergy,
    ppeCategory,
    arcFlashBoundary,
    arcingCurrent,
    clothingRequirement: getPPERequirements(ppeCategory),
    workingDistance,
    arcDuration,
    calculations: {
      normalizedEnergy: En,
      distanceFactor: Math.pow(610 / workingDistance, 2),
      timeFactor: arcDuration / 0.2
    },
    warning: incidentEnergy > 40 ? 'DANGER: Exceeds Category 4' : null,
    standards: ['IEEE 1584-2018', 'NFPA 70E-2021']
  };
}
```

### PPE Categories (NFPA 70E Table 130.7(C)(15)(a))

| Category | Min Arc Rating | Clothing Required |
|----------|---------------|-------------------|
| 1        | 4 cal/cm²     | Arc-rated shirt and pants |
| 2        | 8 cal/cm²     | Arc-rated shirt, pants, and coveralls |
| 3        | 25 cal/cm²    | Arc-rated shirt, pants, coveralls, and jacket |
| 4        | 40 cal/cm²    | Arc-rated full flash suit |

## 🔌 Grounding Calculations (Article 250)

### Grounding Electrode Conductor (Table 250.66)

```typescript
function calculateGroundingConductor(params: GroundingParams): GroundingResult {
  const { serviceSize, conductorType, electrodeType } = params;
  
  // Step 1: Determine GEC size from Table 250.66
  let gecSize: string;
  const copperEquivalent = conductorType === 'aluminum' 
    ? getAluminumEquivalent(serviceSize) 
    : serviceSize;
  
  if (copperEquivalent <= '2') {
    gecSize = '8';
  } else if (copperEquivalent <= '1/0') {
    gecSize = '6';
  } else if (copperEquivalent <= '3/0') {
    gecSize = '4';
  } else if (copperEquivalent <= '350') {
    gecSize = '2';
  } else if (copperEquivalent <= '600') {
    gecSize = '1/0';
  } else if (copperEquivalent <= '1100') {
    gecSize = '2/0';
  } else {
    gecSize = '3/0';
  }
  
  // Step 2: Apply electrode-specific rules
  if (electrodeType === 'rod' || electrodeType === 'pipe') {
    // 250.66(A) - Not required to be larger than 6 AWG
    gecSize = Math.min(gecSize, '6');
  } else if (electrodeType === 'concrete_encased') {
    // 250.66(B) - Not required to be larger than 4 AWG
    gecSize = Math.min(gecSize, '4');
  }
  
  // Step 3: Equipment grounding conductor (Table 250.122)
  const overcurrentDevice = params.breakerSize;
  const egcSize = getEGCSize(overcurrentDevice);
  
  return {
    groundingElectrodeConductor: gecSize,
    equipmentGroundingConductor: egcSize,
    bondingJumper: gecSize, // Same as GEC per 250.102(C)
    electrodeRequirements: getElectrodeRequirements(electrodeType),
    necReferences: ['250.66', '250.122', '250.102(C)']
  };
}
```

## 🧮 Calculation Validation

### Code Compliance Checks

```typescript
class NECValidator {
  validateCalculation(type: string, inputs: any, results: any): ValidationResult {
    const validators = {
      wire_size: this.validateWireSize,
      voltage_drop: this.validateVoltageDrop,
      conduit_fill: this.validateConduitFill,
      load_calculation: this.validateLoadCalc,
      motor_circuit: this.validateMotorCircuit,
      short_circuit: this.validateShortCircuit,
      arc_flash: this.validateArcFlash,
      grounding: this.validateGrounding
    };
    
    const validator = validators[type];
    if (!validator) {
      throw new Error(`Unknown calculation type: ${type}`);
    }
    
    return validator.call(this, inputs, results);
  }
  
  private validateWireSize(inputs: any, results: any): ValidationResult {
    const issues: ValidationIssue[] = [];
    const warnings: string[] = [];
    
    // Check continuous load adjustment
    if (inputs.loadType === 'continuous' && !inputs.current.includes(1.25)) {
      warnings.push('Continuous loads must be calculated at 125% per 210.19(A)(1)');
    }
    
    // Check voltage drop
    if (results.voltageDrop.percentage > 3) {
      warnings.push(`Voltage drop of ${results.voltageDrop.percentage.toFixed(1)}% exceeds NEC recommendation of 3%`);
    }
    
    // Check temperature rating consistency
    if (inputs.temperature > inputs.terminalTemp) {
      issues.push({
        code: 'TEMP_RATING_MISMATCH',
        message: 'Conductor temperature rating exceeds terminal rating',
        reference: '110.14(C)'
      });
    }
    
    return {
      valid: issues.length === 0,
      issues,
      warnings,
      recommendations: this.generateRecommendations(inputs, results)
    };
  }
}
```

## 📊 Calculation Reports

### Report Generation

```typescript
class CalculationReportGenerator {
  generateReport(calculations: Calculation[]): Report {
    const report = new PDFDocument();
    
    // Header
    report.addHeader({
      title: 'Electrical Calculations Report',
      project: calculations[0].projectName,
      date: new Date(),
      preparedBy: calculations[0].userName,
      necVersion: '2023'
    });
    
    // Executive Summary
    report.addSection('Executive Summary', {
      totalCalculations: calculations.length,
      types: [...new Set(calculations.map(c => c.type))],
      compliance: calculations.every(c => c.compliant) ? 'All Compliant' : 'Issues Found'
    });
    
    // Detailed Calculations
    calculations.forEach((calc, index) => {
      report.addCalculation({
        number: index + 1,
        type: calc.type,
        inputs: calc.inputs,
        results: calc.results,
        necReferences: calc.necReferences,
        timestamp: calc.createdAt
      });
    });
    
    // Code References
    report.addSection('NEC References', {
      articles: this.collectNECReferences(calculations),
      tables: this.collectNECTables(calculations)
    });
    
    // Engineer Stamp Block
    report.addStampBlock({
      lines: 5,
      includeDate: true,
      includeLicense: true
    });
    
    return report;
  }
}
```

## 🔍 Common Pitfalls and Best Practices

### Wire Sizing Pitfalls

1. **Forgetting Continuous Load Adjustment**
   - Always multiply continuous loads by 125%
   - Applies before any derating

2. **Ignoring Voltage Drop**
   - Check voltage drop even if ampacity is adequate
   - Consider future load growth

3. **Missing Temperature Corrections**
   - Ambient temperature affects ampacity
   - Rooftop installations need special consideration

### Best Practices

1. **Documentation**
   - Record all assumptions
   - Include NEC references
   - Show calculation steps

2. **Safety Margins**
   - Consider 80% loading for panels
   - Allow for future expansion
   - Use next standard size up when borderline

3. **Verification**
   - Cross-check critical calculations
   - Verify with manufacturer data
   - Consider local amendments

## 📚 References

- NFPA 70: National Electrical Code (2023)
- IEEE 1584: Guide for Arc Flash Calculations (2018)
- NFPA 70E: Standard for Electrical Safety (2021)
- IEEE 141: Recommended Practice for Electric Power Distribution (Red Book)
- IEEE 242: Recommended Practice for Protection and Coordination (Buff Book)

---

For questions about calculations or to report issues, please contact the development team or consult with a licensed electrical engineer.