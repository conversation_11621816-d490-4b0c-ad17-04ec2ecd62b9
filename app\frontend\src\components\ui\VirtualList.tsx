import React, { useRef, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { clsx } from 'clsx';

interface VirtualListProps<T> {
  items: T[];
  height: number | string;
  itemHeight?: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  estimateSize?: (index: number) => number;
  getItemKey?: (item: T, index: number) => string | number;
  onLoadMore?: () => void;
  isLoading?: boolean;
  hasMore?: boolean;
  emptyMessage?: string;
}

export function VirtualList<T>({
  items,
  height,
  itemHeight = 50,
  renderItem,
  overscan = 5,
  className,
  estimateSize,
  getItemKey,
  onLoadMore,
  isLoading = false,
  hasMore = false,
  emptyMessage = 'No items to display'
}: VirtualListProps<T>) {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: estimateSize || (() => itemHeight),
    overscan,
  });

  const virtualItems = virtualizer.getVirtualItems();

  // Handle infinite scrolling
  const lastItem = virtualItems[virtualItems.length - 1];
  
  React.useEffect(() => {
    if (!lastItem) return;
    
    if (
      lastItem.index >= items.length - 1 &&
      hasMore &&
      !isLoading &&
      onLoadMore
    ) {
      onLoadMore();
    }
  }, [hasMore, items.length, isLoading, lastItem, onLoadMore]);

  if (items.length === 0 && !isLoading) {
    return (
      <div className="flex items-center justify-center p-8 text-gray-500">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className={clsx('overflow-auto', className)}
      style={{ height }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const item = items[virtualItem.index];
          const key = getItemKey
            ? getItemKey(item, virtualItem.index)
            : virtualItem.index;

          return (
            <div
              key={key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              {renderItem(item, virtualItem.index)}
            </div>
          );
        })}
        
        {isLoading && (
          <div
            style={{
              position: 'absolute',
              top: `${virtualizer.getTotalSize()}px`,
              left: 0,
              width: '100%',
              padding: '1rem',
              textAlign: 'center',
            }}
          >
            <div className="inline-flex items-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Loading more...
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Specialized version for table-like layouts
interface VirtualTableProps<T> extends Omit<VirtualListProps<T>, 'renderItem'> {
  columns: Array<{
    key: string;
    header: string;
    width?: string;
    render?: (item: T) => React.ReactNode;
  }>;
  onRowClick?: (item: T, index: number) => void;
  selectedIndex?: number;
}

export function VirtualTable<T extends Record<string, any>>({
  items,
  columns,
  height,
  itemHeight = 48,
  overscan = 5,
  className,
  onRowClick,
  selectedIndex,
  ...props
}: VirtualTableProps<T>) {
  const renderRow = useCallback(
    (item: T, index: number) => (
      <div
        className={clsx(
          'flex items-center border-b border-gray-200 hover:bg-gray-50 cursor-pointer px-4',
          selectedIndex === index && 'bg-blue-50'
        )}
        style={{ height: itemHeight }}
        onClick={() => onRowClick?.(item, index)}
      >
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex-1 truncate"
            style={{ width: column.width }}
          >
            {column.render ? column.render(item) : item[column.key]}
          </div>
        ))}
      </div>
    ),
    [columns, itemHeight, onRowClick, selectedIndex]
  );

  return (
    <div className={className}>
      {/* Table Header */}
      <div className="flex items-center border-b-2 border-gray-300 bg-gray-100 px-4 font-semibold">
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex-1 py-3"
            style={{ width: column.width }}
          >
            {column.header}
          </div>
        ))}
      </div>
      
      {/* Virtual List Body */}
      <VirtualList
        {...props}
        items={items}
        height={height}
        itemHeight={itemHeight}
        renderItem={renderRow}
        overscan={overscan}
        className="border border-gray-200"
      />
    </div>
  );
}

// Hook for managing virtual list state with pagination
export function useVirtualListPagination<T>(
  fetchFn: (page: number) => Promise<{ data: T[]; totalPages: number }>,
  pageSize: number = 20
) {
  const [items, setItems] = React.useState<T[]>([]);
  const [page, setPage] = React.useState(1);
  const [totalPages, setTotalPages] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const loadMore = useCallback(async () => {
    if (isLoading || page > totalPages) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchFn(page);
      setItems((prev) => [...prev, ...result.data]);
      setTotalPages(result.totalPages);
      setPage((prev) => prev + 1);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, isLoading, page, totalPages]);

  const reset = useCallback(() => {
    setItems([]);
    setPage(1);
    setTotalPages(0);
    setError(null);
  }, []);

  React.useEffect(() => {
    loadMore();
  }, []); // Initial load

  return {
    items,
    isLoading,
    error,
    hasMore: page <= totalPages,
    loadMore,
    reset,
  };
}