{"comment": "This shows the corrected Python syntax for the failing hooks", "hooks": {"Stop": [{"comment": "Fixed version 1 - Using try/except blocks properly", "command": "python3", "args": ["-c", "import json, datetime, os; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown'), 'duration': os.environ.get('CLAUDE_SESSION_DURATION', 'unknown'), 'tools_used': os.environ.get('CLAUDE_TOOLS_USED', '').split(',') if os.environ.get('CLAUDE_TOOLS_USED') else []}; log_file = os.path.expanduser('~/.claude/session-stats.json'); existing = []; os.makedirs(os.path.dirname(log_file), exist_ok=True); try:\n    with open(log_file, 'r') as f:\n        existing = json.load(f)\nexcept:\n    existing = []\nexisting.append(stats); with open(log_file, 'w') as f:\n    json.dump(existing[-100:], f, indent=2)"]}, {"comment": "Fixed version 2 - Using conditional expression without try/except", "command": "python3", "args": ["-c", "import json, datetime, os, glob; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'project': os.path.basename(os.getcwd()), 'files_modified': len(glob.glob('.claude/backups/*.bak')), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown')}; report_file = '.claude/project-stats.json'; os.makedirs(os.path.dirname(report_file), exist_ok=True); existing = json.load(open(report_file, 'r')) if os.path.exists(report_file) else []; existing.append(stats); with open(report_file, 'w') as f: json.dump(existing[-50:], f, indent=2)"]}]}, "working_one_liners": {"session_stats": "import json, datetime, os; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown'), 'duration': os.environ.get('CLAUDE_SESSION_DURATION', 'unknown'), 'tools_used': os.environ.get('CLAUDE_TOOLS_USED', '').split(',') if os.environ.get('CLAUDE_TOOLS_USED') else []}; log_file = os.path.expanduser('~/.claude/session-stats.json'); os.makedirs(os.path.dirname(log_file), exist_ok=True); existing = json.load(open(log_file, 'r')) if os.path.exists(log_file) else []; existing.append(stats); json.dump(existing[-100:], open(log_file, 'w'), indent=2)", "project_stats": "import json, datetime, os, glob; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'project': os.path.basename(os.getcwd()), 'files_modified': len(glob.glob('.claude/backups/*.bak')), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown')}; report_file = '.claude/project-stats.json'; os.makedirs(os.path.dirname(report_file), exist_ok=True); existing = json.load(open(report_file, 'r')) if os.path.exists(report_file) else []; existing.append(stats); json.dump(existing[-50:], open(report_file, 'w'), indent=2)"}}