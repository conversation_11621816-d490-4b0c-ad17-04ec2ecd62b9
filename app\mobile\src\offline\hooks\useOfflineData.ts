import { useState, useEffect, useCallback } from 'react';
import { BaseRepository } from '../repositories/BaseRepository';
import { BaseEntity } from '../database/entities';
import { SyncEngine } from '../sync/SyncEngine';
import { NetworkMonitor } from '../sync/NetworkMonitor';

interface UseOfflineDataOptions<T> {
  repository: BaseRepository<T>;
  autoSync?: boolean;
  syncOnMount?: boolean;
  cacheKey?: string;
}

interface UseOfflineDataResult<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: number | null;
  refresh: () => Promise<void>;
  create: (item: Partial<T>) => Promise<T>;
  update: (id: string, item: Partial<T>) => Promise<T | null>;
  remove: (id: string) => Promise<boolean>;
  sync: () => Promise<void>;
}

export function useOfflineData<T extends BaseEntity>(
  options: UseOfflineDataOptions<T>
): UseOfflineDataResult<T> {
  const { repository, autoSync = true, syncOnMount = true } = options;
  
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);

  const syncEngine = SyncEngine.getInstance();
  const networkMonitor = new NetworkMonitor();

  // Load data from local database
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const items = await repository.findAll();
      setData(items);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [repository]);

  // Sync with remote server
  const sync = useCallback(async () => {
    if (!isOnline || isSyncing) return;

    try {
      setIsSyncing(true);
      await syncEngine.sync();
      setLastSyncTime(Date.now());
      await loadData(); // Reload data after sync
    } catch (err) {
      console.error('Sync error:', err);
    } finally {
      setIsSyncing(false);
    }
  }, [isOnline, isSyncing, syncEngine, loadData]);

  // CRUD operations
  const create = useCallback(async (item: Partial<T>): Promise<T> => {
    const created = await repository.create(item as any);
    setData(prev => [...prev, created]);
    
    if (autoSync && isOnline) {
      sync(); // Sync in background
    }
    
    return created;
  }, [repository, autoSync, isOnline, sync]);

  const update = useCallback(async (id: string, item: Partial<T>): Promise<T | null> => {
    const updated = await repository.update(id, item as any);
    
    if (updated) {
      setData(prev => prev.map(d => d.id === id ? updated : d));
      
      if (autoSync && isOnline) {
        sync(); // Sync in background
      }
    }
    
    return updated;
  }, [repository, autoSync, isOnline, sync]);

  const remove = useCallback(async (id: string): Promise<boolean> => {
    const removed = await repository.delete(id);
    
    if (removed) {
      setData(prev => prev.filter(d => d.id !== id));
      
      if (autoSync && isOnline) {
        sync(); // Sync in background
      }
    }
    
    return removed;
  }, [repository, autoSync, isOnline, sync]);

  // Set up network monitoring
  useEffect(() => {
    const handleConnectionChange = (connected: boolean) => {
      setIsOnline(connected);
      
      if (connected && autoSync) {
        sync();
      }
    };

    networkMonitor.addListener('connectionChange', handleConnectionChange);
    setIsOnline(networkMonitor.getIsConnected());

    return () => {
      networkMonitor.removeListener('connectionChange', handleConnectionChange);
    };
  }, [networkMonitor, autoSync, sync]);

  // Set up sync status monitoring
  useEffect(() => {
    const unsubscribe = syncEngine.addSyncListener((status) => {
      setIsSyncing(status.isSyncing);
      
      if (status.lastSyncTime) {
        setLastSyncTime(status.lastSyncTime);
      }
    });

    return unsubscribe;
  }, [syncEngine]);

  // Initial data load
  useEffect(() => {
    loadData();
    
    if (syncOnMount && isOnline) {
      sync();
    }
  }, [loadData, syncOnMount, isOnline, sync]);

  return {
    data,
    loading,
    error,
    isOnline,
    isSyncing,
    lastSyncTime,
    refresh: loadData,
    create,
    update,
    remove,
    sync,
  };
}