import { WireSizeService } from '../wire-size';
import { CacheService } from '../../cache.service';

// Mock the cache service
jest.mock('../../cache.service', () => ({
  CacheService: {
    getCachedCalculation: jest.fn().mockResolvedValue(null),
    cacheCalculation: jest.fn().mockResolvedValue(undefined)
  }
}));

describe('WireSizeService', () => {
  let service: WireSizeService;

  beforeEach(() => {
    service = new WireSizeService();
    jest.clearAllMocks();
  });

  describe('Basic Wire Size Calculations', () => {
    it('should calculate wire size for non-continuous load', async () => {
      const input = {
        load_amps: 20,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.load_amps).toBe(20);
      expect(result.adjusted_amps).toBe(20); // No adjustment for non-continuous
      expect(result.continuous_load_factor).toBe(1.0);
      expect(result.temperature_derating_factor).toBe(1.0); // No derating at 75°F
      expect(result.required_ampacity).toBe(20);
      expect(result.selected_wire_size).toBe('12'); // #12 copper rated for 25A
      expect(result.wire_ampacity).toBe(25);
      expect(result.final_wire_size).toBe('12');
      expect(result.necReferences).toContain('310.16');
    });

    it('should apply 125% factor for continuous loads', async () => {
      const input = {
        load_amps: 20,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: true
      };

      const result = await service.calculate(input);

      expect(result.load_amps).toBe(20);
      expect(result.adjusted_amps).toBe(25); // 20 × 1.25
      expect(result.continuous_load_factor).toBe(1.25);
      expect(result.required_ampacity).toBe(25);
      expect(result.selected_wire_size).toBe('10'); // #10 copper rated for 35A
      expect(result.wire_ampacity).toBe(35);
      expect(result.necReferences).toContain('210.19(A)(1)');
      expect(result.necReferences).toContain('215.2(A)(1)');
    });

    it('should handle three-phase systems', async () => {
      const input = {
        load_amps: 50,
        voltage: 480,
        phase: '3PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.phase).toBeUndefined(); // Phase is in input, not output
      expect(result.selected_wire_size).toBe('8'); // #8 copper rated for 50A
      expect(result.wire_ampacity).toBe(50);
    });
  });

  describe('Conductor Type Selection', () => {
    it('should size copper conductors correctly', async () => {
      const copperTests = [
        { amps: 15, expectedSize: '14', ampacity: 20 },
        { amps: 20, expectedSize: '12', ampacity: 25 },
        { amps: 30, expectedSize: '10', ampacity: 35 },
        { amps: 40, expectedSize: '8', ampacity: 50 },
        { amps: 55, expectedSize: '6', ampacity: 65 },
        { amps: 70, expectedSize: '4', ampacity: 85 },
        { amps: 95, expectedSize: '3', ampacity: 100 },
        { amps: 110, expectedSize: '2', ampacity: 115 },
        { amps: 125, expectedSize: '1', ampacity: 130 },
        { amps: 145, expectedSize: '1/0', ampacity: 150 },
        { amps: 165, expectedSize: '2/0', ampacity: 175 },
        { amps: 195, expectedSize: '3/0', ampacity: 200 }
      ];

      for (const test of copperTests) {
        const input = {
          load_amps: test.amps,
          voltage: 240,
          phase: '1PH' as const,
          conductor_type: 'CU' as const,
          ambient_temp_f: 75,
          terminals_rated_75c: true,
          continuous_load: false
        };

        const result = await service.calculate(input);
        
        expect(result.selected_wire_size).toBe(test.expectedSize);
        expect(result.wire_ampacity).toBe(test.ampacity);
      }
    });

    it('should size aluminum conductors correctly', async () => {
      const aluminumTests = [
        { amps: 15, expectedSize: '12', ampacity: 20 },
        { amps: 25, expectedSize: '10', ampacity: 30 },
        { amps: 35, expectedSize: '8', ampacity: 40 },
        { amps: 45, expectedSize: '6', ampacity: 50 },
        { amps: 60, expectedSize: '4', ampacity: 65 },
        { amps: 70, expectedSize: '3', ampacity: 75 },
        { amps: 85, expectedSize: '2', ampacity: 90 },
        { amps: 95, expectedSize: '1', ampacity: 100 },
        { amps: 115, expectedSize: '1/0', ampacity: 120 }
      ];

      for (const test of aluminumTests) {
        const input = {
          load_amps: test.amps,
          voltage: 240,
          phase: '1PH' as const,
          conductor_type: 'AL' as const,
          ambient_temp_f: 75,
          terminals_rated_75c: true,
          continuous_load: false
        };

        const result = await service.calculate(input);
        
        expect(result.selected_wire_size).toBe(test.expectedSize);
        expect(result.wire_ampacity).toBe(test.ampacity);
      }
    });

    it('should require larger aluminum than copper for same load', async () => {
      const input = {
        load_amps: 50,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const copperResult = await service.calculate(input);
      const aluminumResult = await service.calculate({ ...input, conductor_type: 'AL' as const });

      // Copper #8 = 50A, Aluminum #6 = 50A
      expect(copperResult.selected_wire_size).toBe('8');
      expect(aluminumResult.selected_wire_size).toBe('6');
    });
  });

  describe('Temperature Derating', () => {
    it('should not derate at or below 86°F', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 86,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_factor).toBe(1.0);
      expect(result.required_ampacity).toBe(30);
      expect(result.selected_wire_size).toBe('10');
    });

    it('should apply 94% derating for 87-95°F', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 90,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_factor).toBe(0.94);
      expect(result.required_ampacity).toBeCloseTo(30 / 0.94, 1);
      expect(result.selected_wire_size).toBe('10'); // Still #10 due to margin
      expect(result.necReferences).toContain('310.15(B)(1)');
    });

    it('should apply 88% derating for 96-104°F', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 100,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_factor).toBe(0.88);
      expect(result.required_ampacity).toBeCloseTo(30 / 0.88, 1);
      expect(result.selected_wire_size).toBe('8'); // Upsized due to derating
    });

    it('should apply 82% derating for 105-113°F', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 110,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_factor).toBe(0.82);
      expect(result.required_ampacity).toBeCloseTo(30 / 0.82, 1);
      expect(result.selected_wire_size).toBe('8'); // Upsized due to derating
    });

    it('should apply 75% derating above 113°F', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 120,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.temperature_derating_factor).toBe(0.75);
      expect(result.required_ampacity).toBeCloseTo(30 / 0.75, 1);
      expect(result.selected_wire_size).toBe('8'); // #8 = 50A, adequate for 40A required
    });
  });

  describe('Combined Continuous Load and Temperature Derating', () => {
    it('should apply both factors correctly', async () => {
      const input = {
        load_amps: 20,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 100,
        terminals_rated_75c: true,
        continuous_load: true
      };

      const result = await service.calculate(input);

      // 20A × 1.25 = 25A adjusted
      expect(result.adjusted_amps).toBe(25);
      
      // 25A ÷ 0.88 = 28.4A required
      expect(result.temperature_derating_factor).toBe(0.88);
      expect(result.required_ampacity).toBeCloseTo(25 / 0.88, 1);
      
      // #10 copper = 35A, adequate for 28.4A
      expect(result.selected_wire_size).toBe('10');
    });

    it('should handle extreme combined derating', async () => {
      const input = {
        load_amps: 100,
        voltage: 480,
        phase: '3PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 120,
        terminals_rated_75c: true,
        continuous_load: true
      };

      const result = await service.calculate(input);

      // 100A × 1.25 = 125A adjusted
      expect(result.adjusted_amps).toBe(125);
      
      // 125A ÷ 0.75 = 166.7A required
      expect(result.temperature_derating_factor).toBe(0.75);
      expect(result.required_ampacity).toBeCloseTo(125 / 0.75, 1);
      
      // Need wire rated for at least 167A
      expect(result.selected_wire_size).toBe('2/0'); // 175A
    });
  });

  describe('Voltage Drop Calculations', () => {
    it('should not calculate voltage drop without distance', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.voltage_drop_calculated).toBe(false);
      expect(result.voltage_drop_percent).toBeUndefined();
      expect(result.voltage_drop_passes).toBeUndefined();
      expect(result.minimum_size_for_vd).toBeUndefined();
    });

    it('should calculate voltage drop when distance provided', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false,
        distance: 100
      };

      const result = await service.calculate(input);

      expect(result.voltage_drop_calculated).toBe(true);
      expect(result.voltage_drop_percent).toBeDefined();
      expect(result.voltage_drop_percent).toBeGreaterThan(0);
      expect(result.voltage_drop_passes).toBeDefined();
      expect(result.necReferences).toContain('210.19(A)(1) Note 4');
    });

    it('should upsize wire for voltage drop compliance', async () => {
      const input = {
        load_amps: 20,
        voltage: 120,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false,
        distance: 200, // Long distance will require upsizing
        max_voltage_drop_percent: 0.03
      };

      const result = await service.calculate(input);

      expect(result.voltage_drop_calculated).toBe(true);
      expect(result.selected_wire_size).toBe('12'); // Based on ampacity
      
      // Should upsize for voltage drop
      if (result.voltage_drop_passes === false) {
        expect(result.minimum_size_for_vd).toBeDefined();
        expect(result.final_wire_size).not.toBe(result.selected_wire_size);
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Wire size increased')
        );
      }
    });

    it('should respect custom voltage drop limits', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false,
        distance: 150,
        max_voltage_drop_percent: 0.02 // Stricter 2% limit
      };

      const result = await service.calculate(input);

      expect(result.voltage_drop_calculated).toBe(true);
      
      // Check against 2% limit
      if (result.voltage_drop_percent && result.voltage_drop_percent > 2) {
        expect(result.voltage_drop_passes).toBe(false);
        expect(result.minimum_size_for_vd).toBeDefined();
      }
    });

    it('should handle zero distance', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false,
        distance: 0
      };

      const result = await service.calculate(input);

      expect(result.voltage_drop_calculated).toBe(false);
      expect(result.voltage_drop_percent).toBeUndefined();
    });

    it('should warn when no size meets voltage drop', async () => {
      const input = {
        load_amps: 100,
        voltage: 120,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false,
        distance: 1000, // Very long distance
        max_voltage_drop_percent: 0.03
      };

      const result = await service.calculate(input);

      if (!result.voltage_drop_passes && !result.minimum_size_for_vd) {
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('No standard wire size meets voltage drop requirements')
        );
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('Consider reducing circuit length or using parallel conductors')
        );
      }
    });
  });

  describe('Terminal Temperature Rating', () => {
    it('should note when terminals are 75°C rated', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      // Should not have terminal warning
      expect(result.recommendations).not.toContainEqual(
        expect.stringContaining('Verify terminal temperature ratings')
      );
    });

    it('should warn about terminal ratings when not 75°C', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: false,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.recommendations).toContainEqual(
        expect.stringContaining('Verify terminal temperature ratings')
      );
      expect(result.necReferences).toContain('110.14(C)');
    });
  });

  describe('Recommendations', () => {
    it('should note significant spare capacity', async () => {
      const input = {
        load_amps: 15,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      // #14 = 20A for 15A load, but need to check if it mentions spare capacity
      // Actually, 20A is not > 15 × 1.25 (18.75), so it might select #12 = 25A
      if (result.wire_ampacity > result.required_ampacity * 1.25) {
        expect(result.recommendations).toContainEqual(
          expect.stringContaining('significant spare capacity')
        );
      }
    });

    it('should suggest parallel conductors for large continuous loads', async () => {
      const input = {
        load_amps: 150,
        voltage: 480,
        phase: '3PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: true
      };

      const result = await service.calculate(input);

      expect(result.recommendations).toContainEqual(
        expect.stringContaining('Consider parallel conductors')
      );
      expect(result.necReferences).toContain('310.10(G)');
    });

    it('should provide default recommendation when appropriate', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      if (result.recommendations.length === 1) {
        expect(result.recommendations).toContain('Wire size is appropriate for the application');
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle very small loads', async () => {
      const input = {
        load_amps: 1,
        voltage: 120,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.selected_wire_size).toBe('14'); // Minimum size
      expect(result.wire_ampacity).toBe(20);
    });

    it('should handle very large loads', async () => {
      const input = {
        load_amps: 500,
        voltage: 480,
        phase: '3PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.selected_wire_size).toBe('1000'); // Largest standard size
      expect(result.wire_ampacity).toBe(545);
    });

    it('should throw error when no suitable wire size found', async () => {
      const input = {
        load_amps: 1000, // Exceeds largest wire capacity
        voltage: 480,
        phase: '3PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      await expect(service.calculate(input)).rejects.toThrow('No suitable wire size found');
    });

    it('should handle zero load', async () => {
      const input = {
        load_amps: 0,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result.selected_wire_size).toBe('14'); // Minimum size
      expect(result.required_ampacity).toBe(0);
    });

    it('should handle extreme temperatures', async () => {
      const temperatures = [-20, 0, 32, 150, 200];

      for (const temp of temperatures) {
        const input = {
          load_amps: 30,
          voltage: 240,
          phase: '1PH' as const,
          conductor_type: 'CU' as const,
          ambient_temp_f: temp,
          terminals_rated_75c: true,
          continuous_load: false
        };

        const result = await service.calculate(input);
        
        expect(result.selected_wire_size).toBeDefined();
        expect(result.wire_ampacity).toBeGreaterThan(0);
        
        if (temp > 86) {
          expect(result.temperature_derating_factor).toBeLessThan(1);
        }
      }
    });
  });

  describe('Calculation Steps', () => {
    it('should include all calculation steps', async () => {
      const input = {
        load_amps: 50,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 100,
        terminals_rated_75c: true,
        continuous_load: true,
        distance: 150,
        max_voltage_drop_percent: 0.03
      };

      const result = await service.calculate(input);
      const stepsText = result.calculation_steps.join(' ');

      expect(stepsText).toContain('Continuous load adjustment');
      expect(stepsText).toContain('Temperature derating');
      expect(stepsText).toContain('Required ampacity');
      expect(stepsText).toContain('Selected wire size based on ampacity');
      expect(stepsText).toContain('Voltage drop check');
    });
  });

  describe('NEC References', () => {
    it('should include all relevant NEC references', async () => {
      const input = {
        load_amps: 50,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 100,
        terminals_rated_75c: false,
        continuous_load: true,
        distance: 150
      };

      const result = await service.calculate(input);

      // Continuous load references
      expect(result.necReferences).toContain('210.19(A)(1)');
      expect(result.necReferences).toContain('215.2(A)(1)');
      
      // Temperature derating references
      expect(result.necReferences).toContain('310.15(B)(1)');
      expect(result.necReferences).toContain('Table 310.15(B)(1)(1)');
      
      // Ampacity table references
      expect(result.necReferences).toContain('310.16');
      expect(result.necReferences).toContain('Table 310.16');
      
      // Voltage drop references
      expect(result.necReferences).toContain('210.19(A)(1) Note 4');
      expect(result.necReferences).toContain('215.2(A)(4) Note 2');
      
      // Terminal temperature reference
      expect(result.necReferences).toContain('110.14(C)');
      
      // No duplicates
      const uniqueRefs = [...new Set(result.necReferences)];
      expect(result.necReferences.length).toBe(uniqueRefs.length);
    });
  });

  describe('Caching', () => {
    it('should cache calculation results', async () => {
      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      await service.calculate(input);

      expect(CacheService.cacheCalculation).toHaveBeenCalledWith(
        'wire-size',
        input,
        expect.any(Object)
      );
    });

    it('should return cached results when available', async () => {
      const cachedResult = {
        load_amps: 30,
        adjusted_amps: 30,
        continuous_load_factor: 1.0,
        temperature_derating_factor: 1.0,
        required_ampacity: 30,
        selected_wire_size: '10',
        wire_ampacity: 35,
        voltage_drop_calculated: false,
        final_wire_size: '10',
        necReferences: ['310.16'],
        calculation_steps: ['Test'],
        recommendations: ['Test']
      };

      (CacheService.getCachedCalculation as jest.Mock).mockResolvedValueOnce({
        output: cachedResult
      });

      const input = {
        load_amps: 30,
        voltage: 240,
        phase: '1PH' as const,
        conductor_type: 'CU' as const,
        ambient_temp_f: 75,
        terminals_rated_75c: true,
        continuous_load: false
      };

      const result = await service.calculate(input);

      expect(result).toEqual(cachedResult);
      expect(CacheService.cacheCalculation).not.toHaveBeenCalled();
    });
  });
});