import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { VoltageDropCalculation } from '../../types/electrical';
import { CalculationService } from '../../services/calculationService';
import { COMMON_VOLTAGES, WIRE_SIZES, VOLTAGE_DROP_LIMITS } from '../../constants/electrical';

export const VoltageDropCalculator: React.FC = () => {
  const [calculation, setCalculation] = useState<VoltageDropCalculation>({
    voltage: 120,
    phase: 1,
    current: 0,
    distance: 0,
    wireSize: '12',
    wireType: 'copper',
    conduitType: 'emt',
    temperature: 86,
    powerFactor: 0.9,
    calculatedDrop: 0,
    percentageDrop: 0,
    timestamp: new Date(),
  });

  const [realTimeMode, setRealTimeMode] = useState(true);

  useEffect(() => {
    if (realTimeMode && calculation.current > 0 && calculation.distance > 0) {
      handleCalculate();
    }
  }, [calculation.current, calculation.distance, calculation.voltage, calculation.wireSize, calculation.wireType, calculation.powerFactor]);

  const handleCalculate = () => {
    if (calculation.current <= 0 || calculation.distance <= 0) {
      if (!realTimeMode) {
        Alert.alert('Error', 'Please enter valid current and distance values');
      }
      return;
    }

    const result = CalculationService.calculateVoltageDrop(calculation);
    setCalculation(result);

    // Save for offline access
    CalculationService.saveCalculation('voltage-drop', result);
  };

  const getDropIndicatorColor = () => {
    const { percentageDrop } = calculation;
    if (percentageDrop <= 3) return '#4CAF50';
    if (percentageDrop <= 5) return '#FF9800';
    return '#f44336';
  };

  const renderVoltageDropBar = () => {
    const { percentageDrop } = calculation;
    const barWidth = Math.min(percentageDrop * 20, 100); // Scale to 100% at 5% drop

    return (
      <View style={styles.dropBarContainer}>
        <View style={styles.dropBarBackground}>
          <View 
            style={[
              styles.dropBarFill, 
              { 
                width: `${barWidth}%`,
                backgroundColor: getDropIndicatorColor() 
              }
            ]} 
          />
          <View style={[styles.dropBarMarker, { left: '60%' }]} />
          <Text style={[styles.dropBarLabel, { left: '60%' }]}>3%</Text>
          <View style={[styles.dropBarMarker, { left: '100%' }]} />
          <Text style={[styles.dropBarLabel, { left: '100%' }]}>5%</Text>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Voltage Drop Calculator</Text>
        <Text style={styles.subtitle}>Real-time NEC Compliance Check</Text>
      </View>

      <View style={styles.realTimeToggle}>
        <Text style={styles.realTimeLabel}>Real-time Calculation</Text>
        <TouchableOpacity
          style={[styles.toggleButton, realTimeMode && styles.toggleButtonActive]}
          onPress={() => setRealTimeMode(!realTimeMode)}
        >
          <Text style={[styles.toggleText, realTimeMode && styles.toggleTextActive]}>
            {realTimeMode ? 'ON' : 'OFF'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Circuit Parameters</Text>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Voltage</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.voltage}
                onValueChange={(value) => setCalculation({ ...calculation, voltage: value })}
                style={styles.picker}
              >
                {COMMON_VOLTAGES.RESIDENTIAL.map(v => (
                  <Picker.Item key={v} label={`${v}V`} value={v} />
                ))}
                {COMMON_VOLTAGES.COMMERCIAL.map(v => (
                  <Picker.Item key={v} label={`${v}V`} value={v} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Phase</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.phase}
                onValueChange={(value) => setCalculation({ ...calculation, phase: value as 1 | 3 })}
                style={styles.picker}
              >
                <Picker.Item label="Single Phase" value={1} />
                <Picker.Item label="Three Phase" value={3} />
              </Picker>
            </View>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Current (A)</Text>
            <TextInput
              style={styles.input}
              value={calculation.current.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, current: parseFloat(text) || 0 })}
              keyboardType="numeric"
              placeholder="Enter current"
            />
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Distance (ft)</Text>
            <TextInput
              style={styles.input}
              value={calculation.distance.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, distance: parseFloat(text) || 0 })}
              keyboardType="numeric"
              placeholder="One-way distance"
            />
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Wire Configuration</Text>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Wire Type</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.wireType}
                onValueChange={(value) => setCalculation({ ...calculation, wireType: value as 'copper' | 'aluminum' })}
                style={styles.picker}
              >
                <Picker.Item label="Copper" value="copper" />
                <Picker.Item label="Aluminum" value="aluminum" />
              </Picker>
            </View>
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Wire Size</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.wireSize}
                onValueChange={(value) => setCalculation({ ...calculation, wireSize: value })}
                style={styles.picker}
              >
                {WIRE_SIZES.map(size => (
                  <Picker.Item key={size} label={`#${size} AWG`} value={size} />
                ))}
              </Picker>
            </View>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Power Factor</Text>
            <TextInput
              style={styles.input}
              value={calculation.powerFactor.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, powerFactor: parseFloat(text) || 0.9 })}
              keyboardType="numeric"
              placeholder="0.9"
            />
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Temperature (°F)</Text>
            <TextInput
              style={styles.input}
              value={calculation.temperature.toString()}
              onChangeText={(text) => setCalculation({ ...calculation, temperature: parseInt(text) || 86 })}
              keyboardType="numeric"
              placeholder="86"
            />
          </View>
        </View>
      </View>

      {!realTimeMode && (
        <TouchableOpacity style={styles.calculateButton} onPress={handleCalculate}>
          <Text style={styles.calculateButtonText}>Calculate Voltage Drop</Text>
        </TouchableOpacity>
      )}

      {calculation.calculatedDrop > 0 && (
        <View style={styles.resultSection}>
          <Text style={styles.resultTitle}>Voltage Drop Results</Text>
          
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Voltage Drop:</Text>
            <Text style={[styles.resultValue, { color: getDropIndicatorColor() }]}>
              {calculation.calculatedDrop.toFixed(2)} V
            </Text>
          </View>

          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>Percentage Drop:</Text>
            <Text style={[styles.resultValue, { color: getDropIndicatorColor() }]}>
              {calculation.percentageDrop.toFixed(2)} %
            </Text>
          </View>

          {renderVoltageDropBar()}

          <View style={styles.complianceBox}>
            <Text style={styles.complianceTitle}>NEC Compliance</Text>
            <Text style={styles.complianceText}>
              Branch Circuit Limit: {(VOLTAGE_DROP_LIMITS.BRANCH_CIRCUIT * 100).toFixed(0)}%
            </Text>
            <Text style={styles.complianceText}>
              Feeder + Branch Combined: {(VOLTAGE_DROP_LIMITS.COMBINED * 100).toFixed(0)}%
            </Text>
            {calculation.recommendation && (
              <Text style={styles.recommendationText}>{calculation.recommendation}</Text>
            )}
          </View>

          <View style={styles.voltageInfo}>
            <Text style={styles.voltageInfoText}>
              Voltage at Load: {(calculation.voltage - calculation.calculatedDrop).toFixed(1)} V
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  realTimeToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  realTimeLabel: {
    fontSize: 16,
    color: '#333',
  },
  toggleButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  toggleButtonActive: {
    backgroundColor: '#4CAF50',
  },
  toggleText: {
    color: '#666',
    fontWeight: 'bold',
  },
  toggleTextActive: {
    color: 'white',
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  halfInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  calculateButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 18,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  calculateButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultSection: {
    backgroundColor: 'white',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  resultLabel: {
    fontSize: 16,
    color: '#666',
  },
  resultValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  dropBarContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  dropBarBackground: {
    height: 30,
    backgroundColor: '#e0e0e0',
    borderRadius: 15,
    position: 'relative',
  },
  dropBarFill: {
    height: 30,
    borderRadius: 15,
  },
  dropBarMarker: {
    position: 'absolute',
    top: 0,
    width: 2,
    height: 30,
    backgroundColor: '#666',
  },
  dropBarLabel: {
    position: 'absolute',
    top: 35,
    fontSize: 12,
    color: '#666',
    transform: [{ translateX: -10 }],
  },
  complianceBox: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
  },
  complianceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  complianceText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 14,
    color: '#f44336',
    marginTop: 8,
    fontStyle: 'italic',
  },
  voltageInfo: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  voltageInfoText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
});