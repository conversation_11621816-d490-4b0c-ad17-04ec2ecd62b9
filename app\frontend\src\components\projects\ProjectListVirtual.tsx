import { useState, useCallback, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, Calendar, MapPin, Zap } from 'lucide-react';
import { api } from '../../services/api';
import { Project } from '@electrical/shared';
import { VirtualTable } from '../ui/VirtualList';
import { useDebounce } from '../../utils/debounce';
import { format } from 'date-fns';

interface FilterState {
  status?: string;
  type?: string;
  search?: string;
}

export function ProjectListVirtual() {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<FilterState>({});
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 0,
    total: 0
  });

  const debouncedSearch = useDebounce(filters.search || '', 300);

  // Fetch projects with filters
  const fetchProjects = useCallback(async (page: number = 1, append: boolean = false) => {
    setLoading(true);
    try {
      const response = await api.get('/projects', {
        params: {
          page,
          limit: 100,
          search: debouncedSearch || undefined,
          status: filters.status || undefined,
          type: filters.type || undefined,
          sortBy: 'created_at',
          sortOrder: 'desc'
        }
      });

      const newProjects = response.data.data as Project[];
      setProjects(append ? [...projects, ...newProjects] : newProjects);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearch, filters.status, filters.type]);

  // Load more for infinite scrolling
  const loadMore = useCallback(() => {
    if (pagination.page < pagination.totalPages && !loading) {
      fetchProjects(pagination.page + 1, true);
    }
  }, [pagination, loading, fetchProjects]);

  // Reset and fetch when filters change
  useCallback(() => {
    fetchProjects(1, false);
  }, [debouncedSearch, filters.status, filters.type]);

  const columns = [
    {
      key: 'name',
      header: 'Project Name',
      width: '25%',
      render: (project: Project) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">
            {project.name}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {project.customer.name}
          </div>
        </div>
      )
    },
    {
      key: 'address',
      header: 'Location',
      width: '20%',
      render: (project: Project) => (
        <div className="flex items-center text-sm">
          <MapPin className="h-4 w-4 mr-1 text-gray-400" />
          <span className="text-gray-600 dark:text-gray-300">
            {project.city}, {project.state}
          </span>
        </div>
      )
    },
    {
      key: 'type',
      header: 'Type',
      width: '15%',
      render: (project: Project) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          project.type === 'COMMERCIAL' 
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            : project.type === 'INDUSTRIAL'
            ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
            : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        }`}>
          {project.type}
        </span>
      )
    },
    {
      key: 'status',
      header: 'Status',
      width: '15%',
      render: (project: Project) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          project.status === 'COMPLETED' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : project.status === 'IN_PROGRESS'
            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            : project.status === 'ON_HOLD'
            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
        }`}>
          {project.status.replace('_', ' ')}
        </span>
      )
    },
    {
      key: 'voltage_system',
      header: 'Voltage',
      width: '10%',
      render: (project: Project) => (
        <div className="flex items-center text-sm">
          <Zap className="h-4 w-4 mr-1 text-yellow-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {project.voltage_system}
          </span>
        </div>
      )
    },
    {
      key: 'created_at',
      header: 'Created',
      width: '15%',
      render: (project: Project) => (
        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
          <Calendar className="h-4 w-4 mr-1" />
          {format(new Date(project.created_at), 'MMM d, yyyy')}
        </div>
      )
    }
  ];

  const handleRowClick = (project: Project) => {
    navigate(`/projects/${project.id}`);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={filters.search || ''}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="pl-10 input"
              />
            </div>
          </div>
          
          <select
            value={filters.status || ''}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            className="input"
          >
            <option value="">All Status</option>
            <option value="PLANNING">Planning</option>
            <option value="APPROVED">Approved</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="COMPLETED">Completed</option>
            <option value="ON_HOLD">On Hold</option>
          </select>
          
          <select
            value={filters.type || ''}
            onChange={(e) => setFilters({ ...filters, type: e.target.value })}
            className="input"
          >
            <option value="">All Types</option>
            <option value="RESIDENTIAL">Residential</option>
            <option value="COMMERCIAL">Commercial</option>
            <option value="INDUSTRIAL">Industrial</option>
          </select>
        </div>
        
        {pagination.total > 0 && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Showing {projects.length} of {pagination.total} projects
          </div>
        )}
      </div>

      {/* Virtual Table */}
      <div className="flex-1">
        <VirtualTable
          items={projects}
          columns={columns}
          height="calc(100vh - 280px)"
          itemHeight={64}
          onRowClick={handleRowClick}
          onLoadMore={loadMore}
          hasMore={pagination.page < pagination.totalPages}
          isLoading={loading}
          emptyMessage="No projects found"
          getItemKey={(project) => project.id}
        />
      </div>
    </div>
  );
}