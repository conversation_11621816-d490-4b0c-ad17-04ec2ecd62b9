import { Prisma } from '@prisma/client';

// Query parameter types
export interface AnalyticsQueryParams {
  period?: string;
  startDate?: string;
  endDate?: string;
  projectType?: string;
  status?: string;
  category?: string;
  vendorId?: string;
  projectId?: string;
  date?: string;
}

// Project filters type
export interface ProjectFilters extends Prisma.ProjectWhereInput {
  deleted_at: null;
  type?: string;
  status?: string;
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
}

// Material filters type
export interface MaterialFilters extends Prisma.MaterialItemWhereInput {
  category?: string;
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
}

// KPI Metrics type
export interface KPIMetrics {
  revenue: number;
  grossMargin: number;
  netMargin: number;
  customerAcquisitionCost: number;
  customerLifetimeValue: number;
  projectSuccessRate: number;
  avgProjectDuration: number;
  employeeUtilization: number;
  safetyIncidentRate: number;
}

// Revenue data type
export interface RevenueData {
  date: Date;
  revenue: number;
  profit: number;
  margin: number;
  projectCount: number;
}

// Project analytics type
export interface ProjectAnalytics {
  projectType: string;
  count: number;
  revenue: number;
  totalProfit: number;
  totalDuration: number;
  avgDuration: number;
  profitMargin: number;
}

// Labor analytics type
export interface LaborAnalytics {
  employeeId: string;
  employeeName: string;
  hoursWorked: number;
  regularHours: number;
  overtimeHours: number;
  productivity: number;
  costPerHour: number;
  totalCost: number;
}

// Material analytics type
export interface MaterialAnalytics {
  materialId: string;
  materialName: string;
  category: string;
  totalUsed: number;
  totalCost: number;
  avgPrice: number;
  priceVariance: number;
  vendors: string[];
}

// Calculation analytics type
export interface CalculationAnalytics {
  calculationType: string;
  count: number;
  totalTime: number;
  successCount: number;
  avgCompletionTime: number;
  successRate: number;
  commonIssues: string[];
}

// Project completion prediction type
export interface ProjectCompletionPrediction {
  projectId: string;
  projectName: string;
  predictedCompletion: Date;
  confidence: number;
  riskFactors: string[];
}

// Material price forecast type
export interface MaterialPriceForecast {
  materialId: string;
  materialName: string;
  currentPrice: number;
  predictedPrice30Days: number;
  predictedPrice90Days: number;
  trend: 'up' | 'down';
}

// Demand pattern type
export interface DemandPattern {
  period: string;
  demandLevel: number;
  projectTypes: string[];
  confidence: number;
}

// Risk assessment type
export interface RiskAssessment {
  category: string;
  riskLevel: 'low' | 'medium' | 'high';
  description: string;
  mitigationSteps: string[];
}

// Customer churn prediction type
export interface CustomerChurnPrediction {
  customerId: string;
  customerName: string;
  churnProbability: number;
  riskFactors: string[];
  lastProjectDate: Date;
  totalRevenue: number;
}

// PPE category group type
export interface PPECategoryGroup {
  category: string;
  count: number;
  totalIncidentEnergy: number;
  avgIncidentEnergy: number;
}

// Equipment type group type
export interface EquipmentTypeGroup {
  type: string;
  count: number;
  totalBoundary: number;
  avgBoundary: number;
}

// Safety trend type
export interface SafetyTrend {
  date: string;
  incidents: number;
  highRiskCount: number;
}

// Panel load type
export interface PanelLoad {
  panelId: string;
  panelName: string;
  loadPercentage: number;
  phaseBalance: number;
}

// Load by type
export interface LoadByType {
  loadType: string;
  totalLoad: number;
  percentage: number;
}

// Peak demand pattern
export interface PeakDemandPattern {
  hour: number;
  avgLoad: number;
  peakLoad: number;
}

// Export filters type
export interface ExportFilters {
  [key: string]: unknown;
}