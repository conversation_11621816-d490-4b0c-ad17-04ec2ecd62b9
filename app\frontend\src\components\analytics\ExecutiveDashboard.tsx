import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import type { KPIMetrics, RevenueData } from '../../services/analyticsService';
import { format } from 'date-fns';
import { TrendingUp, TrendingDown, DollarSign, Users, Briefcase, Clock, Shield, Award } from 'lucide-react';

interface ExecutiveDashboardProps {
  dateRange: { start: Date; end: Date };
}

const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({ dateRange }) => {
  // Fetch KPI metrics
  const { data: kpiMetrics, isLoading: kpiLoading } = useQuery({
    queryKey: ['kpi-metrics'],
    queryFn: () => analyticsService.getKPIMetrics(),
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  // Fetch revenue analytics
  const { data: revenueData, isLoading: revenueLoading } = useQuery({
    queryKey: ['revenue-analytics', dateRange],
    queryFn: () => analyticsService.getRevenueAnalytics('daily', dateRange),
  });

  // Fetch project analytics
  const { data: projectData, isLoading: projectLoading } = useQuery({
    queryKey: ['project-analytics', dateRange],
    queryFn: () => analyticsService.getProjectAnalytics({ dateRange }),
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const KPICard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    format: formatFn = (v: any) => v,
    color = 'blue' 
  }: any) => {
    const isPositive = change >= 0;
    const colorClasses = {
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      green: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      purple: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      orange: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    };

    return (
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
              {formatFn(value)}
            </p>
            <div className="mt-2 flex items-center">
              {isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {isPositive ? '+' : ''}{formatPercentage(change)}
              </span>
              <span className="ml-2 text-sm text-gray-500">vs last period</span>
            </div>
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </Card>
    );
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  if (kpiLoading || revenueLoading || projectLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Total Revenue"
          value={kpiMetrics?.revenue || 0}
          change={kpiMetrics?.revenueGrowth || 0}
          icon={DollarSign}
          format={formatCurrency}
          color="blue"
        />
        <KPICard
          title="Gross Margin"
          value={kpiMetrics?.grossMargin || 0}
          change={0.02}
          icon={TrendingUp}
          format={formatPercentage}
          color="green"
        />
        <KPICard
          title="Project Success Rate"
          value={kpiMetrics?.projectSuccessRate || 0}
          change={0.05}
          icon={Award}
          format={formatPercentage}
          color="purple"
        />
        <KPICard
          title="Employee Utilization"
          value={kpiMetrics?.employeeUtilization || 0}
          change={-0.03}
          icon={Users}
          format={formatPercentage}
          color="orange"
        />
      </div>

      {/* Revenue Trend Chart */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Revenue & Profit Trend
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <ComposedChart data={revenueData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tickFormatter={(date) => format(new Date(date), 'MMM dd')}
            />
            <YAxis yAxisId="left" tickFormatter={(value) => `$${value / 1000}k`} />
            <YAxis 
              yAxisId="right" 
              orientation="right" 
              tickFormatter={(value) => `${value}%`}
            />
            <Tooltip
              formatter={(value: any, name: string) => {
                if (name === 'margin') return `${value.toFixed(1)}%`;
                return formatCurrency(value);
              }}
              labelFormatter={(label) => format(new Date(label), 'MMM dd, yyyy')}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="revenue" fill="#3B82F6" name="Revenue" />
            <Bar yAxisId="left" dataKey="profit" fill="#10B981" name="Profit" />
            <Line 
              yAxisId="right" 
              type="monotone" 
              dataKey="margin" 
              stroke="#F59E0B" 
              name="Profit Margin %"
              strokeWidth={2}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Type Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Revenue by Project Type
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={projectData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="revenue"
              >
                {projectData?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(value as number)} />
            </PieChart>
          </ResponsiveContainer>
        </Card>

        {/* Profit Margin by Project Type */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Profit Margin by Project Type
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={projectData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="projectType" />
              <YAxis tickFormatter={(value) => `${value}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Bar dataKey="profitMargin" fill="#10B981">
                {projectData?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Customer Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Customer Metrics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Customer Acquisition Cost</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {formatCurrency(kpiMetrics?.customerAcquisitionCost || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Customer Lifetime Value</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {formatCurrency(kpiMetrics?.customerLifetimeValue || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">CLV:CAC Ratio</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {((kpiMetrics?.customerLifetimeValue || 0) / (kpiMetrics?.customerAcquisitionCost || 1)).toFixed(1)}:1
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Operational Metrics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Avg Project Duration</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {kpiMetrics?.avgProjectDuration || 0} days
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Safety Incident Rate</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {kpiMetrics?.safetyIncidentRate || 0} per 1000 hours
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Net Margin</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {formatPercentage(kpiMetrics?.netMargin || 0)}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ExecutiveDashboard;