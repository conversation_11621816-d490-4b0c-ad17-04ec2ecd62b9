import AsyncStorage from '@react-native-async-storage/async-storage';
import { Panel, Circuit } from '../types/electrical';
import { secureApi } from './secureApi';

const CACHE_KEY_PREFIX = 'panel_cache_';
const OFFLINE_PANELS_KEY = 'offline_panels';

class PanelService {
  // Get panel with offline caching
  async getPanel(panelId: string): Promise<Panel> {
    try {
      // Try to get from API
      const response = await secureApi.get(`/panels/${panelId}`);
      const panel = response.data;
      
      // Cache for offline use
      await this.cachePanel(panel);
      
      return panel;
    } catch (error) {
      // If offline, try to get from cache
      const cachedPanel = await this.getCachedPanel(panelId);
      if (cachedPanel) {
        return cachedPanel;
      }
      throw error;
    }
  }

  // Get all panels for a project
  async getProjectPanels(projectId: string): Promise<Panel[]> {
    try {
      const response = await secureApi.get(`/projects/${projectId}/panels`);
      const panels = response.data;
      
      // Cache all panels
      for (const panel of panels) {
        await this.cachePanel(panel);
      }
      
      return panels;
    } catch (error) {
      // If offline, return cached panels
      const cachedPanels = await this.getCachedProjectPanels(projectId);
      return cachedPanels;
    }
  }

  // Update circuit status
  async updateCircuitStatus(
    panelId: string, 
    circuitId: string, 
    status: Circuit['status']
  ): Promise<Circuit> {
    try {
      const response = await secureApi.patch(
        `/panels/${panelId}/circuits/${circuitId}`,
        { status }
      );
      
      // Update cache
      const panel = await this.getPanel(panelId);
      const circuitIndex = panel.circuits.findIndex(c => c.id === circuitId);
      if (circuitIndex !== -1) {
        panel.circuits[circuitIndex] = response.data;
        await this.cachePanel(panel);
      }
      
      return response.data;
    } catch (error) {
      // Queue for sync when online
      await this.queueOfflineUpdate({
        type: 'circuit_status',
        panelId,
        circuitId,
        data: { status },
        timestamp: new Date().toISOString(),
      });
      
      // Update local cache
      const panel = await this.getCachedPanel(panelId);
      if (panel) {
        const circuit = panel.circuits.find(c => c.id === circuitId);
        if (circuit) {
          circuit.status = status;
          circuit.lastUpdated = new Date();
          await this.cachePanel(panel);
          return circuit;
        }
      }
      
      throw error;
    }
  }

  // Get panel by QR code
  async getPanelByQRCode(qrCode: string): Promise<Panel> {
    try {
      const response = await secureApi.get(`/panels/qr/${qrCode}`);
      const panel = response.data;
      await this.cachePanel(panel);
      return panel;
    } catch (error) {
      // Check cache for QR code
      const allPanels = await this.getAllCachedPanels();
      const panel = allPanels.find(p => p.qrCode === qrCode);
      if (panel) {
        return panel;
      }
      throw error;
    }
  }

  // Cache management
  private async cachePanel(panel: Panel): Promise<void> {
    const key = `${CACHE_KEY_PREFIX}${panel.id}`;
    await AsyncStorage.setItem(key, JSON.stringify(panel));
    
    // Update offline panels list
    const offlinePanels = await this.getOfflinePanelsList();
    if (!offlinePanels.includes(panel.id)) {
      offlinePanels.push(panel.id);
      await AsyncStorage.setItem(OFFLINE_PANELS_KEY, JSON.stringify(offlinePanels));
    }
  }

  private async getCachedPanel(panelId: string): Promise<Panel | null> {
    const key = `${CACHE_KEY_PREFIX}${panelId}`;
    const cached = await AsyncStorage.getItem(key);
    return cached ? JSON.parse(cached) : null;
  }

  private async getCachedProjectPanels(projectId: string): Promise<Panel[]> {
    const allPanels = await this.getAllCachedPanels();
    return allPanels.filter(panel => panel.projectId === projectId);
  }

  private async getAllCachedPanels(): Promise<Panel[]> {
    const panelIds = await this.getOfflinePanelsList();
    const panels: Panel[] = [];
    
    for (const id of panelIds) {
      const panel = await this.getCachedPanel(id);
      if (panel) {
        panels.push(panel);
      }
    }
    
    return panels;
  }

  private async getOfflinePanelsList(): Promise<string[]> {
    const list = await AsyncStorage.getItem(OFFLINE_PANELS_KEY);
    return list ? JSON.parse(list) : [];
  }

  // Offline sync queue
  private async queueOfflineUpdate(update: any): Promise<void> {
    const queueKey = 'panel_offline_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    const updates = queue ? JSON.parse(queue) : [];
    updates.push(update);
    await AsyncStorage.setItem(queueKey, JSON.stringify(updates));
  }

  // Sync offline changes
  async syncOfflineChanges(): Promise<void> {
    const queueKey = 'panel_offline_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    if (!queue) return;

    const updates = JSON.parse(queue);
    const failedUpdates = [];

    for (const update of updates) {
      try {
        if (update.type === 'circuit_status') {
          await secureApi.patch(
            `/panels/${update.panelId}/circuits/${update.circuitId}`,
            update.data
          );
        }
      } catch (error) {
        failedUpdates.push(update);
      }
    }

    // Save failed updates back to queue
    if (failedUpdates.length > 0) {
      await AsyncStorage.setItem(queueKey, JSON.stringify(failedUpdates));
    } else {
      await AsyncStorage.removeItem(queueKey);
    }
  }

  // Clear all cached panels
  async clearCache(): Promise<void> {
    const panelIds = await this.getOfflinePanelsList();
    
    for (const id of panelIds) {
      const key = `${CACHE_KEY_PREFIX}${id}`;
      await AsyncStorage.removeItem(key);
    }
    
    await AsyncStorage.removeItem(OFFLINE_PANELS_KEY);
    await AsyncStorage.removeItem('panel_offline_queue');
  }
}

export const panelService = new PanelService();