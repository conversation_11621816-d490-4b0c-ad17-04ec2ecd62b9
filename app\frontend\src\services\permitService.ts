import { api } from './api';

export interface PermitDocument {
  id: string;
  project_id: string;
  document_type: string;
  document_version: number;
  jurisdiction: string;
  jurisdiction_code?: string;
  title: string;
  description?: string;
  template_id?: string;
  status: string;
  permit_number?: string;
  submission_date?: string;
  approval_date?: string;
  expiration_date?: string;
  rejection_reason?: string;
  inspector_notes?: string;
  form_data: string;
  included_calculations?: string;
  included_panels?: string;
  attachments?: string;
  signature_required: boolean;
  signed_by?: string;
  signature_date?: string;
  signature_data?: string;
  generated_pdf_path?: string;
  generation_date?: string;
  file_size_bytes?: number;
  page_count?: number;
  created_by: string;
  reviewed_by?: string;
  created_at: string;
  updated_at: string;
}

export interface JurisdictionTemplate {
  id: string;
  jurisdiction_name: string;
  jurisdiction_code: string;
  state: string;
  document_type: string;
  template_name: string;
  template_version: string;
  effective_date: string;
  expiration_date?: string;
  form_fields: string;
  required_documents: string;
  fee_schedule?: string;
  nec_edition: string;
  local_amendments?: string;
  department_name?: string;
  department_phone?: string;
  department_email?: string;
  submission_url?: string;
  created_at: string;
  updated_at: string;
}

export interface PermitFormData {
  applicant_name: string;
  applicant_license?: string;
  applicant_phone: string;
  applicant_email: string;
  property_address: string;
  property_city: string;
  property_state: string;
  property_zip: string;
  property_owner: string;
  property_type: string;
  project_type: string;
  project_description: string;
  estimated_cost: number;
  square_footage?: number;
  service_size: number;
  voltage_system: string;
  main_disconnect_type: string;
  panel_count: number;
  total_circuits: number;
  grounding_system: string;
  has_generator: boolean;
  generator_size?: number;
  has_solar: boolean;
  solar_system_size?: number;
  total_connected_load: number;
  total_demand_load: number;
  load_calculation_method: string;
  nec_edition: string;
  local_amendments?: string;
  contractor_name: string;
  contractor_license: string;
  contractor_phone: string;
  contractor_email: string;
  contractor_insurance?: string;
}

export interface InspectionRequestData {
  permit_number: string;
  inspection_type: string;
  requested_date: string;
  requested_time?: string;
  work_completed: string[];
  contractor_name: string;
  contractor_license: string;
  contractor_phone: string;
  onsite_contact?: string;
  onsite_phone?: string;
  special_instructions?: string;
}

export interface CalculationReport {
  panel_name: string;
  calculation: {
    id: string;
    type: string;
    input_data: Record<string, unknown>;
    output_data: Record<string, unknown>;
    nec_references: string[];
    created_at: string;
  };
}

export interface CompileReportsResponse {
  arcFlashReports: CalculationReport[];
  shortCircuitReports: CalculationReport[];
  loadCalculations: CalculationReport[];
}

class PermitService {
  async createPermitDocument(data: {
    project_id: string;
    document_type: string;
    jurisdiction: string;
    jurisdiction_code?: string;
    title: string;
    description?: string;
    template_id?: string;
    form_data: PermitFormData;
  }): Promise<PermitDocument> {
    const response = await api.post('/permit-documents', data);
    return response.data;
  }

  async getProjectPermitDocuments(projectId: string): Promise<PermitDocument[]> {
    const response = await api.get(`/permit-documents/project/${projectId}`);
    return response.data;
  }

  async getLatestDocument(
    projectId: string,
    documentType: string
  ): Promise<PermitDocument | null> {
    const response = await api.get(
      `/permit-documents/project/${projectId}/latest/${documentType}`
    );
    return response.data;
  }

  async updatePermitDocument(
    id: string,
    data: Partial<{
      status: string;
      form_data: PermitFormData;
      included_calculations: string[];
      included_panels: string[];
      attachments: string[];
      inspector_notes: string;
    }>
  ): Promise<PermitDocument> {
    const response = await api.put(`/permit-documents/${id}`, data);
    return response.data;
  }

  async submitPermitDocument(
    id: string,
    data: {
      permit_number?: string;
      submission_date: string;
    }
  ): Promise<PermitDocument> {
    const response = await api.post(`/permit-documents/${id}/submit`, data);
    return response.data;
  }

  async generatePermitApplicationPDF(
    id: string
  ): Promise<{ message: string; path: string; pageCount: number; sizeBytes: number }> {
    const response = await api.post(`/permit-documents/${id}/generate-pdf`);
    return response.data;
  }

  async generateLoadCalculationPDF(
    projectId: string,
    panelId: string
  ): Promise<{ message: string; path: string; pageCount: number; sizeBytes: number }> {
    const response = await api.post('/permit-documents/generate-load-calc-pdf', {
      project_id: projectId,
      panel_id: panelId,
    });
    return response.data;
  }

  async generatePanelSchedulePDF(
    panelId: string
  ): Promise<{ message: string; path: string; pageCount: number; sizeBytes: number }> {
    const response = await api.post('/permit-documents/generate-panel-schedule-pdf', {
      panel_id: panelId,
    });
    return response.data;
  }

  async generateInspectionRequestPDF(
    id: string
  ): Promise<{ message: string; path: string; pageCount: number; sizeBytes: number }> {
    const response = await api.post(`/permit-documents/${id}/generate-inspection-pdf`);
    return response.data;
  }

  async getJurisdictionTemplates(
    state?: string,
    documentType?: string
  ): Promise<JurisdictionTemplate[]> {
    const params = new URLSearchParams();
    if (state) params.append('state', state);
    if (documentType) params.append('document_type', documentType);
    
    const response = await api.get(`/permit-documents/templates?${params.toString()}`);
    return response.data;
  }

  async compileCalculationReports(projectId: string): Promise<CompileReportsResponse> {
    const response = await api.get(`/permit-documents/project/${projectId}/compile-reports`);
    return response.data;
  }

  async addDigitalSignature(
    id: string,
    signatureData: string
  ): Promise<PermitDocument> {
    const response = await api.post(`/permit-documents/${id}/sign`, {
      signature_data: signatureData,
    });
    return response.data;
  }

  async updatePermitStatus(
    id: string,
    data: {
      status: 'APPROVED' | 'REJECTED';
      permit_number?: string;
      approval_date?: string;
      rejection_reason?: string;
      expiration_date?: string;
    }
  ): Promise<PermitDocument> {
    const response = await api.put(`/permit-documents/${id}/status`, data);
    return response.data;
  }

  async uploadAttachments(
    id: string,
    files: File[]
  ): Promise<{ message: string; files: Array<{ filename: string; path: string; size: number }> }> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post(`/permit-documents/${id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async downloadPDF(id: string): Promise<Blob> {
    const response = await api.get(`/permit-documents/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Helper method to parse form data
  parseFormData(formDataString: string): PermitFormData | Record<string, never> {
    try {
      return JSON.parse(formDataString) as PermitFormData;
    } catch {
      return {} as Record<string, never>;
    }
  }

  // Helper method to get document type display name
  getDocumentTypeDisplayName(type: string): string {
    const typeMap: Record<string, string> = {
      PERMIT_APPLICATION: 'Permit Application',
      LOAD_CALC_SUMMARY: 'Load Calculation Summary',
      PANEL_SCHEDULE: 'Panel Schedule',
      ONE_LINE_DIAGRAM: 'One-Line Diagram',
      SHORT_CIRCUIT_REPORT: 'Short Circuit Report',
      ARC_FLASH_REPORT: 'Arc Flash Report',
      INSPECTION_REQUEST: 'Inspection Request',
      AS_BUILT: 'As-Built Documentation',
    };
    return typeMap[type] || type;
  }

  // Helper method to get status badge color
  getStatusColor(status: string): string {
    const statusColors: Record<string, string> = {
      DRAFT: 'gray',
      READY: 'blue',
      SUBMITTED: 'yellow',
      APPROVED: 'green',
      REJECTED: 'red',
      EXPIRED: 'gray',
    };
    return statusColors[status] || 'gray';
  }
}

export const permitService = new PermitService();