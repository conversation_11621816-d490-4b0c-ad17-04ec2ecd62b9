import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Vibration,
  Alert,
  ScrollView,
  TextInput,
} from 'react-native';
import { Camera, useCameraDevice, useCodeScanner } from 'react-native-vision-camera';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { BarcodeResult, ScannedItem, MaterialInfo } from '../../types/barcode';
import { barcodeScannerService } from '../../services/barcodeScannerService';

interface Props {
  visible: boolean;
  mode: 'single' | 'batch' | 'continuous';
  onClose: () => void;
  onScanComplete?: (items: ScannedItem[]) => void;
  estimateId?: string;
  locationId?: string;
}

export const EnhancedBarcodeScanner: React.FC<Props> = ({
  visible,
  mode,
  onClose,
  onScanComplete,
  estimateId,
  locationId,
}) => {
  const [isScanning, setIsScanning] = useState(true);
  const [scannedItems, setScannedItems] = useState<ScannedItem[]>([]);
  const [currentMaterial, setCurrentMaterial] = useState<MaterialInfo | null>(null);
  const [quantity, setQuantity] = useState('1');
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [scanCount, setScanCount] = useState(0);
  
  const device = useCameraDevice('back');
  const scanAnimation = useRef(new Animated.Value(0)).current;
  const sessionRef = useRef<any>(null);

  useEffect(() => {
    if (visible) {
      startSession();
      startScanAnimation();
    } else {
      endSession();
    }
  }, [visible]);

  const startSession = async () => {
    sessionRef.current = await barcodeScannerService.startScanSession(mode);
  };

  const endSession = async () => {
    if (sessionRef.current) {
      await barcodeScannerService.endScanSession();
      sessionRef.current = null;
    }
  };

  const startScanAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(scanAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scanAnimation, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13', 'ean-8', 'upc-a', 'upc-e', 'code-128', 'code-39', 'code-93', 'codabar', 'datamatrix', 'aztec', 'pdf417'],
    onCodeScanned: async (codes) => {
      if (!isScanning || codes.length === 0) return;

      const code = codes[0];
      await handleBarcodeScan({
        type: mapCodeType(code.type),
        value: code.value || '',
        format: code.type || '',
      });
    },
  });

  const mapCodeType = (type: string): BarcodeResult['type'] => {
    const typeMap: Record<string, BarcodeResult['type']> = {
      'qr': 'qr',
      'ean-13': 'ean',
      'ean-8': 'ean',
      'upc-a': 'upc',
      'upc-e': 'upc',
      'code-128': 'code128',
      'code-39': 'code39',
      'datamatrix': 'datamatrix',
      'aztec': 'aztec',
      'pdf417': 'pdf417',
    };
    return typeMap[type] || 'code128';
  };

  const handleBarcodeScan = async (barcode: BarcodeResult) => {
    // Vibrate on scan
    Vibration.vibrate(100);

    // Check if it's a custom QR code
    const qrData = barcodeScannerService.parseQRCode(barcode.value);
    if (qrData) {
      await handleQRCodeData(qrData);
      return;
    }

    // Look up material
    const material = await barcodeScannerService.lookupMaterial(barcode.value);
    
    if (material) {
      setCurrentMaterial(material);
      
      if (mode === 'single') {
        setIsScanning(false);
      } else if (mode === 'continuous') {
        // Auto-add with quantity 1
        await addScannedItem(barcode, material, 1);
      }
    } else {
      // Material not found
      Alert.alert(
        'Material Not Found',
        `Barcode ${barcode.value} not found in database.`,
        [
          { text: 'Manual Entry', onPress: () => handleManualEntry(barcode.value) },
          { text: 'Skip', style: 'cancel' },
        ]
      );
    }
  };

  const handleQRCodeData = async (qrData: any) => {
    switch (qrData.type) {
      case 'material':
        // Handle material QR code
        const material = await barcodeScannerService.lookupMaterial(qrData.data.sku);
        if (material) {
          setCurrentMaterial(material);
        }
        break;
      case 'bundle':
        // Handle bundle QR code
        Alert.alert(
          'Bundle Detected',
          `This QR code contains ${qrData.data.items.length} items. Add all to scan?`,
          [
            { text: 'Add All', onPress: () => addBundleItems(qrData.data.items) },
            { text: 'Cancel', style: 'cancel' },
          ]
        );
        break;
      case 'job':
        // Handle job-specific QR code
        if (qrData.data.materials) {
          Alert.alert(
            'Job Materials',
            `Load ${qrData.data.materials.length} materials for this job?`,
            [
              { text: 'Load', onPress: () => loadJobMaterials(qrData.data.materials) },
              { text: 'Cancel', style: 'cancel' },
            ]
          );
        }
        break;
    }
  };

  const addScannedItem = async (
    barcode: BarcodeResult,
    material: MaterialInfo,
    qty: number
  ) => {
    const scannedItem = await barcodeScannerService.processScan(barcode, qty);
    
    setScannedItems(prev => [...prev, scannedItem]);
    setScanCount(prev => prev + 1);

    // Show toast or feedback
    if (mode === 'continuous') {
      // Brief feedback for continuous mode
      setCurrentMaterial(material);
      setTimeout(() => setCurrentMaterial(null), 1500);
    }
  };

  const handleAddItem = async () => {
    if (!currentMaterial) return;

    const qty = parseFloat(quantity) || 1;
    await addScannedItem(
      { type: 'upc', value: currentMaterial.upc || currentMaterial.sku, format: 'UPC' },
      currentMaterial,
      qty
    );

    // Reset for next scan
    setCurrentMaterial(null);
    setQuantity('1');
    setIsScanning(true);
  };

  const handleManualEntry = async (initialValue?: string) => {
    setManualBarcode(initialValue || '');
    setShowManualEntry(true);
  };

  const submitManualEntry = async () => {
    if (!manualBarcode) return;

    const material = await barcodeScannerService.lookupMaterial(manualBarcode);
    if (material) {
      setCurrentMaterial(material);
      setShowManualEntry(false);
      setManualBarcode('');
    } else {
      Alert.alert('Not Found', 'No material found with this code.');
    }
  };

  const removeScannedItem = (index: number) => {
    setScannedItems(prev => prev.filter((_, i) => i !== index));
    setScanCount(prev => prev - 1);
  };

  const handleComplete = async () => {
    if (estimateId && scannedItems.length > 0) {
      await barcodeScannerService.addToEstimate(estimateId, scannedItems);
    }

    if (locationId && scannedItems.length > 0) {
      await barcodeScannerService.addToInventory(scannedItems, locationId);
    }

    if (onScanComplete) {
      onScanComplete(scannedItems);
    }

    onClose();
  };

  const addBundleItems = async (items: any[]) => {
    for (const item of items) {
      const material = await barcodeScannerService.lookupMaterial(item.sku);
      if (material) {
        await addScannedItem(
          { type: 'qr', value: item.sku, format: 'QR' },
          material,
          item.quantity
        );
      }
    }
  };

  const loadJobMaterials = async (materials: any[]) => {
    for (const mat of materials) {
      const material = await barcodeScannerService.lookupMaterial(mat.sku);
      if (material) {
        await addScannedItem(
          { type: 'qr', value: mat.sku, format: 'QR' },
          material,
          mat.quantity
        );
      }
    }
  };

  const scanLineTranslateY = scanAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  if (!device) {
    return (
      <Modal visible={visible} animationType="slide">
        <View style={styles.container}>
          <Text>No camera device found</Text>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.container}>
        <Camera
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={visible && isScanning}
          codeScanner={codeScanner}
          torch={flashEnabled ? 'on' : 'off'}
        />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Icon name="close" size={24} color="white" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>
            {mode === 'single' ? 'Single Scan' : mode === 'batch' ? 'Batch Scan' : 'Continuous Scan'}
          </Text>
          
          <TouchableOpacity 
            onPress={() => setFlashEnabled(!flashEnabled)} 
            style={styles.headerButton}
          >
            <Icon name={flashEnabled ? 'flash-on' : 'flash-off'} size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Scan Frame */}
        {isScanning && (
          <View style={styles.scanArea}>
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
              
              <Animated.View
                style={[
                  styles.scanLine,
                  { transform: [{ translateY: scanLineTranslateY }] }
                ]}
              />
            </View>
            
            <Text style={styles.scanInstruction}>
              {mode === 'continuous' 
                ? 'Scan items continuously' 
                : 'Align barcode within frame'}
            </Text>
          </View>
        )}

        {/* Current Material Display */}
        {currentMaterial && !isScanning && (
          <View style={styles.materialDisplay}>
            <Text style={styles.materialName}>{currentMaterial.name}</Text>
            <Text style={styles.materialSku}>SKU: {currentMaterial.sku}</Text>
            <Text style={styles.materialPrice}>${currentMaterial.price}</Text>
            
            <View style={styles.quantityRow}>
              <Text style={styles.quantityLabel}>Quantity:</Text>
              <TextInput
                style={styles.quantityInput}
                value={quantity}
                onChangeText={setQuantity}
                keyboardType="numeric"
                autoFocus
              />
              <Text style={styles.unitText}>{currentMaterial.unit}</Text>
            </View>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.button, styles.cancelButton]} 
                onPress={() => {
                  setCurrentMaterial(null);
                  setIsScanning(true);
                }}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.button, styles.addButton]} 
                onPress={handleAddItem}
              >
                <Text style={styles.buttonText}>Add Item</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          {/* Scan Count */}
          <View style={styles.scanCounter}>
            <Icon name="inventory" size={20} color="white" />
            <Text style={styles.scanCountText}>{scanCount} items scanned</Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.bottomButtons}>
            <TouchableOpacity 
              style={styles.manualButton}
              onPress={() => handleManualEntry()}
            >
              <Icon name="keyboard" size={20} color="white" />
              <Text style={styles.bottomButtonText}>Manual</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.historyButton}
              onPress={() => {/* Show scan history */}}
            >
              <Icon name="history" size={20} color="white" />
              <Text style={styles.bottomButtonText}>History</Text>
            </TouchableOpacity>

            {scannedItems.length > 0 && (
              <TouchableOpacity 
                style={styles.completeButton}
                onPress={handleComplete}
              >
                <Icon name="check" size={20} color="white" />
                <Text style={styles.bottomButtonText}>Complete</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Manual Entry Modal */}
        <Modal
          visible={showManualEntry}
          animationType="slide"
          transparent
          onRequestClose={() => setShowManualEntry(false)}
        >
          <View style={styles.manualEntryOverlay}>
            <View style={styles.manualEntryContainer}>
              <Text style={styles.manualEntryTitle}>Enter Barcode</Text>
              
              <TextInput
                style={styles.manualEntryInput}
                value={manualBarcode}
                onChangeText={setManualBarcode}
                placeholder="Enter barcode or SKU"
                autoFocus
              />
              
              <View style={styles.manualEntryButtons}>
                <TouchableOpacity 
                  style={[styles.button, styles.cancelButton]}
                  onPress={() => setShowManualEntry(false)}
                >
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.button, styles.addButton]}
                  onPress={submitManualEntry}
                >
                  <Text style={styles.buttonText}>Search</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Scanned Items List (for batch mode) */}
        {mode === 'batch' && scannedItems.length > 0 && (
          <ScrollView style={styles.scannedItemsList}>
            {scannedItems.map((item, index) => (
              <View key={item.id} style={styles.scannedItem}>
                <View style={styles.scannedItemInfo}>
                  <Text style={styles.scannedItemName}>
                    {item.material?.name || item.barcode}
                  </Text>
                  <Text style={styles.scannedItemQuantity}>
                    Qty: {item.quantity}
                  </Text>
                </View>
                <TouchableOpacity onPress={() => removeScannedItem(index)}>
                  <Icon name="delete" size={20} color="#f44336" />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingBottom: 15,
    paddingHorizontal: 20,
    zIndex: 10,
  },
  headerButton: {
    padding: 10,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 280,
    height: 180,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderColor: '#2196F3',
  },
  topLeft: {
    top: 0,
    left: 0,
    borderTopWidth: 4,
    borderLeftWidth: 4,
  },
  topRight: {
    top: 0,
    right: 0,
    borderTopWidth: 4,
    borderRightWidth: 4,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderBottomWidth: 4,
    borderRightWidth: 4,
  },
  scanLine: {
    position: 'absolute',
    left: 10,
    right: 10,
    height: 2,
    backgroundColor: '#2196F3',
    top: '50%',
  },
  scanInstruction: {
    color: 'white',
    marginTop: 20,
    fontSize: 16,
  },
  materialDisplay: {
    position: 'absolute',
    top: '30%',
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
  },
  materialName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  materialSku: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  materialPrice: {
    fontSize: 20,
    color: '#2196F3',
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quantityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  quantityLabel: {
    fontSize: 16,
    marginRight: 10,
  },
  quantityInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    marginRight: 10,
  },
  unitText: {
    fontSize: 16,
    color: '#666',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#666',
  },
  addButton: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingBottom: 30,
    paddingTop: 15,
    paddingHorizontal: 20,
  },
  scanCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  scanCountText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
  },
  bottomButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  manualButton: {
    alignItems: 'center',
    padding: 10,
  },
  historyButton: {
    alignItems: 'center',
    padding: 10,
  },
  completeButton: {
    alignItems: 'center',
    padding: 10,
  },
  bottomButtonText: {
    color: 'white',
    marginTop: 4,
    fontSize: 12,
  },
  manualEntryOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  manualEntryContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '80%',
  },
  manualEntryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  manualEntryInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  manualEntryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scannedItemsList: {
    position: 'absolute',
    bottom: 140,
    left: 20,
    right: 20,
    maxHeight: 200,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 10,
  },
  scannedItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  scannedItemInfo: {
    flex: 1,
  },
  scannedItemName: {
    fontSize: 14,
    fontWeight: '500',
  },
  scannedItemQuantity: {
    fontSize: 12,
    color: '#666',
  },
});