#!/usr/bin/env python3
import json
import sys

def validate_json_file(filepath):
    try:
        with open(filepath, 'r') as f:
            json.load(f)
        print(f"✓ {filepath} is valid JSON")
        return True
    except json.JSONDecodeError as e:
        print(f"✗ {filepath} has JSON error: {e}")
        return False
    except FileNotFoundError:
        print(f"✗ {filepath} not found")
        return False

if __name__ == "__main__":
    files_to_validate = [
        "/home/<USER>/.claude/settings.json",
        "/mnt/c/Projects/electrical/.claude/settings.json"
    ]
    
    all_valid = True
    for file in files_to_validate:
        if not validate_json_file(file):
            all_valid = False
    
    sys.exit(0 if all_valid else 1)