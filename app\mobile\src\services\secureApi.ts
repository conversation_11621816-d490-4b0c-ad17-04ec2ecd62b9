import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import { API_URL, API_TIMEOUT, API_SIGNING_SECRET } from '@env';
import { store } from '@store/index';
import { clearAuth, refreshToken as refreshAuthToken } from '@store/slices/authSlice';
import { addPendingAction } from '@store/slices/offlineSlice';
import { showToast } from '@store/slices/uiSlice';
import NetInfo from '@react-native-community/netinfo';
import { initializePinning, fetch as sslFetch } from 'react-native-ssl-pinning';
import { securityService } from './securityService';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import CryptoJS from 'react-native-crypto-js';
import { Buffer } from 'buffer';

// Certificate hashes for your API servers
// IMPORTANT: Update these with your actual certificate hashes
// To obtain certificate hashes:
// 1. For the leaf certificate: openssl s_client -servername api.electrical-contractor.com -connect api.electrical-contractor.com:443 | openssl x509 -pubkey -noout | openssl pkey -pubin -outform der | openssl dgst -sha256 -binary | openssl enc -base64
// 2. For the intermediate CA: Get the intermediate certificate and run: openssl x509 -in intermediate.crt -pubkey -noout | openssl pkey -pubin -outform der | openssl dgst -sha256 -binary | openssl enc -base64
// 3. Add both hashes to enable certificate rotation
const SSL_PINS = {
  'api.electrical-contractor.com': {
    includeSubdomains: true,
    publicKeyHashes: [
      // Primary certificate hash (current)
      'sha256/YOUR_PRIMARY_CERT_HASH_HERE=',
      // Backup certificate hash (for rotation)
      'sha256/YOUR_BACKUP_CERT_HASH_HERE=',
      // Intermediate CA hash (as fallback)
      'sha256/YOUR_INTERMEDIATE_CA_HASH_HERE=',
    ],
  },
};

// Certificate rotation strategy:
// 1. Always include at least 2 pins: current cert + backup (next cert or CA)
// 2. When rotating certificates:
//    a. Add new certificate hash to the array
//    b. Deploy app update with both old and new hashes
//    c. Update server certificate
//    d. After all users update, remove old hash in next app version
// 3. Monitor certificate expiration and plan rotation 30 days in advance

interface RequestSignature {
  signature: string;
  timestamp: number;
  nonce: string;
}

class SecureApiService {
  private api: AxiosInstance;
  private initialized = false;
  private nonceCache = new Set<string>();
  private readonly NONCE_CACHE_SIZE = 1000;
  private readonly REQUEST_TIMEOUT_WINDOW = 300000; // 5 minutes

  constructor() {
    this.api = axios.create({
      baseURL: API_URL || 'https://api.electrical-contractor.com',
      timeout: parseInt(API_TIMEOUT || '30000', 10),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.startNonceCacheCleanup();
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    // Initialize SSL pinning
    if (Platform.OS === 'ios') {
      await initializePinning(SSL_PINS);
    }

    this.initialized = true;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      async config => {
        // Add auth token from secure storage
        const { token } = await securityService.getSecureToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add device information
        config.headers['X-Device-Id'] = await DeviceInfo.getUniqueId();
        config.headers['X-Device-Platform'] = Platform.OS;
        config.headers['X-App-Version'] = DeviceInfo.getVersion();

        // Add request signature for additional security
        const signature = await this.generateRequestSignature(config);
        config.headers['X-Signature'] = signature.signature;
        config.headers['X-Timestamp'] = signature.timestamp.toString();
        config.headers['X-Nonce'] = signature.nonce;

        return config;
      },
      error => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      response => {
        // Update last active time on successful requests
        securityService.updateLastActiveTime();
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // Handle network errors
        if (!error.response) {
          const netInfo = await NetInfo.fetch();
          if (!netInfo.isConnected) {
            await this.handleOfflineRequest(originalRequest);
          }
          throw error;
        }

        // Handle 401 errors
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const { refreshToken } = await securityService.getSecureToken();
            if (refreshToken) {
              await store.dispatch(refreshAuthToken()).unwrap();
              const { token } = await securityService.getSecureToken();
              if (token && originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${token}`;
              }
              // Regenerate signature with new token
              const signature = await this.generateRequestSignature(originalRequest);
              originalRequest.headers['X-Signature'] = signature.signature;
              originalRequest.headers['X-Timestamp'] = signature.timestamp.toString();
              originalRequest.headers['X-Nonce'] = signature.nonce;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            await this.handleAuthFailure();
            throw refreshError;
          }
        }

        // Handle 403 errors (forbidden - possible security issue)
        if (error.response?.status === 403) {
          const reason = error.response.data?.reason;
          if (reason === 'device_mismatch') {
            await this.handleDeviceMismatch();
          } else if (reason === 'security_violation') {
            await this.handleSecurityViolation();
          }
        }

        throw error;
      }
    );
  }

  private async generateRequestSignature(config: AxiosRequestConfig): Promise<RequestSignature> {
    // Generate timestamp and nonce
    const timestamp = Date.now();
    const nonce = this.generateNonce();
    
    // Get signing secret from environment or secure storage
    const signingSecret = API_SIGNING_SECRET || await this.getSigningSecret();
    
    // Create signature payload
    const method = config.method?.toUpperCase() || 'GET';
    const url = config.url || '';
    const deviceId = await DeviceInfo.getUniqueId();
    
    // Build message parts
    const parts = [
      method,
      url,
      timestamp.toString(),
      nonce,
      deviceId
    ];
    
    // Add body hash if present
    if (config.data && ['POST', 'PUT', 'PATCH'].includes(method)) {
      const bodyString = typeof config.data === 'string' 
        ? config.data 
        : JSON.stringify(config.data);
      const bodyHash = CryptoJS.SHA256(bodyString).toString();
      parts.push(bodyHash);
    }
    
    // Create HMAC signature
    const message = parts.join('\n');
    const signature = CryptoJS.HmacSHA256(message, signingSecret).toString();
    
    return {
      signature,
      timestamp,
      nonce
    };
  }

  private generateNonce(): string {
    // Generate a cryptographically secure random nonce
    const randomBytes = CryptoJS.lib.WordArray.random(16);
    const nonce = randomBytes.toString();
    
    // Ensure nonce is unique
    if (this.nonceCache.has(nonce)) {
      return this.generateNonce();
    }
    
    // Add to cache
    this.nonceCache.add(nonce);
    
    // Limit cache size
    if (this.nonceCache.size > this.NONCE_CACHE_SIZE) {
      const firstNonce = this.nonceCache.values().next().value;
      this.nonceCache.delete(firstNonce);
    }
    
    return nonce;
  }

  private async getSigningSecret(): Promise<string> {
    // In production, retrieve this from secure storage or device keychain
    // This should be provisioned during app installation/first launch
    const { token } = await securityService.getSecureToken();
    if (!token) {
      throw new Error('No signing secret available');
    }
    
    // Derive a signing key from the auth token
    // In production, use a separate signing secret
    const deviceId = await DeviceInfo.getUniqueId();
    return CryptoJS.HmacSHA256(token, deviceId).toString();
  }

  private startNonceCacheCleanup(): void {
    // Clean up old nonces periodically
    setInterval(() => {
      // In a production app, you might want to track timestamps
      // and only clear nonces older than REQUEST_TIMEOUT_WINDOW
      if (this.nonceCache.size > this.NONCE_CACHE_SIZE / 2) {
        this.nonceCache.clear();
      }
    }, 60000); // Run every minute
  }

  private async handleOfflineRequest(request: AxiosRequestConfig): Promise<void> {
    if (request.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
      store.dispatch(
        addPendingAction({
          type: 'API_REQUEST',
          payload: {
            url: request.url,
            method: request.method,
            data: request.data,
          },
        })
      );
      store.dispatch(
        showToast({
          message: 'Action saved offline. Will sync when connection is restored.',
          type: 'info',
        })
      );
    }
  }

  private async handleAuthFailure(): Promise<void> {
    await securityService.clearSecureTokens();
    await securityService.lockApp();
    store.dispatch(clearAuth());
    store.dispatch(
      showToast({
        message: 'Session expired. Please login again.',
        type: 'error',
      })
    );
  }

  private async handleDeviceMismatch(): Promise<void> {
    await securityService.clearAllSecurityData();
    store.dispatch(clearAuth());
    store.dispatch(
      showToast({
        message: 'Security alert: Device mismatch detected. Please login again.',
        type: 'error',
      })
    );
  }

  private async handleSecurityViolation(): Promise<void> {
    await securityService.clearAllSecurityData();
    store.dispatch(clearAuth());
    store.dispatch(
      showToast({
        message: 'Security violation detected. Your account has been locked for protection.',
        type: 'error',
      })
    );
  }

  // Secure fetch with SSL pinning
  async secureFetch(url: string, options?: RequestInit): Promise<Response> {
    if (Platform.OS === 'ios') {
      // Use SSL pinning for iOS
      return sslFetch(url, {
        ...options,
        sslPinning: SSL_PINS,
      });
    } else {
      // For Android, use certificate pinning through network security config
      // Configure in android/app/src/main/res/xml/network_security_config.xml
      return fetch(url, options);
    }
  }

  getAxiosInstance(): AxiosInstance {
    return this.api;
  }
}

export const secureApi = new SecureApiService();
export default secureApi.getAxiosInstance();