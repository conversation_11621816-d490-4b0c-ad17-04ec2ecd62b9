import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Trash2, Calculator, Save, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { Customer, Project } from '@electrical/shared';

const lineItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  material_id: z.string().optional(),
  quantity: z.number().positive('Quantity must be positive'),
  unit_price: z.number().min(0, 'Price must be non-negative'),
  labor_hours: z.number().min(0, 'Labor hours must be non-negative'),
  labor_rate: z.number().min(0, 'Labor rate must be non-negative'),
});

const estimateSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  project_id: z.string().optional(),
  estimate_number: z.string().min(1, 'Estimate number is required'),
  description: z.string().optional(),
  valid_until: z.string().min(1, 'Valid until date is required'),
  labor_rate: z.number().positive('Labor rate must be positive'),
  markup_percent: z.number().min(0).max(100),
  tax_rate: z.number().min(0).max(100),
  notes: z.string().optional(),
  items: z.array(lineItemSchema).min(1, 'At least one item is required'),
});

type EstimateFormData = z.infer<typeof estimateSchema>;

interface EstimateFormProps {
  onClose: () => void;
  onSuccess?: () => void;
}

export function EstimateForm({ onClose, onSuccess }: EstimateFormProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EstimateFormData>({
    resolver: zodResolver(estimateSchema),
    defaultValues: {
      estimate_number: `EST-${Date.now()}`,
      valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      labor_rate: 85,
      markup_percent: 35,
      tax_rate: 8.25,
      items: [{ description: '', quantity: 1, unit_price: 0, labor_hours: 0, labor_rate: 85 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  const watchItems = watch('items');
  const markupPercent = watch('markup_percent');
  const taxRate = watch('tax_rate');

  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      fetchProjects(selectedCustomer);
    }
  }, [selectedCustomer]);

  const fetchCustomers = async () => {
    try {
      const response = await api.get('/customers');
      // The API returns { data: customers[], pagination: {...} }
      setCustomers(response.data.data || []);
    } catch (error) {
      toast.error('Failed to load customers');
    }
  };

  const fetchProjects = async (customerId: string) => {
    try {
      const response = await api.get(`/projects?customer_id=${customerId}`);
      setProjects(response.data);
    } catch (error) {
      toast.error('Failed to load projects');
    }
  };

  const calculateTotals = () => {
    const subtotal = watchItems.reduce((sum, item) => {
      const materialCost = item.quantity * item.unit_price;
      const laborCost = item.labor_hours * item.labor_rate;
      return sum + materialCost + laborCost;
    }, 0);

    const markupAmount = subtotal * (markupPercent / 100);
    const subtotalWithMarkup = subtotal + markupAmount;
    const taxAmount = subtotalWithMarkup * (taxRate / 100);
    const total = subtotalWithMarkup + taxAmount;

    return {
      subtotal,
      markupAmount,
      subtotalWithMarkup,
      taxAmount,
      total,
    };
  };

  const totals = calculateTotals();

  const onSubmit = async (data: EstimateFormData) => {
    try {
      const response = await api.post('/estimates', {
        ...data,
        subtotal: totals.subtotal,
        tax_amount: totals.taxAmount,
        total: totals.total,
      });
      toast.success('Estimate created successfully');
      onSuccess?.();
      onClose();
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Failed to create estimate');
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center sticky top-0 bg-white dark:bg-gray-800 z-10">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Create New Estimate
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <div className="grid grid-cols-1 gap-6 mb-6 lg:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Customer *
              </label>
              <select
                {...register('customer_id')}
                onChange={(e) => {
                  setValue('customer_id', e.target.value);
                  setSelectedCustomer(e.target.value);
                }}
                className="mt-1 input"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
              {errors.customer_id && (
                <p className="mt-1 text-sm text-red-600">{errors.customer_id.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Project (Optional)
              </label>
              <select
                {...register('project_id')}
                className="mt-1 input"
                disabled={!selectedCustomer}
              >
                <option value="">No project</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Estimate Number *
              </label>
              <input
                type="text"
                {...register('estimate_number')}
                className="mt-1 input"
              />
              {errors.estimate_number && (
                <p className="mt-1 text-sm text-red-600">{errors.estimate_number.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Valid Until *
              </label>
              <input
                type="date"
                {...register('valid_until')}
                className="mt-1 input"
              />
              {errors.valid_until && (
                <p className="mt-1 text-sm text-red-600">{errors.valid_until.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Default Labor Rate ($/hr) *
              </label>
              <input
                type="number"
                {...register('labor_rate', { valueAsNumber: true })}
                className="mt-1 input"
                step="0.01"
              />
              {errors.labor_rate && (
                <p className="mt-1 text-sm text-red-600">{errors.labor_rate.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Markup (%)
              </label>
              <input
                type="number"
                {...register('markup_percent', { valueAsNumber: true })}
                className="mt-1 input"
                step="0.1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tax Rate (%)
              </label>
              <input
                type="number"
                {...register('tax_rate', { valueAsNumber: true })}
                className="mt-1 input"
                step="0.01"
              />
            </div>

            <div className="lg:col-span-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description
              </label>
              <textarea
                {...register('description')}
                rows={2}
                className="mt-1 input"
                placeholder="Brief description of the work to be performed..."
              />
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Line Items</h3>
              <button
                type="button"
                onClick={() => append({ description: '', quantity: 1, unit_price: 0, labor_hours: 0, labor_rate: watch('labor_rate') })}
                className="btn-secondary"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Item
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Labor Hours
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Labor Rate
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-3 py-2"></th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {fields.map((field, index) => {
                    const item = watchItems[index];
                    const itemTotal = (item?.quantity || 0) * (item?.unit_price || 0) + (item?.labor_hours || 0) * (item?.labor_rate || 0);
                    
                    return (
                      <tr key={field.id}>
                        <td className="px-3 py-2">
                          <input
                            {...register(`items.${index}.description`)}
                            className="input input-sm w-full"
                            placeholder="Item description"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            {...register(`items.${index}.quantity`, { valueAsNumber: true })}
                            className="input input-sm w-20"
                            step="0.01"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            {...register(`items.${index}.unit_price`, { valueAsNumber: true })}
                            className="input input-sm w-24"
                            step="0.01"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            {...register(`items.${index}.labor_hours`, { valueAsNumber: true })}
                            className="input input-sm w-20"
                            step="0.25"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="number"
                            {...register(`items.${index}.labor_rate`, { valueAsNumber: true })}
                            className="input input-sm w-24"
                            step="0.01"
                          />
                        </td>
                        <td className="px-3 py-2 text-right font-medium">
                          ${itemTotal.toFixed(2)}
                        </td>
                        <td className="px-3 py-2">
                          <button
                            type="button"
                            onClick={() => remove(index)}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            disabled={fields.length === 1}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            {errors.items && (
              <p className="mt-2 text-sm text-red-600">{errors.items.message}</p>
            )}
          </div>

          <div className="mb-6 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span className="font-medium">${totals.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Markup ({markupPercent}%):</span>
                <span className="font-medium">${totals.markupAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Subtotal with Markup:</span>
                <span className="font-medium">${totals.subtotalWithMarkup.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax ({taxRate}%):</span>
                <span className="font-medium">${totals.taxAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-lg font-bold pt-2 border-t border-gray-200 dark:border-gray-700">
                <span>Total:</span>
                <span>${totals.total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="mt-1 input"
              placeholder="Additional notes or terms..."
            />
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary"
            >
              {isSubmitting ? (
                <>
                  <div className="spinner mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Estimate
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}