import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { errorService } from '../services/errorService';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler';

interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

interface RequestQueueItem {
  config: AxiosRequestConfig;
  resolve: (value: AxiosResponse) => void;
  reject: (error: any) => void;
}

export class ApiInterceptors {
  private static correlationId?: string;
  private static requestQueue: RequestQueueItem[] = [];
  private static isRefreshing = false;

  /**
   * Setup request interceptors
   */
  static setupRequestInterceptors(axiosInstance: AxiosInstance): void {
    // Add correlation ID to all requests
    axiosInstance.interceptors.request.use(
      (config) => {
        // Generate or use existing correlation ID
        if (!this.correlationId) {
          this.correlationId = errorService.generateCorrelationId();
        }
        
        config.headers['X-Correlation-ID'] = this.correlationId;
        
        // Add request timestamp
        (config as any).metadata = {
          startTime: Date.now(),
          correlationId: this.correlationId,
        };

        // Add auth token if available
        const token = this.getAuthToken();
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }

        return config;
      },
      (error) => {
        errorService.logError(error, {
          phase: 'request-interceptor',
          correlationId: this.correlationId,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Setup response interceptors
   */
  static setupResponseInterceptors(axiosInstance: AxiosInstance): void {
    axiosInstance.interceptors.response.use(
      (response) => {
        // Log response time
        const requestMetadata = (response.config as any).metadata;
        if (requestMetadata?.startTime) {
          const duration = Date.now() - requestMetadata.startTime;
          
          // Log slow requests
          if (duration > 3000) {
            errorService.logWarning('Slow API request detected', {
              url: response.config.url,
              method: response.config.method,
              duration,
              correlationId: requestMetadata.correlationId,
            });
          }
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // Handle 401 errors (token refresh)
        if (error.response?.status === 401 && !originalRequest._retry) {
          return this.handle401Error(error, axiosInstance);
        }

        // Handle rate limiting
        if (error.response?.status === 429) {
          return this.handleRateLimitError(error, axiosInstance);
        }

        // Handle network errors with retry
        if (!error.response && ErrorHandler.isNetworkError(error)) {
          return this.handleNetworkError(error, axiosInstance);
        }

        // Log error
        const requestMetadata = (originalRequest as any).metadata;
        errorService.logError(error, {
          phase: 'response-interceptor',
          url: originalRequest.url,
          method: originalRequest.method,
          status: error.response?.status,
          correlationId: requestMetadata?.correlationId,
        });

        return Promise.reject(error);
      }
    );
  }

  /**
   * Handle 401 unauthorized errors
   */
  private static async handle401Error(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    if (originalRequest._retry) {
      // Already tried to refresh, logout user
      this.handleLogout();
      return Promise.reject(error);
    }

    originalRequest._retry = true;

    if (!this.isRefreshing) {
      this.isRefreshing = true;

      try {
        // Attempt to refresh token
        const newToken = await this.refreshAuthToken();
        
        if (newToken) {
          // Update token in storage
          this.setAuthToken(newToken);
          
          // Retry all queued requests
          this.processRequestQueue(newToken);
          
          // Retry original request
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        this.handleLogout();
        this.processRequestQueue(null);
        return Promise.reject(refreshError);
      } finally {
        this.isRefreshing = false;
      }
    }

    // Add request to queue while refreshing
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        config: originalRequest,
        resolve,
        reject,
      });
    });
  }

  /**
   * Handle rate limit errors
   */
  private static async handleRateLimitError(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const retryAfter = ErrorHandler.getRetryAfter(error);
    
    if (retryAfter) {
      errorService.logWarning('Rate limit hit, retrying after delay', {
        url: error.config?.url,
        retryAfter,
      });

      // Wait for the specified time
      await new Promise(resolve => setTimeout(resolve, retryAfter));
      
      // Retry the request
      return axiosInstance(error.config!);
    }

    return Promise.reject(error);
  }

  /**
   * Handle network errors with retry
   */
  private static async handleNetworkError(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const config = error.config as AxiosRequestConfig & { 
      _retryCount?: number;
      _retryConfig?: RetryConfig;
    };

    const retryConfig: RetryConfig = config._retryConfig || {
      retries: 3,
      retryDelay: 1000,
    };

    config._retryCount = config._retryCount || 0;

    if (config._retryCount < retryConfig.retries) {
      config._retryCount++;

      const delay = retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);
      
      errorService.logWarning('Network error, retrying request', {
        url: config.url,
        attempt: config._retryCount,
        maxAttempts: retryConfig.retries,
        delay,
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      
      return axiosInstance(config);
    }

    return Promise.reject(error);
  }

  /**
   * Process queued requests after token refresh
   */
  private static processRequestQueue(token: string | null): void {
    this.requestQueue.forEach(({ config, resolve, reject }) => {
      if (token) {
        config.headers = config.headers || {};
        config.headers['Authorization'] = `Bearer ${token}`;
        axios(config).then(resolve).catch(reject);
      } else {
        reject(new Error('Token refresh failed'));
      }
    });

    this.requestQueue = [];
  }

  /**
   * Get auth token from storage
   */
  private static getAuthToken(): string | null {
    try {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        return parsed.state?.token || null;
      }
    } catch {
      // Ignore parsing errors
    }
    return null;
  }

  /**
   * Set auth token in storage
   */
  private static setAuthToken(token: string): void {
    try {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        parsed.state.token = token;
        localStorage.setItem('auth-storage', JSON.stringify(parsed));
      }
    } catch {
      // Ignore storage errors
    }
  }

  /**
   * Refresh authentication token
   */
  private static async refreshAuthToken(): Promise<string | null> {
    try {
      // TODO: Implement actual token refresh logic
      const response = await axios.post('/api/auth/refresh', {
        refreshToken: this.getRefreshToken(),
      });
      
      return response.data.token;
    } catch (error) {
      errorService.logError(error, {
        context: 'token-refresh',
      });
      return null;
    }
  }

  /**
   * Get refresh token from storage
   */
  private static getRefreshToken(): string | null {
    // TODO: Implement refresh token retrieval
    return null;
  }

  /**
   * Handle logout
   */
  private static handleLogout(): void {
    // Clear auth storage
    localStorage.removeItem('auth-storage');
    
    // Redirect to login
    window.location.href = '/login';
  }

  /**
   * Create axios instance with interceptors
   */
  static createAxiosInstance(baseURL: string): AxiosInstance {
    const instance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupRequestInterceptors(instance);
    this.setupResponseInterceptors(instance);

    return instance;
  }

  /**
   * Reset correlation ID (call this on route changes)
   */
  static resetCorrelationId(): void {
    this.correlationId = errorService.generateCorrelationId();
  }
}

// Export configured axios instance
export const apiClient = ApiInterceptors.createAxiosInstance(
  process.env.REACT_APP_API_URL || 'http://localhost:3001/api'
);