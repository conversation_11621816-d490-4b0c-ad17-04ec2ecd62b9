import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  Cell,
  ComposedChart,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import type { PredictiveAnalytics } from '../../services/analyticsService';
import { format, addDays, addMonths } from 'date-fns';
import { TrendingUp, TrendingDown, AlertTriangle, Clock, Users, DollarSign, Activity, Target } from 'lucide-react';

const PredictiveAnalyticsDashboard: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'30days' | '90days' | '6months'>('90days');

  // Fetch predictive analytics
  const { data: predictiveData } = useQuery({
    queryKey: ['predictive-analytics'],
    queryFn: () => analyticsService.getPredictiveAnalytics(),
  });

  // Fetch seasonal demand patterns
  const { data: seasonalData } = useQuery({
    queryKey: ['seasonal-patterns'],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { month: 'Jan', residential: 65, commercial: 80, industrial: 45 },
        { month: 'Feb', residential: 70, commercial: 85, industrial: 50 },
        { month: 'Mar', residential: 85, commercial: 90, industrial: 65 },
        { month: 'Apr', residential: 95, commercial: 95, industrial: 75 },
        { month: 'May', residential: 100, commercial: 100, industrial: 85 },
        { month: 'Jun', residential: 105, commercial: 105, industrial: 95 },
        { month: 'Jul', residential: 110, commercial: 110, industrial: 100 },
        { month: 'Aug', residential: 108, commercial: 108, industrial: 98 },
        { month: 'Sep', residential: 95, commercial: 95, industrial: 85 },
        { month: 'Oct', residential: 85, commercial: 85, industrial: 70 },
        { month: 'Nov', residential: 75, commercial: 75, industrial: 55 },
        { month: 'Dec', residential: 70, commercial: 70, industrial: 50 },
      ];
    },
  });

  // Fetch project success factors
  const { data: successFactors } = useQuery({
    queryKey: ['project-success-factors'],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { factor: 'Team Experience', weight: 0.25, score: 85 },
        { factor: 'Project Complexity', weight: 0.20, score: 70 },
        { factor: 'Customer History', weight: 0.15, score: 90 },
        { factor: 'Material Availability', weight: 0.15, score: 75 },
        { factor: 'Weather Conditions', weight: 0.10, score: 80 },
        { factor: 'Permit Status', weight: 0.15, score: 95 },
      ];
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-green-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Completion Predictions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Project Completion Predictions
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Predicted Completion
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risk Factors
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {predictiveData?.projectCompletionPredictions.slice(0, 5).map((project) => (
                <tr key={project.projectId}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {project.projectName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {format(new Date(project.predictedCompletion), 'MMM dd, yyyy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[100px]">
                        <div
                          className={`h-2 rounded-full ${
                            project.confidence >= 80 ? 'bg-green-600' :
                            project.confidence >= 60 ? 'bg-yellow-600' :
                            'bg-red-600'
                          }`}
                          style={{ width: `${project.confidence}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {project.confidence}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex flex-wrap gap-1">
                      {project.riskFactors.slice(0, 2).map((risk, idx) => (
                        <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                          {risk}
                        </span>
                      ))}
                      {project.riskFactors.length > 2 && (
                        <span className="text-xs text-gray-500">
                          +{project.riskFactors.length - 2} more
                        </span>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Material Price Forecast */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Material Price Forecast
            </h3>
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as any)}
              className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="30days">30 Days</option>
              <option value="90days">90 Days</option>
              <option value="6months">6 Months</option>
            </select>
          </div>
          <div className="space-y-3">
            {predictiveData?.materialPriceForecast.slice(0, 6).map((material) => (
              <div key={material.materialId} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {material.materialName}
                    </span>
                    {getTrendIcon(material.trend)}
                  </div>
                  <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Current: {formatCurrency(material.currentPrice)}
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(material.predictedPrice30Days)}
                  </p>
                  <p className={`text-xs ${
                    material.trend === 'up' ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {material.trend === 'up' ? '+' : '-'}
                    {Math.abs(((material.predictedPrice30Days - material.currentPrice) / material.currentPrice) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Risk Assessment */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Risk Assessment Overview
          </h3>
          <div className="space-y-3">
            {predictiveData?.riskScores.map((risk) => (
              <div key={risk.category} className="border rounded-lg p-3 dark:border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {risk.category}
                  </span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskColor(risk.riskLevel)}`}>
                    {risk.riskLevel.toUpperCase()}
                  </span>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                  {risk.description}
                </p>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  <p className="font-medium mb-1">Mitigation:</p>
                  <ul className="list-disc list-inside space-y-0.5">
                    {risk.mitigationSteps.slice(0, 2).map((step, idx) => (
                      <li key={idx}>{step}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Seasonal Demand Patterns */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Seasonal Demand Patterns
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={seasonalData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip formatter={(value) => `${value}%`} />
            <Legend />
            <Line type="monotone" dataKey="residential" stroke="#3B82F6" strokeWidth={2} name="Residential" />
            <Line type="monotone" dataKey="commercial" stroke="#10B981" strokeWidth={2} name="Commercial" />
            <Line type="monotone" dataKey="industrial" stroke="#F59E0B" strokeWidth={2} name="Industrial" />
          </LineChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Churn Prediction */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Customer Churn Risk
          </h3>
          <div className="space-y-3">
            {predictiveData?.churnPredictions.slice(0, 5).map((customer) => (
              <div key={customer.customerId} className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {customer.customerName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Last project: {format(new Date(customer.lastProjectDate), 'MMM dd, yyyy')}
                  </p>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 w-20">
                      <div
                        className={`h-2 rounded-full ${
                          customer.churnProbability < 30 ? 'bg-green-600' :
                          customer.churnProbability < 60 ? 'bg-yellow-600' :
                          'bg-red-600'
                        }`}
                        style={{ width: `${customer.churnProbability}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {customer.churnProbability}%
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Revenue: {formatCurrency(customer.totalRevenue)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Project Success Factors */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Project Success Factors
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={successFactors}>
              <PolarGrid />
              <PolarAngleAxis dataKey="factor" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar
                name="Current Score"
                dataKey="score"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Demand Pattern Insights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Predicted Demand Patterns
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {predictiveData?.demandPatterns.map((pattern) => (
            <div key={pattern.period} className="border rounded-lg p-4 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {pattern.period}
                </span>
                <span className={`text-sm font-semibold ${
                  pattern.demandLevel > 80 ? 'text-red-600' :
                  pattern.demandLevel > 60 ? 'text-yellow-600' :
                  'text-green-600'
                }`}>
                  {pattern.demandLevel}% capacity
                </span>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Top project types:
                </p>
                {pattern.projectTypes.map((type, idx) => (
                  <span key={idx} className="inline-block mr-2 px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-800 rounded">
                    {type}
                  </span>
                ))}
              </div>
              <div className="mt-2 flex items-center justify-between">
                <span className="text-xs text-gray-500">Confidence</span>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mr-2 w-16">
                    <div
                      className="bg-blue-600 h-1.5 rounded-full"
                      style={{ width: `${pattern.confidence}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {pattern.confidence}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default PredictiveAnalyticsDashboard;