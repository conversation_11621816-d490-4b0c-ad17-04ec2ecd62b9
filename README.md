# Electrical Contracting Application

A comprehensive, modern electrical contracting management system designed to streamline operations, enhance safety compliance, and improve project efficiency for electrical contractors.

## 🚀 Overview

This application provides a complete solution for electrical contractors to manage projects, perform NEC-compliant calculations, track materials, handle inspections, and generate professional estimates. Built with modern technologies and featuring both web and mobile interfaces, it includes AI-powered agents to assist with various tasks.

## ✨ Key Features

### Core Functionality
- **Project Management**: End-to-end project tracking from estimation to completion
- **NEC Calculations**: Built-in calculators for wire sizing, voltage drop, conduit fill, load calculations, and more
- **Panel Scheduling**: Visual panel schedule creation and load balancing
- **Material Management**: Inventory tracking with barcode scanning and automated reordering
- **Inspection Management**: Digital checklists with photo documentation and QR code integration
- **Time Tracking**: Labor hour tracking with GPS location verification
- **Customer Management**: CRM functionality with project history and communication logs

### Advanced Features
- **Arc Flash Analysis**: Comprehensive arc flash calculations with safety recommendations
- **Short Circuit Analysis**: Detailed fault current calculations and protective device coordination
- **AI-Powered Assistance**: Intelligent agents for code compliance, troubleshooting, and optimization
- **Offline Capability**: Full offline functionality with intelligent sync and conflict resolution
- **Real-time Collaboration**: Live updates across all connected devices
- **Advanced Analytics**: Comprehensive dashboards for financial, labor, and material insights

### Safety & Compliance
- **NEC Code Compliance**: Built-in 2023 NEC code references and automatic compliance checking
- **Safety Documentation**: Digital safety briefings and hazard assessments
- **Permit Management**: Automated permit application generation with calculation reports
- **Inspection Preparation**: Pre-inspection checklists and documentation assembly

## 🛠️ Technology Stack

### Frontend
- **Web**: React 18 with TypeScript, Tailwind CSS, Vite
- **Mobile**: React Native with TypeScript, Redux Toolkit
- **UI Libraries**: Headless UI, Lucide Icons, Recharts
- **State Management**: Zustand (web), Redux Toolkit (mobile)
- **Data Fetching**: TanStack Query, Axios

### Backend
- **Runtime**: Node.js 20+ with Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Caching**: Redis for session management and caching
- **Authentication**: JWT with refresh tokens, OAuth2 support
- **Real-time**: Socket.io for live updates
- **Job Queue**: BullMQ for background processing

### AI & Analytics
- **Agent Framework**: Custom TypeScript-based agent system
- **Vector Store**: ChromaDB for semantic search
- **Graph Database**: Neo4j for relationship mapping
- **Time Series**: InfluxDB for metrics and analytics
- **ML Integration**: Support for OpenAI, Anthropic APIs

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Kubernetes with Helm charts
- **CI/CD**: GitHub Actions with automated testing
- **Monitoring**: Prometheus + Grafana stack
- **Security**: SSL/TLS, API key management, rate limiting

## 🚀 Quick Start

### Fastest Way to Start

```bash
# Run this command and follow prompts:
START_HERE.bat
```

This will open two terminals and start:
- Backend API: http://localhost:3001
- Frontend: http://localhost:3000

See [QUICK_START.md](./QUICK_START.md) for troubleshooting.

### Prerequisites
- Node.js 18+ (20+ recommended)
- pnpm package manager

### Manual Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/electrical-contracting-app.git
   cd electrical-contracting-app
   ```

2. **Install dependencies**
   ```bash
   cd app
   pnpm install
   ```

3. **Set up database**
   ```bash
   cd backend
   npx prisma generate
   npx prisma migrate deploy
   ```

4. **Start development services**
   ```bash
   # From the app directory
   pnpm run dev:main  # Starts backend + frontend only
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/api-docs

### Mobile Development

1. **Install React Native dependencies**
   ```bash
   cd app/mobile
   pnpm install
   cd ios && pod install
   ```

2. **Run on simulator/device**
   ```bash
   # iOS
   pnpm ios

   # Android
   pnpm android
   ```

## 📚 Documentation

- [User Guide](./docs/user/README.md) - Complete user documentation
- [Developer Guide](./docs/developer/README.md) - Development setup and guidelines
- [API Documentation](./docs/api/README.md) - REST API reference
- [Architecture Guide](./docs/architecture/README.md) - System design and decisions
- [Deployment Guide](./DEPLOYMENT.md) - Production deployment instructions
- [Security Guide](./SECURITY.md) - Security best practices
- [AI Agents Guide](./docs/agents/README.md) - AI agent architecture and usage

## 🏗️ Architecture Overview

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Web Client    │     │  Mobile Client  │     │   AI Agents     │
│   (React/TS)    │     │ (React Native)  │     │  (TypeScript)   │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                         │
         └───────────────────────┴─────────────────────────┘
                                 │
                    ┌────────────┴────────────┐
                    │     Load Balancer       │
                    │       (Nginx)           │
                    └────────────┬────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌────────┴────────┐   ┌─────────┴────────┐   ┌─────────┴────────┐
│   Backend API   │   │  WebSocket Server │   │   Agent Service  │
│  (Express/TS)   │   │   (Socket.io)    │   │   (TypeScript)   │
└────────┬────────┘   └─────────┬────────┘   └─────────┬────────┘
         │                      │                       │
         └──────────────────────┴───────────────────────┘
                                │
         ┌──────────────────────┼──────────────────────┐
         │                      │                      │
┌────────┴────────┐   ┌────────┴────────┐   ┌────────┴────────┐
│   PostgreSQL    │   │     Redis       │   │     Neo4j       │
│   (Primary DB)  │   │   (Cache/Queue) │   │  (Graph Store)  │
└─────────────────┘   └─────────────────┘   └─────────────────┘
```

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Run specific test suites
pnpm test:unit
pnpm test:integration
pnpm test:e2e

# Run with coverage
pnpm test:coverage
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/CONTRIBUTING.md) for details on:
- Code of Conduct
- Development workflow
- Coding standards
- Pull request process
- Issue reporting

## 📊 Performance

The application is optimized for:
- **Fast Initial Load**: < 2s on 3G networks
- **Responsive UI**: 60fps animations and interactions
- **Efficient Caching**: Intelligent data caching and prefetching
- **Offline Performance**: Full functionality without network
- **Scalability**: Handles 10,000+ concurrent users

## 🔒 Security

- **Authentication**: Multi-factor authentication with biometric support
- **Encryption**: End-to-end encryption for sensitive data
- **Compliance**: SOC 2 Type II, GDPR compliant
- **Audit Logging**: Comprehensive audit trail for all actions
- **API Security**: Rate limiting, API keys, request signing

See [SECURITY.md](./SECURITY.md) for detailed security information.

## 📈 Roadmap

### Q1 2024
- [ ] Advanced scheduling with crew management
- [ ] Integration with accounting software (QuickBooks, Xero)
- [ ] Enhanced mobile offline capabilities
- [ ] Voice-activated commands

### Q2 2024
- [ ] Machine learning for project estimation
- [ ] Augmented reality for panel visualization
- [ ] Advanced inventory predictions
- [ ] Multi-language support

### Q3 2024
- [ ] IoT device integration
- [ ] Blockchain for permit verification
- [ ] Advanced analytics AI
- [ ] White-label capabilities

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- National Electrical Code (NEC) for electrical standards
- The open-source community for amazing tools and libraries
- Our beta testers for invaluable feedback
- All contributors who have helped shape this project

## 📞 Support

- **Documentation**: [docs.electricalapp.com](https://docs.electricalapp.com)
- **Email**: <EMAIL>
- **Discord**: [Join our community](https://discord.gg/electricalapp)
- **Issues**: [GitHub Issues](https://github.com/your-org/electrical-contracting-app/issues)

---

Built with ❤️ by the Electrical Contracting App Team