import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { enhancedLogger } from '../utils/enhanced-logger';
import { measurePerformance } from '../utils/enhanced-logger';

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  checks: {
    [key: string]: {
      status: 'up' | 'down' | 'degraded';
      responseTime?: number;
      message?: string;
      details?: any;
    };
  };
}

export interface HealthCheckDependency {
  name: string;
  check: () => Promise<{ status: 'up' | 'down' | 'degraded'; message?: string; details?: any }>;
  critical?: boolean;
  timeout?: number;
}

export class HealthCheckService {
  private dependencies: HealthCheckDependency[] = [];
  private startTime: number;
  private lastCheckResult?: HealthCheckResult;
  private checkInterval?: NodeJS.Timeout;

  constructor(
    private prisma: PrismaClient,
    private redis?: Redis
  ) {
    this.startTime = Date.now();
    this.setupDefaultChecks();
  }

  /**
   * Setup default health checks
   */
  private setupDefaultChecks(): void {
    // Database check
    this.addDependency({
      name: 'database',
      critical: true,
      timeout: 5000,
      check: async () => {
        try {
          await this.prisma.$queryRaw`SELECT 1`;
          return { status: 'up' };
        } catch (error) {
          return {
            status: 'down',
            message: 'Database connection failed',
            details: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      },
    });

    // Redis check (if configured)
    if (this.redis) {
      this.addDependency({
        name: 'redis',
        critical: false,
        timeout: 3000,
        check: async () => {
          try {
            await this.redis!.ping();
            return { status: 'up' };
          } catch (error) {
            return {
              status: 'down',
              message: 'Redis connection failed',
              details: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        },
      });
    }

    // Memory check
    this.addDependency({
      name: 'memory',
      critical: false,
      check: async () => {
        const used = process.memoryUsage();
        const heapUsedPercent = (used.heapUsed / used.heapTotal) * 100;
        
        if (heapUsedPercent > 90) {
          return {
            status: 'down',
            message: 'Memory usage critical',
            details: {
              heapUsedPercent: heapUsedPercent.toFixed(2),
              heapUsed: `${(used.heapUsed / 1024 / 1024).toFixed(2)} MB`,
              heapTotal: `${(used.heapTotal / 1024 / 1024).toFixed(2)} MB`,
            },
          };
        } else if (heapUsedPercent > 70) {
          return {
            status: 'degraded',
            message: 'Memory usage high',
            details: {
              heapUsedPercent: heapUsedPercent.toFixed(2),
              heapUsed: `${(used.heapUsed / 1024 / 1024).toFixed(2)} MB`,
              heapTotal: `${(used.heapTotal / 1024 / 1024).toFixed(2)} MB`,
            },
          };
        }
        
        return {
          status: 'up',
          details: {
            heapUsedPercent: heapUsedPercent.toFixed(2),
            heapUsed: `${(used.heapUsed / 1024 / 1024).toFixed(2)} MB`,
            heapTotal: `${(used.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          },
        };
      },
    });

    // Disk space check (basic)
    this.addDependency({
      name: 'diskSpace',
      critical: false,
      check: async () => {
        // This is a simplified check - in production, use a proper disk space library
        try {
          const { execSync } = require('child_process');
          const dfOutput = execSync('df -h /').toString();
          const lines = dfOutput.trim().split('\n');
          if (lines.length >= 2) {
            const columns = lines[1].split(/\s+/);
            const usePercent = parseInt(columns[4]);
            
            if (usePercent > 90) {
              return {
                status: 'down',
                message: 'Disk space critical',
                details: { usePercent },
              };
            } else if (usePercent > 80) {
              return {
                status: 'degraded',
                message: 'Disk space low',
                details: { usePercent },
              };
            }
            
            return {
              status: 'up',
              details: { usePercent },
            };
          }
        } catch (error) {
          // If we can't check disk space, don't fail the health check
          return {
            status: 'up',
            message: 'Unable to check disk space',
          };
        }
        
        return { status: 'up' };
      },
    });
  }

  /**
   * Add a health check dependency
   */
  addDependency(dependency: HealthCheckDependency): void {
    this.dependencies.push(dependency);
  }

  /**
   * Perform health check
   */
  async check(): Promise<HealthCheckResult> {
    const checks: HealthCheckResult['checks'] = {};
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    // Run all checks in parallel
    const checkPromises = this.dependencies.map(async (dep) => {
      const startTime = Date.now();
      
      try {
        // Apply timeout if specified
        const checkPromise = dep.check();
        const timeoutPromise = dep.timeout
          ? new Promise<{ status: 'down' as const; message: string }>((resolve) =>
              setTimeout(() => resolve({ status: 'down', message: 'Check timed out' }), dep.timeout!)
            )
          : null;

        const result = timeoutPromise
          ? await Promise.race([checkPromise, timeoutPromise])
          : await checkPromise;

        const responseTime = Date.now() - startTime;

        checks[dep.name] = {
          ...result,
          responseTime,
        };

        // Update overall status
        if (result.status === 'down' && dep.critical) {
          overallStatus = 'unhealthy';
        } else if (result.status === 'down' && overallStatus !== 'unhealthy') {
          overallStatus = 'degraded';
        } else if (result.status === 'degraded' && overallStatus === 'healthy') {
          overallStatus = 'degraded';
        }
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        checks[dep.name] = {
          status: 'down',
          responseTime,
          message: 'Check failed with error',
          details: error instanceof Error ? error.message : 'Unknown error',
        };

        if (dep.critical) {
          overallStatus = 'unhealthy';
        } else if (overallStatus === 'healthy') {
          overallStatus = 'degraded';
        }
      }
    });

    await Promise.all(checkPromises);

    const result: HealthCheckResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      checks,
    };

    this.lastCheckResult = result;

    // Log if unhealthy
    if (overallStatus === 'unhealthy') {
      enhancedLogger.error('Health check failed', { healthCheck: result });
    } else if (overallStatus === 'degraded') {
      enhancedLogger.warn('Health check degraded', { healthCheck: result });
    }

    return result;
  }

  /**
   * Get last health check result
   */
  getLastResult(): HealthCheckResult | undefined {
    return this.lastCheckResult;
  }

  /**
   * Start periodic health checks
   */
  startPeriodicChecks(intervalMs: number = 30000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // Run initial check
    this.check().catch((error) => {
      enhancedLogger.error('Initial health check failed', error);
    });

    // Setup periodic checks
    this.checkInterval = setInterval(() => {
      this.check().catch((error) => {
        enhancedLogger.error('Periodic health check failed', error);
      });
    }, intervalMs);
  }

  /**
   * Stop periodic health checks
   */
  stopPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
    }
  }

  /**
   * Express route handler for health endpoint
   */
  async handleHealthCheck(req: any, res: any): Promise<void> {
    try {
      const result = await measurePerformance('health_check', () => this.check());
      
      const httpStatus = result.status === 'healthy' ? 200 :
                        result.status === 'degraded' ? 200 : 503;

      res.status(httpStatus).json(result);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime,
        checks: {},
        error: 'Health check failed',
      });
    }
  }

  /**
   * Express route handler for liveness probe (Kubernetes)
   */
  handleLiveness(req: any, res: any): void {
    res.status(200).json({ status: 'alive' });
  }

  /**
   * Express route handler for readiness probe (Kubernetes)
   */
  async handleReadiness(req: any, res: any): Promise<void> {
    const lastResult = this.getLastResult();
    
    if (!lastResult) {
      // No check has been performed yet
      res.status(503).json({ status: 'not_ready' });
      return;
    }

    if (lastResult.status === 'healthy') {
      res.status(200).json({ status: 'ready' });
    } else {
      res.status(503).json({ 
        status: 'not_ready',
        reason: lastResult.status,
        failedChecks: Object.entries(lastResult.checks)
          .filter(([_, check]) => check.status !== 'up')
          .map(([name, check]) => ({ name, ...check })),
      });
    }
  }
}