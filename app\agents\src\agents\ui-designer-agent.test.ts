import { UIDesignerAgent } from './ui-designer-agent';
import { MemoryStore } from '../infrastructure/memory-store';

describe('UIDesignerAgent', () => {
  let agent: UIDesignerAgent;
  let memoryStore: MemoryStore;

  beforeEach(() => {
    memoryStore = new MemoryStore('./test-memory');
    agent = new UIDesignerAgent({
      id: 'ui-test-001',
      name: 'Test UI Designer',
      type: 'ui-designer',
      description: 'Test UI Designer Agent',
      memoryStore,
    });
  });

  describe('Component Design', () => {
    it('should design a form component with electrical fields', async () => {
      const result = await agent.processTask('design-component', {
        componentName: 'LoadCalculatorForm',
        componentType: 'form',
        requirements: {
          functionality: 'Calculate electrical loads',
          responsiveness: ['mobile', 'tablet', 'desktop'],
          accessibility: true,
          darkMode: true,
        },
      });

      expect(result).toHaveProperty('design');
      expect(result).toHaveProperty('implementation');
      expect(result).toHaveProperty('accessibility');
      expect(result.design.type).toBe('form');
      expect(result.design.structure.fields).toContainEqual(
        expect.objectContaining({
          name: 'voltage',
          type: 'select',
        })
      );
    });

    it('should design a display component for calculations', async () => {
      const result = await agent.processTask('design-component', {
        componentName: 'CalculationResult',
        componentType: 'display',
      });

      expect(result.design.type).toBe('display');
      expect(result.design.structure.layout).toBe('card');
    });
  });

  describe('Layout Creation', () => {
    it('should create a responsive grid layout', async () => {
      const result = await agent.processTask('create-layout', {
        layoutName: 'EstimateGrid',
        layoutType: 'responsive-grid',
        breakpoints: {
          mobile: '1 column',
          tablet: '2 columns',
          desktop: '3 columns',
        },
      });

      expect(result).toHaveProperty('structure');
      expect(result).toHaveProperty('styles');
      expect(result.structure.grid).toBe('grid gap-4 md:gap-6 lg:gap-8');
    });
  });

  describe('Color Scheme Generation', () => {
    it('should generate electrical safety color scheme', async () => {
      const result = await agent.processTask('generate-color-scheme', {
        baseTheme: 'light',
        purpose: 'electrical-safety',
      });

      expect(result.colors.phase).toEqual({
        A: '#FF0000',
        B: '#000000',
        C: '#0000FF',
        neutral: '#FFFFFF',
        ground: '#00FF00',
      });
      expect(result).toHaveProperty('cssVariables');
      expect(result).toHaveProperty('tailwindConfig');
    });

    it('should generate high contrast color scheme', async () => {
      const result = await agent.processTask('generate-color-scheme', {
        baseTheme: 'dark',
        purpose: 'high-contrast',
      });

      expect(result.colors.foreground).toBe('#FFFFFF');
      expect(result.colors.background).toBe('#000000');
    });
  });

  describe('Accessibility Audit', () => {
    it('should audit component accessibility', async () => {
      const result = await agent.processTask('audit-accessibility', {
        component: 'TestComponent',
        level: 'AA',
        includeColorContrast: true,
        includeKeyboardNav: true,
        includeScreenReader: true,
      });

      expect(result).toHaveProperty('checks');
      expect(result).toHaveProperty('score');
      expect(result.checks).toHaveLength(3);
    });
  });

  describe('Style Generation', () => {
    it('should generate Tailwind styles for button', async () => {
      const result = await agent.processTask('generate-styles', {
        component: 'button',
        variant: 'primary',
        state: 'default',
      });

      expect(result).toHaveProperty('className');
      expect(result.styles.base).toContain('px-4');
      expect(result.styles.base).toContain('py-2');
      expect(result.styles.base).toContain('bg-blue-600');
    });

    it('should generate electrical-specific button styles', async () => {
      const result = await agent.processTask('generate-styles', {
        component: 'button',
        variant: 'electrical',
      });

      expect(result.styles.base).toContain('bg-yellow-500');
      expect(result.styles.base).toContain('text-black');
      expect(result.styles.base).toContain('font-bold');
    });
  });

  describe('Design Validation', () => {
    it('should validate design against electrical standards', async () => {
      const mockDesign = {
        name: 'TestComponent',
        type: 'display',
        electricalIndicators: {
          phaseColors: {
            A: '#FF0000',
            B: '#000000',
            C: '#0000FF',
          },
        },
        fieldOptimizations: {
          touchTargets: 'min-h-[44px]',
          highContrastMode: true,
        },
      };

      const result = await agent.processTask('validate-design', {
        design: mockDesign,
        checkElectrical: true,
        checkAccessibility: true,
        checkPerformance: true,
      });

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should catch incorrect phase colors', async () => {
      const mockDesign = {
        electricalIndicators: {
          phaseColors: {
            A: '#00FF00', // Wrong color
            B: '#000000',
            C: '#0000FF',
          },
        },
      };

      const result = await agent.processTask('validate-design', {
        design: mockDesign,
        checkElectrical: true,
      });

      expect(result.errors).toContain('Phase A must be red (#FF0000)');
    });
  });

  describe('Performance Optimization', () => {
    it('should optimize component for mobile performance', async () => {
      const mockComponent = {
        name: 'TestComponent',
        type: 'composite',
        images: [{ src: '/image1.jpg' }],
        styles: { base: ['px-4', 'py-2'] },
      };

      const result = await agent.processTask('optimize-performance', {
        component: mockComponent,
        target: 'mobile',
      });

      expect(result.optimized.images[0]).toHaveProperty('loading', 'lazy');
      expect(result.optimized.images[0]).toHaveProperty('format', 'webp');
      expect(result.optimized).toHaveProperty('mobileOptimizations');
      expect(result.improvements).toContain('Implemented lazy loading and responsive images');
    });
  });
});