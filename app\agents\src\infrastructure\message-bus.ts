import { EventEmitter } from 'events';
import { z } from 'zod';

// Message types for agent communication
export enum MessageType {
  TASK_REQUEST = 'TASK_REQUEST',
  TASK_RESPONSE = 'TASK_RESPONSE',
  TASK_STATUS = 'TASK_STATUS',
  AGENT_READY = 'AGENT_READY',
  AGENT_ERROR = 'AGENT_ERROR',
  MEMORY_UPDATE = 'MEMORY_UPDATE',
  CALCULATION_REQUEST = 'CALCULATION_REQUEST',
  CALCULATION_RESULT = 'CALCULATION_RESULT',
  MATERIAL_LOOKUP = 'MATERIAL_LOOKUP',
  NEC_LOOKUP = 'NEC_LOOKUP',
}

// Base message schema
const baseMessageSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(MessageType),
  from: z.string(),
  to: z.string().optional(),
  timestamp: z.date(),
  correlationId: z.string().optional(),
});

// Task request schema
export const taskRequestSchema = baseMessageSchema.extend({
  type: z.literal(MessageType.TASK_REQUEST),
  payload: z.object({
    taskType: z.string(),
    priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
    data: z.any(),
    deadline: z.date().optional(),
  }),
});

// Task response schema
export const taskResponseSchema = baseMessageSchema.extend({
  type: z.literal(MessageType.TASK_RESPONSE),
  payload: z.object({
    success: z.boolean(),
    result: z.any().optional(),
    error: z.string().optional(),
  }),
});

// Task status schema
export const taskStatusSchema = baseMessageSchema.extend({
  type: z.literal(MessageType.TASK_STATUS),
  payload: z.object({
    status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED']),
    progress: z.number().min(0).max(100).optional(),
    message: z.string().optional(),
  }),
});

// Agent ready schema
export const agentReadySchema = baseMessageSchema.extend({
  type: z.literal(MessageType.AGENT_READY),
  payload: z.object({
    agentType: z.string(),
    capabilities: z.array(z.string()),
    status: z.enum(['IDLE', 'BUSY', 'OFFLINE']),
  }),
});

// Memory update schema
export const memoryUpdateSchema = baseMessageSchema.extend({
  type: z.literal(MessageType.MEMORY_UPDATE),
  payload: z.object({
    memoryType: z.enum(['SHORT_TERM', 'LONG_TERM', 'EPISODIC', 'SEMANTIC']),
    operation: z.enum(['ADD', 'UPDATE', 'DELETE', 'QUERY']),
    data: z.any(),
  }),
});

// Calculation request schema
export const calculationRequestSchema = baseMessageSchema.extend({
  type: z.literal(MessageType.CALCULATION_REQUEST),
  payload: z.object({
    calculationType: z.enum(['LOAD', 'VOLTAGE_DROP', 'CONDUIT_FILL', 'WIRE_SIZE', 'SHORT_CIRCUIT', 'ARC_FLASH']),
    parameters: z.record(z.any()),
    necEdition: z.string().default('2023'),
  }),
});

// Type definitions
export type Message = z.infer<typeof baseMessageSchema>;
export type TaskRequest = z.infer<typeof taskRequestSchema>;
export type TaskResponse = z.infer<typeof taskResponseSchema>;
export type TaskStatus = z.infer<typeof taskStatusSchema>;
export type AgentReady = z.infer<typeof agentReadySchema>;
export type MemoryUpdate = z.infer<typeof memoryUpdateSchema>;
export type CalculationRequest = z.infer<typeof calculationRequestSchema>;

// Message bus for inter-agent communication
export class MessageBus extends EventEmitter {
  private static instance: MessageBus;
  private agents: Map<string, { type: string; status: string }> = new Map();
  private messageQueue: Map<string, Message[]> = new Map();

  private constructor() {
    super();
    this.setMaxListeners(50); // Support many agents
  }

  static getInstance(): MessageBus {
    if (!MessageBus.instance) {
      MessageBus.instance = new MessageBus();
    }
    return MessageBus.instance;
  }

  // Register an agent with the message bus
  registerAgent(agentId: string, agentType: string): void {
    this.agents.set(agentId, { type: agentType, status: 'IDLE' });
    this.emit('agent:registered', { agentId, agentType });
    
    // Deliver any queued messages
    const queuedMessages = this.messageQueue.get(agentId) || [];
    queuedMessages.forEach(message => {
      this.emit(`message:${agentId}`, message);
    });
    this.messageQueue.delete(agentId);
  }

  // Unregister an agent
  unregisterAgent(agentId: string): void {
    this.agents.delete(agentId);
    this.emit('agent:unregistered', { agentId });
  }

  // Send a message
  async send(message: Message): Promise<void> {
    // Validate message based on type
    switch (message.type) {
      case MessageType.TASK_REQUEST:
        taskRequestSchema.parse(message);
        break;
      case MessageType.TASK_RESPONSE:
        taskResponseSchema.parse(message);
        break;
      case MessageType.TASK_STATUS:
        taskStatusSchema.parse(message);
        break;
      case MessageType.AGENT_READY:
        agentReadySchema.parse(message);
        break;
      case MessageType.MEMORY_UPDATE:
        memoryUpdateSchema.parse(message);
        break;
      case MessageType.CALCULATION_REQUEST:
        calculationRequestSchema.parse(message);
        break;
    }

    // Emit to all agents if no specific recipient
    if (!message.to) {
      this.emit('message:broadcast', message);
      return;
    }

    // Check if recipient is registered
    if (this.agents.has(message.to)) {
      this.emit(`message:${message.to}`, message);
    } else {
      // Queue message for offline agent
      const queue = this.messageQueue.get(message.to) || [];
      queue.push(message);
      this.messageQueue.set(message.to, queue);
    }

    // Log message for debugging
    this.emit('message:sent', message);
  }

  // Subscribe to messages for a specific agent
  subscribe(agentId: string, handler: (message: Message) => void): void {
    this.on(`message:${agentId}`, handler);
  }

  // Subscribe to broadcast messages
  subscribeBroadcast(handler: (message: Message) => void): void {
    this.on('message:broadcast', handler);
  }

  // Unsubscribe from messages
  unsubscribe(agentId: string, handler: (message: Message) => void): void {
    this.off(`message:${agentId}`, handler);
  }

  // Get registered agents
  getAgents(): Map<string, { type: string; status: string }> {
    return new Map(this.agents);
  }

  // Update agent status
  updateAgentStatus(agentId: string, status: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = status;
      this.emit('agent:status', { agentId, status });
    }
  }

  // Create a task request message
  static createTaskRequest(
    from: string,
    to: string | undefined,
    taskType: string,
    data: any,
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM'
  ): TaskRequest {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: MessageType.TASK_REQUEST,
      from,
      to,
      timestamp: new Date(),
      payload: {
        taskType,
        priority,
        data,
      },
    };
  }

  // Create a task response message
  static createTaskResponse(
    from: string,
    to: string,
    correlationId: string,
    success: boolean,
    result?: any,
    error?: string
  ): TaskResponse {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: MessageType.TASK_RESPONSE,
      from,
      to,
      timestamp: new Date(),
      correlationId,
      payload: {
        success,
        result,
        error,
      },
    };
  }

  // Create a calculation request message
  static createCalculationRequest(
    from: string,
    to: string,
    calculationType: CalculationRequest['payload']['calculationType'],
    parameters: Record<string, any>
  ): CalculationRequest {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: MessageType.CALCULATION_REQUEST,
      from,
      to,
      timestamp: new Date(),
      payload: {
        calculationType,
        parameters,
        necEdition: '2023',
      },
    };
  }
}