import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text } from 'react-native';

// Screens
import { DashboardScreen } from '../screens/dashboard/DashboardScreen';
import { CalculatorsScreen } from '../screens/calculators/CalculatorsScreen';
import { PanelsScreen } from '../screens/panels/PanelsScreen';
import { FieldToolsScreen } from '../screens/tools/FieldToolsScreen';
import { SafetyScreen } from '../screens/safety/SafetyScreen';

// Calculator Components
import { LoadCalculator } from '../components/calculators/LoadCalculator';
import { VoltageDropCalculator } from '../components/calculators/VoltageDropCalculator';
import { WireSizeCalculator } from '../components/calculators/WireSizeCalculator';
import { ConduitFillCalculator } from '../components/calculators/ConduitFillCalculator';

// Panel Components
import { PanelScheduleViewer } from '../components/panels/PanelScheduleViewer';
import { CircuitStatusUpdater } from '../components/panels/CircuitStatusUpdater';

// Field Tool Components
import { InspectionChecklist } from '../components/inspections/InspectionChecklist';
import { MaterialTakeoffScanner } from '../components/materials/MaterialTakeoffScanner';
import { QuickEstimateBuilder } from '../components/estimates/QuickEstimateBuilder';
import { TimeTracker } from '../components/time/TimeTracker';

// Safety Components
import { ArcFlashWarning } from '../components/safety/ArcFlashWarning';
import { EmergencyContacts } from '../components/safety/EmergencyContacts';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Calculator Stack
const CalculatorStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="CalculatorsList" 
      component={CalculatorsScreen}
      options={{ title: 'Calculators' }}
    />
    <Stack.Screen 
      name="LoadCalculator" 
      component={LoadCalculator}
      options={{ title: 'Load Calculator' }}
    />
    <Stack.Screen 
      name="VoltageDropCalculator" 
      component={VoltageDropCalculator}
      options={{ title: 'Voltage Drop' }}
    />
    <Stack.Screen 
      name="WireSizeCalculator" 
      component={WireSizeCalculator}
      options={{ title: 'Wire Size' }}
    />
    <Stack.Screen 
      name="ConduitFillCalculator" 
      component={ConduitFillCalculator}
      options={{ title: 'Conduit Fill' }}
    />
  </Stack.Navigator>
);

// Panel Stack
const PanelStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="PanelsList" 
      component={PanelsScreen}
      options={{ title: 'Panels' }}
    />
    <Stack.Screen 
      name="PanelDetail" 
      component={PanelScheduleViewer}
      options={{ title: 'Panel Schedule' }}
    />
  </Stack.Navigator>
);

// Field Tools Stack
const FieldToolsStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="FieldToolsList" 
      component={FieldToolsScreen}
      options={{ title: 'Field Tools' }}
    />
    <Stack.Screen 
      name="InspectionChecklist" 
      component={InspectionChecklist}
      options={{ title: 'Inspection' }}
    />
    <Stack.Screen 
      name="MaterialTakeoff" 
      component={MaterialTakeoffScanner}
      options={{ title: 'Material Takeoff' }}
    />
    <Stack.Screen 
      name="QuickEstimate" 
      component={QuickEstimateBuilder}
      options={{ title: 'Quick Estimate' }}
    />
    <Stack.Screen 
      name="TimeTracking" 
      component={TimeTracker}
      options={{ title: 'Time Tracking' }}
    />
  </Stack.Navigator>
);

// Safety Stack
const SafetyStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="SafetyCenter" 
      component={SafetyScreen}
      options={{ title: 'Safety', headerStyle: { backgroundColor: '#f44336' }, headerTintColor: 'white' }}
    />
    <Stack.Screen 
      name="ArcFlashWarning" 
      component={ArcFlashWarning}
      options={{ title: 'Arc Flash Warning' }}
    />
    <Stack.Screen 
      name="EmergencyContacts" 
      component={EmergencyContacts}
      options={{ title: 'Emergency Contacts', headerStyle: { backgroundColor: '#f44336' }, headerTintColor: 'white' }}
    />
  </Stack.Navigator>
);

// Main Tab Navigator
export const ElectricalNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: '#666',
        tabBarStyle: {
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
        },
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarIcon: ({ color }) => <Text style={{ fontSize: 24, color }}>🏠</Text>,
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Calculators"
        component={CalculatorStack}
        options={{
          tabBarIcon: ({ color }) => <Text style={{ fontSize: 24, color }}>🧮</Text>,
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Panels"
        component={PanelStack}
        options={{
          tabBarIcon: ({ color }) => <Text style={{ fontSize: 24, color }}>📊</Text>,
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Tools"
        component={FieldToolsStack}
        options={{
          tabBarIcon: ({ color }) => <Text style={{ fontSize: 24, color }}>🔧</Text>,
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Safety"
        component={SafetyStack}
        options={{
          tabBarIcon: ({ color }) => <Text style={{ fontSize: 24, color }}>🦺</Text>,
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
};