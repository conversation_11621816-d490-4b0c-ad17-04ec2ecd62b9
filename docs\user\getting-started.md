# Getting Started Guide

Welcome to the Electrical Contracting Application! This guide will walk you through your first steps in using the system effectively.

## 🚀 First Time Setup

### 1. Accessing the Application

#### Web Application
- Open your web browser (Chrome, Firefox, Safari, or Edge recommended)
- Navigate to https://app.electricalcontractor.com
- Bookmark the page for easy access

#### Mobile Application
- **iOS**: Download from the [App Store](https://apps.apple.com/app/electrical-contractor)
- **Android**: Download from [Google Play](https://play.google.com/store/apps/electrical-contractor)
- Allow necessary permissions (camera for scanning, location for job sites)

### 2. Creating Your Account

1. Click **"Sign Up"** on the login page
2. Enter your information:
   - Full name
   - Email address (will be your username)
   - Phone number
   - Company name
   - License number (optional but recommended)
3. Create a strong password (requirements):
   - Minimum 12 characters
   - At least one uppercase letter
   - At least one lowercase letter
   - At least one number
   - At least one special character
4. Verify your email address
5. Complete two-factor authentication setup

### 3. Initial Configuration

#### Company Profile
1. Navigate to **Settings → Company**
2. Complete your company information:
   - Business name and DBA
   - License numbers and expiration dates
   - Insurance information
   - Tax ID numbers
   - Default labor rates
   - Markup percentages

#### User Preferences
1. Go to **Settings → Preferences**
2. Configure:
   - Time zone
   - Date format
   - Currency
   - Measurement units (Imperial/Metric)
   - Default calculation standards (NEC edition)
   - Notification preferences

## 📱 Dashboard Overview

The dashboard is your command center, showing:

### Key Metrics
- **Active Projects**: Current project count and status
- **Today's Schedule**: Appointments and deadlines
- **Pending Tasks**: Items requiring attention
- **Financial Snapshot**: Revenue and expenses overview

### Quick Actions
- **New Project**: Start a new electrical project
- **Quick Calculation**: Access common calculators
- **Add Material**: Record material purchases
- **Time Entry**: Clock in/out or add time

### Navigation Menu
- **Projects**: All project management features
- **Calculations**: NEC-compliant calculators
- **Panels**: Panel schedule management
- **Materials**: Inventory and purchasing
- **Estimates**: Quote creation and tracking
- **Inspections**: Inspection scheduling and checklists
- **Reports**: Analytics and reporting
- **Settings**: System configuration

## 🎯 Your First Project

### Creating a Project

1. Click **"New Project"** from the dashboard
2. Fill in project details:
   ```
   Project Name: Main St. Renovation
   Customer: Select or create new
   Address: 123 Main St, City, State
   Type: Commercial/Residential/Industrial
   Estimated Start: Select date
   Estimated Completion: Select date
   ```
3. Add project scope and notes
4. Set budget estimates
5. Click **"Create Project"**

### Adding Team Members

1. Open your project
2. Go to the **Team** tab
3. Click **"Add Team Member"**
4. Select from existing users or invite new ones
5. Assign roles (Foreman, Electrician, Apprentice)
6. Set permissions for each team member

### Creating Your First Calculation

1. From the project page, click **"New Calculation"**
2. Select calculation type:
   - Wire Size
   - Voltage Drop
   - Conduit Fill
   - Load Calculation
3. Enter required values
4. Review results and code references
5. Save to project documentation

## 💡 Essential Features to Learn First

### 1. Wire Size Calculator
Most frequently used for:
- Determining proper conductor size
- Checking ampacity requirements
- Validating temperature corrections

**Try it now**: Calculate wire size for a 30A, 240V circuit, 100ft run

### 2. Panel Schedules
Critical for:
- Load balancing
- Circuit documentation
- Inspection preparation

**Try it now**: Create a panel schedule for a 200A residential panel

### 3. Material Takeoff
Essential for:
- Accurate estimates
- Inventory management
- Purchase orders

**Try it now**: Add materials for a basic outlet installation

### 4. Time Tracking
Important for:
- Labor cost tracking
- Payroll processing
- Project profitability

**Try it now**: Clock in for your current location

## 🔧 Best Practices

### Data Entry
- Enter data immediately to avoid forgetting details
- Use photos to document installations
- Add notes for unusual situations
- Tag items for easy searching

### Organization
- Use consistent naming conventions
- Create project templates for common jobs
- Set up material favorites for quick access
- Archive completed projects regularly

### Collaboration
- Assign clear responsibilities
- Use in-app messaging for project communication
- Share documents through the system
- Set up notifications for important updates

## 🚨 Common Mistakes to Avoid

1. **Skipping Initial Setup**: Take time to configure settings properly
2. **Not Using Templates**: Create templates for repetitive work
3. **Ignoring Notifications**: Stay informed about project updates
4. **Poor Photo Documentation**: Take clear, well-lit photos
5. **Delayed Data Entry**: Input information while it's fresh

## 📊 Quick Wins

To see immediate value from the system:

1. **Run a Load Calculation**: See how quickly you can perform complex calculations
2. **Create a Panel Schedule**: Experience the visual load balancing
3. **Generate an Estimate**: Use templates to create professional quotes
4. **Track Time on Mobile**: Clock in/out from the field
5. **Scan a Barcode**: Add materials instantly with your phone

## 🎓 Next Steps

Now that you're familiar with the basics:

1. Explore [Projects Management](./features/projects.md) in detail
2. Master all [Calculations](./features/calculations.md)
3. Learn about [Mobile Features](./mobile/README.md)
4. Set up [Integrations](./advanced/integrations.md)

## 💡 Pro Tips

- **Keyboard Shortcuts**: Press `?` anywhere in the app to see available shortcuts
- **Quick Search**: Press `Ctrl/Cmd + K` to search anything
- **Dark Mode**: Toggle in Settings → Appearance
- **Offline Mode**: Enable in Settings → Offline for field work
- **Templates**: Right-click any item to save as template

## 🆘 Getting Help

- **In-App Help**: Click the `?` icon in any screen
- **Video Tutorials**: Available in Help → Tutorials
- **Support Chat**: Click the chat bubble in bottom-right
- **Email Support**: <EMAIL>
- **Phone Support**: 1-800-ELECTRICAL (business hours)

## ✅ Checklist for Success

- [ ] Account created and verified
- [ ] Company profile completed
- [ ] Preferences configured
- [ ] First project created
- [ ] First calculation performed
- [ ] Mobile app installed
- [ ] Team member invited (if applicable)
- [ ] First material added
- [ ] Time tracking tested
- [ ] Backup settings configured

Congratulations! You're now ready to use the Electrical Contracting Application effectively. Remember, the more you use the system, the more time you'll save and the more organized your business will become.