import React, { useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Icon,
  Pressable,
  ScrollView,
  Di<PERSON><PERSON>,
  Badge,
} from 'native-base';
import { RefreshControl } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useDispatch, useSelector } from 'react-redux';
import { MainTabScreenProps } from '@types/navigation';
import { AppDispatch, RootState } from '@store/index';
import { fetchProjects } from '@store/slices/projectSlice';
import { useQuery } from '@tanstack/react-query';
import { projectService } from '@services/projectService';

type Props = MainTabScreenProps<'Dashboard'>;

interface DashboardCard {
  title: string;
  value: string | number;
  icon: string;
  color: string;
  route?: any;
}

const DashboardScreen: React.FC<Props> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { projects } = useSelector((state: RootState) => state.projects);

  const { data: stats, refetch, isRefetching } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      // This would typically fetch from an API endpoint
      const activeProjects = projects.filter(p => p.status === 'in_progress').length;
      const completedProjects = projects.filter(p => p.status === 'completed').length;
      const pendingInspections = 0; // Would come from API
      const overdueInvoices = 0; // Would come from API
      
      return {
        activeProjects,
        completedProjects,
        pendingInspections,
        overdueInvoices,
      };
    },
  });

  useEffect(() => {
    dispatch(fetchProjects());
  }, [dispatch]);

  const dashboardCards: DashboardCard[] = [
    {
      title: 'Active Projects',
      value: stats?.activeProjects || 0,
      icon: 'work',
      color: 'primary.500',
      route: { name: 'Projects' },
    },
    {
      title: 'Completed Projects',
      value: stats?.completedProjects || 0,
      icon: 'check-circle',
      color: 'success.500',
    },
    {
      title: 'Pending Inspections',
      value: stats?.pendingInspections || 0,
      icon: 'assignment',
      color: 'warning.500',
    },
    {
      title: 'Overdue Invoices',
      value: stats?.overdueInvoices || 0,
      icon: 'receipt-long',
      color: 'danger.500',
    },
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'project',
      title: 'New project created',
      description: 'Residential Rewiring - 123 Main St',
      time: '2 hours ago',
      icon: 'add-circle',
      color: 'primary.500',
    },
    {
      id: '2',
      type: 'inspection',
      title: 'Inspection scheduled',
      description: 'Final inspection for Office Building',
      time: '5 hours ago',
      icon: 'event',
      color: 'warning.500',
    },
    {
      id: '3',
      type: 'completion',
      title: 'Project completed',
      description: 'Commercial Lighting Upgrade',
      time: 'Yesterday',
      icon: 'check-circle',
      color: 'success.500',
    },
  ];

  const quickActions = [
    {
      title: 'New Project',
      icon: 'add',
      color: 'primary.500',
      onPress: () => navigation.navigate('Projects', { screen: 'ProjectCreate' }),
    },
    {
      title: 'Calculator',
      icon: 'calculate',
      color: 'secondary.500',
      onPress: () => navigation.navigate('Calculations'),
    },
    {
      title: 'Scan QR',
      icon: 'qr-code-scanner',
      color: 'tertiary.500',
      onPress: () => navigation.navigate('BarcodeScanner', { onScan: () => {} }),
    },
    {
      title: 'Reports',
      icon: 'assessment',
      color: 'info.500',
      onPress: () => {},
    },
  ];

  return (
    <Box flex={1} bg="gray.50" safeArea>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
        }
      >
        <VStack space={6} px={4} py={4}>
          {/* Welcome Section */}
          <VStack space={2}>
            <Heading size="lg">Welcome back, {user?.name || 'User'}!</Heading>
            <Text color="gray.600">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </VStack>

          {/* Stats Grid */}
          <VStack space={4}>
            <HStack space={4}>
              {dashboardCards.slice(0, 2).map((card, index) => (
                <Pressable
                  key={index}
                  flex={1}
                  onPress={() => card.route && navigation.navigate(card.route.name)}
                >
                  <Box bg="white" p={4} rounded="lg" shadow={2}>
                    <HStack space={3} alignItems="center">
                      <Icon as={MaterialIcons} name={card.icon} size={8} color={card.color} />
                      <VStack flex={1}>
                        <Text fontSize="2xl" fontWeight="bold">
                          {card.value}
                        </Text>
                        <Text fontSize="xs" color="gray.600">
                          {card.title}
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>
                </Pressable>
              ))}
            </HStack>
            <HStack space={4}>
              {dashboardCards.slice(2, 4).map((card, index) => (
                <Pressable
                  key={index}
                  flex={1}
                  onPress={() => card.route && navigation.navigate(card.route.name)}
                >
                  <Box bg="white" p={4} rounded="lg" shadow={2}>
                    <HStack space={3} alignItems="center">
                      <Icon as={MaterialIcons} name={card.icon} size={8} color={card.color} />
                      <VStack flex={1}>
                        <Text fontSize="2xl" fontWeight="bold">
                          {card.value}
                        </Text>
                        <Text fontSize="xs" color="gray.600">
                          {card.title}
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>
                </Pressable>
              ))}
            </HStack>
          </VStack>

          {/* Quick Actions */}
          <VStack space={3}>
            <Heading size="md">Quick Actions</Heading>
            <HStack space={4}>
              {quickActions.map((action, index) => (
                <Pressable key={index} flex={1} onPress={action.onPress}>
                  <Box bg="white" p={4} rounded="lg" shadow={2} alignItems="center">
                    <Icon as={MaterialIcons} name={action.icon} size={8} color={action.color} />
                    <Text fontSize="xs" mt={2}>
                      {action.title}
                    </Text>
                  </Box>
                </Pressable>
              ))}
            </HStack>
          </VStack>

          {/* Recent Activity */}
          <VStack space={3}>
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md">Recent Activity</Heading>
              <Pressable>
                <Text color="primary.500" fontSize="sm">
                  View All
                </Text>
              </Pressable>
            </HStack>
            <Box bg="white" rounded="lg" shadow={2}>
              {recentActivities.map((activity, index) => (
                <Box key={activity.id}>
                  <HStack space={3} p={4} alignItems="center">
                    <Icon
                      as={MaterialIcons}
                      name={activity.icon}
                      size={6}
                      color={activity.color}
                    />
                    <VStack flex={1}>
                      <Text fontWeight="medium">{activity.title}</Text>
                      <Text fontSize="sm" color="gray.600">
                        {activity.description}
                      </Text>
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        {activity.time}
                      </Text>
                    </VStack>
                  </HStack>
                  {index < recentActivities.length - 1 && <Divider />}
                </Box>
              ))}
            </Box>
          </VStack>
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default DashboardScreen;