import { 
  LoadCalculation, 
  VoltageDropCalculation, 
  WireSizeCalculation, 
  ConduitFillCalculation,
  DwellingLoad,
  CommercialLoad,
  WireInConduit 
} from '../types/electrical';
import { 
  NEC_TABLES, 
  DWELLING_LOADS, 
  MOTOR_CONSTANTS, 
  VOLTAGE_DROP_LIMITS 
} from '../constants/electrical';

export class CalculationService {
  // Load Calculation Methods
  static calculateDwellingLoad(dwelling: DwellingLoad): number {
    let totalLoad = 0;

    // General lighting load (NEC 220.12)
    const lightingLoad = dwelling.squareFootage * DWELLING_LOADS.GENERAL_LIGHTING;
    
    // Small appliance circuits (NEC 220.52)
    const smallApplianceLoad = dwelling.smallApplianceCircuits * DWELLING_LOADS.SMALL_APPLIANCE_CIRCUIT;
    
    // Laundry circuit (NEC 220.52)
    const laundryLoad = dwelling.laundryCircuit ? DWELLING_LOADS.LAUNDRY_CIRCUIT : 0;

    // First 3000W at 100%
    const basicLoad = lightingLoad + smallApplianceLoad + laundryLoad;
    let demandLoad = Math.min(basicLoad, 3000);

    // Next 117,000W at 35%
    if (basicLoad > 3000) {
      const next = Math.min(basicLoad - 3000, 117000);
      demandLoad += next * DWELLING_LOADS.DEMAND_FACTORS.NEXT_117000;
    }

    // Over 120,000W at 25%
    if (basicLoad > 120000) {
      demandLoad += (basicLoad - 120000) * DWELLING_LOADS.DEMAND_FACTORS.OVER_120000;
    }

    totalLoad = demandLoad;

    // Add fixed appliances at 100%
    totalLoad += dwelling.electricRange || 0;
    totalLoad += dwelling.electricDryer || 0;
    totalLoad += dwelling.electricWaterHeater || 0;
    
    // HVAC loads (use largest)
    const hvacLoad = Math.max(
      dwelling.airConditioner || 0,
      dwelling.heatPump || 0,
      dwelling.electricHeat || 0
    );
    totalLoad += hvacLoad;

    // Additional loads
    if (dwelling.additionalLoads) {
      dwelling.additionalLoads.forEach(load => {
        totalLoad += load.watts * load.quantity;
      });
    }

    return Math.round(totalLoad);
  }

  static calculateCommercialLoad(commercial: CommercialLoad): number {
    let totalLoad = 0;

    // Lighting load with demand factor
    totalLoad += commercial.lightingLoad * commercial.demandFactors.lighting;

    // Receptacle load with demand factor
    totalLoad += commercial.receptacleLoad * commercial.demandFactors.receptacle;

    // Motor loads
    commercial.motorLoads.forEach(motor => {
      const motorWatts = this.calculateMotorLoad(
        motor.hp,
        motor.voltage,
        motor.phase,
        motor.powerFactor
      );
      totalLoad += motorWatts * motor.quantity * commercial.demandFactors.motor;
    });

    // Special loads
    commercial.specialLoads.forEach(load => {
      const loadValue = load.type === 'continuous' ? load.watts * 1.25 : load.watts;
      totalLoad += loadValue * load.demandFactor;
    });

    return Math.round(totalLoad);
  }

  private static calculateMotorLoad(
    hp: number, 
    voltage: number, 
    phase: number, 
    powerFactor: number
  ): number {
    const watts = hp * 746; // Convert HP to watts
    const efficiency = MOTOR_CONSTANTS.EFFICIENCY.STANDARD;
    
    if (phase === 3) {
      return watts / (efficiency * powerFactor);
    } else {
      return watts / (efficiency * powerFactor);
    }
  }

  // Voltage Drop Calculation
  static calculateVoltageDrop(calc: VoltageDropCalculation): VoltageDropCalculation {
    const { voltage, phase, current, distance, wireSize, wireType, powerFactor } = calc;
    
    // Get resistance based on wire type
    const ampacityTable = wireType === 'copper' 
      ? NEC_TABLES.COPPER_AMPACITY_75C 
      : NEC_TABLES.ALUMINUM_AMPACITY_75C;
    
    // Simplified resistance calculation (would use actual resistance tables in production)
    const baseResistance = wireType === 'copper' ? 1.0 : 1.6;
    const sizeMultiplier = this.getWireSizeMultiplier(wireSize);
    const resistance = (baseResistance / sizeMultiplier) * (distance * 2 / 1000); // Ohms

    let voltageDrop: number;
    
    if (phase === 1) {
      // Single phase: VD = 2 × I × R × cos(θ)
      voltageDrop = 2 * current * resistance * powerFactor;
    } else {
      // Three phase: VD = √3 × I × R × cos(θ)
      voltageDrop = Math.sqrt(3) * current * resistance * powerFactor;
    }

    const percentageDrop = (voltageDrop / voltage) * 100;
    
    let recommendation = '';
    if (percentageDrop > VOLTAGE_DROP_LIMITS.BRANCH_CIRCUIT * 100) {
      recommendation = `Voltage drop exceeds ${VOLTAGE_DROP_LIMITS.BRANCH_CIRCUIT * 100}% limit. Consider larger wire size.`;
    }

    return {
      ...calc,
      calculatedDrop: Math.round(voltageDrop * 100) / 100,
      percentageDrop: Math.round(percentageDrop * 100) / 100,
      recommendation,
      timestamp: new Date()
    };
  }

  // Wire Size Calculation
  static calculateWireSize(calc: WireSizeCalculation): WireSizeCalculation {
    const { current, voltage, wireType, temperature, conduitFill } = calc;
    
    const ampacityTable = wireType === 'copper' 
      ? NEC_TABLES.COPPER_AMPACITY_75C 
      : NEC_TABLES.ALUMINUM_AMPACITY_75C;

    // Temperature correction
    const tempCorrection = this.getTemperatureCorrection(temperature);
    
    // Conduit fill correction (simplified)
    const fillCorrection = conduitFill > 3 ? 0.8 : 1.0;
    
    // Find appropriate wire size
    let recommendedSize = '';
    let ampacity = 0;
    
    for (const [size, amp] of Object.entries(ampacityTable)) {
      const correctedAmpacity = amp * tempCorrection * fillCorrection;
      if (correctedAmpacity >= current) {
        recommendedSize = size;
        ampacity = amp;
        break;
      }
    }

    return {
      ...calc,
      recommendedSize,
      ampacity,
      derating: tempCorrection * fillCorrection,
      timestamp: new Date()
    };
  }

  // Conduit Fill Calculation
  static calculateConduitFill(calc: ConduitFillCalculation): ConduitFillCalculation {
    const { conduitType, conduitSize, wires } = calc;
    
    // Calculate total wire area (simplified - would use actual wire area tables)
    let totalWireArea = 0;
    wires.forEach(wire => {
      const area = this.getWireArea(wire.size, wire.type);
      totalWireArea += area * wire.quantity;
    });

    // Get conduit area (simplified - would use actual conduit area tables)
    const conduitArea = this.getConduitArea(conduitSize, conduitType);
    
    // Determine max fill percentage based on number of wires
    const totalWires = wires.reduce((sum, w) => sum + w.quantity, 0);
    const maxFill = totalWires === 1 ? 0.53 : totalWires === 2 ? 0.31 : 0.4;
    
    const fillPercentage = (totalWireArea / conduitArea) * 100;
    const isCompliant = fillPercentage <= maxFill * 100;

    return {
      ...calc,
      fillPercentage: Math.round(fillPercentage * 10) / 10,
      maxFill: maxFill * 100,
      isCompliant,
      timestamp: new Date()
    };
  }

  // Helper methods
  private static getWireSizeMultiplier(size: string): number {
    const multipliers: Record<string, number> = {
      '14': 1,
      '12': 1.6,
      '10': 2.5,
      '8': 4,
      '6': 6.3,
      '4': 10,
      '3': 12.5,
      '2': 16,
      '1': 20,
      '1/0': 25,
      '2/0': 31.5,
      '3/0': 40,
      '4/0': 50,
    };
    return multipliers[size] || 1;
  }

  private static getTemperatureCorrection(temp: number): number {
    // Simplified temperature correction
    if (temp <= 86) return 1.0;
    if (temp <= 95) return 0.91;
    if (temp <= 104) return 0.87;
    if (temp <= 113) return 0.82;
    if (temp <= 122) return 0.76;
    return 0.71;
  }

  private static getWireArea(size: string, type: string): number {
    // Simplified wire area calculation (would use actual tables)
    const baseAreas: Record<string, number> = {
      '14': 0.0097,
      '12': 0.0133,
      '10': 0.0211,
      '8': 0.0366,
      '6': 0.0507,
      '4': 0.0824,
      '3': 0.0973,
      '2': 0.1158,
      '1': 0.1562,
    };
    return baseAreas[size] || 0.01;
  }

  private static getConduitArea(size: string, type: string): number {
    // Simplified conduit area calculation (would use actual tables)
    const areas: Record<string, number> = {
      '1/2': 0.304,
      '3/4': 0.533,
      '1': 0.864,
      '1-1/4': 1.380,
      '1-1/2': 1.610,
      '2': 2.067,
      '2-1/2': 3.356,
      '3': 4.618,
      '3-1/2': 5.858,
      '4': 7.069,
    };
    return areas[size] || 1;
  }

  // Save calculation to offline storage
  static async saveCalculation(type: string, calculation: any): Promise<void> {
    try {
      const key = `calculation_${type}_${Date.now()}`;
      const calculations = await this.getCalculations(type);
      calculations.push({ ...calculation, id: key });
      
      // Keep only last 50 calculations
      if (calculations.length > 50) {
        calculations.shift();
      }
      
      // Save to AsyncStorage (implementation would go here)
    } catch (error) {
      console.error('Error saving calculation:', error);
    }
  }

  static async getCalculations(type: string): Promise<any[]> {
    try {
      // Get from AsyncStorage (implementation would go here)
      return [];
    } catch (error) {
      console.error('Error getting calculations:', error);
      return [];
    }
  }
}