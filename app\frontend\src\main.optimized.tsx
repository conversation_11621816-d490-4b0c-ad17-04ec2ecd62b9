import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import App from './AppOptimized';
import './index.css';
import { performanceMonitor } from './services/performance';

// Initialize performance monitoring
performanceMonitor.measureOperation('app-initialization', async () => {
  // Configure React Query with optimized settings
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        retry: 1,
        refetchOnWindowFocus: false,
        // Performance optimizations
        structuralSharing: true, // Reduce re-renders
        refetchOnMount: 'always', // Better cache usage
        networkMode: 'online', // Optimize for online-first
      },
      mutations: {
        retry: 1,
        networkMode: 'online',
      },
    },
  });

  // Enable React concurrent features for better performance
  const root = ReactDOM.createRoot(document.getElementById('root')!, {
    // Enable concurrent features
    unstable_enableAsyncRendering: true,
  } as any);

  root.render(
    <React.StrictMode>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <App />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#f3f4f6',
                borderRadius: '0.5rem',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#f3f4f6',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f3f4f6',
                },
              },
            }}
          />
          {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
        </QueryClientProvider>
      </BrowserRouter>
    </React.StrictMode>
  );

  // Report performance metrics
  if (import.meta.env.PROD) {
    // Report when the app is fully loaded
    window.addEventListener('load', () => {
      const perfData = performanceMonitor.getPerformanceSummary();
      if (perfData) {
        // Performance data available
      }
    });

    // Monitor visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        // App going to background - good time to send metrics
        performanceMonitor.measureOperation('visibility-hidden', () => {});
      }
    });
  }
});

// Error boundary for performance tracking
window.addEventListener('error', (event) => {
  performanceMonitor.measureOperation('js-error', () => {
    // Global error occurred
  });
});

window.addEventListener('unhandledrejection', (event) => {
  performanceMonitor.measureOperation('promise-rejection', () => {
    // Unhandled promise rejection occurred
  });
});

// Service Worker registration with performance tracking
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  performanceMonitor.measureOperation('sw-registration', async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      // Service Worker registered successfully
    } catch (error) {
      // Service Worker registration failed
    }
  });
}