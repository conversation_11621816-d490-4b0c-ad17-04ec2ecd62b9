# Load Testing Infrastructure

Comprehensive load testing suite for the electrical contracting application, designed to verify system performance with 100+ concurrent users.

## Overview

This load testing infrastructure uses Artillery and k6 to simulate realistic user behavior and identify performance bottlenecks. It includes:

- Multiple test scenarios (load, stress, spike, endurance)
- Real-time resource monitoring
- Automated results analysis
- Performance recommendations

## Quick Start

1. **Install Dependencies**
   ```bash
   cd load-testing
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your test configuration
   ```

3. **Generate Test Data**
   ```bash
   node scripts/test-data-generator.js
   ```

4. **Run Load Test**
   ```bash
   npm run test:load
   ```

## Test Scenarios

### 1. Full Load Test (100 Concurrent Users)
Simulates normal production load with gradual ramp-up to 100 concurrent users.

```bash
npm run test:load
```

**Phases:**
- Warm-up: 1 minute (2 users/sec)
- Ramp-up: 5 minutes (2→20 users/sec)
- Sustained: 30 minutes (100 concurrent users)
- Cool-down: 2 minutes

**Traffic Distribution:**
- 25% - Electrical calculations (CPU intensive)
- 20% - Panel schedule operations (DB intensive)
- 15% - Material searches
- 15% - User authentication
- 10% - WebSocket connections
- 10% - Complex estimates
- 5% - File exports

### 2. Stress Test (Finding Breaking Point)
Gradually increases load until system failure to identify maximum capacity.

```bash
npm run test:stress
```

**Phases:**
- Initial: 5 users/sec
- Ramp to 250 users: 5 minutes
- Ramp to 500 users: 5 minutes
- Ramp to 1000 users: 5 minutes
- Sustain maximum: 10 minutes

### 3. Spike Test (Sudden Load Increases)
Tests system behavior under sudden traffic spikes.

```bash
npm run test:spike
```

**Pattern:**
- Normal load: 50 users
- Spike to 500 users: 1 minute
- Recovery phase
- Larger spike to 750 users

### 4. Endurance Test (4 Hours)
Extended test to identify memory leaks and resource exhaustion.

```bash
npm run test:endurance
```

**Duration:** 4 hours at moderate load (50 concurrent users)

## k6 Tests

Alternative load testing with k6 for more detailed metrics:

```bash
# Standard load test
npm run test:k6

# Stress test with k6
npm run test:k6:stress
```

## Resource Monitoring

Run the resource monitor in a separate terminal during tests:

```bash
npm run monitor
```

**Monitors:**
- CPU usage and temperature
- Memory utilization
- Disk I/O
- Network traffic
- Database connections
- Redis memory
- API response times

## Test Endpoints

### High-Priority Endpoints (Tested Heavily)

1. **Calculations** (CPU Intensive)
   - `/api/calculations/voltage-drop`
   - `/api/calculations/short-circuit`
   - `/api/calculations/arc-flash`

2. **Panel Operations** (Database Intensive)
   - `/api/panels` (with circuits and calculations)
   - `/api/panels/:id/schedule`
   - `/api/panels/:id/circuits`

3. **Material Search** (Query Intensive)
   - `/api/materials/search`
   - `/api/materials/pricing/calculate`

4. **Real-time Operations** (WebSocket)
   - Project collaboration rooms
   - Estimate updates
   - Live notifications

## Performance Metrics

### Target Thresholds

- **Response Time**
  - p95: < 1000ms
  - p99: < 2000ms
  
- **Error Rate**
  - < 1% under normal load
  - < 5% under stress

- **Throughput**
  - 100+ requests/second sustained

- **Resource Usage**
  - CPU: < 80%
  - Memory: < 85%
  - Database connections: < 80% of pool

## Results Analysis

After each test, analyze results:

```bash
npm run analyze -- artillery-report.json
```

**Analysis Includes:**
- Response time percentiles
- Error distribution
- Endpoint performance
- Resource utilization
- Bottleneck identification
- Optimization recommendations

## Test Data

Test users are pre-generated with realistic data:
- 200 test users with unique credentials
- Randomized electrical calculation parameters
- Varied panel configurations
- Realistic material searches

## Customization

### Adding New Scenarios

1. Create scenario file in `scenarios/`
2. Define test phases and user behavior
3. Add npm script to `package.json`

### Modifying Test Data

Edit `scripts/test-data-generator.js` to customize:
- User profiles
- Calculation parameters
- Panel configurations
- Material data

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure API server is running
   - Check API_BASE_URL in .env

2. **High Error Rate**
   - Review server logs
   - Check database connection pool
   - Monitor Redis memory

3. **Timeout Errors**
   - Increase timeout in config
   - Check server resources
   - Review slow queries

### Debug Mode

Enable detailed logging:
```bash
DEBUG=artillery* npm run test:load
```

## Reports

Results are saved in `results/` directory:
- `artillery-report.json` - Raw test data
- `k6-load-test-results.json` - k6 metrics
- `resource-metrics-*.json` - System resources
- `reports/` - Analyzed reports and CSVs

## Best Practices

1. **Before Testing**
   - Ensure test environment mirrors production
   - Clear caches and reset databases
   - Verify monitoring is active

2. **During Testing**
   - Monitor resources in real-time
   - Watch for early warning signs
   - Document any anomalies

3. **After Testing**
   - Analyze all results
   - Compare with previous tests
   - Implement optimizations
   - Re-test improvements

## Integration

### CI/CD Integration

```yaml
# Example GitHub Actions
- name: Run Load Test
  run: |
    npm install
    npm run test:load
    npm run analyze -- results/latest.json
```

### Monitoring Integration

Export metrics to monitoring systems:
- Prometheus/Grafana
- DataDog
- New Relic
- CloudWatch

## Optimization Opportunities

Based on load testing, common optimizations include:

1. **Database**
   - Add missing indexes
   - Optimize complex queries
   - Increase connection pool
   - Implement read replicas

2. **Caching**
   - Cache calculation results
   - Material search caching
   - Panel schedule caching
   - Redis optimization

3. **Application**
   - Async processing for heavy calculations
   - WebSocket connection pooling
   - Request batching
   - Memory leak fixes

4. **Infrastructure**
   - Horizontal scaling
   - Load balancer tuning
   - CDN for static assets
   - Auto-scaling policies