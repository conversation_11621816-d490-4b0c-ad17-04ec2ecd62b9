import React, { useEffect, useRef } from 'react';
import {
  Animated,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useSyncStatus } from '@offline/hooks/useSyncStatus';

interface OfflineIndicatorProps {
  position?: 'top' | 'bottom';
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ 
  position = 'top' 
}) => {
  const { isOnline } = useSyncStatus();
  const slideAnim = useRef(new Animated.Value(-100)).current;

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: isOnline ? -100 : 0,
      useNativeDriver: true,
      tension: 100,
      friction: 10,
    }).start();
  }, [isOnline, slideAnim]);

  const positionStyle = position === 'top' 
    ? { top: 0 } 
    : { bottom: 0 };

  const translateStyle = position === 'top'
    ? { translateY: slideAnim }
    : { translateY: Animated.multiply(slideAnim, -1) };

  return (
    <Animated.View
      style={[
        styles.container,
        positionStyle,
        { transform: [translateStyle] },
      ]}
    >
      <View style={styles.content}>
        <Icon name="cloud-off" size={20} color="white" />
        <Text style={styles.text}>You're offline</Text>
        <Text style={styles.subtext}>Changes will sync when connected</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: '#424242',
    zIndex: 1000,
    elevation: 10,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  text: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  subtext: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
});