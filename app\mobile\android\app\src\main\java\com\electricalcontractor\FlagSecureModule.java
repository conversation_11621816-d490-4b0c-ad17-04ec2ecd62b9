package com.electricalcontractor;

import android.app.Activity;
import android.view.WindowManager;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.module.annotations.ReactModule;

@ReactModule(name = "FlagSecure")
public class FlagSecureModule extends ReactContextBaseJavaModule {
    
    private static final String MODULE_NAME = "FlagSecure";
    
    public FlagSecureModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @ReactMethod
    public void activate() {
        final Activity activity = getCurrentActivity();
        if (activity != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    activity.getWindow().setFlags(
                        WindowManager.LayoutParams.FLAG_SECURE,
                        WindowManager.LayoutParams.FLAG_SECURE
                    );
                }
            });
        }
    }
    
    @ReactMethod
    public void deactivate() {
        final Activity activity = getCurrentActivity();
        if (activity != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
                }
            });
        }
    }
    
    @ReactMethod
    public void isActive(Promise promise) {
        try {
            final Activity activity = getCurrentActivity();
            if (activity != null) {
                int flags = activity.getWindow().getAttributes().flags;
                boolean isSecure = (flags & WindowManager.LayoutParams.FLAG_SECURE) != 0;
                promise.resolve(isSecure);
            } else {
                promise.resolve(false);
            }
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }
}