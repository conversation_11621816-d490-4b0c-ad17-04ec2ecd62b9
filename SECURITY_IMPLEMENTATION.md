# Security Implementation Guide

This document outlines the security implementations for the Electrical Contractor mobile app and backend API.

## SSL Certificate Pinning

### Mobile App (React Native)

The mobile app implements SSL certificate pinning to prevent man-in-the-middle attacks.

#### Obtaining Certificate Hashes

1. **Get the leaf certificate hash:**
   ```bash
   openssl s_client -servername api.electrical-contractor.com -connect api.electrical-contractor.com:443 | \
   openssl x509 -pubkey -noout | \
   openssl pkey -pubin -outform der | \
   openssl dgst -sha256 -binary | \
   openssl enc -base64
   ```

2. **Get the intermediate CA certificate hash:**
   ```bash
   # First, save the intermediate certificate to a file
   openssl s_client -showcerts -servername api.electrical-contractor.com -connect api.electrical-contractor.com:443 < /dev/null | \
   awk '/BEGIN CERTIFICATE/,/END CERTIFICATE/ {if (++m==2) print}' > intermediate.crt
   
   # Then get its hash
   openssl x509 -in intermediate.crt -pubkey -noout | \
   openssl pkey -pubin -outform der | \
   openssl dgst -sha256 -binary | \
   openssl enc -base64
   ```

3. **Update the hashes in `/app/mobile/src/services/secureApi.ts`:**
   ```typescript
   const SSL_PINS = {
     'api.electrical-contractor.com': {
       includeSubdomains: true,
       publicKeyHashes: [
         'sha256/YOUR_PRIMARY_CERT_HASH_HERE=',
         'sha256/YOUR_BACKUP_CERT_HASH_HERE=',
         'sha256/YOUR_INTERMEDIATE_CA_HASH_HERE=',
       ],
     },
   };
   ```

#### Certificate Rotation Strategy

1. **Planning:** Monitor certificate expiration dates and plan rotation 30 days in advance
2. **Preparation:** Generate new certificate and obtain its hash
3. **App Update:** 
   - Add new certificate hash to the pin array
   - Deploy app update with both old and new hashes
4. **Server Update:** Update server certificate after majority of users have updated
5. **Cleanup:** Remove old certificate hash in the next app release

#### Android Network Security Configuration

Create or update `android/app/src/main/res/xml/network_security_config.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.electrical-contractor.com</domain>
        <pin-set expiration="2025-01-01">
            <pin digest="SHA-256">YOUR_PRIMARY_CERT_HASH_HERE=</pin>
            <pin digest="SHA-256">YOUR_BACKUP_CERT_HASH_HERE=</pin>
            <pin digest="SHA-256">YOUR_INTERMEDIATE_CA_HASH_HERE=</pin>
        </pin-set>
    </domain-config>
</network-security-config>
```

## Request Signing (HMAC-SHA256)

### Implementation Details

Both the mobile app and backend implement HMAC-SHA256 request signing with replay protection.

#### Signature Components

1. **HTTP Method** (uppercase)
2. **Request Path**
3. **Timestamp** (Unix timestamp in milliseconds)
4. **Nonce** (unique random value)
5. **Device ID** (for mobile requests)
6. **Body Hash** (SHA256 hash of request body, if present)

#### Signature Generation

```
message = METHOD + '\n' + PATH + '\n' + TIMESTAMP + '\n' + NONCE + '\n' + DEVICE_ID + '\n' + BODY_HASH
signature = HMAC-SHA256(message, secret)
```

#### Request Headers

- `X-Signature`: The HMAC signature
- `X-Timestamp`: Request timestamp
- `X-Nonce`: Unique nonce
- `X-Device-Id`: Device identifier (mobile only)

### Replay Protection

1. **Timestamp Validation:** Requests older than 5 minutes are rejected
2. **Nonce Tracking:** Each nonce is stored in Redis for 10 minutes to prevent reuse
3. **Device Binding:** User requests are tied to registered devices

### Environment Variables

Add these to your `.env` files:

#### Mobile App (.env)
```
API_URL=https://api.electrical-contractor.com
API_TIMEOUT=30000
API_SIGNING_SECRET=your-64-character-hex-secret-here
```

#### Backend (.env)
```
# Security
ENCRYPTION_KEY=your-32-character-minimum-encryption-key
API_SIGNING_SECRET=your-64-character-hex-secret-for-api-keys
USER_SIGNING_SECRET=your-64-character-hex-secret-for-users
```

### Generating Secrets

Generate secure secrets using:

```bash
# Generate a 64-character hex secret
openssl rand -hex 32
```

## Device Binding

The system implements device binding for additional security:

1. **First Login:** Device ID is automatically registered
2. **Subsequent Logins:** Only registered devices are allowed
3. **Device Management:** Users can view and revoke devices through the API

### API Endpoints for Device Management

```typescript
// List user's registered devices
GET /api/users/devices

// Revoke a device
DELETE /api/users/devices/:deviceId
```

## Security Best Practices

1. **Secret Management:**
   - Never commit secrets to version control
   - Use environment variables or secure key management services
   - Rotate signing secrets periodically

2. **Certificate Management:**
   - Monitor certificate expiration dates
   - Implement certificate transparency monitoring
   - Have a rollback plan for certificate updates

3. **Monitoring:**
   - Log all signature validation failures
   - Monitor for replay attacks
   - Track device registration anomalies

4. **Client Security:**
   - Implement jailbreak/root detection
   - Use encrypted storage for sensitive data
   - Implement biometric authentication where possible

## Testing

### Testing Certificate Pinning

```bash
# Test with correct certificate (should succeed)
curl -X GET https://api.electrical-contractor.com/api/health

# Test with self-signed certificate (should fail in app)
# Set up a local proxy with self-signed cert and try to intercept
```

### Testing Request Signing

```typescript
// Example test request with signature
const timestamp = Date.now();
const nonce = crypto.randomBytes(16).toString('hex');
const message = `GET\n/api/users\n${timestamp}\n${nonce}\ndevice123`;
const signature = crypto.createHmac('sha256', secret).update(message).digest('hex');

const response = await fetch('https://api.electrical-contractor.com/api/users', {
  headers: {
    'X-Signature': signature,
    'X-Timestamp': timestamp.toString(),
    'X-Nonce': nonce,
    'X-Device-Id': 'device123'
  }
});
```

## Troubleshooting

### Common Issues

1. **"Missing signature headers" error:**
   - Ensure all required headers are present
   - Check header names (case-sensitive)

2. **"Invalid or expired timestamp" error:**
   - Verify device time is synchronized
   - Check timezone settings

3. **"Duplicate request detected" error:**
   - Ensure nonces are truly random
   - Don't retry requests with same nonce

4. **Certificate pinning failures:**
   - Verify certificate hashes are correct
   - Check certificate chain completeness
   - Ensure no proxy is intercepting requests

### Debug Mode

For development, you can temporarily disable security features:

```typescript
// Mobile app - Add to .env.development
DISABLE_SSL_PINNING=true
DISABLE_REQUEST_SIGNING=true

// Backend - Add to .env.development
SKIP_SIGNATURE_VERIFICATION=true
```

**WARNING:** Never use these settings in production!