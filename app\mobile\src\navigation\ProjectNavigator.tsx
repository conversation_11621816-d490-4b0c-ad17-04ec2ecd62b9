import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ProjectStackParamList } from '@types/navigation';

import ProjectListScreen from '@screens/projects/ProjectListScreen';
import ProjectCreateScreen from '@screens/projects/ProjectCreateScreen';
import ProjectEditScreen from '@screens/projects/ProjectEditScreen';

const Stack = createNativeStackNavigator<ProjectStackParamList>();

const ProjectNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#2196f3',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="ProjectList"
        component={ProjectListScreen}
        options={{ title: 'Projects' }}
      />
      <Stack.Screen
        name="ProjectCreate"
        component={ProjectCreateScreen}
        options={{ title: 'New Project' }}
      />
      <Stack.Screen
        name="ProjectEdit"
        component={ProjectEditScreen}
        options={{ title: 'Edit Project' }}
      />
    </Stack.Navigator>
  );
};

export default ProjectNavigator;