import { PrismaClient, Circuit } from '@prisma/client';
import { CircuitSchema } from '@electrical/shared';
import { z } from 'zod';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';
import { panelService, panelEvents } from './panel.service';
import { getTransactionService, TransactionResult } from './transaction.service';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

export const circuitService = {
  // Create a new circuit
  async createCircuit(data: z.infer<typeof CircuitSchema>) {
    try {
      // Validate circuit number is available
      const existingCircuit = await prisma.circuit.findUnique({
        where: {
          panel_id_circuit_number: {
            panel_id: data.panel_id,
            circuit_number: data.circuit_number
          }
        }
      });
      
      if (existingCircuit) {
        throw new AppError(`Circuit ${data.circuit_number} already exists in panel`, 400);
      }
      
      // Validate panel has space
      const panel = await prisma.panel.findUnique({
        where: { id: data.panel_id },
        include: { circuits: true }
      });
      
      if (!panel) {
        throw new AppError('Panel not found', 404);
      }
      
      const spacesUsed = panel.circuits.reduce((sum, circuit) => 
        sum + (circuit.is_space ? 0 : circuit.poles), 0
      );
      
      if (spacesUsed + data.poles > panel.spaces_total) {
        throw new AppError('Not enough space in panel', 400);
      }
      
      // Create circuit
      const circuit = await prisma.circuit.create({
        data: {
          ...data,
          calculated_load: data.connected_load * data.demand_factor,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
      
      logger.info(`Circuit created: ${circuit.id} - Circuit ${circuit.circuit_number}`);
      
      // Trigger panel load recalculation
      await panelService.calculatePanelLoad(data.panel_id);
      
      return circuit;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error({
        message: 'Error creating circuit',
        circuitData: data,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw new AppError(
        `Failed to create circuit ${data.circuit_number} in panel ${data.panel_id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  },

  // Get all circuits for a panel
  async getCircuitsByPanel(panelId: string) {
    try {
      const circuits = await prisma.circuit.findMany({
        where: { panel_id: panelId },
        orderBy: { circuit_number: 'asc' }
      });
      
      return circuits;
    } catch (error) {
      logger.error({
        message: 'Error fetching circuits',
        panelId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw new AppError(
        `Failed to fetch circuits for panel ${panelId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  },

  // Get circuit details
  async getCircuit(circuitId: string) {
    try {
      const circuit = await prisma.circuit.findUnique({
        where: { id: circuitId },
        include: {
          panel: true
        }
      });
      
      if (!circuit) {
        throw new AppError('Circuit not found', 404);
      }
      
      return circuit;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error({
        message: 'Error fetching circuit',
        circuitId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw new AppError(
        `Failed to fetch circuit ${circuitId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  },

  // Update circuit
  async updateCircuit(circuitId: string, data: Partial<z.infer<typeof CircuitSchema>>) {
    try {
      const existingCircuit = await prisma.circuit.findUnique({
        where: { id: circuitId }
      });
      
      if (!existingCircuit) {
        throw new AppError('Circuit not found', 404);
      }
      
      // If circuit number is changing, check availability
      if (data.circuit_number && data.circuit_number !== existingCircuit.circuit_number) {
        const conflictingCircuit = await prisma.circuit.findUnique({
          where: {
            panel_id_circuit_number: {
              panel_id: existingCircuit.panel_id,
              circuit_number: data.circuit_number
            }
          }
        });
        
        if (conflictingCircuit) {
          throw new AppError(`Circuit ${data.circuit_number} already exists in panel`, 400);
        }
      }
      
      // Update calculated load if connected load or demand factor changes
      let updates: any = { ...data, updated_at: new Date() };
      if (data.connected_load !== undefined || data.demand_factor !== undefined) {
        const connectedLoad = data.connected_load ?? existingCircuit.connected_load;
        const demandFactor = data.demand_factor ?? existingCircuit.demand_factor;
        updates.calculated_load = connectedLoad * demandFactor;
      }
      
      const circuit = await prisma.circuit.update({
        where: { id: circuitId },
        data: updates
      });
      
      logger.info(`Circuit updated: ${circuit.id}`);
      
      // Trigger panel load recalculation
      await panelService.calculatePanelLoad(existingCircuit.panel_id);
      
      return circuit;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error({
        message: 'Error updating circuit',
        circuitId,
        updateData: data,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw new AppError(
        `Failed to update circuit ${circuitId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  },

  // Delete circuit
  async deleteCircuit(circuitId: string) {
    try {
      const circuit = await prisma.circuit.findUnique({
        where: { id: circuitId }
      });
      
      if (!circuit) {
        throw new AppError('Circuit not found', 404);
      }
      
      await prisma.circuit.delete({
        where: { id: circuitId }
      });
      
      logger.info(`Circuit deleted: ${circuitId}`);
      
      // Trigger panel load recalculation
      await panelService.calculatePanelLoad(circuit.panel_id);
      
      return true;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error deleting circuit:', error);
      throw new AppError('Failed to delete circuit', 500);
    }
  },

  // Bulk create circuits (for initial panel setup) with transaction support
  async bulkCreateCircuits(panelId: string, circuitData: Array<z.infer<typeof CircuitSchema>>, userId: string = 'system') {
    const result = await transactionService.executeWithRetry(async (tx) => {
      const panel = await tx.panel.findUnique({
        where: { id: panelId }
      });
      
      if (!panel) {
        throw new AppError('Panel not found', 404);
      }
      
      // Validate total space requirement
      const totalSpaces = circuitData.reduce((sum, circuit) => 
        sum + (circuit.is_space ? 0 : circuit.poles), 0
      );
      
      if (totalSpaces > panel.spaces_total) {
        throw new AppError('Total circuits exceed panel capacity', 400);
      }
      
      // Validate circuit numbers are unique
      const circuitNumbers = circuitData.map(c => c.circuit_number);
      const uniqueNumbers = new Set(circuitNumbers);
      if (circuitNumbers.length !== uniqueNumbers.size) {
        throw new AppError('Duplicate circuit numbers in batch', 400);
      }
      
      // Check for existing circuits with same numbers
      const existingCircuits = await tx.circuit.findMany({
        where: {
          panel_id: panelId,
          circuit_number: { in: circuitNumbers }
        }
      });
      
      if (existingCircuits.length > 0) {
        const existingNumbers = existingCircuits.map(c => c.circuit_number).join(', ');
        throw new AppError(`Circuit numbers already exist: ${existingNumbers}`, 400);
      }
      
      // Create all circuits within transaction
      const circuits: Circuit[] = [];
      for (const data of circuitData) {
        const circuit = await tx.circuit.create({
          data: {
            ...data,
            panel_id: panelId,
            calculated_load: data.connected_load * data.demand_factor,
            created_at: new Date(),
            updated_at: new Date()
          }
        });
        circuits.push(circuit);
      }
      
      // Create audit log
      await createAuditLog({
        action: 'CIRCUIT_BULK_CREATE',
        userId: userId,
        resourceType: 'PANEL_CIRCUITS',
        resourceId: panelId,
        details: {
          circuitCount: circuits.length,
          circuitNumbers: circuitNumbers
        }
      });
      
      // Update panel spaces_used
      await tx.panel.update({
        where: { id: panelId },
        data: {
          spaces_used: totalSpaces,
          updated_at: new Date()
        }
      });
      
      return circuits;
    }, 3); // Retry up to 3 times
    
    if (!result.success) {
      logger.error('Failed to bulk create circuits:', result.error);
      throw result.error || new AppError('Failed to bulk create circuits', 500);
    }
    
    logger.info(`Bulk created ${result.data!.length} circuits for panel ${panelId}`);
    
    // Trigger panel load recalculation outside transaction
    await panelService.calculatePanelLoad(panelId, userId);
    
    return result.data!;
  },

  // Move circuit to different position with transaction support
  async moveCircuit(circuitId: string, newCircuitNumber: number, userId: string = 'system') {
    const result = await transactionService.executeTransaction(async (tx) => {
      const circuit = await tx.circuit.findUnique({
        where: { id: circuitId }
      });
      
      if (!circuit) {
        throw new AppError('Circuit not found', 404);
      }
      
      const oldCircuitNumber = circuit.circuit_number;
      
      // Check if target position is available
      const targetCircuit = await tx.circuit.findUnique({
        where: {
          panel_id_circuit_number: {
            panel_id: circuit.panel_id,
            circuit_number: newCircuitNumber
          }
        }
      });
      
      let swapOccurred = false;
      
      if (targetCircuit && targetCircuit.id !== circuitId) {
        // Swap positions
        await tx.circuit.update({
          where: { id: targetCircuit.id },
          data: { 
            circuit_number: oldCircuitNumber,
            updated_at: new Date()
          }
        });
        
        await tx.circuit.update({
          where: { id: circuitId },
          data: { 
            circuit_number: newCircuitNumber,
            updated_at: new Date()
          }
        });
        
        swapOccurred = true;
      } else {
        // Just move to empty position
        await tx.circuit.update({
          where: { id: circuitId },
          data: { 
            circuit_number: newCircuitNumber,
            updated_at: new Date()
          }
        });
      }
      
      // Create audit log
      await createAuditLog({
        action: 'CIRCUIT_MOVE',
        userId: userId,
        resourceType: 'CIRCUIT',
        resourceId: circuitId,
        details: {
          from: oldCircuitNumber,
          to: newCircuitNumber,
          swapped: swapOccurred,
          swappedWith: targetCircuit?.id
        }
      });
      
      return {
        moved: true,
        swapped: swapOccurred,
        fromNumber: oldCircuitNumber,
        toNumber: newCircuitNumber
      };
    });
    
    if (!result.success) {
      logger.error('Failed to move circuit:', result.error);
      throw result.error || new AppError('Failed to move circuit', 500);
    }
    
    logger.info(`Circuit move completed`, result.data);
    return result.data!;
  },

  // Balance panel loads
  async balancePanelLoads(panelId: string) {
    try {
      const panel = await prisma.panel.findUnique({
        where: { id: panelId },
        include: { circuits: true }
      });
      
      if (!panel) {
        throw new AppError('Panel not found', 404);
      }
      
      // Get single-pole circuits that can be rearranged
      const singlePoleCircuits = panel.circuits.filter(c => 
        c.poles === 1 && !c.is_spare && !c.is_space
      ).sort((a, b) => b.calculated_load - a.calculated_load);
      
      if (panel.phase_config === 'SINGLE_PHASE') {
        // Balance between L1 and L2
        let l1Load = 0;
        let l2Load = 0;
        const l1Circuits: Circuit[] = [];
        const l2Circuits: Circuit[] = [];
        
        // Distribute circuits to balance loads
        for (const circuit of singlePoleCircuits) {
          if (l1Load <= l2Load) {
            l1Circuits.push(circuit);
            l1Load += circuit.calculated_load;
          } else {
            l2Circuits.push(circuit);
            l2Load += circuit.calculated_load;
          }
        }
        
        // Assign odd positions to L1, even to L2
        const updates = [];
        let position = 1;
        
        for (const circuit of l1Circuits) {
          updates.push(prisma.circuit.update({
            where: { id: circuit.id },
            data: { circuit_number: position }
          }));
          position += 2;
        }
        
        position = 2;
        for (const circuit of l2Circuits) {
          updates.push(prisma.circuit.update({
            where: { id: circuit.id },
            data: { circuit_number: position }
          }));
          position += 2;
        }
        
        await prisma.$transaction(updates);
      } else {
        // Three-phase balancing
        let phaseALoad = 0;
        let phaseBLoad = 0;
        let phaseCLoad = 0;
        const phaseACircuits: Circuit[] = [];
        const phaseBCircuits: Circuit[] = [];
        const phaseCCircuits: Circuit[] = [];
        
        // Distribute circuits to balance loads across three phases
        for (const circuit of singlePoleCircuits) {
          if (phaseALoad <= phaseBLoad && phaseALoad <= phaseCLoad) {
            phaseACircuits.push(circuit);
            phaseALoad += circuit.calculated_load;
          } else if (phaseBLoad <= phaseCLoad) {
            phaseBCircuits.push(circuit);
            phaseBLoad += circuit.calculated_load;
          } else {
            phaseCCircuits.push(circuit);
            phaseCLoad += circuit.calculated_load;
          }
        }
        
        // Assign positions based on phase rotation
        const updates = [];
        let position = 1;
        
        const maxCircuits = Math.max(
          phaseACircuits.length,
          phaseBCircuits.length,
          phaseCCircuits.length
        );
        
        for (let i = 0; i < maxCircuits; i++) {
          if (i < phaseACircuits.length) {
            updates.push(prisma.circuit.update({
              where: { id: phaseACircuits[i].id },
              data: { circuit_number: position }
            }));
            position++;
          }
          if (i < phaseBCircuits.length) {
            updates.push(prisma.circuit.update({
              where: { id: phaseBCircuits[i].id },
              data: { circuit_number: position }
            }));
            position++;
          }
          if (i < phaseCCircuits.length) {
            updates.push(prisma.circuit.update({
              where: { id: phaseCCircuits[i].id },
              data: { circuit_number: position }
            }));
            position++;
          }
        }
        
        await prisma.$transaction(updates);
      }
      
      logger.info(`Balanced loads for panel ${panelId}`);
      
      // Recalculate panel load
      await panelService.calculatePanelLoad(panelId);
      
      return true;
    } catch (error) {
      if (error instanceof AppError) throw error;
      logger.error('Error balancing panel loads:', error);
      throw new AppError('Failed to balance panel loads', 500);
    }
  }
};