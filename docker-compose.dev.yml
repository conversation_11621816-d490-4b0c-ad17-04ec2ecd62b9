version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
      target: builder
    container_name: electrical_backend_dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=***************************************************/electrical_contracting
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev_jwt_secret_change_in_production
      - CORS_ORIGIN=http://localhost:5173
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=electrical_token_123
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=electrical123
      - CHROMADB_URL=http://chromadb:8000
    volumes:
      - ./app/backend/src:/app/app/backend/src
      - ./app/backend/prisma:/app/app/backend/prisma
      - ./shared/src:/app/shared/src
    command: npm run dev
    depends_on:
      - postgres
      - redis
      - neo4j
      - chromadb
      - influxdb
    networks:
      - electrical-network

  # Frontend UI
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
      target: builder
    container_name: electrical_frontend_dev
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000
      - VITE_SOCKET_URL=ws://localhost:3000
    volumes:
      - ./app/frontend/src:/app/app/frontend/src
      - ./app/frontend/index.html:/app/app/frontend/index.html
      - ./shared/src:/app/shared/src
    command: npm run dev -- --host
    depends_on:
      - backend
    networks:
      - electrical-network

  # Agents Service
  agents:
    build:
      context: .
      dockerfile: docker/Dockerfile.agents
      target: builder
    container_name: electrical_agents_dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=electrical123
      - CHROMADB_URL=http://chromadb:8000
      - BACKEND_URL=http://backend:3000
    volumes:
      - ./app/agents/src:/app/app/agents/src
      - ./shared/src:/app/shared/src
    command: npm run dev
    depends_on:
      - redis
      - neo4j
      - chromadb
    networks:
      - electrical-network

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: electrical_postgres_dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=electrical
      - POSTGRES_PASSWORD=electrical123
      - POSTGRES_DB=electrical_contracting
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U electrical"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - electrical-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: electrical_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes --requirepass electrical123
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "electrical123", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - electrical-network

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5
    container_name: electrical_neo4j_dev
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/electrical123
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4JLABS_PLUGINS=["apoc"]
    volumes:
      - neo4j_data_dev:/data
      - neo4j_logs_dev:/logs
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "electrical123", "MATCH () RETURN count(*) LIMIT 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - electrical-network

  # ChromaDB Vector Store
  chromadb:
    image: chromadb/chroma:latest
    container_name: electrical_chromadb_dev
    ports:
      - "8000:8000"
    volumes:
      - chromadb_data_dev:/chroma/chroma
    environment:
      - IS_PERSISTENT=TRUE
      - ANONYMIZED_TELEMETRY=FALSE
    networks:
      - electrical-network

  # InfluxDB Time Series Database
  influxdb:
    image: influxdb:2
    container_name: electrical_influxdb_dev
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=electrical
      - DOCKER_INFLUXDB_INIT_PASSWORD=electrical123
      - DOCKER_INFLUXDB_INIT_ORG=electrical_contracting
      - DOCKER_INFLUXDB_INIT_BUCKET=metrics
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=electrical_token_123
    volumes:
      - influxdb_data_dev:/var/lib/influxdb2
    networks:
      - electrical-network

  # Development Tools
  adminer:
    image: adminer
    container_name: electrical_adminer_dev
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    networks:
      - electrical-network

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog
    container_name: electrical_mailhog_dev
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - electrical-network

networks:
  electrical-network:
    driver: bridge

volumes:
  postgres_data_dev:
  redis_data_dev:
  neo4j_data_dev:
  neo4j_logs_dev:
  chromadb_data_dev:
  influxdb_data_dev: