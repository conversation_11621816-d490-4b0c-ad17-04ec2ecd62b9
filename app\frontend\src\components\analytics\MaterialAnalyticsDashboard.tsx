import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ScatterChart,
  Scatter,
  ZAxis,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import type { MaterialAnalytics, VendorPerformance } from '../../services/analyticsService';
import { format } from 'date-fns';
import { Package, TrendingUp, TrendingDown, DollarSign, BarChart3, AlertCircle } from 'lucide-react';

interface MaterialAnalyticsDashboardProps {
  dateRange: { start: Date; end: Date };
}

const MaterialAnalyticsDashboard: React.FC<MaterialAnalyticsDashboardProps> = ({ dateRange }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedVendor, setSelectedVendor] = useState<string>('all');

  // Fetch material analytics
  const { data: materialData } = useQuery({
    queryKey: ['material-analytics', dateRange, selectedCategory, selectedVendor],
    queryFn: () => analyticsService.getMaterialAnalytics({
      dateRange,
      category: selectedCategory === 'all' ? undefined : selectedCategory,
      vendorId: selectedVendor === 'all' ? undefined : selectedVendor,
    }),
  });

  // Fetch material cost trends
  const { data: costTrends } = useQuery({
    queryKey: ['material-cost-trends', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { month: 'Jan', wire: 45000, conduit: 28000, devices: 18000, panels: 35000 },
        { month: 'Feb', wire: 48000, conduit: 26000, devices: 20000, panels: 32000 },
        { month: 'Mar', wire: 52000, conduit: 29000, devices: 19000, panels: 38000 },
        { month: 'Apr', wire: 49000, conduit: 31000, devices: 22000, panels: 36000 },
        { month: 'May', wire: 53000, conduit: 30000, devices: 21000, panels: 40000 },
        { month: 'Jun', wire: 55000, conduit: 32000, devices: 23000, panels: 42000 },
      ];
    },
  });

  // Fetch vendor performance data
  const { data: vendorData } = useQuery({
    queryKey: ['vendor-performance', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        {
          vendorName: 'ElectroSupply Co',
          orderCount: 145,
          avgPrice: 100,
          deliveryPerformance: 94,
          qualityScore: 98,
          totalSpend: 145000,
        },
        {
          vendorName: 'Industrial Electric',
          orderCount: 98,
          avgPrice: 105,
          deliveryPerformance: 88,
          qualityScore: 95,
          totalSpend: 102900,
        },
        {
          vendorName: 'ProWire Distribution',
          orderCount: 76,
          avgPrice: 95,
          deliveryPerformance: 92,
          qualityScore: 96,
          totalSpend: 72200,
        },
        {
          vendorName: 'National Electric Supply',
          orderCount: 112,
          avgPrice: 98,
          deliveryPerformance: 90,
          qualityScore: 94,
          totalSpend: 109760,
        },
      ];
    },
  });

  // Fetch inventory turnover data
  const { data: turnoverData } = useQuery({
    queryKey: ['inventory-turnover', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { category: 'Wire', turnoverRate: 8.5, targetRate: 10 },
        { category: 'Conduit', turnoverRate: 6.2, targetRate: 8 },
        { category: 'Devices', turnoverRate: 12.3, targetRate: 12 },
        { category: 'Panels', turnoverRate: 4.5, targetRate: 6 },
        { category: 'Fixtures', turnoverRate: 7.8, targetRate: 8 },
        { category: 'Misc', turnoverRate: 5.1, targetRate: 7 },
      ];
    },
  });

  // Fetch price variance data
  const { data: priceVariance } = useQuery({
    queryKey: ['price-variance', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { material: '12 AWG THHN', budgeted: 0.45, actual: 0.52, variance: 15.6 },
        { material: '3/4" EMT', budgeted: 2.80, actual: 3.10, variance: 10.7 },
        { material: '20A Breaker', budgeted: 12.50, actual: 13.25, variance: 6.0 },
        { material: '4" Square Box', budgeted: 1.85, actual: 1.95, variance: 5.4 },
        { material: '200A Panel', budgeted: 185.00, actual: 195.00, variance: 5.4 },
      ];
    },
  });

  // Fetch most used materials
  const { data: topMaterials } = useQuery({
    queryKey: ['top-materials', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { name: '12 AWG THHN Black', quantity: 15800, unit: 'FT', cost: 8216 },
        { name: '3/4" EMT Conduit', quantity: 4200, unit: 'FT', cost: 13020 },
        { name: '1G Deep Device Box', quantity: 850, unit: 'EA', cost: 1275 },
        { name: '20A Single Pole Breaker', quantity: 320, unit: 'EA', cost: 4000 },
        { name: '4" Square Box', quantity: 680, unit: 'EA', cost: 1326 },
        { name: '15A Duplex Receptacle', quantity: 420, unit: 'EA', cost: 1680 },
        { name: '3-Way Switch', quantity: 180, unit: 'EA', cost: 900 },
        { name: '1/2" EMT Connector', quantity: 1200, unit: 'EA', cost: 600 },
      ];
    },
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex items-center gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Category
          </label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="block w-48 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="all">All Categories</option>
            <option value="WIRE">Wire</option>
            <option value="CONDUIT">Conduit</option>
            <option value="DEVICES">Devices</option>
            <option value="PANELS">Panels</option>
            <option value="FIXTURES">Fixtures</option>
            <option value="MISC">Miscellaneous</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Vendor
          </label>
          <select
            value={selectedVendor}
            onChange={(e) => setSelectedVendor(e.target.value)}
            className="block w-48 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="all">All Vendors</option>
            {vendorData?.map((vendor) => (
              <option key={vendor.vendorName} value={vendor.vendorName}>
                {vendor.vendorName}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Material Cost Trends */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Material Cost Trends by Category
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={costTrends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
            <Tooltip formatter={(value) => formatCurrency(value as number)} />
            <Legend />
            <Area type="monotone" dataKey="wire" stackId="1" stroke="#3B82F6" fill="#3B82F6" name="Wire" />
            <Area type="monotone" dataKey="conduit" stackId="1" stroke="#10B981" fill="#10B981" name="Conduit" />
            <Area type="monotone" dataKey="devices" stackId="1" stroke="#F59E0B" fill="#F59E0B" name="Devices" />
            <Area type="monotone" dataKey="panels" stackId="1" stroke="#8B5CF6" fill="#8B5CF6" name="Panels" />
          </AreaChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Vendor Performance Comparison */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Vendor Performance Comparison
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={vendorData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="vendorName" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar
                name="Delivery Performance"
                dataKey="deliveryPerformance"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
              <Radar
                name="Quality Score"
                dataKey="qualityScore"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.6}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </Card>

        {/* Inventory Turnover Rates */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Inventory Turnover Rates
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={turnoverData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip formatter={(value) => `${value}x`} />
              <Legend />
              <Bar dataKey="turnoverRate" fill="#3B82F6" name="Actual" />
              <Bar dataKey="targetRate" fill="#10B981" name="Target" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Price Variance Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Price Variance Analysis
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Material
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Budgeted Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actual Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Variance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Impact
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {priceVariance?.map((item) => (
                <tr key={item.material}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {item.material}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    ${item.budgeted.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    ${item.actual.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.variance > 10 ? 'bg-red-100 text-red-800' : 
                      item.variance > 5 ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-green-100 text-green-800'
                    }`}>
                      {item.variance > 0 ? '+' : ''}{item.variance.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {item.variance > 10 && <AlertCircle className="h-4 w-4 text-red-500 mr-1" />}
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        ${((item.actual - item.budgeted) * 1000).toFixed(0)} impact
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Most Used Materials */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Materials by Usage
          </h3>
          <div className="space-y-3">
            {topMaterials?.slice(0, 8).map((material, index) => (
              <div key={material.name} className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400 mr-3 w-6">
                    {index + 1}.
                  </span>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {material.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {material.quantity.toLocaleString()} {material.unit}
                    </p>
                  </div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(material.cost)}
                </span>
              </div>
            ))}
          </div>
        </Card>

        {/* Vendor Spend Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Vendor Spend Distribution
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={vendorData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="totalSpend"
              >
                {vendorData?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(value as number)} />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
    </div>
  );
};

export default MaterialAnalyticsDashboard;