import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Alert } from '../../components/ui/alert';
import { permitService, PermitDocument } from '../../services/permitService';
import { PermitApplicationForm } from '../../components/permits/PermitApplicationForm';
import { PermitDocumentList } from '../../components/permits/PermitDocumentList';
import { InspectionRequestForm } from '../../components/permits/InspectionRequestForm';
import { CalculationReportsSection } from '../../components/permits/CalculationReportsSection';
import { 
  FileText, 
  Plus, 
  Download, 
  Send, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  Clock,
  FileSignature
} from 'lucide-react';

const PermitDocumentsPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [documents, setDocuments] = useState<PermitDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('documents');
  const [showNewPermitForm, setShowNewPermitForm] = useState(false);
  const [showInspectionForm, setShowInspectionForm] = useState(false);

  useEffect(() => {
    if (projectId) {
      loadDocuments();
    }
  }, [projectId]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const docs = await permitService.getProjectPermitDocuments(projectId!);
      setDocuments(docs);
    } catch (err) {
      setError('Failed to load permit documents');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentCreated = () => {
    setShowNewPermitForm(false);
    setShowInspectionForm(false);
    loadDocuments();
  };

  const getDocumentStats = () => {
    const stats = {
      total: documents.length,
      draft: documents.filter(d => d.status === 'DRAFT').length,
      ready: documents.filter(d => d.status === 'READY').length,
      submitted: documents.filter(d => d.status === 'SUBMITTED').length,
      approved: documents.filter(d => d.status === 'APPROVED').length,
      rejected: documents.filter(d => d.status === 'REJECTED').length,
    };
    return stats;
  };

  const stats = getDocumentStats();

  if (!projectId) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <div>No project selected. Please select a project first.</div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Permit Documents</h1>
          <p className="text-gray-600 mt-1">Manage permit applications and related documents</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowNewPermitForm(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Permit Application
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowInspectionForm(true)}
            className="flex items-center gap-2"
          >
            <FileSignature className="h-4 w-4" />
            Request Inspection
          </Button>
        </div>
      </div>

      {/* Document Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-sm text-gray-600">Total Documents</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">{stats.draft}</div>
            <p className="text-sm text-gray-600">Drafts</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.ready}</div>
            <p className="text-sm text-gray-600">Ready</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.submitted}</div>
            <p className="text-sm text-gray-600">Submitted</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-sm text-gray-600">Approved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-sm text-gray-600">Rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="calculations">Calculation Reports</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          {loading ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading documents...</p>
              </CardContent>
            </Card>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <div>{error}</div>
            </Alert>
          ) : (
            <PermitDocumentList 
              documents={documents} 
              onUpdate={loadDocuments}
              projectId={projectId}
            />
          )}
        </TabsContent>

        <TabsContent value="calculations" className="space-y-4">
          <CalculationReportsSection projectId={projectId} />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Jurisdiction Templates</CardTitle>
              <CardDescription>
                Pre-configured templates for common jurisdictions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Select from available jurisdiction templates when creating new permit documents.
                Templates include all required fields and local amendments.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* New Permit Application Form Modal */}
      {showNewPermitForm && (
        <PermitApplicationForm
          projectId={projectId}
          onClose={() => setShowNewPermitForm(false)}
          onSuccess={handleDocumentCreated}
        />
      )}

      {/* Inspection Request Form Modal */}
      {showInspectionForm && (
        <InspectionRequestForm
          projectId={projectId}
          onClose={() => setShowInspectionForm(false)}
          onSuccess={handleDocumentCreated}
        />
      )}
    </div>
  );
};

export default PermitDocumentsPage;