import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { ConduitFillCalculation, WireInConduit } from '../../types/electrical';
import { CalculationService } from '../../services/calculationService';
import { CONDUIT_SIZES, WIRE_SIZES } from '../../constants/electrical';

export const ConduitFillCalculator: React.FC = () => {
  const [calculation, setCalculation] = useState<ConduitFillCalculation>({
    conduitType: 'EMT',
    conduitSize: '3/4',
    wires: [],
    fillPercentage: 0,
    maxFill: 40,
    isCompliant: true,
    timestamp: new Date(),
  });

  const [fillAnimation] = useState(new Animated.Value(0));

  const addWire = () => {
    setCalculation({
      ...calculation,
      wires: [
        ...calculation.wires,
        { size: '12', type: 'THHN', quantity: 1, area: 0.0133 },
      ],
    });
  };

  const updateWire = (index: number, field: keyof WireInConduit, value: any) => {
    const updatedWires = [...calculation.wires];
    updatedWires[index] = { ...updatedWires[index], [field]: value };
    setCalculation({ ...calculation, wires: updatedWires });
  };

  const removeWire = (index: number) => {
    const updatedWires = [...calculation.wires];
    updatedWires.splice(index, 1);
    setCalculation({ ...calculation, wires: updatedWires });
  };

  const handleCalculate = () => {
    if (calculation.wires.length === 0) {
      Alert.alert('Error', 'Please add at least one wire');
      return;
    }

    const result = CalculationService.calculateConduitFill(calculation);
    setCalculation(result);

    // Animate the fill gauge
    Animated.timing(fillAnimation, {
      toValue: result.fillPercentage,
      duration: 1000,
      useNativeDriver: false,
    }).start();

    // Save for offline access
    CalculationService.saveCalculation('conduit-fill', result);
  };

  const renderFillGauge = () => {
    const fillColor = calculation.isCompliant ? '#4CAF50' : '#f44336';
    const rotation = fillAnimation.interpolate({
      inputRange: [0, 100],
      outputRange: ['0deg', '180deg'],
    });

    return (
      <View style={styles.gaugeContainer}>
        <View style={styles.gauge}>
          <View style={styles.gaugeBackground}>
            <Animated.View
              style={[
                styles.gaugeFill,
                {
                  backgroundColor: fillColor,
                  transform: [{ rotate: rotation }],
                },
              ]}
            />
          </View>
          <View style={styles.gaugeCenter}>
            <Text style={styles.gaugePercentage}>
              {calculation.fillPercentage.toFixed(1)}%
            </Text>
            <Text style={styles.gaugeLabel}>Fill</Text>
          </View>
        </View>
        <View style={styles.gaugeLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#4CAF50' }]} />
            <Text style={styles.legendText}>Compliant (≤{calculation.maxFill}%)</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#f44336' }]} />
            <Text style={styles.legendText}>Over Limit (>{calculation.maxFill}%)</Text>
          </View>
        </View>
      </View>
    );
  };

  const getTotalWireCount = () => {
    return calculation.wires.reduce((sum, wire) => sum + wire.quantity, 0);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Conduit Fill Calculator</Text>
        <Text style={styles.subtitle}>Visual Fill Gauge</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Conduit Information</Text>

        <View style={styles.row}>
          <View style={styles.halfInput}>
            <Text style={styles.label}>Conduit Type</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.conduitType}
                onValueChange={(value) => setCalculation({ ...calculation, conduitType: value as any })}
                style={styles.picker}
              >
                <Picker.Item label="EMT" value="EMT" />
                <Picker.Item label="PVC" value="PVC" />
                <Picker.Item label="Rigid" value="RIGID" />
                <Picker.Item label="FMC" value="FMC" />
                <Picker.Item label="LFMC" value="LFMC" />
              </Picker>
            </View>
          </View>

          <View style={styles.halfInput}>
            <Text style={styles.label}>Conduit Size</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={calculation.conduitSize}
                onValueChange={(value) => setCalculation({ ...calculation, conduitSize: value })}
                style={styles.picker}
              >
                {(CONDUIT_SIZES[calculation.conduitType as keyof typeof CONDUIT_SIZES] || CONDUIT_SIZES.EMT).map(size => (
                  <Picker.Item key={size} label={`${size}"`} value={size} />
                ))}
              </Picker>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Wires in Conduit</Text>
          <TouchableOpacity style={styles.addButton} onPress={addWire}>
            <Text style={styles.addButtonText}>+ Add Wire</Text>
          </TouchableOpacity>
        </View>

        {calculation.wires.length === 0 ? (
          <Text style={styles.noWiresText}>No wires added. Tap "Add Wire" to begin.</Text>
        ) : (
          calculation.wires.map((wire, index) => (
            <View key={index} style={styles.wireRow}>
              <View style={styles.wireSize}>
                <Text style={styles.wireLabel}>Size</Text>
                <View style={styles.wirePickerContainer}>
                  <Picker
                    selectedValue={wire.size}
                    onValueChange={(value) => updateWire(index, 'size', value)}
                    style={styles.wirePicker}
                  >
                    {WIRE_SIZES.slice(0, 15).map(size => (
                      <Picker.Item key={size} label={`#${size}`} value={size} />
                    ))}
                  </Picker>
                </View>
              </View>

              <View style={styles.wireType}>
                <Text style={styles.wireLabel}>Type</Text>
                <View style={styles.wirePickerContainer}>
                  <Picker
                    selectedValue={wire.type}
                    onValueChange={(value) => updateWire(index, 'type', value)}
                    style={styles.wirePicker}
                  >
                    <Picker.Item label="THHN" value="THHN" />
                    <Picker.Item label="THWN" value="THWN" />
                    <Picker.Item label="XHHW" value="XHHW" />
                  </Picker>
                </View>
              </View>

              <View style={styles.wireQuantity}>
                <Text style={styles.wireLabel}>Qty</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => updateWire(index, 'quantity', Math.max(1, wire.quantity - 1))}
                  >
                    <Text style={styles.quantityButtonText}>-</Text>
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{wire.quantity}</Text>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => updateWire(index, 'quantity', wire.quantity + 1)}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeWire(index)}
              >
                <Text style={styles.removeButtonText}>×</Text>
              </TouchableOpacity>
            </View>
          ))
        )}

        <View style={styles.wireSummary}>
          <Text style={styles.wireSummaryText}>
            Total Conductors: {getTotalWireCount()}
          </Text>
          <Text style={styles.wireSummaryText}>
            Max Fill: {getTotalWireCount() <= 1 ? '53%' : getTotalWireCount() === 2 ? '31%' : '40%'}
          </Text>
        </View>
      </View>

      <TouchableOpacity style={styles.calculateButton} onPress={handleCalculate}>
        <Text style={styles.calculateButtonText}>Calculate Fill</Text>
      </TouchableOpacity>

      {calculation.fillPercentage > 0 && (
        <View style={styles.resultSection}>
          {renderFillGauge()}

          <View style={styles.complianceCard}>
            <Text style={styles.complianceStatus}>
              {calculation.isCompliant ? '✓ COMPLIANT' : '✗ OVERFILLED'}
            </Text>
            <Text style={styles.complianceText}>
              {calculation.isCompliant
                ? 'This conduit configuration meets NEC requirements.'
                : 'This conduit is overfilled. Consider using a larger conduit size or multiple conduits.'}
            </Text>
          </View>

          <View style={styles.detailsCard}>
            <Text style={styles.detailsTitle}>Fill Details</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Conduit:</Text>
              <Text style={styles.detailValue}>
                {calculation.conduitSize}" {calculation.conduitType}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Total Conductors:</Text>
              <Text style={styles.detailValue}>{getTotalWireCount()}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Fill Percentage:</Text>
              <Text style={[
                styles.detailValue,
                { color: calculation.isCompliant ? '#4CAF50' : '#f44336' }
              ]}>
                {calculation.fillPercentage.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Maximum Allowed:</Text>
              <Text style={styles.detailValue}>{calculation.maxFill}%</Text>
            </View>
          </View>

          <View style={styles.necReference}>
            <Text style={styles.necReferenceText}>
              Per NEC Chapter 9, Table 1 and Annex C
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  row: {
    flexDirection: 'row',
    marginTop: 15,
  },
  halfInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  noWiresText: {
    textAlign: 'center',
    color: '#666',
    marginVertical: 20,
  },
  wireRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  wireSize: {
    flex: 2,
    marginRight: 10,
  },
  wireType: {
    flex: 2,
    marginRight: 10,
  },
  wireQuantity: {
    flex: 1,
    marginRight: 10,
  },
  wireLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  wirePickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
    height: 40,
    justifyContent: 'center',
  },
  wirePicker: {
    height: 40,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    backgroundColor: '#f9f9f9',
    height: 40,
  },
  quantityButton: {
    width: 30,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: 18,
    color: '#2196F3',
  },
  quantityText: {
    fontSize: 16,
    color: '#333',
  },
  removeButton: {
    width: 30,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    fontSize: 24,
    color: '#f44336',
  },
  wireSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  wireSummaryText: {
    fontSize: 14,
    color: '#666',
  },
  calculateButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 18,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  calculateButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultSection: {
    marginBottom: 20,
  },
  gaugeContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  gauge: {
    width: 200,
    height: 100,
    position: 'relative',
  },
  gaugeBackground: {
    position: 'absolute',
    width: 200,
    height: 100,
    backgroundColor: '#e0e0e0',
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
    overflow: 'hidden',
  },
  gaugeFill: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    bottom: 0,
    transformOrigin: 'center bottom',
  },
  gaugeCenter: {
    position: 'absolute',
    bottom: 0,
    left: 50,
    right: 50,
    alignItems: 'center',
  },
  gaugePercentage: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#333',
  },
  gaugeLabel: {
    fontSize: 14,
    color: '#666',
  },
  gaugeLegend: {
    marginTop: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  legendColor: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 10,
  },
  legendText: {
    fontSize: 14,
    color: '#666',
  },
  complianceCard: {
    backgroundColor: 'white',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  complianceStatus: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  complianceText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  detailsCard: {
    backgroundColor: 'white',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  necReference: {
    alignItems: 'center',
    marginVertical: 10,
  },
  necReferenceText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});