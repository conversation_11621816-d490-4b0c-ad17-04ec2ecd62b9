# Example environment configuration file
# Copy this to .env and update with your values

# Environment
NODE_ENV=production
LOG_LEVEL=info

# Application
APP_NAME="Electrical Contractor"
APP_URL=https://electrical-app.com
API_URL=https://api.electrical-app.com

# Database
DATABASE_URL=postgresql://electrical:password@localhost:5432/electrical_contracting
POSTGRES_USER=electrical
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=electrical_contracting

# Redis
REDIS_URL=redis://:password@localhost:6379
REDIS_PASSWORD=your_redis_password

# Authentication
JWT_SECRET=your_very_long_random_jwt_secret
JWT_EXPIRES_IN=7d
SESSION_SECRET=your_very_long_random_session_secret
REFRESH_TOKEN_SECRET=your_very_long_random_refresh_secret

# Neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# ChromaDB
CHROMADB_URL=http://localhost:8000
CHROMADB_TOKEN=your_chromadb_token

# InfluxDB
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=electrical_contracting
INFLUXDB_BUCKET=metrics
INFLUXDB_USER=electrical
INFLUXDB_PASSWORD=your_influxdb_password

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Storage
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=********

# Security
CORS_ORIGIN=https://electrical-app.com,https://www.electrical-app.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
BCRYPT_ROUNDS=12

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
PROMETHEUS_ENABLED=true
METRICS_PORT=9090

# External APIs
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
WEATHER_API_KEY=your_weather_api_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

# Backup
S3_ACCESS_KEY_ID=your_s3_access_key
S3_SECRET_ACCESS_KEY=your_s3_secret_key
S3_BACKUP_BUCKET=electrical-backups
S3_REGION=us-east-1

# Feature Flags
ENABLE_MOBILE_SYNC=true
ENABLE_AI_AGENTS=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_REAL_TIME_UPDATES=true

# Development
DEV_MODE=false
DEBUG_SQL=false
SEED_DATABASE=false

# Mobile App
MOBILE_API_KEY=your_mobile_api_key
PUSH_NOTIFICATION_SERVER=https://fcm.googleapis.com/fcm/send
FCM_SERVER_KEY=your_fcm_server_key

# Webhooks
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/WEBHOOK/URL