import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Cable, AlertCircle, FileText, Info } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';

const wireSizeSchema = z.object({
  project_id: z.string().optional(),
  load_amps: z.number().positive(),
  voltage: z.number().positive(),
  phase: z.enum(['SINGLE', 'THREE']),
  conductor_material: z.enum(['COPPER', 'ALUMINUM']),
  insulation_type: z.enum(['THHN', 'XHHW', 'RHW', 'USE', 'UF']),
  ambient_temp_f: z.number().default(86),
  conductors_in_conduit: z.number().int().positive().default(3),
  length_feet: z.number().positive(),
  voltage_drop_limit: z.number().min(1).max(5).default(3),
  ground_type: z.enum(['EQUIPMENT', 'GROUNDING_ELECTRODE']).default('EQUIPMENT'),
  continuous_load: z.boolean().default(false),
  motor_load: z.boolean().default(false),
  terminal_rating: z.enum(['60C', '75C', '90C']).default('75C'),
});

type WireSizeFormData = z.infer<typeof wireSizeSchema>;

interface WireSizeCalculationResult {
  phaseWireSize: string;
  neutralWireSize: string;
  groundWireSize: string;
  baseLoadAmps: number;
  adjustedLoadAmps: number;
  requiredAmpacity: number;
  selectedWireAmpacity?: number;
  temperatureDeration?: number;
  conduitFillDeration?: number;
  totalDeration?: number;
  voltageDrop?: number;
  voltageDropPercent?: number;
  necReferences?: string[];
  warnings?: string[];
}

export function WireSizeCalculator() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<WireSizeCalculationResult | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<WireSizeFormData>({
    resolver: zodResolver(wireSizeSchema),
    defaultValues: {
      voltage: 120,
      phase: 'SINGLE',
      conductor_material: 'COPPER',
      insulation_type: 'THHN',
      ambient_temp_f: 86,
      conductors_in_conduit: 3,
      voltage_drop_limit: 3,
      ground_type: 'EQUIPMENT',
      continuous_load: false,
      motor_load: false,
      terminal_rating: '75C',
    },
  });

  const continuousLoad = watch('continuous_load');
  const motorLoad = watch('motor_load');

  const onSubmit = async (data: WireSizeFormData) => {
    setIsCalculating(true);
    try {
      const response = await api.post('/calculations/wire-size', data);
      setResult(response.data);
      toast.success('Wire size calculation completed');
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message || 'Calculation failed');
      } else if (typeof error === 'object' && error !== null && 'response' in error) {
        const axiosError = error as { response?: { data?: { error?: { message?: string } } } };
        toast.error(axiosError.response?.data?.error?.message || 'Calculation failed');
      } else {
        toast.error('Calculation failed');
      }
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Cable className="mr-2 h-5 w-5" />
          Wire Size Calculator (NEC Articles 310, 250)
        </h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Load Current (Amps)
              </label>
              <input
                type="number"
                {...register('load_amps', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="30"
                step="0.1"
              />
              {errors.load_amps && (
                <p className="mt-1 text-sm text-red-600">{errors.load_amps.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Voltage (V)
              </label>
              <select
                {...register('voltage', { valueAsNumber: true })}
                className="mt-1 input"
              >
                <option value={120}>120V</option>
                <option value={208}>208V</option>
                <option value={240}>240V</option>
                <option value={277}>277V</option>
                <option value={480}>480V</option>
                <option value={600}>600V</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phase
              </label>
              <select
                {...register('phase')}
                className="mt-1 input"
              >
                <option value="SINGLE">Single Phase</option>
                <option value="THREE">Three Phase</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conductor Material
              </label>
              <select
                {...register('conductor_material')}
                className="mt-1 input"
              >
                <option value="COPPER">Copper</option>
                <option value="ALUMINUM">Aluminum</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Insulation Type
              </label>
              <select
                {...register('insulation_type')}
                className="mt-1 input"
              >
                <option value="THHN">THHN/THWN-2</option>
                <option value="XHHW">XHHW-2</option>
                <option value="RHW">RHW-2</option>
                <option value="USE">USE-2</option>
                <option value="UF">UF-B</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Terminal Temperature Rating
              </label>
              <select
                {...register('terminal_rating')}
                className="mt-1 input"
              >
                <option value="60C">60°C</option>
                <option value="75C">75°C</option>
                <option value="90C">90°C</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">Typically 75°C for most equipment</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Circuit Length (feet)
              </label>
              <input
                type="number"
                {...register('length_feet', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="100"
              />
              {errors.length_feet && (
                <p className="mt-1 text-sm text-red-600">{errors.length_feet.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Voltage Drop Limit (%)
              </label>
              <input
                type="number"
                {...register('voltage_drop_limit', { valueAsNumber: true })}
                className="mt-1 input"
                step="0.5"
                min="1"
                max="5"
              />
              <p className="mt-1 text-xs text-gray-500">NEC recommends 3% for branch circuits</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Ambient Temperature (°F)
              </label>
              <input
                type="number"
                {...register('ambient_temp_f', { valueAsNumber: true })}
                className="mt-1 input"
              />
              <p className="mt-1 text-xs text-gray-500">Default 86°F (30°C)</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conductors in Conduit
              </label>
              <input
                type="number"
                {...register('conductors_in_conduit', { valueAsNumber: true })}
                className="mt-1 input"
                min="1"
              />
              <p className="mt-1 text-xs text-gray-500">For derating calculation</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Ground Wire Type
              </label>
              <select
                {...register('ground_type')}
                className="mt-1 input"
              >
                <option value="EQUIPMENT">Equipment Grounding</option>
                <option value="GROUNDING_ELECTRODE">Grounding Electrode</option>
              </select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('continuous_load')}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                Continuous load (3+ hours)
              </label>
            </div>
            {continuousLoad && (
              <div className="ml-6 text-xs text-amber-600 dark:text-amber-400 flex items-start">
                <Info className="h-4 w-4 mr-1 flex-shrink-0 mt-0.5" />
                <span>125% factor will be applied per NEC 215.2(A)(1)</span>
              </div>
            )}

            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('motor_load')}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                Motor load
              </label>
            </div>
            {motorLoad && (
              <div className="ml-6 text-xs text-amber-600 dark:text-amber-400 flex items-start">
                <Info className="h-4 w-4 mr-1 flex-shrink-0 mt-0.5" />
                <span>125% factor will be applied per NEC 430.22</span>
              </div>
            )}
          </div>

          <button
            type="submit"
            disabled={isCalculating}
            className="btn-primary w-full sm:w-auto"
          >
            {isCalculating ? (
              <>
                <div className="spinner mr-2" />
                Calculating...
              </>
            ) : (
              <>
                <Cable className="mr-2 h-4 w-4" />
                Calculate Wire Size
              </>
            )}
          </button>
        </form>

        {result && (
          <div className="mt-6 space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-green-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                    Recommended Wire Sizes
                  </h3>
                  <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                    <dl className="space-y-1">
                      <div className="flex justify-between">
                        <dt>Phase Conductor:</dt>
                        <dd className="font-bold text-lg">{result.phaseWireSize} AWG</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Neutral Conductor:</dt>
                        <dd className="font-medium">{result.neutralWireSize} AWG</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Ground Conductor:</dt>
                        <dd className="font-medium">{result.groundWireSize} AWG</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Calculation Details
                </h4>
              </div>
              <div className="p-4">
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt>Base Load Current:</dt>
                    <dd>{result.baseLoadAmps.toFixed(1)} A</dd>
                  </div>
                  {result.adjustedLoadAmps !== result.baseLoadAmps && (
                    <div className="flex justify-between text-primary-600 dark:text-primary-400">
                      <dt>Adjusted Load Current:</dt>
                      <dd>{result.adjustedLoadAmps.toFixed(1)} A</dd>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <dt>Wire Ampacity Required:</dt>
                    <dd>{result.requiredAmpacity.toFixed(1)} A</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Selected Wire Ampacity:</dt>
                    <dd>{result.selectedAmpacity} A</dd>
                  </div>
                  {result.temperatureFactor !== 1 && (
                    <div className="flex justify-between">
                      <dt>Temperature Derating:</dt>
                      <dd>{(result.temperatureFactor * 100).toFixed(0)}%</dd>
                    </div>
                  )}
                  {result.bundlingFactor !== 1 && (
                    <div className="flex justify-between">
                      <dt>Bundling Derating:</dt>
                      <dd>{(result.bundlingFactor * 100).toFixed(0)}%</dd>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <dt>Derated Ampacity:</dt>
                    <dd>{result.deratedAmpacity.toFixed(1)} A</dd>
                  </div>
                </dl>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Voltage Drop Verification
                </h4>
              </div>
              <div className="p-4">
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt>Calculated Voltage Drop:</dt>
                    <dd className={result.voltageDropPercent > result.voltageDropLimit ? 'text-red-600 dark:text-red-400' : ''}>
                      {result.voltageDropPercent.toFixed(2)}%
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Voltage Drop Limit:</dt>
                    <dd>{result.voltageDropLimit}%</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Voltage at Load:</dt>
                    <dd>{result.voltageAtLoad.toFixed(1)} V</dd>
                  </div>
                  {result.voltageDropPercent > result.voltageDropLimit && (
                    <div className="mt-2 p-2 bg-yellow-100 dark:bg-yellow-900/50 rounded">
                      <p className="text-xs text-yellow-800 dark:text-yellow-200">
                        Wire size was increased to meet voltage drop requirements
                      </p>
                    </div>
                  )}
                </dl>
              </div>
            </div>

            {result.factors && result.factors.length > 0 && (
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4">
                <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-2">
                  Applied Factors
                </h4>
                <ul className="list-disc list-inside text-sm text-amber-700 dark:text-amber-300 space-y-1">
                  {result.factors.map((factor: string, index: number) => (
                    <li key={index}>{factor}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-md p-4">
              <div className="flex">
                <FileText className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    NEC References
                  </h4>
                  <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                    {result.necReferences.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}