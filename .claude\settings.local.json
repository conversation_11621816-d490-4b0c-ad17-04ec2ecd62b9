{"permissions": {"allow": ["Bash(claude migrate-installer:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(export PATH=\"$HOME/.claude/local/bin:$PATH\")", "<PERSON><PERSON>(claude doctor)", "<PERSON><PERSON>(tty)", "Bash(npm uninstall:*)", "Bash(claude --version)", "Bash(find:*)", "Bash(ls:*)", "Bash(cmd.exe:*)", "Bash(sudo service:*)", "Bash(systemctl status:*)", "Bash(pg_lsclusters:*)", "mcp__memory__read_graph", "mcp__postgres__query", "mcp__context7__resolve-library-id", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(sudo:*)", "Bash(PGPASSWORD='itsMike818!':*)", "Bash(npm search:*)", "Bash(sqlite3:*)", "Bash(npx mcp-server-sqlite-npx:*)"], "deny": []}}