import React from 'react';
import { useParams } from 'react-router-dom';
import { PanelList } from '../components/panels';

export const PanelsPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();

  if (!projectId) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No project selected</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <PanelList />
    </div>
  );
};