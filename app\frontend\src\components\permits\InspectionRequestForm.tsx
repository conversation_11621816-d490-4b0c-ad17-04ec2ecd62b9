import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { permitService, PermitDocument, InspectionRequestData } from '../../services/permitService';
import { X, CheckSquare } from 'lucide-react';

interface InspectionRequestFormProps {
  projectId: string;
  onClose: () => void;
  onSuccess: () => void;
}

export const InspectionRequestForm: React.FC<InspectionRequestFormProps> = ({
  projectId,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [permitDocuments, setPermitDocuments] = useState<PermitDocument[]>([]);
  const [selectedPermit, setSelectedPermit] = useState<PermitDocument | null>(null);
  const [formData, setFormData] = useState<InspectionRequestData>({
    permit_number: '',
    inspection_type: 'ROUGH',
    requested_date: new Date().toISOString().split('T')[0],
    requested_time: 'Morning',
    work_completed: [],
    contractor_name: '',
    contractor_license: '',
    contractor_phone: '',
    onsite_contact: '',
    onsite_phone: '',
    special_instructions: '',
  });

  const inspectionTypes = [
    { value: 'ROUGH', label: 'Rough-In Inspection' },
    { value: 'SERVICE_RELEASE', label: 'Service Release' },
    { value: 'FINAL', label: 'Final Inspection' },
    { value: 'RE_INSPECTION', label: 'Re-inspection' },
  ];

  const workCompletedOptions = [
    'All wiring installed and secured',
    'Grounding and bonding complete',
    'Panels installed and circuits labeled',
    'All devices installed',
    'Service equipment installed',
    'Arc fault breakers installed',
    'GFCI protection installed',
    'Smoke detectors installed',
    'All corrections from previous inspection completed',
  ];

  useEffect(() => {
    loadPermitDocuments();
  }, []);

  const loadPermitDocuments = async () => {
    try {
      const docs = await permitService.getProjectPermitDocuments(projectId);
      const approvedPermits = docs.filter(
        d => d.document_type === 'PERMIT_APPLICATION' && 
        (d.status === 'APPROVED' || d.status === 'SUBMITTED')
      );
      setPermitDocuments(approvedPermits);
      
      if (approvedPermits.length > 0) {
        setSelectedPermit(approvedPermits[0]);
        setFormData(prev => ({
          ...prev,
          permit_number: approvedPermits[0].permit_number || '',
        }));
      }
    } catch (error) {
      console.error('Failed to load permit documents:', error);
    }
  };

  const handlePermitSelect = (permitId: string) => {
    const permit = permitDocuments.find(p => p.id === permitId);
    if (permit) {
      setSelectedPermit(permit);
      setFormData(prev => ({
        ...prev,
        permit_number: permit.permit_number || '',
      }));
      
      // Pre-fill contractor info from permit
      const permitFormData = JSON.parse(permit.form_data);
      if (permitFormData) {
        setFormData(prev => ({
          ...prev,
          contractor_name: permitFormData.contractor_name || '',
          contractor_license: permitFormData.contractor_license || '',
          contractor_phone: permitFormData.contractor_phone || '',
        }));
      }
    }
  };

  const handleWorkCompletedToggle = (item: string) => {
    setFormData(prev => ({
      ...prev,
      work_completed: prev.work_completed.includes(item)
        ? prev.work_completed.filter(i => i !== item)
        : [...prev.work_completed, item]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);

      await permitService.createPermitDocument({
        project_id: projectId,
        document_type: 'INSPECTION_REQUEST',
        jurisdiction: selectedPermit?.jurisdiction || 'Unknown',
        jurisdiction_code: selectedPermit?.jurisdiction_code,
        title: `${formData.inspection_type.replace('_', ' ')} Inspection Request`,
        description: `Inspection request for permit ${formData.permit_number}`,
        form_data: formData,
      });

      onSuccess();
    } catch (error) {
      console.error('Failed to create inspection request:', error);
      alert('Failed to create inspection request');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof InspectionRequestData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Request Electrical Inspection</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Permit Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Permit Information</h3>
              {permitDocuments.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="permit_select">Select Permit</Label>
                    <Select
                      id="permit_select"
                      value={selectedPermit?.id || ''}
                      onChange={(e) => handlePermitSelect(e.target.value)}
                    >
                      {permitDocuments.map(permit => (
                        <option key={permit.id} value={permit.id}>
                          {permit.permit_number || 'Pending'} - {permit.title}
                        </option>
                      ))}
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="permit_number">Permit Number</Label>
                    <Input
                      id="permit_number"
                      value={formData.permit_number}
                      onChange={(e) => updateFormData('permit_number', e.target.value)}
                      placeholder="Enter if known"
                    />
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <p className="text-sm text-yellow-800">
                    No approved permit found. Please ensure your permit application has been submitted and approved before requesting an inspection.
                  </p>
                </div>
              )}
            </div>

            {/* Inspection Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Inspection Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="inspection_type">Inspection Type *</Label>
                  <Select
                    id="inspection_type"
                    value={formData.inspection_type}
                    onChange={(e) => updateFormData('inspection_type', e.target.value)}
                    required
                  >
                    {inspectionTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </Select>
                </div>
                <div>
                  <Label htmlFor="requested_date">Requested Date *</Label>
                  <Input
                    id="requested_date"
                    type="date"
                    value={formData.requested_date}
                    onChange={(e) => updateFormData('requested_date', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="requested_time">Preferred Time</Label>
                  <Select
                    id="requested_time"
                    value={formData.requested_time}
                    onChange={(e) => updateFormData('requested_time', e.target.value)}
                  >
                    <option value="Morning">Morning (8AM-12PM)</option>
                    <option value="Afternoon">Afternoon (12PM-5PM)</option>
                    <option value="Any Time">Any Time</option>
                  </Select>
                </div>
              </div>
            </div>

            {/* Work Completed */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Work Ready for Inspection</h3>
              <div className="space-y-2">
                {workCompletedOptions.map(option => (
                  <label
                    key={option}
                    className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-2 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={formData.work_completed.includes(option)}
                      onChange={() => handleWorkCompletedToggle(option)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contact Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contractor_name">Contractor Name *</Label>
                  <Input
                    id="contractor_name"
                    value={formData.contractor_name}
                    onChange={(e) => updateFormData('contractor_name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractor_license">License Number *</Label>
                  <Input
                    id="contractor_license"
                    value={formData.contractor_license}
                    onChange={(e) => updateFormData('contractor_license', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractor_phone">Contractor Phone *</Label>
                  <Input
                    id="contractor_phone"
                    type="tel"
                    value={formData.contractor_phone}
                    onChange={(e) => updateFormData('contractor_phone', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="onsite_contact">On-Site Contact</Label>
                  <Input
                    id="onsite_contact"
                    value={formData.onsite_contact}
                    onChange={(e) => updateFormData('onsite_contact', e.target.value)}
                    placeholder="If different from contractor"
                  />
                </div>
                <div>
                  <Label htmlFor="onsite_phone">On-Site Phone</Label>
                  <Input
                    id="onsite_phone"
                    type="tel"
                    value={formData.onsite_phone}
                    onChange={(e) => updateFormData('onsite_phone', e.target.value)}
                    placeholder="If different from contractor"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="special_instructions">Special Instructions</Label>
                <textarea
                  id="special_instructions"
                  className="w-full px-3 py-2 border rounded-md"
                  rows={3}
                  value={formData.special_instructions}
                  onChange={(e) => updateFormData('special_instructions', e.target.value)}
                  placeholder="Gate codes, special access instructions, etc."
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || permitDocuments.length === 0}
              >
                {loading ? 'Creating...' : 'Submit Inspection Request'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};