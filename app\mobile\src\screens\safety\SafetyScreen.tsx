import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';

interface Props {
  navigation: NavigationProp<any>;
}

export const SafetyScreen: React.FC<Props> = ({ navigation }) => {
  const safetyFeatures = [
    {
      id: 'arc-flash',
      title: 'Arc Flash Warning',
      subtitle: 'PPE requirements & boundaries',
      icon: '⚡',
      color: '#f44336',
      screen: 'ArcFlashWarning',
      priority: 'high',
    },
    {
      id: 'emergency',
      title: 'Emergency Contacts',
      subtitle: 'Quick dial emergency numbers',
      icon: '🚨',
      color: '#FF5722',
      screen: 'EmergencyContacts',
      priority: 'high',
    },
    {
      id: 'ppe',
      title: 'PPE Requirements',
      subtitle: 'Equipment requirements by task',
      icon: '🦺',
      color: '#FF9800',
      screen: 'PPERequirements',
      priority: 'medium',
    },
    {
      id: 'violations',
      title: 'Safety Violations',
      subtitle: 'Report safety issues',
      icon: '⚠️',
      color: '#FFC107',
      screen: 'SafetyViolations',
      priority: 'medium',
    },
  ];

  const handleFeaturePress = (feature: any) => {
    if (feature.screen) {
      navigation.navigate(feature.screen);
    } else {
      Alert.alert('Coming Soon', `${feature.title} feature is coming soon!`);
    }
  };

  const renderFeatureCard = (feature: any) => (
    <TouchableOpacity
      key={feature.id}
      style={[
        styles.featureCard,
        feature.priority === 'high' && styles.highPriorityCard,
      ]}
      onPress={() => handleFeaturePress(feature)}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, { backgroundColor: feature.color + '20' }]}>
        <Text style={styles.icon}>{feature.icon}</Text>
      </View>
      <View style={styles.featureInfo}>
        <Text style={styles.featureTitle}>{feature.title}</Text>
        <Text style={styles.featureSubtitle}>{feature.subtitle}</Text>
      </View>
      <Text style={styles.arrow}>→</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Safety Center</Text>
        <Text style={styles.subtitle}>Stay safe on the job site</Text>
      </View>

      <View style={styles.emergencyBanner}>
        <Text style={styles.emergencyText}>🚨 In case of emergency</Text>
        <TouchableOpacity
          style={styles.call911Button}
          onPress={() => Alert.alert('Emergency', 'Call 911?', [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Call', onPress: () => console.log('Calling 911...') },
          ])}
        >
          <Text style={styles.call911Text}>CALL 911</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.featuresSection}>
        {safetyFeatures.map(renderFeatureCard)}
      </View>

      <View style={styles.safetyTips}>
        <Text style={styles.tipsTitle}>Daily Safety Reminders</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipIcon}>👷</Text>
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>PPE Check</Text>
            <Text style={styles.tipText}>
              Always wear appropriate PPE for the task
            </Text>
          </View>
        </View>

        <View style={styles.tipCard}>
          <Text style={styles.tipIcon}>🔌</Text>
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>Test Before Touch</Text>
            <Text style={styles.tipText}>
              Always test circuits with a meter before working
            </Text>
          </View>
        </View>

        <View style={styles.tipCard}>
          <Text style={styles.tipIcon}>🔒</Text>
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>Lockout/Tagout</Text>
            <Text style={styles.tipText}>
              Follow LOTO procedures for all equipment
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.resourcesSection}>
        <Text style={styles.resourcesTitle}>Safety Resources</Text>
        <TouchableOpacity style={styles.resourceLink}>
          <Text style={styles.resourceText}>NFPA 70E Standards</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.resourceLink}>
          <Text style={styles.resourceText}>OSHA Electrical Safety</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.resourceLink}>
          <Text style={styles.resourceText}>Company Safety Manual</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#f44336',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 5,
  },
  emergencyBanner: {
    backgroundColor: '#FFEBEE',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 2,
    borderBottomColor: '#FFCDD2',
  },
  emergencyText: {
    fontSize: 16,
    color: '#C62828',
    fontWeight: 'bold',
  },
  call911Button: {
    backgroundColor: '#f44336',
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  call911Text: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  featuresSection: {
    padding: 15,
  },
  featureCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    marginBottom: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  highPriorityCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  icon: {
    fontSize: 24,
  },
  featureInfo: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  featureSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 20,
    color: '#999',
  },
  safetyTips: {
    padding: 20,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  tipCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    padding: 15,
    marginBottom: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  tipText: {
    fontSize: 13,
    color: '#666',
  },
  resourcesSection: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 10,
  },
  resourcesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  resourceLink: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  resourceText: {
    fontSize: 14,
    color: '#2196F3',
  },
});