# Testing Guide

Comprehensive testing ensures the reliability, performance, and security of the Electrical Contracting Application. This guide covers our testing strategy, tools, and best practices.

## 🎯 Testing Philosophy

- **Test Pyramid**: More unit tests, fewer E2E tests
- **Fast Feedback**: Tests should run quickly
- **Reliable**: No flaky tests
- **Maintainable**: Easy to understand and update
- **Comprehensive**: Cover edge cases and error paths

## 🧪 Testing Stack

### Tools and Frameworks

- **Unit Testing**: Jest + React Testing Library
- **Integration Testing**: Supertest + Jest
- **E2E Testing**: Playwright
- **Performance Testing**: K6 + Artillery
- **Security Testing**: OWASP ZAP + Custom Scripts
- **Mobile Testing**: Detox (React Native)

### Coverage Requirements

- **Overall**: Minimum 80%
- **Critical Paths**: 100%
- **New Code**: 90%
- **API Endpoints**: 95%

## 🔧 Unit Testing

### Backend Unit Tests

#### Service Testing
```typescript
// src/services/__tests__/calculation.service.test.ts
import { CalculationService } from '../calculation.service';
import { PrismaClient } from '@prisma/client';
import { mockDeep } from 'jest-mock-extended';

describe('CalculationService', () => {
  let service: CalculationService;
  let prisma: jest.Mocked<PrismaClient>;

  beforeEach(() => {
    prisma = mockDeep<PrismaClient>();
    service = new CalculationService(prisma);
    jest.clearAllMocks();
  });

  describe('calculateWireSize', () => {
    it('should calculate correct wire size for residential load', async () => {
      const input = {
        current: 30,
        voltage: 240,
        distance: 100,
        phase: 'single',
        wireType: 'copper',
        temperature: 75
      };

      const result = await service.calculateWireSize(input);

      expect(result).toMatchObject({
        wireSize: '10 AWG',
        ampacity: 35,
        voltageDrop: {
          percentage: 2.5,
          voltage: 6
        }
      });
    });

    it('should apply derating factors correctly', async () => {
      const input = {
        current: 30,
        voltage: 240,
        distance: 100,
        conductorsInConduit: 7,
        ambientTemp: 40
      };

      const result = await service.calculateWireSize(input);

      expect(result.ampacity).toBeLessThan(35);
      expect(result.deratingFactors).toContain('conductor_count');
      expect(result.deratingFactors).toContain('temperature');
    });

    it('should throw ValidationError for invalid input', async () => {
      const input = {
        current: -10,
        voltage: 0
      };

      await expect(service.calculateWireSize(input))
        .rejects.toThrow('Invalid input parameters');
    });
  });
});
```

#### Utility Testing
```typescript
// src/utils/__tests__/electrical.test.ts
import {
  calculateVoltageDrop,
  getWireResistance,
  applyTemperatureCorrection
} from '../electrical';

describe('Electrical Utilities', () => {
  describe('calculateVoltageDrop', () => {
    it('should calculate single-phase voltage drop', () => {
      const drop = calculateVoltageDrop({
        current: 20,
        length: 100,
        wireSize: '12',
        voltage: 120,
        phase: 'single'
      });

      expect(drop).toBeCloseTo(3.2, 1);
    });

    it('should calculate three-phase voltage drop', () => {
      const drop = calculateVoltageDrop({
        current: 30,
        length: 200,
        wireSize: '10',
        voltage: 208,
        phase: 'three'
      });

      expect(drop).toBeCloseTo(4.8, 1);
    });
  });

  describe('getWireResistance', () => {
    const testCases = [
      { size: '14', type: 'copper', expected: 3.14 },
      { size: '12', type: 'copper', expected: 1.98 },
      { size: '10', type: 'copper', expected: 1.24 },
      { size: '12', type: 'aluminum', expected: 3.28 }
    ];

    test.each(testCases)(
      'should return $expected ohms/1000ft for $size AWG $type',
      ({ size, type, expected }) => {
        expect(getWireResistance(size, type)).toBe(expected);
      }
    );
  });
});
```

### Frontend Unit Tests

#### Component Testing
```typescript
// src/components/__tests__/WireSizeCalculator.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { WireSizeCalculator } from '../WireSizeCalculator';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { calculateWireSize } from '@/services/calculations';

jest.mock('@/services/calculations');

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('WireSizeCalculator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render input fields', () => {
    renderWithProviders(<WireSizeCalculator />);

    expect(screen.getByLabelText(/current/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/voltage/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/distance/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /calculate/i })).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    renderWithProviders(<WireSizeCalculator />);

    await user.click(screen.getByRole('button', { name: /calculate/i }));

    expect(screen.getByText(/current is required/i)).toBeInTheDocument();
    expect(screen.getByText(/voltage is required/i)).toBeInTheDocument();
  });

  it('should calculate wire size on valid input', async () => {
    const mockResult = {
      wireSize: '10 AWG',
      ampacity: 35,
      voltageDrop: { percentage: 2.5 }
    };
    
    (calculateWireSize as jest.Mock).mockResolvedValueOnce(mockResult);

    const user = userEvent.setup();
    renderWithProviders(<WireSizeCalculator />);

    await user.type(screen.getByLabelText(/current/i), '30');
    await user.type(screen.getByLabelText(/voltage/i), '240');
    await user.type(screen.getByLabelText(/distance/i), '100');
    await user.click(screen.getByRole('button', { name: /calculate/i }));

    await waitFor(() => {
      expect(screen.getByText(/recommended wire size/i)).toBeInTheDocument();
      expect(screen.getByText(/10 AWG/)).toBeInTheDocument();
    });
  });

  it('should display error on calculation failure', async () => {
    (calculateWireSize as jest.Mock).mockRejectedValueOnce(
      new Error('Calculation failed')
    );

    const user = userEvent.setup();
    renderWithProviders(<WireSizeCalculator />);

    await user.type(screen.getByLabelText(/current/i), '30');
    await user.type(screen.getByLabelText(/voltage/i), '240');
    await user.type(screen.getByLabelText(/distance/i), '100');
    await user.click(screen.getByRole('button', { name: /calculate/i }));

    await waitFor(() => {
      expect(screen.getByText(/calculation failed/i)).toBeInTheDocument();
    });
  });
});
```

#### Hook Testing
```typescript
// src/hooks/__tests__/useCalculations.test.ts
import { renderHook, act } from '@testing-library/react';
import { useCalculations } from '../useCalculations';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCalculations', () => {
  it('should perform calculations', async () => {
    const { result } = renderHook(() => useCalculations(), {
      wrapper: createWrapper()
    });

    let calculationResult;
    await act(async () => {
      calculationResult = await result.current.calculate('wireSize', {
        current: 30,
        voltage: 240
      });
    });

    expect(calculationResult).toHaveProperty('wireSize');
    expect(calculationResult).toHaveProperty('ampacity');
  });

  it('should cache calculation results', async () => {
    const { result } = renderHook(() => useCalculations(), {
      wrapper: createWrapper()
    });

    const input = { current: 30, voltage: 240 };
    
    await act(async () => {
      await result.current.calculate('wireSize', input);
    });

    // Second call should use cache
    const start = Date.now();
    await act(async () => {
      await result.current.calculate('wireSize', input);
    });
    const duration = Date.now() - start;

    expect(duration).toBeLessThan(10); // Should be instant from cache
  });
});
```

## 🔗 Integration Testing

### API Integration Tests
```typescript
// src/routes/__tests__/calculations.integration.test.ts
import request from 'supertest';
import { app } from '../../app';
import { PrismaClient } from '@prisma/client';
import { generateAuthToken } from '../../utils/auth';

describe('Calculations API', () => {
  let prisma: PrismaClient;
  let authToken: string;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
    
    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        password_hash: 'hashed_password'
      }
    });
    
    authToken = generateAuthToken(user.id);
  });

  afterAll(async () => {
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('POST /api/calculations/wire-size', () => {
    it('should calculate wire size with valid input', async () => {
      const response = await request(app)
        .post('/api/calculations/wire-size')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          current: 30,
          voltage: 240,
          distance: 100,
          phase: 'single'
        });

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          wireSize: expect.any(String),
          ampacity: expect.any(Number),
          voltageDrop: {
            percentage: expect.any(Number),
            voltage: expect.any(Number)
          }
        }
      });
    });

    it('should save calculation to database', async () => {
      const projectId = 'proj_123';
      
      await request(app)
        .post('/api/calculations/wire-size')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          current: 30,
          voltage: 240,
          distance: 100,
          projectId
        });

      const calculation = await prisma.calculation.findFirst({
        where: { projectId }
      });

      expect(calculation).toBeTruthy();
      expect(calculation?.type).toBe('wire_size');
    });

    it('should return 400 for invalid input', async () => {
      const response = await request(app)
        .post('/api/calculations/wire-size')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          current: -10,
          voltage: 0
        });

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 401 without auth token', async () => {
      const response = await request(app)
        .post('/api/calculations/wire-size')
        .send({
          current: 30,
          voltage: 240
        });

      expect(response.status).toBe(401);
    });
  });
});
```

### Database Integration Tests
```typescript
// src/services/__tests__/project.service.integration.test.ts
import { ProjectService } from '../project.service';
import { PrismaClient } from '@prisma/client';
import { cleanupDatabase, createTestUser, createTestCustomer } from '../../test/helpers';

describe('ProjectService Integration', () => {
  let service: ProjectService;
  let prisma: PrismaClient;
  let userId: string;
  let customerId: string;

  beforeAll(async () => {
    prisma = new PrismaClient();
    service = new ProjectService(prisma);
    
    const user = await createTestUser(prisma);
    userId = user.id;
    
    const customer = await createTestCustomer(prisma);
    customerId = customer.id;
  });

  afterEach(async () => {
    await cleanupDatabase(prisma, ['project']);
  });

  afterAll(async () => {
    await cleanupDatabase(prisma);
    await prisma.$disconnect();
  });

  it('should create project with materials and panels', async () => {
    const projectData = {
      name: 'Test Project',
      customerId,
      budget: 50000,
      materials: [
        { materialId: 'mat_1', quantity: 100 },
        { materialId: 'mat_2', quantity: 50 }
      ],
      panels: [
        {
          name: 'Panel A',
          location: 'Basement',
          voltage: 208,
          amperage: 200
        }
      ]
    };

    const project = await service.createProject(userId, projectData);

    expect(project.id).toBeTruthy();
    expect(project.materials).toHaveLength(2);
    expect(project.panels).toHaveLength(1);

    // Verify in database
    const dbProject = await prisma.project.findUnique({
      where: { id: project.id },
      include: { materials: true, panels: true }
    });

    expect(dbProject?.materials).toHaveLength(2);
    expect(dbProject?.panels[0].name).toBe('Panel A');
  });

  it('should handle concurrent project updates', async () => {
    const project = await service.createProject(userId, {
      name: 'Concurrent Test',
      customerId
    });

    // Simulate concurrent updates
    const updates = Promise.all([
      service.updateProject(project.id, { progress: 25 }),
      service.updateProject(project.id, { progress: 50 }),
      service.updateProject(project.id, { progress: 75 })
    ]);

    await expect(updates).resolves.toBeDefined();

    const finalProject = await prisma.project.findUnique({
      where: { id: project.id }
    });

    // One of the updates should win
    expect([25, 50, 75]).toContain(finalProject?.progress);
  });
});
```

## 🎭 End-to-End Testing

### Playwright E2E Tests
```typescript
// e2e/calculations.spec.ts
import { test, expect } from '@playwright/test';
import { login, createProject } from './helpers';

test.describe('Wire Size Calculator', () => {
  test.beforeEach(async ({ page }) => {
    await login(page, '<EMAIL>', 'password');
    await page.goto('/calculations/wire-size');
  });

  test('should calculate wire size for typical residential circuit', async ({ page }) => {
    // Fill form
    await page.fill('[data-testid="current-input"]', '20');
    await page.fill('[data-testid="voltage-input"]', '120');
    await page.fill('[data-testid="distance-input"]', '50');
    
    await page.selectOption('[data-testid="wire-type-select"]', 'copper');
    await page.selectOption('[data-testid="temp-rating-select"]', '75');

    // Calculate
    await page.click('[data-testid="calculate-button"]');

    // Verify results
    await expect(page.locator('[data-testid="wire-size-result"]')).toContainText('12 AWG');
    await expect(page.locator('[data-testid="voltage-drop-result"]')).toContainText('2.');
    
    // Save to project
    const project = await createProject(page);
    await page.selectOption('[data-testid="project-select"]', project.id);
    await page.click('[data-testid="save-calculation-button"]');

    await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible();
  });

  test('should show warnings for high voltage drop', async ({ page }) => {
    await page.fill('[data-testid="current-input"]', '50');
    await page.fill('[data-testid="voltage-input"]', '120');
    await page.fill('[data-testid="distance-input"]', '200');

    await page.click('[data-testid="calculate-button"]');

    await expect(page.locator('[data-testid="voltage-drop-warning"]')).toBeVisible();
    await expect(page.locator('[data-testid="voltage-drop-warning"]')).toContainText('exceeds 3%');
  });

  test('should validate input ranges', async ({ page }) => {
    // Test negative current
    await page.fill('[data-testid="current-input"]', '-10');
    await expect(page.locator('[data-testid="current-error"]')).toContainText('must be positive');

    // Test excessive voltage
    await page.fill('[data-testid="voltage-input"]', '50000');
    await expect(page.locator('[data-testid="voltage-error"]')).toContainText('maximum 4160V');
  });
});
```

### Mobile E2E Tests (Detox)
```typescript
// e2e/mobile/calculations.e2e.js
describe('Mobile Calculations', () => {
  beforeAll(async () => {
    await device.launchApp();
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password');
    await element(by.id('login-button')).tap();
  });

  it('should perform wire size calculation', async () => {
    await element(by.id('tab-tools')).tap();
    await element(by.id('wire-size-calculator')).tap();

    await element(by.id('current-input')).typeText('30');
    await element(by.id('voltage-input')).typeText('240');
    await element(by.id('distance-input')).typeText('100');

    await element(by.id('calculate-button')).tap();

    await expect(element(by.id('result-card'))).toBeVisible();
    await expect(element(by.text('10 AWG'))).toBeVisible();
  });

  it('should work offline', async () => {
    await device.setURLBlacklist(['.*']);

    await element(by.id('tab-tools')).tap();
    await element(by.id('wire-size-calculator')).tap();

    await element(by.id('current-input')).replaceText('20');
    await element(by.id('voltage-input')).replaceText('120');
    await element(by.id('distance-input')).replaceText('50');

    await element(by.id('calculate-button')).tap();

    await expect(element(by.id('result-card'))).toBeVisible();
    await expect(element(by.id('offline-indicator'))).toBeVisible();

    await device.clearURLBlacklist();
  });
});
```

## 🚀 Performance Testing

### Load Testing with K6
```javascript
// tests/performance/api-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '30s', target: 10 },   // Ramp up
    { duration: '1m', target: 50 },    // Stay at 50 users
    { duration: '2m', target: 100 },   // Ramp to 100 users
    { duration: '30s', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    errors: ['rate<0.05'],            // Error rate under 5%
  },
};

const BASE_URL = 'https://api.electricalapp.com';

export function setup() {
  // Login and get token
  const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
    email: '<EMAIL>',
    password: 'loadtest123'
  }), {
    headers: { 'Content-Type': 'application/json' }
  });

  const { token } = JSON.parse(loginRes.body);
  return { token };
}

export default function(data) {
  const headers = {
    'Authorization': `Bearer ${data.token}`,
    'Content-Type': 'application/json'
  };

  // Scenario 1: Calculate wire size
  const calcRes = http.post(`${BASE_URL}/calculations/wire-size`,
    JSON.stringify({
      current: Math.floor(Math.random() * 100) + 10,
      voltage: [120, 208, 240, 480][Math.floor(Math.random() * 4)],
      distance: Math.floor(Math.random() * 200) + 50
    }),
    { headers }
  );

  check(calcRes, {
    'calculation successful': (r) => r.status === 200,
    'response time OK': (r) => r.timings.duration < 500,
  });

  errorRate.add(calcRes.status !== 200);

  sleep(1);

  // Scenario 2: Get projects
  const projectsRes = http.get(`${BASE_URL}/projects?limit=20`, { headers });

  check(projectsRes, {
    'projects retrieved': (r) => r.status === 200,
    'has projects': (r) => JSON.parse(r.body).data.projects.length > 0,
  });

  sleep(0.5);
}
```

### Frontend Performance Testing
```typescript
// tests/performance/lighthouse.test.ts
import { test, expect } from '@playwright/test';
import { playAudit } from 'playwright-lighthouse';

test.describe('Performance Tests', () => {
  test('should meet performance benchmarks for calculator page', async ({ page, browserName }) => {
    test.skip(browserName !== 'chromium', 'Lighthouse only works in Chromium');

    await page.goto('/calculations/wire-size');

    const auditResult = await playAudit({
      page,
      thresholds: {
        performance: 90,
        accessibility: 90,
        'best-practices': 85,
        seo: 80,
      },
      port: 9222,
    });

    expect(auditResult.lhr.categories.performance.score * 100).toBeGreaterThan(90);
  });

  test('should render large panel schedule efficiently', async ({ page }) => {
    await page.goto('/panels/large-test-panel');

    const startTime = Date.now();
    await page.waitForSelector('[data-testid="panel-loaded"]');
    const loadTime = Date.now() - startTime;

    expect(loadTime).toBeLessThan(2000); // Should load in under 2 seconds

    // Test interaction performance
    const interactionStart = Date.now();
    await page.click('[data-testid="circuit-20"]');
    await page.waitForSelector('[data-testid="circuit-details"]');
    const interactionTime = Date.now() - interactionStart;

    expect(interactionTime).toBeLessThan(100); // Should respond in under 100ms
  });
});
```

## 🔒 Security Testing

### Security Test Suite
```typescript
// tests/security/api-security.test.ts
import request from 'supertest';
import { app } from '../../src/app';

describe('API Security Tests', () => {
  describe('SQL Injection Prevention', () => {
    it('should prevent SQL injection in search queries', async () => {
      const maliciousInput = "'; DROP TABLE users; --";
      
      const response = await request(app)
        .get(`/api/projects/search?q=${encodeURIComponent(maliciousInput)}`)
        .set('Authorization', 'Bearer valid_token');

      expect(response.status).not.toBe(500);
      expect(response.body).not.toContain('error');
      
      // Verify tables still exist
      const healthCheck = await request(app).get('/health');
      expect(healthCheck.status).toBe(200);
    });
  });

  describe('XSS Prevention', () => {
    it('should sanitize user input in responses', async () => {
      const xssPayload = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', 'Bearer valid_token')
        .send({
          name: xssPayload,
          description: 'Test project'
        });

      expect(response.body.data.name).not.toContain('<script>');
      expect(response.body.data.name).toContain('&lt;script&gt;');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const requests = Array(101).fill(null).map(() =>
        request(app)
          .get('/api/calculations/wire-size')
          .set('Authorization', 'Bearer valid_token')
      );

      const responses = await Promise.all(requests);
      const tooManyRequests = responses.filter(r => r.status === 429);

      expect(tooManyRequests.length).toBeGreaterThan(0);
    });
  });

  describe('Authentication', () => {
    it('should reject requests without valid tokens', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(401);
    });

    it('should prevent token reuse after logout', async () => {
      const loginRes = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'password' });

      const { token } = loginRes.body.data;

      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`);

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(401);
    });
  });
});
```

## 📊 Test Reporting

### Coverage Reports
```bash
# Generate coverage report
pnpm test:coverage

# View HTML report
open coverage/lcov-report/index.html
```

### Custom Test Reporter
```typescript
// tests/reporters/custom-reporter.js
class ElectricalTestReporter {
  onRunStart(results, options) {
    console.log('🧪 Starting Electrical App Test Suite');
  }

  onTestResult(test, testResult) {
    const status = testResult.success ? '✅' : '❌';
    console.log(`${status} ${testResult.testFilePath}`);
    
    if (!testResult.success) {
      testResult.testResults.forEach(result => {
        if (result.status === 'failed') {
          console.log(`  ❌ ${result.title}`);
          console.log(`     ${result.failureMessages.join('\n     ')}`);
        }
      });
    }
  }

  onRunComplete(contexts, results) {
    const { numTotalTests, numPassedTests, numFailedTests } = results;
    
    console.log('\n📊 Test Summary:');
    console.log(`Total: ${numTotalTests}`);
    console.log(`Passed: ${numPassedTests} ✅`);
    console.log(`Failed: ${numFailedTests} ❌`);
    console.log(`Duration: ${(results.runtime / 1000).toFixed(2)}s`);
  }
}

module.exports = ElectricalTestReporter;
```

## 🎯 Testing Best Practices

### Test Organization
```
tests/
├── unit/
│   ├── services/
│   ├── utils/
│   └── components/
├── integration/
│   ├── api/
│   └── database/
├── e2e/
│   ├── web/
│   └── mobile/
├── performance/
├── security/
├── fixtures/
├── helpers/
└── setup/
```

### Test Data Management
```typescript
// tests/fixtures/projects.ts
export const createMockProject = (overrides = {}): Project => ({
  id: 'proj_test_123',
  name: 'Test Project',
  customerId: 'cust_test_456',
  status: 'active',
  budget: 50000,
  spent: 25000,
  progress: 50,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-15'),
  ...overrides
});

// tests/helpers/database.ts
export async function seedTestData(prisma: PrismaClient) {
  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Test User',
      password_hash: await hash('password123')
    }
  });

  const customer = await prisma.customer.create({
    data: {
      name: 'Test Customer',
      email: '<EMAIL>'
    }
  });

  return { user, customer };
}
```

### Continuous Testing
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Run unit tests
        run: pnpm test:unit
        
      - name: Run integration tests
        run: pnpm test:integration
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/test
          
      - name: Run E2E tests
        run: pnpm test:e2e
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## ✅ Testing Checklist

Before submitting PR:
- [ ] All tests pass locally
- [ ] New features have tests
- [ ] Edge cases are tested
- [ ] Error paths are tested
- [ ] Integration tests for API changes
- [ ] E2E tests for user-facing changes
- [ ] Performance impact evaluated
- [ ] Security implications tested
- [ ] Test documentation updated

---

Remember: Good tests are an investment in code quality and team confidence. Write tests that you'd want to maintain!