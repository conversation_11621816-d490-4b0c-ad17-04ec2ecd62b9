apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: electrical-staging

bases:
  - ../../base

namePrefix: staging-

commonLabels:
  environment: staging

patchesStrategicMerge:
  - deployment-patches.yaml

configMapGenerator:
  - name: backend-config
    behavior: merge
    literals:
      - cors-origin=https://staging.electrical-app.com
      - log-level=info

secretGenerator:
  - name: backend-secrets
    behavior: merge
    literals:
      - database-url=**************************************************************/electrical_staging

replicas:
  - name: backend
    count: 2
  - name: frontend
    count: 2
  - name: agents
    count: 1
  - name: postgres
    count: 1
  - name: redis
    count: 2