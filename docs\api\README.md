# API Documentation

The Electrical Contracting Application provides a comprehensive RESTful API for all platform functionality. This documentation covers authentication, endpoints, request/response formats, and best practices.

## 🌐 API Overview

### Base URLs
- **Production**: `https://api.electricalapp.com/v1`
- **Staging**: `https://staging-api.electricalapp.com/v1`
- **Development**: `http://localhost:3000/v1`

### Format
- All requests and responses use JSON
- UTF-8 encoding
- ISO 8601 date formats
- Decimal numbers as strings for precision

### Versioning
- API version in URL path (e.g., `/v1/`)
- Backwards compatibility maintained
- Deprecation notices via headers
- Migration guides provided

## 🔐 Authentication

### JWT Authentication
The API uses JWT (JSON Web Tokens) for authentication.

#### Login
```http
POST /v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "electrician"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  }
}
```

#### Using Tokens
Include the access token in the Authorization header:
```http
GET /v1/projects
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Refresh Token
```http
POST /v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### API Key Authentication
For machine-to-machine communication:
```http
GET /v1/projects
X-API-Key: your_api_key_here
```

## 📚 Core Resources

### Projects

#### List Projects
```http
GET /v1/projects?page=1&limit=20&status=active&sort=-createdAt
Authorization: Bearer {token}
```

Query Parameters:
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `status` (string): Filter by status (active, completed, archived)
- `customerId` (string): Filter by customer
- `sort` (string): Sort field with `-` prefix for descending
- `search` (string): Search in name and description

Response:
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "proj_123",
        "name": "Main Street Renovation",
        "customer": {
          "id": "cust_456",
          "name": "ABC Company"
        },
        "status": "active",
        "startDate": "2024-01-15T00:00:00Z",
        "endDate": "2024-03-15T00:00:00Z",
        "budget": "25000.00",
        "spent": "12500.00",
        "progress": 50,
        "createdAt": "2024-01-10T10:00:00Z",
        "updatedAt": "2024-01-20T15:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

#### Get Project
```http
GET /v1/projects/{projectId}
Authorization: Bearer {token}
```

#### Create Project
```http
POST /v1/projects
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "New Office Building",
  "customerId": "cust_789",
  "description": "Complete electrical installation for 3-story office building",
  "startDate": "2024-02-01",
  "endDate": "2024-05-01",
  "budget": "75000.00",
  "address": {
    "street": "123 Business Park Dr",
    "city": "Austin",
    "state": "TX",
    "zip": "78701"
  },
  "type": "commercial",
  "phases": [
    {
      "name": "Rough-in",
      "startDate": "2024-02-01",
      "endDate": "2024-03-01"
    }
  ]
}
```

#### Update Project
```http
PATCH /v1/projects/{projectId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "active",
  "progress": 25,
  "notes": "Completed rough-in phase"
}
```

#### Delete Project
```http
DELETE /v1/projects/{projectId}
Authorization: Bearer {token}
```

### Calculations

#### Wire Size Calculation
```http
POST /v1/calculations/wire-size
Authorization: Bearer {token}
Content-Type: application/json

{
  "current": 50,
  "voltage": 240,
  "phase": "single",
  "wireType": "copper",
  "insulationType": "THHN",
  "temperature": 75,
  "ambientTemp": 30,
  "conductorsInConduit": 3,
  "distance": 150,
  "projectId": "proj_123"  // Optional, to save to project
}
```

Response:
```json
{
  "success": true,
  "data": {
    "calculation": {
      "id": "calc_789",
      "type": "wire_size",
      "inputs": { ... },
      "results": {
        "wireSize": "6 AWG",
        "ampacity": {
          "base": 65,
          "adjusted": 52,
          "derated": true
        },
        "voltageDrop": {
          "voltage": 5.52,
          "percentage": 2.3
        },
        "nec_references": [
          "310.16",
          "310.15(B)(2)(a)"
        ]
      },
      "warnings": [],
      "createdAt": "2024-01-20T10:00:00Z"
    }
  }
}
```

#### Voltage Drop Calculation
```http
POST /v1/calculations/voltage-drop
Authorization: Bearer {token}
Content-Type: application/json

{
  "wireSize": "6",
  "wireType": "copper",
  "voltage": 240,
  "phase": "single",
  "current": 50,
  "distance": 150,
  "powerFactor": 0.9,
  "conduitType": "steel"
}
```

#### Load Calculation
```http
POST /v1/calculations/load
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "residential",
  "squareFootage": 2500,
  "appliances": [
    {
      "type": "range",
      "watts": 12000
    },
    {
      "type": "dryer",
      "watts": 5000
    },
    {
      "type": "water_heater",
      "watts": 4500
    }
  ],
  "hvac": {
    "heating": 10000,
    "cooling": 24000
  },
  "additionalLoads": []
}
```

### Panels

#### Create Panel Schedule
```http
POST /v1/panels
Authorization: Bearer {token}
Content-Type: application/json

{
  "projectId": "proj_123",
  "name": "Panel A",
  "location": "Electrical Room",
  "type": "main",
  "voltage": 208,
  "phase": "three",
  "amperage": 200,
  "spaces": 42,
  "mounting": "surface",
  "circuits": [
    {
      "number": 1,
      "name": "Lighting - Office",
      "breaker": 20,
      "load": 1800,
      "pole": "single"
    }
  ]
}
```

#### Get Panel Schedule
```http
GET /v1/panels/{panelId}
Authorization: Bearer {token}
```

Response includes visual representation data:
```json
{
  "success": true,
  "data": {
    "panel": {
      "id": "panel_123",
      "name": "Panel A",
      "circuits": [...],
      "summary": {
        "totalLoad": 35420,
        "demandLoad": 28336,
        "phaseA": 11807,
        "phaseB": 11807,
        "phaseC": 11807,
        "neutralCurrent": 0,
        "utilization": 68.2
      },
      "visual": {
        "svg": "<svg>...</svg>",
        "pdf_url": "https://..."
      }
    }
  }
}
```

### Materials

#### Search Materials
```http
GET /v1/materials/search?q=wire&category=conductors
Authorization: Bearer {token}
```

#### Add Material to Project
```http
POST /v1/projects/{projectId}/materials
Authorization: Bearer {token}
Content-Type: application/json

{
  "materialId": "mat_123",
  "quantity": 500,
  "unit": "feet",
  "location": "Main Panel",
  "notes": "For branch circuits"
}
```

#### Barcode Lookup
```http
GET /v1/materials/barcode/{barcode}
Authorization: Bearer {token}
```

### Inspections

#### Create Inspection
```http
POST /v1/inspections
Authorization: Bearer {token}
Content-Type: application/json

{
  "projectId": "proj_123",
  "type": "rough_in",
  "scheduledDate": "2024-02-15T10:00:00Z",
  "inspector": "John Smith",
  "checklist": "standard_rough_in",
  "areas": [
    "Main Panel",
    "Kitchen",
    "Bathrooms"
  ]
}
```

#### Submit Inspection Results
```http
PUT /v1/inspections/{inspectionId}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "passed",
  "items": [
    {
      "id": "item_1",
      "status": "pass",
      "notes": ""
    },
    {
      "id": "item_2", 
      "status": "fail",
      "notes": "Missing ground wire",
      "photos": ["photo_123"]
    }
  ],
  "inspectorSignature": "base64_signature_data",
  "completedAt": "2024-02-15T11:30:00Z"
}
```

## 📊 Advanced Features

### Batch Operations
```http
POST /v1/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "operations": [
    {
      "method": "POST",
      "path": "/v1/calculations/wire-size",
      "body": { ... }
    },
    {
      "method": "GET",
      "path": "/v1/materials/mat_123"
    }
  ]
}
```

### WebSocket Events
Connect to real-time updates:
```javascript
const socket = io('wss://api.electricalapp.com', {
  auth: {
    token: 'your_jwt_token'
  }
});

socket.on('project:update', (data) => {
  console.log('Project updated:', data);
});
```

### File Uploads
```http
POST /v1/uploads
Authorization: Bearer {token}
Content-Type: multipart/form-data

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="panel.jpg"
Content-Type: image/jpeg

[binary data]
------WebKitFormBoundary
Content-Disposition: form-data; name="type"

inspection_photo
------WebKitFormBoundary
```

## 🔧 Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "fields": {
        "current": "Current must be a positive number"
      }
    },
    "requestId": "req_abc123"
  }
}
```

### Common Error Codes
- `UNAUTHORIZED` (401): Invalid or missing authentication
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `VALIDATION_ERROR` (400): Invalid input data
- `RATE_LIMITED` (429): Too many requests
- `SERVER_ERROR` (500): Internal server error

## 📘 Best Practices

### Rate Limiting
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated
- Headers indicate remaining quota:
  ```
  X-RateLimit-Limit: 1000
  X-RateLimit-Remaining: 950
  X-RateLimit-Reset: 1640995200
  ```

### Pagination
- Always paginate list endpoints
- Default limit: 20 items
- Maximum limit: 100 items
- Use cursor-based pagination for large datasets

### Filtering and Sorting
- Use query parameters for filtering
- Prefix with `-` for descending sort
- Multiple sort fields comma-separated
- Example: `?sort=-createdAt,name`

### Webhook Integration
Register webhooks for events:
```http
POST /v1/webhooks
Authorization: Bearer {token}
Content-Type: application/json

{
  "url": "https://your-server.com/webhook",
  "events": ["project.created", "inspection.completed"],
  "secret": "your_webhook_secret"
}
```

## 🧪 Testing

### Sandbox Environment
- Base URL: `https://sandbox-api.electricalapp.com/v1`
- Test data resets daily
- No real charges or notifications
- Same API structure as production

### Postman Collection
- Download: [Electrical API.postman_collection.json](./postman-collection.json)
- Includes all endpoints with examples
- Environment variables for easy switching
- Pre-request scripts for authentication

### API Explorer
- Interactive documentation: https://api.electricalapp.com/docs
- Try endpoints directly in browser
- Automatic authentication handling
- Request/response examples

## 📦 SDKs

### JavaScript/TypeScript
```bash
npm install @electrical/api-client
```

```typescript
import { ElectricalAPI } from '@electrical/api-client';

const api = new ElectricalAPI({
  apiKey: 'your_api_key',
  environment: 'production'
});

const projects = await api.projects.list({ status: 'active' });
```

### Python
```bash
pip install electrical-api
```

```python
from electrical_api import ElectricalAPI

api = ElectricalAPI(api_key='your_api_key')
projects = api.projects.list(status='active')
```

## 🔄 Changelog

### v1.2.0 (Latest)
- Added batch operations endpoint
- Improved error messages
- Added webhook support
- Performance improvements

### v1.1.0
- Added material barcode scanning
- Enhanced inspection workflows
- Added real-time notifications
- Bug fixes

### v1.0.0
- Initial API release
- Core functionality
- Authentication system
- Basic calculations

---

For additional help, contact <EMAIL> or visit our [Developer Portal](https://developers.electricalapp.com).