import React from 'react';
import { Center, Spinner, Text, VStack } from 'native-base';

interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ message = 'Loading...' }) => {
  return (
    <Center flex={1} bg="white">
      <VStack space={4} alignItems="center">
        <Spinner size="lg" color="primary.500" />
        <Text fontSize="md" color="gray.600">
          {message}
        </Text>
      </VStack>
    </Center>
  );
};