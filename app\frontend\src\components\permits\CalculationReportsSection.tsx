import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert } from '../ui/alert';
import { permitService, CompileReportsResponse } from '../../services/permitService';
import { panelService } from '../../services/panelService';
import { format } from 'date-fns';
import { 
  FileText, 
  Download, 
  Zap, 
  Activity, 
  Calculator,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface CalculationReportsSectionProps {
  projectId: string;
}

export const CalculationReportsSection: React.FC<CalculationReportsSectionProps> = ({
  projectId
}) => {
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<CompileReportsResponse | null>(null);
  const [panels, setPanels] = useState<any[]>([]);
  const [generating, setGenerating] = useState<string | null>(null);

  useEffect(() => {
    loadReports();
  }, [projectId]);

  const loadReports = async () => {
    try {
      setLoading(true);
      const [reportsData, panelsData] = await Promise.all([
        permitService.compileCalculationReports(projectId),
        panelService.getPanelsByProject(projectId)
      ]);
      setReports(reportsData);
      setPanels(panelsData);
    } catch (error) {
      console.error('Failed to load reports:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateLoadCalc = async (panelId: string) => {
    try {
      setGenerating(`load-${panelId}`);
      const result = await permitService.generateLoadCalculationPDF(projectId, panelId);
      alert(`Load calculation PDF generated! ${result.pageCount} pages`);
    } catch (error) {
      console.error('Failed to generate load calculation:', error);
      alert('Failed to generate load calculation PDF');
    } finally {
      setGenerating(null);
    }
  };

  const handleGeneratePanelSchedule = async (panelId: string) => {
    try {
      setGenerating(`schedule-${panelId}`);
      const result = await permitService.generatePanelSchedulePDF(panelId);
      alert(`Panel schedule PDF generated! ${result.pageCount} pages`);
    } catch (error) {
      console.error('Failed to generate panel schedule:', error);
      alert('Failed to generate panel schedule PDF');
    } finally {
      setGenerating(null);
    }
  };

  const getReportStatus = (panel: any, reportType: 'arc_flash' | 'short_circuit' | 'load') => {
    if (!reports) return null;
    
    let reportList;
    switch (reportType) {
      case 'arc_flash':
        reportList = reports.arcFlashReports;
        break;
      case 'short_circuit':
        reportList = reports.shortCircuitReports;
        break;
      case 'load':
        reportList = reports.loadCalculations;
        break;
    }

    const report = reportList.find(r => r.panel_name === panel.name);
    return report ? report.calculation : null;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading calculation reports...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Arc Flash Reports</p>
                <p className="text-2xl font-bold">
                  {reports?.arcFlashReports.length || 0} / {panels.length}
                </p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Short Circuit Reports</p>
                <p className="text-2xl font-bold">
                  {reports?.shortCircuitReports.length || 0} / {panels.length}
                </p>
              </div>
              <Activity className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Load Calculations</p>
                <p className="text-2xl font-bold">
                  {reports?.loadCalculations.length || 0} / {panels.length}
                </p>
              </div>
              <Calculator className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Panel Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Panel Calculation Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4">Panel</th>
                  <th className="text-center p-4">Arc Flash</th>
                  <th className="text-center p-4">Short Circuit</th>
                  <th className="text-center p-4">Load Calculation</th>
                  <th className="text-center p-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {panels.map(panel => {
                  const arcFlash = getReportStatus(panel, 'arc_flash');
                  const shortCircuit = getReportStatus(panel, 'short_circuit');
                  const loadCalc = getReportStatus(panel, 'load');

                  return (
                    <tr key={panel.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium">{panel.name}</p>
                          <p className="text-sm text-gray-600">{panel.location}</p>
                          <p className="text-sm text-gray-500">
                            {panel.voltage_system} - {panel.ampere_rating}A
                          </p>
                        </div>
                      </td>
                      <td className="p-4 text-center">
                        {arcFlash ? (
                          <div>
                            <Badge variant="success">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Complete
                            </Badge>
                            <p className="text-xs text-gray-600 mt-1">
                              PPE Cat {arcFlash.ppe_category}
                            </p>
                            <p className="text-xs text-gray-500">
                              {format(new Date(arcFlash.calculation_date), 'MM/dd/yyyy')}
                            </p>
                          </div>
                        ) : (
                          <Badge variant="secondary">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Required
                          </Badge>
                        )}
                      </td>
                      <td className="p-4 text-center">
                        {shortCircuit ? (
                          <div>
                            <Badge variant="success">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Complete
                            </Badge>
                            <p className="text-xs text-gray-600 mt-1">
                              {shortCircuit.symmetrical_fault_3ph.toFixed(1)} kA
                            </p>
                            <p className="text-xs text-gray-500">
                              {format(new Date(shortCircuit.calculation_date), 'MM/dd/yyyy')}
                            </p>
                          </div>
                        ) : (
                          <Badge variant="secondary">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Required
                          </Badge>
                        )}
                      </td>
                      <td className="p-4 text-center">
                        {loadCalc ? (
                          <div>
                            <Badge variant="success">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Complete
                            </Badge>
                            <p className="text-xs text-gray-600 mt-1">
                              {loadCalc.load_percentage.toFixed(1)}% Load
                            </p>
                            <p className="text-xs text-gray-500">
                              {format(new Date(loadCalc.calculation_date), 'MM/dd/yyyy')}
                            </p>
                          </div>
                        ) : (
                          <Badge variant="secondary">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Required
                          </Badge>
                        )}
                      </td>
                      <td className="p-4 text-center">
                        <div className="flex justify-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleGenerateLoadCalc(panel.id)}
                            disabled={!loadCalc || generating === `load-${panel.id}`}
                          >
                            {generating === `load-${panel.id}` ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900" />
                            ) : (
                              <>
                                <FileText className="h-4 w-4 mr-1" />
                                Load PDF
                              </>
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleGeneratePanelSchedule(panel.id)}
                            disabled={generating === `schedule-${panel.id}`}
                          >
                            {generating === `schedule-${panel.id}` ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900" />
                            ) : (
                              <>
                                <FileText className="h-4 w-4 mr-1" />
                                Schedule PDF
                              </>
                            )}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {panels.length === 0 && (
            <div className="text-center py-8">
              <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No panels found for this project</p>
              <p className="text-sm text-gray-500 mt-1">
                Add panels to generate calculation reports
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Missing Reports Alert */}
      {panels.length > 0 && reports && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <div>
            <p className="font-medium">Required for Permit Submission:</p>
            <ul className="list-disc list-inside text-sm mt-2">
              {reports.arcFlashReports.length < panels.length && (
                <li>Arc flash analysis for all panels</li>
              )}
              {reports.shortCircuitReports.length < panels.length && (
                <li>Short circuit analysis for all panels</li>
              )}
              {reports.loadCalculations.length < panels.length && (
                <li>Load calculations for all panels</li>
              )}
            </ul>
          </div>
        </Alert>
      )}
    </div>
  );
};