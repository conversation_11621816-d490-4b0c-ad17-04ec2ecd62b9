import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './stores/auth';
import { Layout } from './components/layout/Layout';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { DashboardPage } from './pages/DashboardPage';
import { CustomersPage } from './pages/customers/CustomersPage';
import { ProjectsPage } from './pages/projects/ProjectsPage';
import { ProjectDetailPage } from './pages/projects/ProjectDetailPage';
import { EstimatesPage } from './pages/estimates/EstimatesPage';
import { CalculationsPage } from './pages/calculations/CalculationsPage';
import { MaterialsPage } from './pages/materials/MaterialsPage';
import { PanelsPage } from './pages/PanelsPage';
import { PanelFormPage } from './pages/PanelFormPage';
import { PanelSchedulePage } from './pages/PanelSchedulePage';
import { ArcFlashPage } from './pages/arc-flash/ArcFlashPage';
import { ShortCircuitPage } from './pages/ShortCircuitPage';
import PermitDocumentsPage from './pages/permits/PermitDocumentsPage';
import { InspectionsPage } from './pages/inspections/InspectionsPage';
import { InspectionFormPage } from './pages/inspections/InspectionFormPage';
import { InspectionChecklistPage } from './pages/inspections/InspectionChecklistPage';
import { InspectionMobilePage } from './pages/inspections/InspectionMobilePage';
import AnalyticsDashboard from './pages/analytics/AnalyticsDashboard';
import { AIAssistant } from './components/ai/AIAssistant';
import { useEffect } from 'react';
import { useOfflineSync } from './hooks/useOfflineSync';
import { EnhancedErrorBoundary } from './components/EnhancedErrorBoundary';
import { ErrorHandler } from './utils/error-handler';
import { performanceMonitor } from './services/performance-monitor';
import { apiClient } from './utils/api-interceptors';

function App(): JSX.Element {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const { initializeOfflineSync } = useOfflineSync();

  useEffect(() => {
    checkAuth();
    initializeOfflineSync();
    
    // Set up error notification handler
    ErrorHandler.setNotificationCallback((message, type) => {
      // TODO: Integrate with your notification system
      console.log(`[${type.toUpperCase()}] ${message}`);
    });
    
    // Clean up performance monitoring on unmount
    return () => {
      performanceMonitor.cleanup();
    };
  }, [checkAuth, initializeOfflineSync]);

  
  return (
    <EnhancedErrorBoundary
      showDetails={process.env.NODE_ENV === 'development'}
      resetOnPropsChange
      resetKeys={[isAuthenticated]}
    >
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={!isAuthenticated ? <LoginPage /> : <Navigate to="/" />} />
        <Route path="/register" element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/" />} />
        
        {/* Mobile inspection route (no auth required for QR code access) */}
        <Route path="/inspection/mobile/:qrCodeId" element={<InspectionMobilePage />} />
        
        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<Layout />}>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/customers" element={<CustomersPage />} />
            <Route path="/customers/:id" element={<CustomersPage />} />
            <Route path="/projects" element={<ProjectsPage />} />
            <Route path="/projects/:id" element={<ProjectDetailPage />} />
            <Route path="/projects/:projectId/panels" element={<PanelsPage />} />
            <Route path="/projects/:projectId/panels/new" element={<PanelFormPage />} />
            <Route path="/projects/:projectId/panels/:panelId" element={<PanelSchedulePage />} />
            <Route path="/projects/:projectId/panels/:panelId/edit" element={<PanelFormPage />} />
            <Route path="/projects/:projectId/panels/:panelId/schedule" element={<PanelSchedulePage />} />
            <Route path="/projects/:projectId/arc-flash" element={<ArcFlashPage />} />
            <Route path="/projects/:projectId/short-circuit" element={<ShortCircuitPage />} />
            <Route path="/projects/:projectId/permits" element={<PermitDocumentsPage />} />
            <Route path="/projects/:projectId/inspections" element={<InspectionsPage />} />
            <Route path="/projects/:projectId/inspections/new" element={<InspectionFormPage />} />
            <Route path="/projects/:projectId/inspections/:inspectionId" element={<InspectionChecklistPage />} />
            <Route path="/estimates" element={<EstimatesPage />} />
            <Route path="/estimates/:id" element={<EstimatesPage />} />
            <Route path="/calculations" element={<CalculationsPage />} />
            <Route path="/materials" element={<MaterialsPage />} />
            <Route path="/analytics" element={<AnalyticsDashboard />} />
          </Route>
        </Route>
        
        {/* Catch all */}
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
      
      {/* AI Assistant - shown when authenticated */}
      {isAuthenticated && <AIAssistant />}
    </ErrorBoundary>
  );
}

export default App;