const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('=== Comprehensive Migration Fix Script ===\n');

const dbPath = path.join(__dirname, 'prisma', 'dev.db');

// Step 1: Check if database exists
console.log('Step 1: Checking database...');
if (!fs.existsSync(dbPath)) {
    console.log('✗ Database not found. Creating with prisma db push...');
    try {
        execSync('npx prisma db push', { stdio: 'inherit', cwd: __dirname });
    } catch (error) {
        console.error('Failed to create database:', error.message);
        process.exit(1);
    }
} else {
    console.log('✓ Database exists');
}

// Step 2: Check current migration status
console.log('\nStep 2: Checking migration status...');
try {
    const output = execSync('npx prisma migrate status', { encoding: 'utf-8', cwd: __dirname });
    console.log(output);
} catch (error) {
    console.log('Migration status check returned an error (this is expected if migrations are pending)');
    if (error.stdout) {
        console.log(error.stdout.toString());
    }
}

// Step 3: Apply indexes manually if needed
console.log('\nStep 3: Applying performance indexes manually...');
try {
    const sqlite3 = require('sqlite3').verbose();
    const db = new sqlite3.Database(dbPath);
    
    // Read the migration SQL
    const migrationPath = path.join(__dirname, 'prisma', 'migrations', '20240115_performance_indexes', 'migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf-8');
    
    // Split SQL statements
    const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt && !stmt.startsWith('--'));
    
    let completed = 0;
    const total = statements.length;
    
    const executeStatements = () => {
        return new Promise((resolve, reject) => {
            db.serialize(() => {
                statements.forEach((stmt) => {
                    const fullStmt = stmt + ';';
                    db.run(fullStmt, (err) => {
                        completed++;
                        if (err) {
                            if (!err.message.includes('already exists')) {
                                console.log(`✗ Failed to execute: ${err.message}`);
                            }
                        }
                        if (completed === total) {
                            resolve();
                        }
                    });
                });
            });
        });
    };
    
    executeStatements().then(() => {
        console.log('✓ Indexes applied successfully');
        db.close();
        
        // Step 4: Mark migration as resolved
        console.log('\nStep 4: Marking migration as resolved...');
        try {
            execSync('npx prisma migrate resolve --applied "20240115_performance_indexes"', { 
                stdio: 'inherit', 
                cwd: __dirname 
            });
            console.log('✓ Migration marked as resolved');
        } catch (error) {
            console.log('✗ Failed to mark migration as resolved:', error.message);
        }
        
        // Step 5: Fix the second migration
        console.log('\nStep 5: Fixing security hardening migration...');
        const badMigrationPath = path.join(__dirname, 'prisma', 'migrations', '20240116_security_hardening', 'migration.sql');
        const fixedMigrationPath = path.join(__dirname, 'prisma', 'migrations', '20240116_security_hardening', 'migration-fixed.sql');
        
        if (fs.existsSync(fixedMigrationPath)) {
            // Backup original
            const backupPath = badMigrationPath + '.backup';
            if (!fs.existsSync(backupPath)) {
                fs.copyFileSync(badMigrationPath, backupPath);
                console.log('✓ Backed up original migration to:', path.basename(backupPath));
            }
            
            // Replace with fixed version
            fs.copyFileSync(fixedMigrationPath, badMigrationPath);
            console.log('✓ Replaced migration with fixed version');
        }
        
        // Step 6: Deploy all migrations
        console.log('\nStep 6: Deploying all migrations...');
        try {
            execSync('npx prisma migrate deploy', { stdio: 'inherit', cwd: __dirname });
            console.log('✓ All migrations deployed successfully');
        } catch (error) {
            console.log('✗ Failed to deploy migrations:', error.message);
        }
        
        // Step 7: Final status check
        console.log('\nStep 7: Final migration status...');
        try {
            const finalStatus = execSync('npx prisma migrate status', { encoding: 'utf-8', cwd: __dirname });
            console.log(finalStatus);
            console.log('\n✓ Migration fix completed successfully!');
        } catch (error) {
            console.log('Final status check error:', error.message);
        }
    });
    
} catch (error) {
    console.error('Error during index application:', error.message);
    console.log('\nMake sure sqlite3 is installed: npm install sqlite3');
}