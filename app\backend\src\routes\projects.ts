import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../index';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';
import { ProjectSchema } from '@electrical/shared';
import { emitToProject } from '../socket';
import { io } from '../index';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Pagination and filter schema
const projectQuerySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  search: z.string().optional(),
  status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']).optional(),
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']).optional(),
  customer_id: z.string().uuid().optional(),
  sortBy: z.enum(['name', 'created_at', 'updated_at', 'status']).default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// Create project schema
const createProjectSchema = ProjectSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});

// Update project schema
const updateProjectSchema = createProjectSchema.partial();

// Get all projects
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const params = projectQuerySchema.parse(req.query);
    const skip = (params.page - 1) * params.limit;
    
    // Build where clause
    const where = {
      ...(params.search && {
        OR: [
          { name: { contains: params.search, mode: 'insensitive' as const } },
          { address: { contains: params.search, mode: 'insensitive' as const } },
          { permit_number: { contains: params.search, mode: 'insensitive' as const } }
        ]
      }),
      ...(params.status && { status: params.status }),
      ...(params.type && { type: params.type }),
      ...(params.customer_id && { customer_id: params.customer_id })
    };
    
    // Get projects with pagination
    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        skip,
        take: params.limit,
        orderBy: { [params.sortBy]: params.sortOrder },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              estimates: true,
              calculations: true
            }
          }
        }
      }),
      prisma.project.count({ where })
    ]);
    
    res.json({
      data: projects,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get project by ID
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      include: {
        customer: true,
        estimates: {
          orderBy: { version: 'desc' },
          take: 5,
          include: {
            _count: {
              select: {
                material_items: true,
                labor_items: true
              }
            }
          }
        },
        calculations: {
          orderBy: { created_at: 'desc' },
          take: 10
        }
      }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

// Create project
router.post('/', authorize('admin', 'estimator', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const data = createProjectSchema.parse(req.body);
    
    // Verify customer exists
    const customer = await prisma.customer.findFirst({
      where: {
        id: data.customer_id,
        deleted_at: null
      }
    });
    
    if (!customer) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    const project = await prisma.project.create({
      data,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    res.status(201).json(project);
  } catch (error) {
    next(error);
  }
});

// Update project
router.put('/:id', authorize('admin', 'estimator', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const data = updateProjectSchema.parse(req.body);
    
    // Check if project exists
    const existing = await prisma.project.findUnique({
      where: { id: req.params.id }
    });
    
    if (!existing) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    // If changing customer, verify new customer exists
    if (data.customer_id) {
      const customer = await prisma.customer.findFirst({
        where: {
          id: data.customer_id,
          deleted_at: null
        }
      });
      
      if (!customer) {
        throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
      }
    }
    
    const project = await prisma.project.update({
      where: { id: req.params.id },
      data,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    // Emit update to connected clients
    emitToProject(io, project.id, 'project:updated', project);
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

// Delete project (only if no estimates)
router.delete('/:id', authorize('admin'), async (req: AuthRequest, res, next) => {
  try {
    // Check if project has estimates
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      include: {
        estimates: {
          take: 1
        }
      }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    if (project.estimates.length > 0) {
      throw new AppError(
        400,
        'Cannot delete project with estimates',
        true,
        'ESTIMATES_EXIST'
      );
    }
    
    await prisma.project.delete({
      where: { id: req.params.id }
    });
    
    res.json({ message: 'Project deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Get project estimates
router.get('/:id/estimates', async (req: AuthRequest, res, next) => {
  try {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    const estimates = await prisma.estimate.findMany({
      where: { project_id: req.params.id },
      orderBy: { version: 'desc' },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        approver: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            material_items: true,
            labor_items: true
          }
        }
      }
    });
    
    res.json(estimates);
  } catch (error) {
    next(error);
  }
});

// Get project calculations
router.get('/:id/calculations', async (req: AuthRequest, res, next) => {
  try {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id }
    });
    
    if (!project) {
      throw new AppError(404, 'Project not found', true, 'PROJECT_NOT_FOUND');
    }
    
    const calculations = await prisma.calculationLog.findMany({
      where: { project_id: req.params.id },
      orderBy: { created_at: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    res.json(calculations);
  } catch (error) {
    next(error);
  }
});

// Update project status
router.patch('/:id/status', authorize('admin', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const statusSchema = z.object({
      status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD'])
    });
    
    const { status } = statusSchema.parse(req.body);
    
    const project = await prisma.project.update({
      where: { id: req.params.id },
      data: { status },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    
    // Emit status change
    emitToProject(io, project.id, 'project:status:changed', {
      projectId: project.id,
      status,
      changedBy: req.user?.userId
    });
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

// Update inspection status
router.patch('/:id/inspection', authorize('admin', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const inspectionSchema = z.object({
      inspection_status: z.enum(['PENDING', 'ROUGH_IN_PASSED', 'FINAL_PASSED', 'FAILED'])
    });
    
    const { inspection_status } = inspectionSchema.parse(req.body);
    
    const project = await prisma.project.update({
      where: { id: req.params.id },
      data: { inspection_status }
    });
    
    // Emit inspection update
    emitToProject(io, project.id, 'project:inspection:updated', {
      projectId: project.id,
      inspection_status,
      updatedBy: req.user?.userId
    });
    
    res.json(project);
  } catch (error) {
    next(error);
  }
});

export { router as projectsRouter };