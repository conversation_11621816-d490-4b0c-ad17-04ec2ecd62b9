import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Plus, Calendar, CheckCircle, XCircle, AlertCircle, Clock, QrCode, FileText } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Select } from '../ui/select';
import inspectionService, { InspectionChecklist } from '../../services/inspectionService';
import { format } from 'date-fns';
import { QRCodeModal } from './QRCodeModal';

const statusColors = {
  PENDING: 'bg-gray-500',
  SCHEDULED: 'bg-blue-500',
  IN_PROGRESS: 'bg-yellow-500',
  PASSED: 'bg-green-500',
  FAILED: 'bg-red-500',
  PARTIAL_PASS: 'bg-orange-500',
  CANCELLED: 'bg-gray-400'
};

const statusIcons = {
  PENDING: Clock,
  SCHEDULED: Calendar,
  IN_PROGRESS: AlertCircle,
  PASSED: CheckCircle,
  FAILED: XCircle,
  PARTIAL_PASS: AlertCircle,
  CANCELLED: XCircle
};

export const InspectionList: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [inspections, setInspections] = useState<InspectionChecklist[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInspection, setSelectedInspection] = useState<InspectionChecklist | null>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    inspectionType: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    if (projectId) {
      loadInspections();
    }
  }, [projectId, filters]);

  const loadInspections = async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      const data = await inspectionService.listProjectInspections(projectId, {
        status: filters.status || undefined,
        inspectionType: filters.inspectionType || undefined,
        dateFrom: filters.dateFrom ? new Date(filters.dateFrom) : undefined,
        dateTo: filters.dateTo ? new Date(filters.dateTo) : undefined
      });
      setInspections(data);
    } catch (error) {
      console.error('Failed to load inspections:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInspection = () => {
    navigate(`/projects/${projectId}/inspections/new`);
  };

  const handleInspectionClick = (inspection: InspectionChecklist) => {
    navigate(`/projects/${projectId}/inspections/${inspection.id}`);
  };

  const handleGenerateReport = async (inspection: InspectionChecklist, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const result = await inspectionService.generateReport(inspection.id);
      // Open the report in a new tab
      window.open(result.reportPath, '_blank');
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  const getInspectionProgress = (inspection: InspectionChecklist) => {
    const total = inspection.checklist_items.length;
    const inspected = inspection.checklist_items.filter(
      item => item.status !== 'NOT_INSPECTED'
    ).length;
    return total > 0 ? Math.round((inspected / total) * 100) : 0;
  };

  const getInspectionSummary = (inspection: InspectionChecklist) => {
    const items = inspection.checklist_items;
    const passed = items.filter(i => i.status === 'PASS').length;
    const failed = items.filter(i => i.status === 'FAIL').length;
    const na = items.filter(i => i.status === 'NA').length;
    const corrected = items.filter(i => i.status === 'CORRECTED').length;
    
    return { passed, failed, na, corrected, total: items.length };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Inspections</h2>
        <Button onClick={handleCreateInspection}>
          <Plus className="h-4 w-4 mr-2" />
          New Inspection
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Select
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
          >
            <option value="">All Statuses</option>
            <option value="PENDING">Pending</option>
            <option value="SCHEDULED">Scheduled</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="PASSED">Passed</option>
            <option value="FAILED">Failed</option>
            <option value="PARTIAL_PASS">Partial Pass</option>
          </Select>

          <Select
            value={filters.inspectionType}
            onChange={(e) => setFilters({ ...filters, inspectionType: e.target.value })}
          >
            <option value="">All Types</option>
            <option value="ROUGH_IN">Rough-In</option>
            <option value="FINAL">Final</option>
            <option value="SERVICE">Service</option>
            <option value="POOL_SPA">Pool/Spa</option>
            <option value="SOLAR">Solar</option>
            <option value="GENERATOR">Generator</option>
            <option value="EMERGENCY_SYSTEM">Emergency System</option>
          </Select>

          <Input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
            placeholder="From Date"
          />

          <Input
            type="date"
            value={filters.dateTo}
            onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
            placeholder="To Date"
          />
        </div>
      </Card>

      {/* Inspection List */}
      <div className="grid gap-4">
        {inspections.length === 0 ? (
          <Card className="p-8 text-center">
            <p className="text-gray-500">No inspections found</p>
          </Card>
        ) : (
          inspections.map((inspection) => {
            const StatusIcon = statusIcons[inspection.status];
            const progress = getInspectionProgress(inspection);
            const summary = getInspectionSummary(inspection);
            
            return (
              <Card
                key={inspection.id}
                className="p-6 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => handleInspectionClick(inspection)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <StatusIcon className={`h-5 w-5 ${statusColors[inspection.status]} text-white rounded-full p-1`} />
                      <h3 className="text-lg font-semibold">
                        {inspection.inspection_type.replace(/_/g, ' ')} Inspection
                        {inspection.inspection_subtype && ` - ${inspection.inspection_subtype}`}
                      </h3>
                      <Badge variant="secondary">
                        #{inspection.inspection_number}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-gray-600">
                          <strong>Status:</strong> {inspection.status.replace(/_/g, ' ')}
                        </p>
                        {inspection.inspection_date && (
                          <p className="text-sm text-gray-600">
                            <strong>Inspection Date:</strong> {format(new Date(inspection.inspection_date), 'MMM d, yyyy')}
                          </p>
                        )}
                        {inspection.scheduled_date && !inspection.inspection_date && (
                          <p className="text-sm text-gray-600">
                            <strong>Scheduled:</strong> {format(new Date(inspection.scheduled_date), 'MMM d, yyyy')}
                          </p>
                        )}
                        {inspection.inspector_name && (
                          <p className="text-sm text-gray-600">
                            <strong>Inspector:</strong> {inspection.inspector_name}
                          </p>
                        )}
                      </div>

                      <div>
                        <p className="text-sm text-gray-600">
                          <strong>Progress:</strong> {progress}% ({summary.passed + summary.failed + summary.na + summary.corrected}/{summary.total} items)
                        </p>
                        <div className="flex gap-2 mt-1 text-xs">
                          <span className="text-green-600">✓ {summary.passed}</span>
                          <span className="text-red-600">✗ {summary.failed}</span>
                          <span className="text-gray-500">N/A {summary.na}</span>
                          {summary.corrected > 0 && (
                            <span className="text-blue-600">↻ {summary.corrected}</span>
                          )}
                        </div>
                        {inspection.corrections_required && (
                          <Badge variant="destructive" className="mt-2">
                            Corrections Required
                          </Badge>
                        )}
                        {inspection.reinspection_required && (
                          <Badge variant="warning" className="mt-2">
                            Reinspection Required
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-4 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>

                  <div className="flex gap-2 ml-4">
                    {inspection.qr_code_id && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedInspection(inspection);
                          setShowQRModal(true);
                        }}
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                    )}
                    {inspection.report_generated && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => handleGenerateReport(inspection, e)}
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })
        )}
      </div>

      {/* QR Code Modal */}
      {selectedInspection && (
        <QRCodeModal
          isOpen={showQRModal}
          onClose={() => {
            setShowQRModal(false);
            setSelectedInspection(null);
          }}
          inspection={selectedInspection}
        />
      )}
    </div>
  );
};