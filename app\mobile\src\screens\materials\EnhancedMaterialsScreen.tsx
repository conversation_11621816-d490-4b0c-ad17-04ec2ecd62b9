import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { EnhancedBarcodeScanner } from '../../components/scanner/EnhancedBarcodeScanner';
import { ScanHistory } from '../../components/scanner/ScanHistory';
import { InventoryManagement } from '../../components/inventory/InventoryManagement';
import { barcodeScannerService } from '../../services/barcodeScannerService';
import { MaterialInfo, ScannedItem, ScanAnalytics } from '../../types/barcode';

type Props = NativeStackScreenProps<RootStackParamList, 'Materials'>;

export const EnhancedMaterialsScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<'scan' | 'history' | 'inventory' | 'analytics'>('scan');
  const [showScanner, setShowScanner] = useState(false);
  const [scanMode, setScanMode] = useState<'single' | 'batch' | 'continuous'>('single');
  const [recentScans, setRecentScans] = useState<ScannedItem[]>([]);
  const [analytics, setAnalytics] = useState<ScanAnalytics | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    initializeScanner();
    loadRecentScans();
    loadAnalytics();
  }, []);

  const initializeScanner = async () => {
    await barcodeScannerService.initialize();
  };

  const loadRecentScans = async () => {
    const history = await barcodeScannerService.getScanHistory(5);
    setRecentScans(history);
  };

  const loadAnalytics = async () => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    const data = await barcodeScannerService.getScanAnalytics(startDate, endDate);
    setAnalytics(data);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRecentScans();
    await loadAnalytics();
    setRefreshing(false);
  };

  const handleScanComplete = (items: ScannedItem[]) => {
    loadRecentScans();
    loadAnalytics();
    
    if (items.length === 1) {
      // Navigate to material detail for single scan
      const material = items[0].material;
      if (material) {
        navigation.navigate('MaterialDetail', { material });
      }
    } else if (items.length > 1) {
      // Show summary for batch scan
      Alert.alert(
        'Scan Complete',
        `Successfully scanned ${items.length} items`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'single-scan':
        setScanMode('single');
        setShowScanner(true);
        break;
      case 'batch-scan':
        setScanMode('batch');
        setShowScanner(true);
        break;
      case 'continuous-scan':
        setScanMode('continuous');
        setShowScanner(true);
        break;
      case 'manual-entry':
        navigation.navigate('ManualMaterialEntry');
        break;
      case 'import-csv':
        navigation.navigate('BulkImport');
        break;
      case 'generate-qr':
        navigation.navigate('QRGenerator');
        break;
    }
  };

  const renderScanTab = () => (
    <ScrollView
      style={styles.tabContent}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.quickAction, { backgroundColor: '#2196F3' }]}
          onPress={() => handleQuickAction('single-scan')}
        >
          <Icon name="qr-code-scanner" size={32} color="white" />
          <Text style={styles.quickActionText}>Single Scan</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickAction, { backgroundColor: '#4CAF50' }]}
          onPress={() => handleQuickAction('batch-scan')}
        >
          <Icon name="playlist-add" size={32} color="white" />
          <Text style={styles.quickActionText}>Batch Scan</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickAction, { backgroundColor: '#FF9800' }]}
          onPress={() => handleQuickAction('continuous-scan')}
        >
          <Icon name="all-inclusive" size={32} color="white" />
          <Text style={styles.quickActionText}>Continuous</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickAction, { backgroundColor: '#9C27B0' }]}
          onPress={() => handleQuickAction('manual-entry')}
        >
          <Icon name="keyboard" size={32} color="white" />
          <Text style={styles.quickActionText}>Manual Entry</Text>
        </TouchableOpacity>
      </View>

      {/* Recent Scans */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Scans</Text>
        {recentScans.length === 0 ? (
          <Text style={styles.emptyText}>No recent scans</Text>
        ) : (
          recentScans.map((scan) => (
            <TouchableOpacity
              key={scan.id}
              style={styles.recentScanItem}
              onPress={() => {
                if (scan.material) {
                  navigation.navigate('MaterialDetail', { material: scan.material });
                }
              }}
            >
              <View style={styles.scanItemInfo}>
                <Text style={styles.scanItemName}>
                  {scan.material?.name || 'Unknown Material'}
                </Text>
                <Text style={styles.scanItemDetails}>
                  {scan.barcode} • Qty: {scan.quantity}
                </Text>
                <Text style={styles.scanItemTime}>
                  {new Date(scan.timestamp).toLocaleString()}
                </Text>
              </View>
              <Icon name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>
          ))
        )}
      </View>

      {/* Additional Actions */}
      <View style={styles.additionalActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleQuickAction('import-csv')}
        >
          <Icon name="upload-file" size={20} color="#666" />
          <Text style={styles.actionButtonText}>Import CSV</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleQuickAction('generate-qr')}
        >
          <Icon name="qr-code" size={20} color="#666" />
          <Text style={styles.actionButtonText}>Generate QR</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => barcodeScannerService.updateOfflineCatalog()}
        >
          <Icon name="sync" size={20} color="#666" />
          <Text style={styles.actionButtonText}>Update Catalog</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderAnalyticsTab = () => (
    <ScrollView style={styles.tabContent}>
      {analytics ? (
        <>
          {/* Summary Stats */}
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{analytics.totalScans}</Text>
              <Text style={styles.statLabel}>Total Scans</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{analytics.uniqueMaterials}</Text>
              <Text style={styles.statLabel}>Unique Materials</Text>
            </View>
          </View>

          {/* Top Scanned Materials */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Scanned Materials</Text>
            {analytics.topScannedMaterials.map((item, index) => (
              <View key={item.materialId} style={styles.topMaterialItem}>
                <Text style={styles.topMaterialRank}>#{index + 1}</Text>
                <View style={styles.topMaterialInfo}>
                  <Text style={styles.topMaterialName}>{item.name}</Text>
                  <Text style={styles.topMaterialSku}>{item.sku}</Text>
                </View>
                <Text style={styles.topMaterialCount}>{item.scanCount} scans</Text>
              </View>
            ))}
          </View>

          {/* Scans by Category */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Scans by Category</Text>
            {Object.entries(analytics.scansByCategory).map(([category, count]) => (
              <View key={category} style={styles.categoryItem}>
                <Text style={styles.categoryName}>{category}</Text>
                <View style={styles.categoryBar}>
                  <View
                    style={[
                      styles.categoryBarFill,
                      {
                        width: `${(count / analytics.totalScans) * 100}%`,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.categoryCount}>{count}</Text>
              </View>
            ))}
          </View>
        </>
      ) : (
        <Text style={styles.loadingText}>Loading analytics...</Text>
      )}
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Materials</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Settings')}>
          <Icon name="settings" size={24} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'scan' && styles.activeTab]}
          onPress={() => setActiveTab('scan')}
        >
          <Icon name="qr-code-scanner" size={20} color={activeTab === 'scan' ? '#2196F3' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'scan' && styles.activeTabText]}>
            Scan
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <Icon name="history" size={20} color={activeTab === 'history' ? '#2196F3' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            History
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'inventory' && styles.activeTab]}
          onPress={() => setActiveTab('inventory')}
        >
          <Icon name="inventory" size={20} color={activeTab === 'inventory' ? '#2196F3' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'inventory' && styles.activeTabText]}>
            Inventory
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'analytics' && styles.activeTab]}
          onPress={() => setActiveTab('analytics')}
        >
          <Icon name="analytics" size={20} color={activeTab === 'analytics' ? '#2196F3' : '#666'} />
          <Text style={[styles.tabText, activeTab === 'analytics' && styles.activeTabText]}>
            Analytics
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {activeTab === 'scan' && renderScanTab()}
      {activeTab === 'history' && <ScanHistory />}
      {activeTab === 'inventory' && <InventoryManagement />}
      {activeTab === 'analytics' && renderAnalyticsTab()}

      {/* Scanner */}
      <EnhancedBarcodeScanner
        visible={showScanner}
        mode={scanMode}
        onClose={() => setShowScanner(false)}
        onScanComplete={handleScanComplete}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#2196F3',
  },
  tabText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#2196F3',
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 15,
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    aspectRatio: 1.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    elevation: 3,
  },
  quickActionText: {
    color: 'white',
    marginTop: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 15,
    marginBottom: 15,
    padding: 15,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    marginVertical: 20,
  },
  recentScanItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  scanItemInfo: {
    flex: 1,
  },
  scanItemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  scanItemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  scanItemTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  additionalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 15,
    paddingVertical: 20,
  },
  actionButton: {
    alignItems: 'center',
  },
  actionButtonText: {
    marginTop: 4,
    fontSize: 12,
    color: '#666',
  },
  statsGrid: {
    flexDirection: 'row',
    padding: 15,
    justifyContent: 'space-around',
  },
  statCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 150,
    elevation: 2,
  },
  statValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  topMaterialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  topMaterialRank: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
    width: 40,
  },
  topMaterialInfo: {
    flex: 1,
  },
  topMaterialName: {
    fontSize: 14,
    fontWeight: '500',
  },
  topMaterialSku: {
    fontSize: 12,
    color: '#666',
  },
  topMaterialCount: {
    fontSize: 14,
    color: '#666',
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryName: {
    width: 100,
    fontSize: 14,
    color: '#666',
  },
  categoryBar: {
    flex: 1,
    height: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    marginHorizontal: 10,
    overflow: 'hidden',
  },
  categoryBarFill: {
    height: '100%',
    backgroundColor: '#2196F3',
  },
  categoryCount: {
    width: 40,
    textAlign: 'right',
    fontSize: 14,
    color: '#666',
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 50,
    color: '#666',
  },
});