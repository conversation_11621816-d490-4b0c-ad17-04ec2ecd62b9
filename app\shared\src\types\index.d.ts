import { z } from 'zod';
export declare const CustomerSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    email: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    address: z.<PERSON>odOptional<z.ZodString>;
    city: z.ZodOptional<z.ZodString>;
    state: z.ZodOptional<z.ZodString>;
    zip: z.ZodOptional<z.ZodString>;
    license_number: z.ZodOptional<z.ZodString>;
    insurance_expiry: z.ZodOptional<z.ZodDate>;
    credit_limit: z.ZodOptional<z.ZodNumber>;
    payment_terms: z.<PERSON>od<PERSON><PERSON><PERSON><z.ZodEnum<["NET15", "NET30", "NET45", "COD"]>>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
    deleted_at: z.ZodNullable<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    payment_terms: "NET15" | "NET30" | "NET45" | "COD";
    created_at: Date;
    updated_at: Date;
    deleted_at: Date | null;
    state?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
    address?: string | undefined;
    city?: string | undefined;
    zip?: string | undefined;
    license_number?: string | undefined;
    insurance_expiry?: Date | undefined;
    credit_limit?: number | undefined;
}, {
    id: string;
    name: string;
    created_at: Date;
    updated_at: Date;
    deleted_at: Date | null;
    state?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
    address?: string | undefined;
    city?: string | undefined;
    zip?: string | undefined;
    license_number?: string | undefined;
    insurance_expiry?: Date | undefined;
    credit_limit?: number | undefined;
    payment_terms?: "NET15" | "NET30" | "NET45" | "COD" | undefined;
}>;
export type Customer = z.infer<typeof CustomerSchema>;
export declare const ProjectSchema: z.ZodObject<{
    id: z.ZodString;
    customer_id: z.ZodString;
    name: z.ZodString;
    address: z.ZodString;
    city: z.ZodString;
    state: z.ZodString;
    zip: z.ZodString;
    type: z.ZodEnum<["RESIDENTIAL", "COMMERCIAL", "INDUSTRIAL"]>;
    status: z.ZodEnum<["PLANNING", "APPROVED", "IN_PROGRESS", "COMPLETED", "ON_HOLD"]>;
    voltage_system: z.ZodEnum<["120/240V_1PH", "208V_3PH", "240V_3PH", "480V_3PH", "277/480V_3PH"]>;
    service_size: z.ZodNumber;
    square_footage: z.ZodOptional<z.ZodNumber>;
    permit_number: z.ZodOptional<z.ZodString>;
    permit_expiry: z.ZodOptional<z.ZodDate>;
    inspection_status: z.ZodOptional<z.ZodEnum<["PENDING", "ROUGH_IN_PASSED", "FINAL_PASSED", "FAILED"]>>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    type: "RESIDENTIAL" | "COMMERCIAL" | "INDUSTRIAL";
    status: "IN_PROGRESS" | "COMPLETED" | "PLANNING" | "APPROVED" | "ON_HOLD";
    name: string;
    state: string;
    address: string;
    city: string;
    zip: string;
    created_at: Date;
    updated_at: Date;
    customer_id: string;
    voltage_system: "120/240V_1PH" | "208V_3PH" | "240V_3PH" | "480V_3PH" | "277/480V_3PH";
    service_size: number;
    square_footage?: number | undefined;
    permit_number?: string | undefined;
    permit_expiry?: Date | undefined;
    inspection_status?: "PENDING" | "FAILED" | "ROUGH_IN_PASSED" | "FINAL_PASSED" | undefined;
}, {
    id: string;
    type: "RESIDENTIAL" | "COMMERCIAL" | "INDUSTRIAL";
    status: "IN_PROGRESS" | "COMPLETED" | "PLANNING" | "APPROVED" | "ON_HOLD";
    name: string;
    state: string;
    address: string;
    city: string;
    zip: string;
    created_at: Date;
    updated_at: Date;
    customer_id: string;
    voltage_system: "120/240V_1PH" | "208V_3PH" | "240V_3PH" | "480V_3PH" | "277/480V_3PH";
    service_size: number;
    square_footage?: number | undefined;
    permit_number?: string | undefined;
    permit_expiry?: Date | undefined;
    inspection_status?: "PENDING" | "FAILED" | "ROUGH_IN_PASSED" | "FINAL_PASSED" | undefined;
}>;
export type Project = z.infer<typeof ProjectSchema>;
export declare const MaterialItemSchema: z.ZodObject<{
    id: z.ZodString;
    estimate_id: z.ZodString;
    catalog_number: z.ZodString;
    description: z.ZodString;
    category: z.ZodEnum<["WIRE", "CONDUIT", "DEVICES", "PANELS", "FIXTURES", "MISC"]>;
    unit: z.ZodEnum<["FT", "EA", "BOX", "ROLL", "HR"]>;
    quantity: z.ZodNumber;
    unit_cost: z.ZodNumber;
    markup_percent: z.ZodNumber;
    waste_percent: z.ZodNumber;
    tax_rate: z.ZodNumber;
    extended_cost: z.ZodNumber;
    tax_amount: z.ZodNumber;
    total_amount: z.ZodNumber;
    supplier: z.ZodOptional<z.ZodString>;
    wire_size: z.ZodOptional<z.ZodString>;
    wire_type: z.ZodOptional<z.ZodEnum<["THHN", "THWN", "XHHW", "NM", "MC", "USE"]>>;
    conduit_size: z.ZodOptional<z.ZodString>;
    conduit_type: z.ZodOptional<z.ZodEnum<["EMT", "RMC", "PVC", "LFMC", "LFNC"]>>;
    voltage_rating: z.ZodOptional<z.ZodNumber>;
    amperage_rating: z.ZodOptional<z.ZodNumber>;
    phase: z.ZodOptional<z.ZodEnum<["1PH", "3PH"]>>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    unit: "FT" | "EA" | "BOX" | "ROLL" | "HR";
    description: string;
    created_at: Date;
    updated_at: Date;
    estimate_id: string;
    catalog_number: string;
    category: "WIRE" | "CONDUIT" | "DEVICES" | "PANELS" | "FIXTURES" | "MISC";
    quantity: number;
    unit_cost: number;
    markup_percent: number;
    waste_percent: number;
    tax_rate: number;
    extended_cost: number;
    tax_amount: number;
    total_amount: number;
    supplier?: string | undefined;
    wire_size?: string | undefined;
    wire_type?: "THHN" | "THWN" | "XHHW" | "NM" | "MC" | "USE" | undefined;
    conduit_size?: string | undefined;
    conduit_type?: "EMT" | "RMC" | "PVC" | "LFMC" | "LFNC" | undefined;
    voltage_rating?: number | undefined;
    amperage_rating?: number | undefined;
    phase?: "1PH" | "3PH" | undefined;
}, {
    id: string;
    unit: "FT" | "EA" | "BOX" | "ROLL" | "HR";
    description: string;
    created_at: Date;
    updated_at: Date;
    estimate_id: string;
    catalog_number: string;
    category: "WIRE" | "CONDUIT" | "DEVICES" | "PANELS" | "FIXTURES" | "MISC";
    quantity: number;
    unit_cost: number;
    markup_percent: number;
    waste_percent: number;
    tax_rate: number;
    extended_cost: number;
    tax_amount: number;
    total_amount: number;
    supplier?: string | undefined;
    wire_size?: string | undefined;
    wire_type?: "THHN" | "THWN" | "XHHW" | "NM" | "MC" | "USE" | undefined;
    conduit_size?: string | undefined;
    conduit_type?: "EMT" | "RMC" | "PVC" | "LFMC" | "LFNC" | undefined;
    voltage_rating?: number | undefined;
    amperage_rating?: number | undefined;
    phase?: "1PH" | "3PH" | undefined;
}>;
export type MaterialItem = z.infer<typeof MaterialItemSchema>;
export declare const LaborItemSchema: z.ZodObject<{
    id: z.ZodString;
    estimate_id: z.ZodString;
    description: z.ZodString;
    trade: z.ZodEnum<["ELECTRICIAN", "APPRENTICE", "HELPER", "FOREMAN"]>;
    hours: z.ZodNumber;
    rate: z.ZodNumber;
    overtime_hours: z.ZodDefault<z.ZodNumber>;
    overtime_rate: z.ZodNumber;
    burden_percent: z.ZodNumber;
    extended_cost: z.ZodNumber;
    burden_amount: z.ZodNumber;
    total_amount: z.ZodNumber;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    description: string;
    created_at: Date;
    updated_at: Date;
    estimate_id: string;
    extended_cost: number;
    total_amount: number;
    trade: "ELECTRICIAN" | "APPRENTICE" | "HELPER" | "FOREMAN";
    hours: number;
    rate: number;
    overtime_hours: number;
    overtime_rate: number;
    burden_percent: number;
    burden_amount: number;
}, {
    id: string;
    description: string;
    created_at: Date;
    updated_at: Date;
    estimate_id: string;
    extended_cost: number;
    total_amount: number;
    trade: "ELECTRICIAN" | "APPRENTICE" | "HELPER" | "FOREMAN";
    hours: number;
    rate: number;
    overtime_rate: number;
    burden_percent: number;
    burden_amount: number;
    overtime_hours?: number | undefined;
}>;
export type LaborItem = z.infer<typeof LaborItemSchema>;
export declare const EstimateSchema: z.ZodObject<{
    id: z.ZodString;
    project_id: z.ZodString;
    version: z.ZodNumber;
    status: z.ZodEnum<["DRAFT", "SENT", "APPROVED", "REJECTED", "EXPIRED"]>;
    valid_until: z.ZodDate;
    subtotal: z.ZodNumber;
    tax_total: z.ZodNumber;
    total_amount: z.ZodNumber;
    profit_margin: z.ZodNumber;
    contingency_percent: z.ZodNumber;
    notes: z.ZodOptional<z.ZodString>;
    terms: z.ZodOptional<z.ZodString>;
    created_by: z.ZodString;
    approved_by: z.ZodOptional<z.ZodString>;
    approved_at: z.ZodOptional<z.ZodDate>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: "APPROVED" | "DRAFT" | "SENT" | "REJECTED" | "EXPIRED";
    created_at: Date;
    updated_at: Date;
    total_amount: number;
    project_id: string;
    version: number;
    valid_until: Date;
    subtotal: number;
    tax_total: number;
    profit_margin: number;
    contingency_percent: number;
    created_by: string;
    notes?: string | undefined;
    terms?: string | undefined;
    approved_by?: string | undefined;
    approved_at?: Date | undefined;
}, {
    id: string;
    status: "APPROVED" | "DRAFT" | "SENT" | "REJECTED" | "EXPIRED";
    created_at: Date;
    updated_at: Date;
    total_amount: number;
    project_id: string;
    version: number;
    valid_until: Date;
    subtotal: number;
    tax_total: number;
    profit_margin: number;
    contingency_percent: number;
    created_by: string;
    notes?: string | undefined;
    terms?: string | undefined;
    approved_by?: string | undefined;
    approved_at?: Date | undefined;
}>;
export type Estimate = z.infer<typeof EstimateSchema>;
export declare const LoadCalculationSchema: z.ZodObject<{
    project_id: z.ZodString;
    calculation_type: z.ZodEnum<["STANDARD", "OPTIONAL", "EXISTING"]>;
    general_lighting_va: z.ZodNumber;
    small_appliance_va: z.ZodNumber;
    laundry_va: z.ZodNumber;
    appliance_loads: z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        va: z.ZodNumber;
        demand_factor: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        name: string;
        va: number;
        demand_factor: number;
    }, {
        name: string;
        va: number;
        demand_factor: number;
    }>, "many">;
    heating_cooling_va: z.ZodNumber;
    largest_motor_va: z.ZodNumber;
    other_loads_va: z.ZodNumber;
    total_computed_va: z.ZodNumber;
    demand_factor: z.ZodNumber;
    total_demand_va: z.ZodNumber;
    required_amperage: z.ZodNumber;
    recommended_service: z.ZodNumber;
    nec_article_reference: z.ZodString;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    created_at: Date;
    project_id: string;
    calculation_type: "STANDARD" | "OPTIONAL" | "EXISTING";
    general_lighting_va: number;
    small_appliance_va: number;
    laundry_va: number;
    appliance_loads: {
        name: string;
        va: number;
        demand_factor: number;
    }[];
    demand_factor: number;
    heating_cooling_va: number;
    largest_motor_va: number;
    other_loads_va: number;
    total_computed_va: number;
    total_demand_va: number;
    required_amperage: number;
    recommended_service: number;
    nec_article_reference: string;
}, {
    created_at: Date;
    project_id: string;
    calculation_type: "STANDARD" | "OPTIONAL" | "EXISTING";
    general_lighting_va: number;
    small_appliance_va: number;
    laundry_va: number;
    appliance_loads: {
        name: string;
        va: number;
        demand_factor: number;
    }[];
    demand_factor: number;
    heating_cooling_va: number;
    largest_motor_va: number;
    other_loads_va: number;
    total_computed_va: number;
    total_demand_va: number;
    required_amperage: number;
    recommended_service: number;
    nec_article_reference: string;
}>;
export type LoadCalculation = z.infer<typeof LoadCalculationSchema>;
export declare const VoltageDropSchema: z.ZodObject<{
    project_id: z.ZodString;
    circuit_name: z.ZodString;
    voltage: z.ZodNumber;
    phase: z.ZodEnum<["1PH", "3PH"]>;
    amperage: z.ZodNumber;
    distance: z.ZodNumber;
    conductor_size: z.ZodString;
    conductor_type: z.ZodEnum<["CU", "AL"]>;
    conduit_type: z.ZodEnum<["STEEL", "PVC", "AL"]>;
    power_factor: z.ZodDefault<z.ZodNumber>;
    ambient_temp_f: z.ZodDefault<z.ZodNumber>;
    calculated_vd_volts: z.ZodNumber;
    calculated_vd_percent: z.ZodNumber;
    nec_limit_percent: z.ZodNumber;
    passes_nec: z.ZodBoolean;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    created_at: Date;
    conduit_type: "PVC" | "AL" | "STEEL";
    phase: "1PH" | "3PH";
    project_id: string;
    circuit_name: string;
    voltage: number;
    amperage: number;
    distance: number;
    conductor_size: string;
    conductor_type: "CU" | "AL";
    power_factor: number;
    ambient_temp_f: number;
    calculated_vd_volts: number;
    calculated_vd_percent: number;
    nec_limit_percent: number;
    passes_nec: boolean;
}, {
    created_at: Date;
    conduit_type: "PVC" | "AL" | "STEEL";
    phase: "1PH" | "3PH";
    project_id: string;
    circuit_name: string;
    voltage: number;
    amperage: number;
    distance: number;
    conductor_size: string;
    conductor_type: "CU" | "AL";
    calculated_vd_volts: number;
    calculated_vd_percent: number;
    nec_limit_percent: number;
    passes_nec: boolean;
    power_factor?: number | undefined;
    ambient_temp_f?: number | undefined;
}>;
export type VoltageDrop = z.infer<typeof VoltageDropSchema>;
export declare const MaterialPriceHistorySchema: z.ZodObject<{
    id: z.ZodString;
    catalog_number: z.ZodString;
    supplier: z.ZodString;
    unit_cost: z.ZodNumber;
    effective_date: z.ZodDate;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    created_at: Date;
    catalog_number: string;
    unit_cost: number;
    supplier: string;
    effective_date: Date;
}, {
    id: string;
    created_at: Date;
    catalog_number: string;
    unit_cost: number;
    supplier: string;
    effective_date: Date;
}>;
export type MaterialPriceHistory = z.infer<typeof MaterialPriceHistorySchema>;
export declare const AgentMessageSchema: z.ZodObject<{
    id: z.ZodString;
    sender: z.ZodEnum<["project_manager", "coding_agent", "ui_designer", "frontend_agent", "backend_agent", "research_agent", "debugging_agent", "memory_agent", "prompt_engineering_agent"]>;
    recipient: z.ZodEnum<["project_manager", "coding_agent", "ui_designer", "frontend_agent", "backend_agent", "research_agent", "debugging_agent", "memory_agent", "prompt_engineering_agent", "all"]>;
    priority: z.ZodEnum<["critical", "high", "medium", "low"]>;
    task_type: z.ZodEnum<["feature_implementation", "bug_fix", "research", "optimization", "testing", "documentation", "code_review"]>;
    message: z.ZodString;
    context: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodUnknown>>;
    status: z.ZodEnum<["pending", "in_progress", "completed", "failed"]>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: "completed" | "failed" | "pending" | "in_progress";
    message: string;
    priority: "critical" | "high" | "medium" | "low";
    created_at: Date;
    updated_at: Date;
    sender: "project_manager" | "coding_agent" | "ui_designer" | "frontend_agent" | "backend_agent" | "research_agent" | "debugging_agent" | "memory_agent" | "prompt_engineering_agent";
    recipient: "project_manager" | "coding_agent" | "ui_designer" | "frontend_agent" | "backend_agent" | "research_agent" | "debugging_agent" | "memory_agent" | "prompt_engineering_agent" | "all";
    task_type: "research" | "feature_implementation" | "bug_fix" | "optimization" | "testing" | "documentation" | "code_review";
    context?: Record<string, unknown> | undefined;
}, {
    id: string;
    status: "completed" | "failed" | "pending" | "in_progress";
    message: string;
    priority: "critical" | "high" | "medium" | "low";
    created_at: Date;
    updated_at: Date;
    sender: "project_manager" | "coding_agent" | "ui_designer" | "frontend_agent" | "backend_agent" | "research_agent" | "debugging_agent" | "memory_agent" | "prompt_engineering_agent";
    recipient: "project_manager" | "coding_agent" | "ui_designer" | "frontend_agent" | "backend_agent" | "research_agent" | "debugging_agent" | "memory_agent" | "prompt_engineering_agent" | "all";
    task_type: "research" | "feature_implementation" | "bug_fix" | "optimization" | "testing" | "documentation" | "code_review";
    context?: Record<string, unknown> | undefined;
}>;
export type AgentMessage = z.infer<typeof AgentMessageSchema>;
export declare const CalculationLogSchema: z.ZodObject<{
    id: z.ZodString;
    calculation_type: z.ZodEnum<["LOAD_CALC", "VOLTAGE_DROP", "CONDUIT_FILL", "WIRE_SIZE", "BREAKER_SIZE", "GROUNDING"]>;
    input_data: z.ZodRecord<z.ZodString, z.ZodUnknown>;
    output_data: z.ZodRecord<z.ZodString, z.ZodUnknown>;
    nec_references: z.ZodArray<z.ZodString, "many">;
    performed_by: z.ZodString;
    project_id: z.ZodOptional<z.ZodString>;
    created_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    created_at: Date;
    calculation_type: "VOLTAGE_DROP" | "CONDUIT_FILL" | "WIRE_SIZE" | "LOAD_CALC" | "BREAKER_SIZE" | "GROUNDING";
    input_data: Record<string, unknown>;
    output_data: Record<string, unknown>;
    nec_references: string[];
    performed_by: string;
    project_id?: string | undefined;
}, {
    id: string;
    created_at: Date;
    calculation_type: "VOLTAGE_DROP" | "CONDUIT_FILL" | "WIRE_SIZE" | "LOAD_CALC" | "BREAKER_SIZE" | "GROUNDING";
    input_data: Record<string, unknown>;
    output_data: Record<string, unknown>;
    nec_references: string[];
    performed_by: string;
    project_id?: string | undefined;
}>;
export type CalculationLog = z.infer<typeof CalculationLogSchema>;
export declare const PanelSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    project_id: z.ZodString;
    name: z.ZodString;
    location: z.ZodString;
    panel_type: z.ZodEnum<["MAIN", "SUB", "DISTRIBUTION"]>;
    manufacturer: z.ZodOptional<z.ZodString>;
    model_number: z.ZodOptional<z.ZodString>;
    catalog_number: z.ZodOptional<z.ZodString>;
    voltage_system: z.ZodEnum<["120/240V_1PH", "208V_3PH", "240V_3PH", "480V_3PH", "277/480V_3PH"]>;
    ampere_rating: z.ZodNumber;
    bus_rating: z.ZodNumber;
    main_breaker_size: z.ZodOptional<z.ZodNumber>;
    phase_config: z.ZodEnum<["SINGLE_PHASE", "THREE_PHASE_3W", "THREE_PHASE_4W"]>;
    mounting_type: z.ZodEnum<["SURFACE", "FLUSH", "RECESSED"]>;
    enclosure_type: z.ZodEnum<["NEMA_1", "NEMA_3R", "NEMA_4", "NEMA_4X"]>;
    spaces_total: z.ZodNumber;
    spaces_used: z.ZodOptional<z.ZodNumber>;
    fed_from_panel_id: z.ZodOptional<z.ZodString>;
    fed_from_circuit: z.ZodOptional<z.ZodNumber>;
    notes: z.ZodOptional<z.ZodString>;
    created_at: z.ZodOptional<z.ZodDate>;
    updated_at: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    name: string;
    voltage_system: "120/240V_1PH" | "208V_3PH" | "240V_3PH" | "480V_3PH" | "277/480V_3PH";
    project_id: string;
    location: string;
    panel_type: "MAIN" | "SUB" | "DISTRIBUTION";
    ampere_rating: number;
    bus_rating: number;
    phase_config: "SINGLE_PHASE" | "THREE_PHASE_3W" | "THREE_PHASE_4W";
    mounting_type: "SURFACE" | "FLUSH" | "RECESSED";
    enclosure_type: "NEMA_1" | "NEMA_3R" | "NEMA_4" | "NEMA_4X";
    spaces_total: number;
    id?: string | undefined;
    created_at?: Date | undefined;
    updated_at?: Date | undefined;
    catalog_number?: string | undefined;
    notes?: string | undefined;
    manufacturer?: string | undefined;
    model_number?: string | undefined;
    main_breaker_size?: number | undefined;
    spaces_used?: number | undefined;
    fed_from_panel_id?: string | undefined;
    fed_from_circuit?: number | undefined;
}, {
    name: string;
    voltage_system: "120/240V_1PH" | "208V_3PH" | "240V_3PH" | "480V_3PH" | "277/480V_3PH";
    project_id: string;
    location: string;
    panel_type: "MAIN" | "SUB" | "DISTRIBUTION";
    ampere_rating: number;
    bus_rating: number;
    phase_config: "SINGLE_PHASE" | "THREE_PHASE_3W" | "THREE_PHASE_4W";
    mounting_type: "SURFACE" | "FLUSH" | "RECESSED";
    enclosure_type: "NEMA_1" | "NEMA_3R" | "NEMA_4" | "NEMA_4X";
    spaces_total: number;
    id?: string | undefined;
    created_at?: Date | undefined;
    updated_at?: Date | undefined;
    catalog_number?: string | undefined;
    notes?: string | undefined;
    manufacturer?: string | undefined;
    model_number?: string | undefined;
    main_breaker_size?: number | undefined;
    spaces_used?: number | undefined;
    fed_from_panel_id?: string | undefined;
    fed_from_circuit?: number | undefined;
}>;
export type Panel = z.infer<typeof PanelSchema>;
export declare const CircuitSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    panel_id: z.ZodString;
    circuit_number: z.ZodNumber;
    description: z.ZodString;
    breaker_size: z.ZodNumber;
    breaker_type: z.ZodEnum<["STANDARD", "GFCI", "AFCI", "GFCI_AFCI", "SPACE_ONLY"]>;
    poles: z.ZodNumber;
    phase_connection: z.ZodOptional<z.ZodString>;
    wire_size: z.ZodString;
    wire_type: z.ZodEnum<["THHN", "THWN-2", "XHHW-2", "NM", "MC", "USE-2", "RHH", "RHW-2"]>;
    wire_count: z.ZodNumber;
    conduit_type: z.ZodOptional<z.ZodEnum<["EMT", "PVC", "RIGID", "MC_CABLE", "NM_CABLE", "FLEX"]>>;
    conduit_size: z.ZodOptional<z.ZodString>;
    voltage: z.ZodNumber;
    load_type: z.ZodEnum<["LIGHTING", "RECEPTACLE", "MOTOR", "HVAC", "APPLIANCE", "FEEDER", "EQUIPMENT"]>;
    continuous_load: z.ZodDefault<z.ZodBoolean>;
    connected_load: z.ZodNumber;
    demand_factor: z.ZodDefault<z.ZodNumber>;
    calculated_load: z.ZodOptional<z.ZodNumber>;
    room_area: z.ZodOptional<z.ZodString>;
    control_type: z.ZodOptional<z.ZodEnum<["SWITCH", "TIMER", "PHOTOCELL", "OCCUPANCY", "DIMMER", "CONTACTOR"]>>;
    is_spare: z.ZodDefault<z.ZodBoolean>;
    is_space: z.ZodDefault<z.ZodBoolean>;
    notes: z.ZodOptional<z.ZodString>;
    created_at: z.ZodOptional<z.ZodDate>;
    updated_at: z.ZodOptional<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    description: string;
    wire_size: string;
    wire_type: "THHN" | "NM" | "MC" | "THWN-2" | "XHHW-2" | "USE-2" | "RHH" | "RHW-2";
    demand_factor: number;
    voltage: number;
    panel_id: string;
    circuit_number: number;
    breaker_size: number;
    breaker_type: "STANDARD" | "GFCI" | "AFCI" | "GFCI_AFCI" | "SPACE_ONLY";
    poles: number;
    wire_count: number;
    load_type: "LIGHTING" | "RECEPTACLE" | "MOTOR" | "HVAC" | "APPLIANCE" | "FEEDER" | "EQUIPMENT";
    continuous_load: boolean;
    connected_load: number;
    is_spare: boolean;
    is_space: boolean;
    id?: string | undefined;
    created_at?: Date | undefined;
    updated_at?: Date | undefined;
    conduit_size?: string | undefined;
    conduit_type?: "EMT" | "PVC" | "RIGID" | "MC_CABLE" | "NM_CABLE" | "FLEX" | undefined;
    notes?: string | undefined;
    phase_connection?: string | undefined;
    calculated_load?: number | undefined;
    room_area?: string | undefined;
    control_type?: "SWITCH" | "TIMER" | "PHOTOCELL" | "OCCUPANCY" | "DIMMER" | "CONTACTOR" | undefined;
}, {
    description: string;
    wire_size: string;
    wire_type: "THHN" | "NM" | "MC" | "THWN-2" | "XHHW-2" | "USE-2" | "RHH" | "RHW-2";
    voltage: number;
    panel_id: string;
    circuit_number: number;
    breaker_size: number;
    breaker_type: "STANDARD" | "GFCI" | "AFCI" | "GFCI_AFCI" | "SPACE_ONLY";
    poles: number;
    wire_count: number;
    load_type: "LIGHTING" | "RECEPTACLE" | "MOTOR" | "HVAC" | "APPLIANCE" | "FEEDER" | "EQUIPMENT";
    connected_load: number;
    id?: string | undefined;
    created_at?: Date | undefined;
    updated_at?: Date | undefined;
    conduit_size?: string | undefined;
    conduit_type?: "EMT" | "PVC" | "RIGID" | "MC_CABLE" | "NM_CABLE" | "FLEX" | undefined;
    notes?: string | undefined;
    demand_factor?: number | undefined;
    phase_connection?: string | undefined;
    continuous_load?: boolean | undefined;
    calculated_load?: number | undefined;
    room_area?: string | undefined;
    control_type?: "SWITCH" | "TIMER" | "PHOTOCELL" | "OCCUPANCY" | "DIMMER" | "CONTACTOR" | undefined;
    is_spare?: boolean | undefined;
    is_space?: boolean | undefined;
}>;
export type Circuit = z.infer<typeof CircuitSchema>;
export declare const PanelLoadCalculationSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    panel_id: z.ZodString;
    calculation_date: z.ZodDate;
    phase_a_load: z.ZodNumber;
    phase_b_load: z.ZodNumber;
    phase_c_load: z.ZodNumber;
    neutral_load: z.ZodNumber;
    total_connected_load: z.ZodNumber;
    total_demand_load: z.ZodNumber;
    load_percentage: z.ZodNumber;
    phase_imbalance_percent: z.ZodNumber;
    power_factor: z.ZodNumber;
    ambient_temperature: z.ZodNumber;
    derating_factor: z.ZodNumber;
    notes: z.ZodOptional<z.ZodString>;
    created_by: z.ZodString;
}, "strip", z.ZodTypeAny, {
    created_by: string;
    power_factor: number;
    panel_id: string;
    calculation_date: Date;
    phase_a_load: number;
    phase_b_load: number;
    phase_c_load: number;
    neutral_load: number;
    total_connected_load: number;
    total_demand_load: number;
    load_percentage: number;
    phase_imbalance_percent: number;
    ambient_temperature: number;
    derating_factor: number;
    id?: string | undefined;
    notes?: string | undefined;
}, {
    created_by: string;
    power_factor: number;
    panel_id: string;
    calculation_date: Date;
    phase_a_load: number;
    phase_b_load: number;
    phase_c_load: number;
    neutral_load: number;
    total_connected_load: number;
    total_demand_load: number;
    load_percentage: number;
    phase_imbalance_percent: number;
    ambient_temperature: number;
    derating_factor: number;
    id?: string | undefined;
    notes?: string | undefined;
}>;
export type PanelLoadCalculation = z.infer<typeof PanelLoadCalculationSchema>;
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    name: z.ZodString;
    role: z.ZodEnum<["ADMIN", "ELECTRICIAN", "APPRENTICE", "ESTIMATOR", "VIEWER"]>;
    license_number: z.ZodOptional<z.ZodString>;
    license_state: z.ZodOptional<z.ZodString>;
    license_expiry: z.ZodOptional<z.ZodDate>;
    phone: z.ZodOptional<z.ZodString>;
    is_active: z.ZodDefault<z.ZodBoolean>;
    last_login: z.ZodOptional<z.ZodDate>;
    created_at: z.ZodDate;
    updated_at: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    email: string;
    created_at: Date;
    updated_at: Date;
    role: "ELECTRICIAN" | "APPRENTICE" | "ADMIN" | "ESTIMATOR" | "VIEWER";
    is_active: boolean;
    phone?: string | undefined;
    license_number?: string | undefined;
    license_state?: string | undefined;
    license_expiry?: Date | undefined;
    last_login?: Date | undefined;
}, {
    id: string;
    name: string;
    email: string;
    created_at: Date;
    updated_at: Date;
    role: "ELECTRICIAN" | "APPRENTICE" | "ADMIN" | "ESTIMATOR" | "VIEWER";
    phone?: string | undefined;
    license_number?: string | undefined;
    license_state?: string | undefined;
    license_expiry?: Date | undefined;
    is_active?: boolean | undefined;
    last_login?: Date | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
//# sourceMappingURL=index.d.ts.map