import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Linking,
  Alert,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { EmergencyContact } from '../../types/electrical';
import { EMERGENCY_ROLES } from '../../constants/electrical';

interface Props {
  projectId?: string;
}

export const EmergencyContacts: React.FC<Props> = ({ projectId }) => {
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContacts();
  }, [projectId]);

  const loadContacts = async () => {
    try {
      // Load project-specific contacts if projectId provided
      const key = projectId ? `emergency_contacts_${projectId}` : 'emergency_contacts_global';
      const stored = await AsyncStorage.getItem(key);
      
      if (stored) {
        setContacts(JSON.parse(stored));
      } else {
        // Load default contacts
        setContacts(getDefaultContacts());
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      setContacts(getDefaultContacts());
    } finally {
      setLoading(false);
    }
  };

  const getDefaultContacts = (): EmergencyContact[] => {
    return [
      {
        id: '1',
        name: 'Emergency Services',
        role: 'Fire Department',
        phone: '911',
        priority: 1,
        available24x7: true,
      },
      {
        id: '2',
        name: 'Electrical Inspector',
        role: 'Electrical Inspector',
        phone: '',
        priority: 2,
        available24x7: false,
      },
      {
        id: '3',
        name: 'Utility Company',
        role: 'Utility Company',
        phone: '',
        priority: 3,
        available24x7: true,
      },
    ];
  };

  const makeCall = (contact: EmergencyContact) => {
    if (!contact.phone) {
      Alert.alert('No Phone Number', 'Phone number not available for this contact.');
      return;
    }

    const phoneUrl = Platform.OS === 'ios' 
      ? `telprompt:${contact.phone}`
      : `tel:${contact.phone}`;

    Linking.canOpenURL(phoneUrl)
      .then(supported => {
        if (supported) {
          Alert.alert(
            'Confirm Call',
            `Call ${contact.name} at ${contact.phone}?`,
            [
              { text: 'Cancel', style: 'cancel' },
              { 
                text: 'Call', 
                onPress: () => {
                  Linking.openURL(phoneUrl);
                  logEmergencyCall(contact);
                }
              },
            ]
          );
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device.');
        }
      })
      .catch(err => console.error('Error checking phone support:', err));
  };

  const logEmergencyCall = async (contact: EmergencyContact) => {
    try {
      const log = {
        contactId: contact.id,
        contactName: contact.name,
        timestamp: new Date().toISOString(),
        projectId,
      };
      
      const logKey = 'emergency_call_log';
      const existingLog = await AsyncStorage.getItem(logKey);
      const logs = existingLog ? JSON.parse(existingLog) : [];
      logs.push(log);
      
      // Keep only last 100 entries
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      
      await AsyncStorage.setItem(logKey, JSON.stringify(logs));
    } catch (error) {
      console.error('Error logging call:', error);
    }
  };

  const getContactIcon = (role: string) => {
    switch (role) {
      case 'Fire Department': return '🚒';
      case 'Electrical Inspector': return '👷';
      case 'Utility Company': return '⚡';
      case 'Project Manager': return '📋';
      case 'Safety Officer': return '🦺';
      case 'General Contractor': return '🏗️';
      case 'Building Owner': return '🏢';
      case 'Emergency Electrician': return '🔧';
      default: return '📞';
    }
  };

  const renderContact = (contact: EmergencyContact) => {
    const isEmergency = contact.phone === '911';
    
    return (
      <TouchableOpacity
        key={contact.id}
        style={[
          styles.contactCard,
          isEmergency && styles.emergencyCard
        ]}
        onPress={() => makeCall(contact)}
        disabled={!contact.phone}
      >
        <View style={styles.contactHeader}>
          <Text style={styles.contactIcon}>{getContactIcon(contact.role)}</Text>
          <View style={styles.contactInfo}>
            <Text style={[styles.contactName, isEmergency && styles.emergencyText]}>
              {contact.name}
            </Text>
            <Text style={[styles.contactRole, isEmergency && styles.emergencySubtext]}>
              {contact.role}
            </Text>
          </View>
        </View>

        <View style={styles.contactDetails}>
          <Text style={[styles.phoneNumber, isEmergency && styles.emergencyPhone]}>
            {contact.phone || 'Not configured'}
          </Text>
          {contact.alternatePhone && (
            <Text style={styles.alternatePhone}>Alt: {contact.alternatePhone}</Text>
          )}
          {contact.available24x7 && (
            <View style={styles.availabilityBadge}>
              <Text style={styles.availabilityText}>24/7</Text>
            </View>
          )}
        </View>

        {contact.phone && (
          <View style={[styles.callButton, isEmergency && styles.emergencyCallButton]}>
            <Text style={styles.callButtonText}>CALL</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading emergency contacts...</Text>
      </View>
    );
  }

  const emergencyContacts = contacts.filter(c => c.priority === 1);
  const primaryContacts = contacts.filter(c => c.priority === 2);
  const secondaryContacts = contacts.filter(c => c.priority > 2);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Emergency Contacts</Text>
        <Text style={styles.subtitle}>Tap to call immediately</Text>
      </View>

      {emergencyContacts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🚨 EMERGENCY</Text>
          {emergencyContacts.map(renderContact)}
        </View>
      )}

      {primaryContacts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Primary Contacts</Text>
          {primaryContacts.map(renderContact)}
        </View>
      )}

      {secondaryContacts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Contacts</Text>
          {secondaryContacts.map(renderContact)}
        </View>
      )}

      <View style={styles.safetyTips}>
        <Text style={styles.tipsTitle}>Emergency Procedures</Text>
        <Text style={styles.tipItem}>• For electrical fires, use Class C fire extinguisher</Text>
        <Text style={styles.tipItem}>• Never use water on electrical fires</Text>
        <Text style={styles.tipItem}>• Evacuate area and call 911 immediately</Text>
        <Text style={styles.tipItem}>• Shut off main breaker if safely accessible</Text>
        <Text style={styles.tipItem}>• Do not re-enter until cleared by authorities</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#f44336',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 5,
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  contactCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emergencyCard: {
    backgroundColor: '#FFEBEE',
    borderWidth: 2,
    borderColor: '#f44336',
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  emergencyText: {
    color: '#f44336',
  },
  contactRole: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emergencySubtext: {
    color: '#f44336',
  },
  contactDetails: {
    marginRight: 15,
    alignItems: 'flex-end',
  },
  phoneNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  emergencyPhone: {
    color: '#f44336',
    fontSize: 24,
  },
  alternatePhone: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  availabilityBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 5,
  },
  availabilityText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  callButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  emergencyCallButton: {
    backgroundColor: '#f44336',
  },
  callButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  safetyTips: {
    backgroundColor: '#FFF3E0',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    marginTop: 30,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#E65100',
  },
  tipItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
    lineHeight: 20,
  },
});