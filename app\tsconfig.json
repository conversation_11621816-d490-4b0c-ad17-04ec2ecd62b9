{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022"], "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "composite": true, "declaration": true, "declarationMap": true}, "references": [{"path": "./shared"}, {"path": "./backend"}, {"path": "./frontend"}, {"path": "./agents"}], "exclude": ["node_modules", "dist", "build", "coverage"]}