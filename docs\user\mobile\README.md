# Mobile App User Guide

The Electrical Contractor mobile app provides full functionality on the go, with offline capabilities and field-specific tools designed for electrical professionals.

## 📱 Getting Started

### System Requirements

#### iOS
- iOS 13.0 or later
- iPhone 7 or newer
- iPad (6th generation) or newer
- 500MB free storage minimum

#### Android  
- Android 8.0 (API level 26) or later
- 2GB RAM minimum
- 500MB free storage minimum
- Camera with autofocus for scanning

### Installation

1. **Download the App**
   - iOS: Search "Electrical Contractor Pro" in App Store
   - Android: Search "Electrical Contractor Pro" in Google Play

2. **First Launch**
   - Allow requested permissions:
     - Camera (for barcode/QR scanning)
     - Location (for job site verification)
     - Notifications (for updates and alerts)
     - Storage (for offline data)

3. **Sign In**
   - Use your existing web credentials
   - Enable biometric login for quick access
   - Set up PIN as backup authentication

## 🏠 Home Screen Overview

### Dashboard Widgets
- **Today's Schedule**: Your appointments and tasks
- **Active Timer**: Current time tracking session
- **Quick Actions**: One-tap access to common tasks
- **Sync Status**: Offline data synchronization state
- **Weather**: Current conditions at job sites

### Bottom Navigation
1. **Home**: Dashboard and quick actions
2. **Projects**: Project list and details
3. **Tools**: Calculators and utilities
4. **Scanner**: Barcode/QR code scanner
5. **More**: Settings and additional features

## 📋 Core Features

### Project Management

#### Viewing Projects
- Swipe to refresh project list
- Tap project for details
- Long-press for quick actions
- Filter by status, customer, or date

#### Project Details
- **Overview**: Key information and status
- **Tasks**: To-do items and assignments  
- **Photos**: Job site documentation
- **Materials**: Used and needed items
- **Time**: Labor hours logged
- **Notes**: Text and voice notes

#### Offline Projects
- Star projects for offline access
- Auto-downloads related data
- Full functionality without internet
- Syncs changes when connected

### Time Tracking

#### Clock In/Out
1. Tap the timer icon
2. Select project and task
3. Tap "Start Timer"
4. GPS location recorded automatically
5. Tap "Stop Timer" when finished

#### Manual Entry
- Tap "+" on time tracking screen
- Select date and time range
- Choose project and task
- Add notes if needed
- Submit entry

#### Time Reports
- View daily/weekly summaries
- Export timesheets
- Track overtime automatically
- Submit for approval

### Material Management

#### Barcode Scanning
1. Tap scanner icon
2. Point at barcode
3. Auto-focuses and scans
4. Shows material details
5. Add to project or inventory

#### Quick Material Add
- Search by name or number
- Recent items for quick access
- Take photo of materials
- Voice-to-text for descriptions

#### Inventory Tracking
- View stock levels
- Low stock alerts
- Reorder from app
- Track material locations

### Calculations On-the-Go

All calculations work offline with instant results:

#### Quick Calc
- Wire size calculator
- Voltage drop calculator  
- Conduit fill calculator
- Basic load calculator

#### Voice Input
- Tap microphone icon
- Speak values clearly
- Automatic unit detection
- Confirms before calculating

#### Calculation History
- Recent calculations saved
- Swipe to delete
- Tap to recalculate
- Share results easily

### Photo Documentation

#### Taking Photos
1. Tap camera icon in project
2. Frame shot (grid available)
3. Tap shutter or volume button
4. Add annotations if needed
5. Auto-uploads when online

#### Photo Features
- **Markup Tools**: Draw, text, arrows
- **Timestamps**: Automatic date/time
- **Geotagging**: Location recorded
- **Categories**: Panel, rough-in, finish, etc.
- **Compression**: Optimized for storage

### Inspection Management

#### Digital Checklists
- Tap through inspection items
- Mark pass/fail/NA
- Add photos to items
- Required items highlighted
- Progress bar shows completion

#### Generating Reports
1. Complete all required items
2. Add inspector notes
3. Capture signatures
4. Generate PDF report
5. Email or share directly

## 🔧 Field Tools

### Panel Scanner
1. Open scanner from tools
2. Scan panel QR code
3. View/edit panel schedule
4. Update circuit statuses
5. Add photos and notes

### Arc Flash Scanner
- Scan equipment labels
- View safety requirements
- See required PPE
- Access one-line diagrams
- Emergency contacts

### Measurement Tools
- **Level**: Using device sensors
- **Angle Finder**: For conduit bends
- **Distance**: Rough measurements
- **Light Meter**: Using camera
- **Sound Meter**: For equipment rooms

### Reference Library
- NEC code book (searchable)
- Conduit fill tables
- Wire ampacity tables
- Motor FLC tables
- Calculation formulas

## 📶 Offline Functionality

### What Works Offline
- ✅ All calculations
- ✅ Viewing downloaded projects
- ✅ Taking photos
- ✅ Time tracking
- ✅ Creating notes
- ✅ Barcode scanning
- ✅ Reference materials

### Offline Setup
1. Go to Settings → Offline
2. Select projects to download
3. Choose data to include
4. Set storage limits
5. Enable auto-download

### Sync Management
- **Automatic Sync**: When connection restored
- **Manual Sync**: Pull down on any screen
- **Conflict Resolution**: Choose version to keep
- **Sync History**: View what was synced
- **Data Priority**: Critical data first

## 🔒 Security Features

### App Security
- **Biometric Login**: Face ID/Touch ID/Fingerprint
- **PIN Protection**: 6-digit PIN required
- **Auto-Lock**: After specified time
- **Remote Wipe**: If device lost
- **Encrypted Storage**: All local data encrypted

### Session Management
- View active sessions
- Remote logout capability
- Suspicious activity alerts
- Two-factor authentication
- Security audit logs

## ⚡ Performance Tips

### Battery Optimization
- Enable battery saver mode
- Reduce photo quality if needed
- Turn off GPS when not needed
- Use dark mode
- Close unused projects

### Storage Management  
- Regularly clean old photos
- Archive completed projects
- Clear calculation history
- Manage offline data
- Use cloud backup

### Network Usage
- Download projects on WiFi
- Queue uploads for WiFi
- Compress photos before upload
- Sync during off-hours
- Monitor data usage

## 🎯 Pro Tips

### Shortcuts
- **3D Touch/Long Press**: Quick actions on icons
- **Swipe Actions**: On list items
- **Shake to Undo**: In text fields
- **Pinch to Zoom**: On photos and diagrams
- **Double Tap**: Quick zoom on images

### Voice Commands
- "Start timer for [project]"
- "Calculate wire size"
- "Scan barcode"
- "Take photo"
- "Find [material name]"

### Gestures
- **Swipe Right**: Go back
- **Swipe Down**: Refresh
- **Swipe Left**: Delete (in lists)
- **Two-Finger Tap**: Multi-select
- **Long Press**: Context menu

## 🐛 Troubleshooting

### Common Issues

#### App Crashes
1. Force close app
2. Restart device
3. Check for updates
4. Clear app cache
5. Reinstall if needed

#### Sync Problems
1. Check internet connection
2. Verify credentials
3. Check sync settings
4. Clear sync queue
5. Contact support

#### Scanner Not Working
1. Check camera permissions
2. Clean camera lens
3. Improve lighting
4. Update app
5. Restart device

#### Slow Performance
1. Close other apps
2. Clear app cache
3. Reduce photo quality
4. Archive old data
5. Free up storage

## 📞 Getting Help

### In-App Support
- Tap More → Help
- Browse help articles
- Submit support ticket
- Live chat (business hours)
- Call support directly

### Diagnostic Tools
- More → Settings → Diagnostics
- Send logs to support
- Check sync status
- View error logs
- Test connectivity

## 🔄 Updates

### App Updates
- Automatic updates recommended
- Check App Store/Play Store
- Read update notes
- Test in safe mode first
- Report any issues

### Data Updates
- NEC codes updated annually
- Material prices weekly
- Tax rates quarterly
- Permit requirements as needed

## ✅ Mobile Best Practices

1. **Regular Backups**: Enable automatic backups
2. **Stay Updated**: Install updates promptly
3. **Secure Device**: Use lock screen
4. **Manage Storage**: Clean up regularly
5. **Test Offline**: Verify critical features work
6. **Sync Often**: Don't let data pile up
7. **Document Everything**: Photos save time later
8. **Use Templates**: For common tasks
9. **Train Team**: Everyone should know basics
10. **Report Issues**: Help improve the app