import 'react-native-gesture-handler/jestSetup';

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('react-native-vision-camera', () => ({
  Camera: {
    getAvailableCameraDevices: jest.fn().mockResolvedValue([]),
    getCameraPermissionStatus: jest.fn().mockResolvedValue('authorized'),
    getMicrophonePermissionStatus: jest.fn().mockResolvedValue('authorized'),
    requestCameraPermission: jest.fn().mockResolvedValue('authorized'),
    requestMicrophonePermission: jest.fn().mockResolvedValue('authorized'),
  },
}));