"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSchema = exports.PanelLoadCalculationSchema = exports.CircuitSchema = exports.PanelSchema = exports.CalculationLogSchema = exports.AgentMessageSchema = exports.MaterialPriceHistorySchema = exports.VoltageDropSchema = exports.LoadCalculationSchema = exports.EstimateSchema = exports.LaborItemSchema = exports.MaterialItemSchema = exports.ProjectSchema = exports.CustomerSchema = void 0;
const zod_1 = require("zod");
const decimal_js_1 = require("decimal.js");
// Configure Decimal for electrical precision (4 decimal places)
decimal_js_1.Decimal.set({ precision: 10, rounding: 4 }); // 4 = ROUND_UP
// Customer types
exports.CustomerSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    name: zod_1.z.string().min(1).max(255),
    email: zod_1.z.string().email().optional(),
    phone: zod_1.z.string().regex(/^\+?1?\d{10}$/).optional(),
    address: zod_1.z.string().optional(),
    city: zod_1.z.string().optional(),
    state: zod_1.z.string().length(2).optional(),
    zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/).optional(),
    license_number: zod_1.z.string().optional(),
    insurance_expiry: zod_1.z.date().optional(),
    credit_limit: zod_1.z.number().positive().optional(),
    payment_terms: zod_1.z.enum(['NET15', 'NET30', 'NET45', 'COD']).default('NET30'),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date(),
    deleted_at: zod_1.z.date().nullable()
});
// Project types with electrical specifications
exports.ProjectSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    customer_id: zod_1.z.string().uuid(),
    name: zod_1.z.string().min(1).max(255),
    address: zod_1.z.string(),
    city: zod_1.z.string(),
    state: zod_1.z.string().length(2),
    zip: zod_1.z.string().regex(/^\d{5}(-\d{4})?$/),
    type: zod_1.z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']),
    status: zod_1.z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']),
    voltage_system: zod_1.z.enum(['120/240V_1PH', '208V_3PH', '240V_3PH', '480V_3PH', '277/480V_3PH']),
    service_size: zod_1.z.number().int().positive(), // In amperes
    square_footage: zod_1.z.number().int().positive().optional(),
    permit_number: zod_1.z.string().optional(),
    permit_expiry: zod_1.z.date().optional(),
    inspection_status: zod_1.z.enum(['PENDING', 'ROUGH_IN_PASSED', 'FINAL_PASSED', 'FAILED']).optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
// Material types with electrical specifications
exports.MaterialItemSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    estimate_id: zod_1.z.string().uuid(),
    catalog_number: zod_1.z.string(),
    description: zod_1.z.string(),
    category: zod_1.z.enum(['WIRE', 'CONDUIT', 'DEVICES', 'PANELS', 'FIXTURES', 'MISC']),
    unit: zod_1.z.enum(['FT', 'EA', 'BOX', 'ROLL', 'HR']),
    quantity: zod_1.z.number().positive(),
    unit_cost: zod_1.z.number().positive(),
    markup_percent: zod_1.z.number().min(0).max(100),
    waste_percent: zod_1.z.number().min(0).max(25),
    tax_rate: zod_1.z.number().min(0).max(0.15),
    extended_cost: zod_1.z.number(), // Computed: quantity * unit_cost * (1 + markup_percent/100) * (1 + waste_percent/100)
    tax_amount: zod_1.z.number(), // Computed: extended_cost * tax_rate
    total_amount: zod_1.z.number(), // Computed: extended_cost + tax_amount
    supplier: zod_1.z.string().optional(),
    wire_size: zod_1.z.string().optional(), // AWG or kcmil
    wire_type: zod_1.z.enum(['THHN', 'THWN', 'XHHW', 'NM', 'MC', 'USE']).optional(),
    conduit_size: zod_1.z.string().optional(), // Trade size
    conduit_type: zod_1.z.enum(['EMT', 'RMC', 'PVC', 'LFMC', 'LFNC']).optional(),
    voltage_rating: zod_1.z.number().optional(),
    amperage_rating: zod_1.z.number().optional(),
    phase: zod_1.z.enum(['1PH', '3PH']).optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
// Labor types with trade classifications
exports.LaborItemSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    estimate_id: zod_1.z.string().uuid(),
    description: zod_1.z.string(),
    trade: zod_1.z.enum(['ELECTRICIAN', 'APPRENTICE', 'HELPER', 'FOREMAN']),
    hours: zod_1.z.number().positive(),
    rate: zod_1.z.number().positive(),
    overtime_hours: zod_1.z.number().min(0).default(0),
    overtime_rate: zod_1.z.number().positive(), // Typically 1.5x or 2x regular rate
    burden_percent: zod_1.z.number().min(0).max(50), // Labor burden (taxes, insurance, etc.)
    extended_cost: zod_1.z.number(), // Computed: (hours * rate) + (overtime_hours * overtime_rate)
    burden_amount: zod_1.z.number(), // Computed: extended_cost * burden_percent / 100
    total_amount: zod_1.z.number(), // Computed: extended_cost + burden_amount
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
// Estimate with version control
exports.EstimateSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    project_id: zod_1.z.string().uuid(),
    version: zod_1.z.number().int().positive(),
    status: zod_1.z.enum(['DRAFT', 'SENT', 'APPROVED', 'REJECTED', 'EXPIRED']),
    valid_until: zod_1.z.date(),
    subtotal: zod_1.z.number(), // Computed from material and labor items
    tax_total: zod_1.z.number(), // Computed from material tax
    total_amount: zod_1.z.number(), // Computed: subtotal + tax_total
    profit_margin: zod_1.z.number().min(0).max(50),
    contingency_percent: zod_1.z.number().min(0).max(25),
    notes: zod_1.z.string().optional(),
    terms: zod_1.z.string().optional(),
    created_by: zod_1.z.string().uuid(),
    approved_by: zod_1.z.string().uuid().optional(),
    approved_at: zod_1.z.date().optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
// Electrical calculation types
exports.LoadCalculationSchema = zod_1.z.object({
    project_id: zod_1.z.string().uuid(),
    calculation_type: zod_1.z.enum(['STANDARD', 'OPTIONAL', 'EXISTING']),
    general_lighting_va: zod_1.z.number(), // 3 VA/sq ft residential, varies commercial
    small_appliance_va: zod_1.z.number(), // 1500W per circuit, min 2 circuits
    laundry_va: zod_1.z.number(), // 1500W
    appliance_loads: zod_1.z.array(zod_1.z.object({
        name: zod_1.z.string(),
        va: zod_1.z.number(),
        demand_factor: zod_1.z.number().min(0).max(1)
    })),
    heating_cooling_va: zod_1.z.number(),
    largest_motor_va: zod_1.z.number(),
    other_loads_va: zod_1.z.number(),
    total_computed_va: zod_1.z.number(),
    demand_factor: zod_1.z.number().min(0).max(1),
    total_demand_va: zod_1.z.number(),
    required_amperage: zod_1.z.number(), // total_demand_va / voltage
    recommended_service: zod_1.z.number(), // Next standard size up
    nec_article_reference: zod_1.z.string(),
    created_at: zod_1.z.date()
});
// Voltage drop calculation
exports.VoltageDropSchema = zod_1.z.object({
    project_id: zod_1.z.string().uuid(),
    circuit_name: zod_1.z.string(),
    voltage: zod_1.z.number(),
    phase: zod_1.z.enum(['1PH', '3PH']),
    amperage: zod_1.z.number(),
    distance: zod_1.z.number(), // One-way distance in feet
    conductor_size: zod_1.z.string(), // AWG or kcmil
    conductor_type: zod_1.z.enum(['CU', 'AL']),
    conduit_type: zod_1.z.enum(['STEEL', 'PVC', 'AL']),
    power_factor: zod_1.z.number().min(0).max(1).default(0.9),
    ambient_temp_f: zod_1.z.number().default(86), // 30°C
    calculated_vd_volts: zod_1.z.number(),
    calculated_vd_percent: zod_1.z.number(),
    nec_limit_percent: zod_1.z.number(), // 3% branch, 5% total
    passes_nec: zod_1.z.boolean(),
    created_at: zod_1.z.date()
});
// Price history for materials
exports.MaterialPriceHistorySchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    catalog_number: zod_1.z.string(),
    supplier: zod_1.z.string(),
    unit_cost: zod_1.z.number().positive(),
    effective_date: zod_1.z.date(),
    created_at: zod_1.z.date()
});
// Agent message types
exports.AgentMessageSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    sender: zod_1.z.enum(['project_manager', 'coding_agent', 'ui_designer', 'frontend_agent',
        'backend_agent', 'research_agent', 'debugging_agent', 'memory_agent',
        'prompt_engineering_agent']),
    recipient: zod_1.z.enum(['project_manager', 'coding_agent', 'ui_designer', 'frontend_agent',
        'backend_agent', 'research_agent', 'debugging_agent', 'memory_agent',
        'prompt_engineering_agent', 'all']),
    priority: zod_1.z.enum(['critical', 'high', 'medium', 'low']),
    task_type: zod_1.z.enum(['feature_implementation', 'bug_fix', 'research', 'optimization',
        'testing', 'documentation', 'code_review']),
    message: zod_1.z.string(),
    context: zod_1.z.record(zod_1.z.unknown()).optional(),
    status: zod_1.z.enum(['pending', 'in_progress', 'completed', 'failed']),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
// Calculation log for audit trail
exports.CalculationLogSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    calculation_type: zod_1.z.enum(['LOAD_CALC', 'VOLTAGE_DROP', 'CONDUIT_FILL',
        'WIRE_SIZE', 'BREAKER_SIZE', 'GROUNDING']),
    input_data: zod_1.z.record(zod_1.z.unknown()),
    output_data: zod_1.z.record(zod_1.z.unknown()),
    nec_references: zod_1.z.array(zod_1.z.string()),
    performed_by: zod_1.z.string().uuid(),
    project_id: zod_1.z.string().uuid().optional(),
    created_at: zod_1.z.date()
});
// Panel and circuit types for panel schedules
exports.PanelSchema = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    project_id: zod_1.z.string().uuid(),
    name: zod_1.z.string().min(1).max(255),
    location: zod_1.z.string(),
    panel_type: zod_1.z.enum(['MAIN', 'SUB', 'DISTRIBUTION']),
    manufacturer: zod_1.z.string().optional(),
    model_number: zod_1.z.string().optional(),
    catalog_number: zod_1.z.string().optional(),
    voltage_system: zod_1.z.enum(['120/240V_1PH', '208V_3PH', '240V_3PH', '480V_3PH', '277/480V_3PH']),
    ampere_rating: zod_1.z.number().int().positive(),
    bus_rating: zod_1.z.number().int().positive(),
    main_breaker_size: zod_1.z.number().int().positive().optional(),
    phase_config: zod_1.z.enum(['SINGLE_PHASE', 'THREE_PHASE_3W', 'THREE_PHASE_4W']),
    mounting_type: zod_1.z.enum(['SURFACE', 'FLUSH', 'RECESSED']),
    enclosure_type: zod_1.z.enum(['NEMA_1', 'NEMA_3R', 'NEMA_4', 'NEMA_4X']),
    spaces_total: zod_1.z.number().int().positive(),
    spaces_used: zod_1.z.number().int().min(0).optional(),
    fed_from_panel_id: zod_1.z.string().uuid().optional(),
    fed_from_circuit: zod_1.z.number().int().positive().optional(),
    notes: zod_1.z.string().optional(),
    created_at: zod_1.z.date().optional(),
    updated_at: zod_1.z.date().optional()
});
exports.CircuitSchema = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    panel_id: zod_1.z.string().uuid(),
    circuit_number: zod_1.z.number().int().positive(),
    description: zod_1.z.string().min(1).max(255),
    breaker_size: zod_1.z.number().int().positive(),
    breaker_type: zod_1.z.enum(['STANDARD', 'GFCI', 'AFCI', 'GFCI_AFCI', 'SPACE_ONLY']),
    poles: zod_1.z.number().int().min(1).max(3),
    phase_connection: zod_1.z.string().optional(),
    wire_size: zod_1.z.string(),
    wire_type: zod_1.z.enum(['THHN', 'THWN-2', 'XHHW-2', 'NM', 'MC', 'USE-2', 'RHH', 'RHW-2']),
    wire_count: zod_1.z.number().int().positive(),
    conduit_type: zod_1.z.enum(['EMT', 'PVC', 'RIGID', 'MC_CABLE', 'NM_CABLE', 'FLEX']).optional(),
    conduit_size: zod_1.z.string().optional(),
    voltage: zod_1.z.number().int().positive(),
    load_type: zod_1.z.enum(['LIGHTING', 'RECEPTACLE', 'MOTOR', 'HVAC', 'APPLIANCE', 'FEEDER', 'EQUIPMENT']),
    continuous_load: zod_1.z.boolean().default(false),
    connected_load: zod_1.z.number().positive(),
    demand_factor: zod_1.z.number().min(0).max(1).default(1),
    calculated_load: zod_1.z.number().positive().optional(),
    room_area: zod_1.z.string().optional(),
    control_type: zod_1.z.enum(['SWITCH', 'TIMER', 'PHOTOCELL', 'OCCUPANCY', 'DIMMER', 'CONTACTOR']).optional(),
    is_spare: zod_1.z.boolean().default(false),
    is_space: zod_1.z.boolean().default(false),
    notes: zod_1.z.string().optional(),
    created_at: zod_1.z.date().optional(),
    updated_at: zod_1.z.date().optional()
});
exports.PanelLoadCalculationSchema = zod_1.z.object({
    id: zod_1.z.string().uuid().optional(),
    panel_id: zod_1.z.string().uuid(),
    calculation_date: zod_1.z.date(),
    phase_a_load: zod_1.z.number().min(0),
    phase_b_load: zod_1.z.number().min(0),
    phase_c_load: zod_1.z.number().min(0),
    neutral_load: zod_1.z.number().min(0),
    total_connected_load: zod_1.z.number().min(0),
    total_demand_load: zod_1.z.number().min(0),
    load_percentage: zod_1.z.number().min(0).max(100),
    phase_imbalance_percent: zod_1.z.number().min(0),
    power_factor: zod_1.z.number().min(0).max(1),
    ambient_temperature: zod_1.z.number(),
    derating_factor: zod_1.z.number().min(0).max(1),
    notes: zod_1.z.string().optional(),
    created_by: zod_1.z.string().uuid()
});
// User and authentication types
exports.UserSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    email: zod_1.z.string().email(),
    name: zod_1.z.string().min(1).max(255),
    role: zod_1.z.enum(['ADMIN', 'ELECTRICIAN', 'APPRENTICE', 'ESTIMATOR', 'VIEWER']),
    license_number: zod_1.z.string().optional(),
    license_state: zod_1.z.string().length(2).optional(),
    license_expiry: zod_1.z.date().optional(),
    phone: zod_1.z.string().optional(),
    is_active: zod_1.z.boolean().default(true),
    last_login: zod_1.z.date().optional(),
    created_at: zod_1.z.date(),
    updated_at: zod_1.z.date()
});
//# sourceMappingURL=index.js.map