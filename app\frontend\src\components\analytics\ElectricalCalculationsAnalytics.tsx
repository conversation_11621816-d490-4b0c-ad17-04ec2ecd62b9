import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  AreaChart,
  Area,
  ComposedChart,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import { format } from 'date-fns';
import { Zap, AlertTriangle, Shield, Activity, Calculator, TrendingUp } from 'lucide-react';

interface ElectricalCalculationsAnalyticsProps {
  dateRange: { start: Date; end: Date };
}

const ElectricalCalculationsAnalytics: React.FC<ElectricalCalculationsAnalyticsProps> = ({ dateRange }) => {
  const [selectedCalculationType, setSelectedCalculationType] = useState<string>('all');

  // Fetch calculation analytics
  const { data: calculationData } = useQuery({
    queryKey: ['calculation-analytics', dateRange],
    queryFn: () => analyticsService.getCalculationAnalytics(dateRange),
  });

  // Fetch load distribution analytics
  const { data: loadDistribution } = useQuery({
    queryKey: ['load-distribution', dateRange],
    queryFn: () => analyticsService.getLoadDistributionAnalytics(),
  });

  // Fetch arc flash analytics
  const { data: arcFlashData } = useQuery({
    queryKey: ['arc-flash-analytics', dateRange],
    queryFn: () => analyticsService.getArcFlashAnalytics(dateRange),
  });

  // Fetch safety margin analysis
  const { data: safetyMargins } = useQuery({
    queryKey: ['safety-margins', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { category: 'Wire Sizing', avgMargin: 25, minMargin: 15, recommended: 20 },
        { category: 'Conduit Fill', avgMargin: 18, minMargin: 10, recommended: 15 },
        { category: 'Voltage Drop', avgMargin: 2.8, minMargin: 1.5, recommended: 3 },
        { category: 'Panel Loading', avgMargin: 22, minMargin: 15, recommended: 20 },
        { category: 'Short Circuit', avgMargin: 35, minMargin: 25, recommended: 30 },
      ];
    },
  });

  // Fetch code compliance rates
  const { data: complianceData } = useQuery({
    queryKey: ['code-compliance', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { article: 'Art. 210 - Branch Circuits', complianceRate: 98.5, violations: 12 },
        { article: 'Art. 220 - Load Calculations', complianceRate: 96.8, violations: 18 },
        { article: 'Art. 250 - Grounding', complianceRate: 99.2, violations: 5 },
        { article: 'Art. 310 - Conductors', complianceRate: 97.5, violations: 15 },
        { article: 'Art. 430 - Motors', complianceRate: 95.2, violations: 28 },
      ];
    },
  });

  // Fetch common calculation patterns
  const { data: calculationPatterns } = useQuery({
    queryKey: ['calculation-patterns', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { hour: 0, residential: 12, commercial: 5, industrial: 2 },
        { hour: 6, residential: 15, commercial: 8, industrial: 5 },
        { hour: 8, residential: 25, commercial: 35, industrial: 20 },
        { hour: 10, residential: 30, commercial: 45, industrial: 28 },
        { hour: 12, residential: 22, commercial: 38, industrial: 25 },
        { hour: 14, residential: 28, commercial: 42, industrial: 30 },
        { hour: 16, residential: 35, commercial: 40, industrial: 22 },
        { hour: 18, residential: 20, commercial: 25, industrial: 10 },
        { hour: 20, residential: 18, commercial: 15, industrial: 5 },
        { hour: 22, residential: 10, commercial: 8, industrial: 3 },
      ];
    },
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const MetricCard = ({ title, value, subtitle, icon: Icon, color = 'blue' }: any) => {
    const colorClasses = {
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      green: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      red: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };

    return (
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Calculations"
          value={arcFlashData?.totalCalculations || 0}
          subtitle="This period"
          icon={Calculator}
          color="blue"
        />
        <MetricCard
          title="Avg Completion Time"
          value="2.3 min"
          subtitle="Per calculation"
          icon={Activity}
          color="green"
        />
        <MetricCard
          title="Code Compliance"
          value="97.4%"
          subtitle="NEC 2023"
          icon={Shield}
          color="yellow"
        />
        <MetricCard
          title="High Risk Findings"
          value={arcFlashData?.byPPECategory?.find(c => c.category === 4)?.count || 0}
          subtitle="PPE Cat 4"
          icon={AlertTriangle}
          color="red"
        />
      </div>

      {/* Common Calculation Types */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Calculation Types Distribution
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={calculationData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {calculationData?.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Load Distribution Patterns */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Load Distribution by Type
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={loadDistribution?.loadByType}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="loadType" />
              <YAxis tickFormatter={(value) => `${value}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Bar dataKey="percentage" fill="#3B82F6">
                {loadDistribution?.loadByType?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Card>

        {/* Safety Margin Analysis */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Safety Margin Analysis
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={safetyMargins}>
              <PolarGrid />
              <PolarAngleAxis dataKey="category" />
              <PolarRadiusAxis angle={90} domain={[0, 40]} />
              <Radar
                name="Average Margin"
                dataKey="avgMargin"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
              <Radar
                name="Recommended"
                dataKey="recommended"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.3}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Arc Flash Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Arc Flash PPE Category Distribution
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={arcFlashData?.byPPECategory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3B82F6" name="Count" />
            </BarChart>
          </ResponsiveContainer>
          <div className="space-y-4">
            {arcFlashData?.byPPECategory?.map((category) => (
              <div key={category.category} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded-full mr-3 ${
                    category.category === 1 ? 'bg-green-500' :
                    category.category === 2 ? 'bg-yellow-500' :
                    category.category === 3 ? 'bg-orange-500' :
                    'bg-red-500'
                  }`} />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    PPE Category {category.category}
                  </span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">
                    {category.count} locations
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Avg: {category.avgIncidentEnergy.toFixed(1)} cal/cm²
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Code Compliance Rates */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          NEC Code Compliance Rates
        </h3>
        <div className="space-y-3">
          {complianceData?.map((item) => (
            <div key={item.article} className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {item.article}
                </p>
                <div className="mt-1 flex items-center">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-4">
                    <div
                      className={`h-2 rounded-full ${
                        item.complianceRate >= 98 ? 'bg-green-600' :
                        item.complianceRate >= 95 ? 'bg-yellow-600' :
                        'bg-red-600'
                      }`}
                      style={{ width: `${item.complianceRate}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-16 text-right">
                    {item.complianceRate}%
                  </span>
                </div>
              </div>
              {item.violations > 0 && (
                <div className="ml-4 text-sm text-red-600 dark:text-red-400">
                  {item.violations} violations
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Calculation Usage Patterns */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Calculation Usage Patterns by Hour
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={calculationPatterns}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="hour" 
              tickFormatter={(hour) => `${hour}:00`}
            />
            <YAxis />
            <Tooltip 
              labelFormatter={(hour) => `${hour}:00`}
            />
            <Legend />
            <Area type="monotone" dataKey="residential" stackId="1" stroke="#3B82F6" fill="#3B82F6" name="Residential" />
            <Area type="monotone" dataKey="commercial" stackId="1" stroke="#10B981" fill="#10B981" name="Commercial" />
            <Area type="monotone" dataKey="industrial" stackId="1" stroke="#F59E0B" fill="#F59E0B" name="Industrial" />
          </AreaChart>
        </ResponsiveContainer>
      </Card>

      {/* Panel Load Visualization */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Panel Load Distribution
        </h3>
        <div className="space-y-3">
          {loadDistribution?.panelLoads?.slice(0, 5).map((panel) => (
            <div key={panel.panelId} className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {panel.panelName}
                </p>
                <div className="mt-1 flex items-center">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-3 mr-4">
                    <div
                      className={`h-3 rounded-full ${
                        panel.loadPercentage > 80 ? 'bg-red-600' :
                        panel.loadPercentage > 60 ? 'bg-yellow-600' :
                        'bg-green-600'
                      }`}
                      style={{ width: `${panel.loadPercentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-16 text-right">
                    {panel.loadPercentage}%
                  </span>
                </div>
              </div>
              <div className="ml-4 text-sm text-gray-500 dark:text-gray-400">
                Balance: {panel.phaseBalance.toFixed(1)}%
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default ElectricalCalculationsAnalytics;