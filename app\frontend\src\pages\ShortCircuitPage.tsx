import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ShortCircuitCalculator } from '../components/short-circuit/ShortCircuitCalculator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '../components/ui/alert';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  Zap, 
  AlertCircle, 
  Calculator, 
  FileText, 
  CheckCircle2,
  XCircle,
  ArrowLeft,
  Info,
  BookOpen
} from 'lucide-react';
import { shortCircuitService } from '../services/shortCircuitService';
import { projectService } from '../services/projectService';

export const ShortCircuitPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<any>(null);
  const [panelsRequiringAnalysis, setPanelsRequiringAnalysis] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (projectId) {
      loadProjectData();
    }
  }, [projectId]);

  const loadProjectData = async () => {
    try {
      setLoading(true);
      const [projectData, panels] = await Promise.all([
        projectService.getProject(projectId!),
        shortCircuitService.getPanelsRequiringAnalysis(projectId!)
      ]);
      
      setProject(projectData);
      setPanelsRequiringAnalysis(panels);
    } catch (err) {
      console.error('Error loading project data:', err);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading project data...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error || 'Project not found'}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(`/projects/${projectId}`)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Zap className="h-6 w-6" />
              Short Circuit Analysis
            </h1>
            <p className="text-muted-foreground">
              {project.name} - {project.address}, {project.city}, {project.state}
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="calculator" className="space-y-4">
        <TabsList>
          <TabsTrigger value="calculator">Calculator</TabsTrigger>
          <TabsTrigger value="panels">Panel Status</TabsTrigger>
          <TabsTrigger value="education">Education</TabsTrigger>
        </TabsList>

        <TabsContent value="calculator">
          <ShortCircuitCalculator projectId={projectId!} />
        </TabsContent>

        <TabsContent value="panels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Panel Analysis Status</CardTitle>
              <CardDescription>
                Overview of short circuit calculations for all panels in this project
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {panelsRequiringAnalysis.length === 0 ? (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No panels found in this project. Add panels to perform short circuit analysis.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid gap-4">
                    {panelsRequiringAnalysis.map((panel) => (
                      <div
                        key={panel.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex-1">
                          <h3 className="font-semibold">{panel.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {panel.location} - {panel.voltage_system} - {panel.ampere_rating}A
                          </p>
                          {panel.lastCalculationDate && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Last calculated: {new Date(panel.lastCalculationDate).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {panel.hasCalculation ? (
                            panel.calculationExpired ? (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Expired
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Current
                              </Badge>
                            )
                          ) : (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">
                              <XCircle className="h-3 w-3 mr-1" />
                              Required
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="education" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Understanding Short Circuit Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="prose dark:prose-invert max-w-none">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold">What is Short Circuit Analysis?</h3>
                  <p className="text-muted-foreground">
                    Short circuit analysis calculates the maximum current that can flow through an electrical 
                    system during a fault condition. This information is critical for:
                  </p>
                  <ul className="list-disc pl-6 space-y-1 text-muted-foreground">
                    <li>Selecting circuit breakers with adequate interrupting ratings (AIC)</li>
                    <li>Verifying bus bar bracing adequacy</li>
                    <li>Ensuring equipment can safely interrupt fault currents</li>
                    <li>Complying with NEC 110.9 and 110.10 requirements</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold">Point-to-Point Method</h3>
                  <p className="text-muted-foreground">
                    The point-to-point method calculates fault current by determining the total impedance 
                    from the source to the fault point, including:
                  </p>
                  <ul className="list-disc pl-6 space-y-1 text-muted-foreground">
                    <li><strong>Source impedance:</strong> Based on utility available fault current</li>
                    <li><strong>Transformer impedance:</strong> From nameplate %Z and X/R ratio</li>
                    <li><strong>Conductor impedance:</strong> Based on length, size, and material</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold">Key Formulas</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg font-mono text-sm space-y-2">
                    <p>Three-phase fault: I = V / (√3 × Z)</p>
                    <p>Total impedance: Z = √(R² + X²)</p>
                    <p>X/R ratio affects asymmetrical current</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold">Equipment Ratings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold">Standard AIC Ratings</h4>
                      <p className="text-sm text-muted-foreground">
                        10, 14, 18, 22, 25, 35, 42, 50, 65, 85, 100, 150, 200 kA
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold">Residential</h4>
                      <p className="text-sm text-muted-foreground">
                        Typically 10-22 kA is adequate
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold">Commercial/Industrial</h4>
                      <p className="text-sm text-muted-foreground">
                        Often requires 35-65 kA or higher
                      </p>
                    </div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Important Safety Note</AlertTitle>
                  <AlertDescription>
                    Always verify equipment ratings against calculated fault currents. Using equipment 
                    with inadequate interrupting ratings can result in catastrophic failure, fire, and 
                    serious injury.
                  </AlertDescription>
                </Alert>

                <div>
                  <h3 className="text-lg font-semibold">Series Ratings</h3>
                  <p className="text-muted-foreground">
                    When fault currents exceed branch breaker ratings, series-rated combinations per 
                    NEC 240.86 can be used. This requires:
                  </p>
                  <ul className="list-disc pl-6 space-y-1 text-muted-foreground">
                    <li>Tested and listed combination from manufacturer</li>
                    <li>Upstream device (main) with higher AIC rating</li>
                    <li>Proper coordination between devices</li>
                    <li>Clear labeling of series-rated equipment</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};