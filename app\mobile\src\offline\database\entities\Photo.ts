import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

@Entity('photos')
export class Photo extends BaseEntity {
  @Column()
  filename: string;

  @Column()
  localPath: string;

  @Column({ nullable: true })
  remotePath: string;

  @Column({ nullable: true })
  thumbnailPath: string;

  @Column({ type: 'integer' })
  size: number;

  @Column({ nullable: true })
  mimeType: string;

  @Column({ type: 'integer', nullable: true })
  width: number;

  @Column({ type: 'integer', nullable: true })
  height: number;

  @Column({ nullable: true })
  caption: string;

  @Column({ nullable: true })
  category: string; // 'panel', 'wiring', 'site', 'inspection', etc.

  @Column({ nullable: true })
  location: string;

  @Column({ type: 'text', nullable: true })
  tags: string; // JSON array of tags

  @Column({ nullable: true })
  takenAt: string;

  @Column({ nullable: true })
  takenBy: string;

  @Column({ type: 'boolean', default: false })
  isCompressed: boolean;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string (EXIF data, etc.)

  @Column()
  projectId: string;

  @ManyToOne(() => Project, project => project.photos)
  project: Project;
}