import { Router } from 'express';
import { z } from 'zod';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { ExportService } from '../services/export.service';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Export format schema
const exportFormatSchema = z.object({
  format: z.enum(['json', 'csv']).default('json')
});

// Export customers
router.get('/customers', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const { format } = exportFormatSchema.parse(req.query);
    await ExportService.streamCustomers(res, format);
  } catch (error) {
    next(error);
  }
});

// Export projects
router.get('/projects', authorize('admin', 'estimator', 'foreman'), async (req: AuthRequest, res, next) => {
  try {
    const querySchema = exportFormatSchema.extend({
      status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']).optional(),
      type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']).optional(),
      customer_id: z.string().uuid().optional()
    });
    
    const { format, ...filters } = querySchema.parse(req.query);
    await ExportService.streamProjects(res, format, filters);
  } catch (error) {
    next(error);
  }
});

// Export estimates
router.get('/estimates', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const querySchema = exportFormatSchema.extend({
      project_id: z.string().uuid().optional()
    });
    
    const { format, project_id } = querySchema.parse(req.query);
    await ExportService.streamEstimates(res, format, project_id);
  } catch (error) {
    next(error);
  }
});

// Export calculations
router.get('/calculations', async (req: AuthRequest, res, next) => {
  try {
    const querySchema = exportFormatSchema.extend({
      calculation_type: z.enum([
        'LOAD_CALC', 'VOLTAGE_DROP', 'CONDUIT_FILL', 
        'WIRE_SIZE', 'BREAKER_SIZE', 'GROUNDING'
      ]).optional(),
      project_id: z.string().uuid().optional(),
      performed_by: z.string().uuid().optional()
    });
    
    const { format, ...filters } = querySchema.parse(req.query);
    await ExportService.streamCalculations(res, format, filters);
  } catch (error) {
    next(error);
  }
});

// Export material prices
router.get('/material-prices', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const { format } = exportFormatSchema.parse(req.query);
    await ExportService.streamMaterialPrices(res, format);
  } catch (error) {
    next(error);
  }
});

// Export specific project data (comprehensive export)
router.get('/project/:id/full', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const projectId = req.params.id;
    
    // For full project export, we'll create a comprehensive JSON structure
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="project-${projectId}-full.json"`);
    
    // This is a special case where we might want to include everything
    // but still use streaming to handle large projects
    res.write('{"project":');
    
    // Stream project data with all relations
    // Implementation would fetch and stream each section
    
    res.end('}');
  } catch (error) {
    next(error);
  }
});

export { router as exportsRouter };