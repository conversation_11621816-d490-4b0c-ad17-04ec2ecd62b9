#!/bin/bash

# Electrical App Deployment Script
# Usage: ./deploy.sh [environment] [version]

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
NAMESPACE="electrical-${ENVIRONMENT}"
DOCKER_REGISTRY="ghcr.io/yourcompany/electrical"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Validate environment
validate_environment() {
    if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|production)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT"
        echo "Usage: $0 [dev|staging|production] [version]"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Create namespace if not exists
create_namespace() {
    log_info "Creating namespace $NAMESPACE if not exists..."
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
}

# Deploy secrets
deploy_secrets() {
    log_info "Deploying secrets..."
    
    # Check if secrets exist
    if kubectl get secret backend-secrets -n "$NAMESPACE" &> /dev/null; then
        log_warning "Secrets already exist, skipping creation"
    else
        log_info "Creating secrets from environment variables..."
        # In production, use a proper secret management tool
        kubectl create secret generic backend-secrets \
            --from-literal=database-url="${DATABASE_URL}" \
            --from-literal=redis-url="${REDIS_URL}" \
            --from-literal=jwt-secret="${JWT_SECRET}" \
            --from-literal=neo4j-uri="${NEO4J_URI}" \
            --from-literal=neo4j-user="${NEO4J_USER}" \
            --from-literal=neo4j-password="${NEO4J_PASSWORD}" \
            --from-literal=influxdb-token="${INFLUXDB_TOKEN}" \
            --from-literal=sentry-dsn="${SENTRY_DSN}" \
            -n "$NAMESPACE"
    fi
}

# Deploy with Helm
deploy_helm() {
    log_info "Deploying application with Helm..."
    
    # Update dependencies
    helm dependency update helm/electrical-app
    
    # Deploy or upgrade
    helm upgrade --install electrical-app helm/electrical-app \
        --namespace "$NAMESPACE" \
        --values "helm/electrical-app/values-${ENVIRONMENT}.yaml" \
        --set image.backend.tag="$VERSION" \
        --set image.frontend.tag="$VERSION" \
        --set image.agents.tag="$VERSION" \
        --wait \
        --timeout 10m
}

# Deploy with Kustomize
deploy_kustomize() {
    log_info "Deploying application with Kustomize..."
    
    # Build and apply
    kubectl apply -k "kubernetes/overlays/${ENVIRONMENT}"
    
    # Update image tags
    kubectl set image deployment/backend backend="${DOCKER_REGISTRY}/backend:${VERSION}" -n "$NAMESPACE"
    kubectl set image deployment/frontend frontend="${DOCKER_REGISTRY}/frontend:${VERSION}" -n "$NAMESPACE"
    kubectl set image deployment/agents agents="${DOCKER_REGISTRY}/agents:${VERSION}" -n "$NAMESPACE"
    
    # Wait for rollout
    log_info "Waiting for rollout to complete..."
    kubectl rollout status deployment/backend -n "$NAMESPACE"
    kubectl rollout status deployment/frontend -n "$NAMESPACE"
    kubectl rollout status deployment/agents -n "$NAMESPACE"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    kubectl run migrations-${VERSION} \
        --image="${DOCKER_REGISTRY}/backend:${VERSION}" \
        --namespace="$NAMESPACE" \
        --rm \
        --restart=Never \
        --env="DATABASE_URL=${DATABASE_URL}" \
        --command -- npx prisma migrate deploy
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check pod status
    kubectl get pods -n "$NAMESPACE"
    
    # Check services
    kubectl get services -n "$NAMESPACE"
    
    # Check ingress
    kubectl get ingress -n "$NAMESPACE"
    
    # Run smoke tests
    if [[ -f "scripts/smoke-tests.sh" ]]; then
        log_info "Running smoke tests..."
        ./scripts/smoke-tests.sh "$ENVIRONMENT"
    fi
}

# Main deployment flow
main() {
    log_info "Starting deployment to $ENVIRONMENT with version $VERSION"
    
    validate_environment
    check_prerequisites
    create_namespace
    deploy_secrets
    
    # Choose deployment method
    if [[ -f "helm/electrical-app/Chart.yaml" ]]; then
        deploy_helm
    else
        deploy_kustomize
    fi
    
    run_migrations
    verify_deployment
    
    log_info "Deployment completed successfully!"
}

# Run main function
main "$@"