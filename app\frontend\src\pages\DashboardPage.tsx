import { TrendingUp, Users, Briefcase, FileText, DollarSign } from 'lucide-react';

const stats = [
  { name: 'Total Customers', value: '124', icon: Users, change: '+12%', changeType: 'positive' },
  { name: 'Active Projects', value: '18', icon: Briefcase, change: '+5%', changeType: 'positive' },
  { name: 'Pending Estimates', value: '7', icon: FileText, change: '-2%', changeType: 'negative' },
  { name: 'Revenue This Month', value: '$48,352', icon: DollarSign, change: '+18%', changeType: 'positive' },
];

export function DashboardPage() {
  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Welcome back! Here's an overview of your business.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Projects</h3>
          </div>
          <div className="card-body">
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
                {[1, 2, 3, 4].map((i) => (
                  <li key={i} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          Kitchen Renovation - Smith Residence
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Updated 2 hours ago
                        </p>
                      </div>
                      <div>
                        <span className="badge badge-success">In Progress</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Calculations</h3>
          </div>
          <div className="card-body">
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
                {[1, 2, 3, 4].map((i) => (
                  <li key={i} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          Load Calculation - 200A Service
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          NEC 2023 Compliant
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">30 min ago</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}