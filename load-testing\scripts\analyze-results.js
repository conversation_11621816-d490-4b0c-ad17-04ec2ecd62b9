const fs = require('fs');
const path = require('path');
const { parse } = require('json2csv');
const chalk = require('chalk');
const { table } = require('table');

class LoadTestAnalyzer {
  constructor(resultsPath) {
    this.resultsPath = resultsPath;
    this.results = null;
    this.analysis = {
      summary: {},
      endpoints: {},
      errors: {},
      performance: {},
      recommendations: []
    };
  }

  async analyze(resultsFile) {
    console.log(chalk.cyan.bold('\\n=== LOAD TEST RESULTS ANALYSIS ===\\n'));
    
    try {
      // Load results
      this.results = this.loadResults(resultsFile);
      
      // Run analysis
      this.analyzeSummary();
      this.analyzeEndpoints();
      this.analyzeErrors();
      this.analyzePerformance();
      this.generateRecommendations();
      
      // Display results
      this.displayAnalysis();
      
      // Export reports
      await this.exportReports();
      
    } catch (error) {
      console.error(chalk.red('Error analyzing results:'), error);
    }
  }

  loadResults(filename) {
    const filePath = path.join(this.resultsPath, filename);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Results file not found: ${filePath}`);
    }
    
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  }

  analyzeSummary() {
    const aggregate = this.results.aggregate;
    
    this.analysis.summary = {
      testDuration: aggregate.lastMetric - aggregate.firstMetric,
      totalRequests: aggregate.counters['http.requests'],
      totalDataTransferred: aggregate.counters['http.downloaded_bytes'],
      scenariosCreated: aggregate.counters['vusers.created'],
      scenariosCompleted: aggregate.counters['vusers.completed'],
      scenariosFailed: aggregate.counters['vusers.failed'] || 0,
      
      // Response times
      responseTime: {
        min: aggregate.summaries['http.response_time'].min,
        max: aggregate.summaries['http.response_time'].max,
        median: aggregate.summaries['http.response_time'].median,
        p95: aggregate.summaries['http.response_time'].p95,
        p99: aggregate.summaries['http.response_time'].p99
      },
      
      // Request rate
      requestRate: {
        mean: aggregate.rates['http.request_rate'].mean,
        p95: aggregate.rates['http.request_rate'].p95
      },
      
      // Error rate
      errorRate: this.calculateErrorRate(aggregate),
      
      // Status codes
      statusCodes: this.extractStatusCodes(aggregate)
    };
  }

  analyzeEndpoints() {
    // Group metrics by endpoint
    const endpointMetrics = {};
    
    // Extract endpoint-specific metrics if available
    if (this.results.aggregate.customStats) {
      Object.entries(this.results.aggregate.customStats).forEach(([key, value]) => {
        if (key.includes('http.response_time.') && key.includes('/')) {
          const endpoint = key.replace('http.response_time.', '');
          if (!endpointMetrics[endpoint]) {
            endpointMetrics[endpoint] = {};
          }
          endpointMetrics[endpoint].responseTime = value;
        }
      });
    }
    
    // Analyze each endpoint
    Object.entries(endpointMetrics).forEach(([endpoint, metrics]) => {
      this.analysis.endpoints[endpoint] = {
        responseTime: {
          median: metrics.responseTime?.median || 0,
          p95: metrics.responseTime?.p95 || 0,
          p99: metrics.responseTime?.p99 || 0
        },
        requestCount: metrics.requestCount || 0,
        errorCount: metrics.errorCount || 0,
        errorRate: metrics.requestCount > 0 
          ? (metrics.errorCount / metrics.requestCount) * 100 
          : 0
      };
    });
  }

  analyzeErrors() {
    const errors = this.results.aggregate.errors || [];
    const errorsByType = {};
    const errorsByEndpoint = {};
    
    errors.forEach(error => {
      // Group by error type
      const errorType = error.code || error.message || 'Unknown';
      errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;
      
      // Group by endpoint
      if (error.url) {
        const endpoint = new URL(error.url).pathname;
        if (!errorsByEndpoint[endpoint]) {
          errorsByEndpoint[endpoint] = {};
        }
        errorsByEndpoint[endpoint][errorType] = 
          (errorsByEndpoint[endpoint][errorType] || 0) + 1;
      }
    });
    
    this.analysis.errors = {
      total: errors.length,
      byType: errorsByType,
      byEndpoint: errorsByEndpoint,
      topErrors: Object.entries(errorsByType)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([error, count]) => ({ error, count }))
    };
  }

  analyzePerformance() {
    const aggregate = this.results.aggregate;
    
    // Calculate percentiles
    const responseTimePercentiles = {
      p50: aggregate.summaries['http.response_time'].median,
      p90: aggregate.summaries['http.response_time'].p90 || 0,
      p95: aggregate.summaries['http.response_time'].p95,
      p99: aggregate.summaries['http.response_time'].p99
    };
    
    // Identify slow requests
    const slowRequests = [];
    if (this.results.intermediate && this.results.intermediate.length > 0) {
      const lastReport = this.results.intermediate[this.results.intermediate.length - 1];
      if (lastReport.slowRequests) {
        slowRequests.push(...lastReport.slowRequests);
      }
    }
    
    // Calculate throughput
    const duration = this.analysis.summary.testDuration / 1000; // seconds
    const throughput = {
      requestsPerSecond: this.analysis.summary.totalRequests / duration,
      dataPerSecond: this.analysis.summary.totalDataTransferred / duration
    };
    
    // Resource utilization (if available from monitoring)
    const resourceMetrics = this.extractResourceMetrics();
    
    this.analysis.performance = {
      responseTimePercentiles,
      slowRequests: slowRequests.slice(0, 10),
      throughput,
      concurrency: {
        peak: aggregate.counters['vusers.created_by_name.0'] || 0,
        average: this.calculateAverageConcurrency()
      },
      resources: resourceMetrics
    };
  }

  generateRecommendations() {
    const recommendations = [];
    const { summary, endpoints, errors, performance } = this.analysis;
    
    // Response time recommendations
    if (summary.responseTime.p95 > 1000) {
      recommendations.push({
        category: 'Performance',
        severity: 'High',
        issue: `95th percentile response time (${summary.responseTime.p95}ms) exceeds 1 second`,
        recommendation: 'Optimize slow endpoints, implement caching, or scale horizontally'
      });
    }
    
    if (summary.responseTime.p99 > 2000) {
      recommendations.push({
        category: 'Performance',
        severity: 'Medium',
        issue: `99th percentile response time (${summary.responseTime.p99}ms) exceeds 2 seconds`,
        recommendation: 'Investigate and optimize outlier requests'
      });
    }
    
    // Error rate recommendations
    if (summary.errorRate > 5) {
      recommendations.push({
        category: 'Reliability',
        severity: 'Critical',
        issue: `High error rate (${summary.errorRate.toFixed(2)}%)`,
        recommendation: 'Investigate error causes and improve error handling'
      });
    }
    
    // Endpoint-specific recommendations
    Object.entries(endpoints).forEach(([endpoint, metrics]) => {
      if (metrics.responseTime.p95 > 2000) {
        recommendations.push({
          category: 'Endpoint Performance',
          severity: 'High',
          issue: `Slow endpoint: ${endpoint} (p95: ${metrics.responseTime.p95}ms)`,
          recommendation: 'Optimize database queries, add indexes, or implement caching'
        });
      }
      
      if (metrics.errorRate > 10) {
        recommendations.push({
          category: 'Endpoint Reliability',
          severity: 'High',
          issue: `High error rate on ${endpoint} (${metrics.errorRate.toFixed(2)}%)`,
          recommendation: 'Review endpoint implementation and error handling'
        });
      }
    });
    
    // Resource recommendations
    if (performance.resources) {
      if (performance.resources.cpu?.max > 80) {
        recommendations.push({
          category: 'Resources',
          severity: 'High',
          issue: `High CPU usage (${performance.resources.cpu.max}%)`,
          recommendation: 'Optimize CPU-intensive operations or scale compute resources'
        });
      }
      
      if (performance.resources.memory?.max > 85) {
        recommendations.push({
          category: 'Resources',
          severity: 'High',
          issue: `High memory usage (${performance.resources.memory.max}%)`,
          recommendation: 'Investigate memory leaks or increase memory allocation'
        });
      }
    }
    
    // Database recommendations
    if (errors.byType['ECONNREFUSED'] > 0 || errors.byType['ER_CON_COUNT_ERROR'] > 0) {
      recommendations.push({
        category: 'Database',
        severity: 'Critical',
        issue: 'Database connection errors detected',
        recommendation: 'Increase connection pool size or implement connection pooling'
      });
    }
    
    // WebSocket recommendations
    if (errors.byType['WebSocket'] > 0) {
      recommendations.push({
        category: 'Real-time',
        severity: 'Medium',
        issue: 'WebSocket connection failures',
        recommendation: 'Review WebSocket implementation and connection limits'
      });
    }
    
    this.analysis.recommendations = recommendations;
  }

  displayAnalysis() {
    // Summary
    console.log(chalk.yellow.bold('Test Summary:'));
    const summaryData = [
      ['Metric', 'Value'],
      ['Duration', `${(this.analysis.summary.testDuration / 1000).toFixed(2)}s`],
      ['Total Requests', this.analysis.summary.totalRequests.toLocaleString()],
      ['Requests/Second', this.analysis.performance.throughput.requestsPerSecond.toFixed(2)],
      ['Data Transferred', this.formatBytes(this.analysis.summary.totalDataTransferred)],
      ['Error Rate', `${this.analysis.summary.errorRate.toFixed(2)}%`],
      ['Success Rate', `${(100 - this.analysis.summary.errorRate).toFixed(2)}%`]
    ];
    console.log(table(summaryData));
    
    // Response Times
    console.log(chalk.green.bold('\\nResponse Time Percentiles:'));
    const responseData = [
      ['Percentile', 'Time (ms)'],
      ['Min', this.analysis.summary.responseTime.min],
      ['Median (p50)', this.analysis.summary.responseTime.median],
      ['p95', this.analysis.summary.responseTime.p95],
      ['p99', this.analysis.summary.responseTime.p99],
      ['Max', this.analysis.summary.responseTime.max]
    ];
    console.log(table(responseData));
    
    // Status Codes
    console.log(chalk.blue.bold('\\nHTTP Status Codes:'));
    const statusData = [['Status', 'Count', 'Percentage']];
    Object.entries(this.analysis.summary.statusCodes).forEach(([status, count]) => {
      const percentage = (count / this.analysis.summary.totalRequests * 100).toFixed(2);
      statusData.push([status, count.toLocaleString(), `${percentage}%`]);
    });
    console.log(table(statusData));
    
    // Top Errors
    if (this.analysis.errors.topErrors.length > 0) {
      console.log(chalk.red.bold('\\nTop Errors:'));
      const errorData = [['Error', 'Count']];
      this.analysis.errors.topErrors.forEach(({ error, count }) => {
        errorData.push([error.substring(0, 50), count]);
      });
      console.log(table(errorData));
    }
    
    // Recommendations
    if (this.analysis.recommendations.length > 0) {
      console.log(chalk.magenta.bold('\\nRecommendations:'));
      const recommendationsByCategory = {};
      
      this.analysis.recommendations.forEach(rec => {
        if (!recommendationsByCategory[rec.category]) {
          recommendationsByCategory[rec.category] = [];
        }
        recommendationsByCategory[rec.category].push(rec);
      });
      
      Object.entries(recommendationsByCategory).forEach(([category, recs]) => {
        console.log(chalk.cyan(`\\n${category}:`));
        recs.forEach(rec => {
          const severityColor = 
            rec.severity === 'Critical' ? 'red' :
            rec.severity === 'High' ? 'yellow' :
            'white';
          
          console.log(chalk[severityColor](`  [${rec.severity}] ${rec.issue}`));
          console.log(chalk.gray(`    → ${rec.recommendation}`));
        });
      });
    }
  }

  async exportReports() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(this.resultsPath, 'reports');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    // Export JSON report
    const jsonReport = {
      timestamp: new Date().toISOString(),
      analysis: this.analysis,
      raw: this.results
    };
    
    const jsonPath = path.join(reportDir, `analysis-${timestamp}.json`);
    fs.writeFileSync(jsonPath, JSON.stringify(jsonReport, null, 2));
    
    // Export CSV summaries
    await this.exportCSVReports(reportDir, timestamp);
    
    console.log(chalk.green(`\\nReports exported to: ${reportDir}`));
  }

  async exportCSVReports(reportDir, timestamp) {
    // Response time percentiles CSV
    const percentileData = Object.entries(this.analysis.performance.responseTimePercentiles)
      .map(([percentile, value]) => ({ percentile, responseTime: value }));
    
    const percentileCsv = parse(percentileData);
    fs.writeFileSync(
      path.join(reportDir, `response-percentiles-${timestamp}.csv`),
      percentileCsv
    );
    
    // Endpoint performance CSV
    const endpointData = Object.entries(this.analysis.endpoints).map(([endpoint, metrics]) => ({
      endpoint,
      median: metrics.responseTime.median,
      p95: metrics.responseTime.p95,
      p99: metrics.responseTime.p99,
      errorRate: metrics.errorRate
    }));
    
    if (endpointData.length > 0) {
      const endpointCsv = parse(endpointData);
      fs.writeFileSync(
        path.join(reportDir, `endpoint-performance-${timestamp}.csv`),
        endpointCsv
      );
    }
    
    // Recommendations CSV
    if (this.analysis.recommendations.length > 0) {
      const recommendationsCsv = parse(this.analysis.recommendations);
      fs.writeFileSync(
        path.join(reportDir, `recommendations-${timestamp}.csv`),
        recommendationsCsv
      );
    }
  }

  // Helper methods
  calculateErrorRate(aggregate) {
    const totalRequests = aggregate.counters['http.requests'] || 0;
    const errorCount = 
      (aggregate.counters['http.codes.4xx'] || 0) + 
      (aggregate.counters['http.codes.5xx'] || 0) +
      (aggregate.counters['errors'] || 0);
    
    return totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0;
  }

  extractStatusCodes(aggregate) {
    const statusCodes = {};
    
    Object.entries(aggregate.counters).forEach(([key, value]) => {
      if (key.startsWith('http.codes.')) {
        const code = key.replace('http.codes.', '');
        statusCodes[code] = value;
      }
    });
    
    return statusCodes;
  }

  calculateAverageConcurrency() {
    if (!this.results.intermediate || this.results.intermediate.length === 0) {
      return 0;
    }
    
    const concurrencyValues = this.results.intermediate
      .map(report => report.scenariosCreated - report.scenariosCompleted)
      .filter(value => value >= 0);
    
    return concurrencyValues.length > 0
      ? concurrencyValues.reduce((a, b) => a + b, 0) / concurrencyValues.length
      : 0;
  }

  extractResourceMetrics() {
    // This would be populated from the resource monitoring data
    // For now, return placeholder data
    return {
      cpu: { avg: 0, max: 0 },
      memory: { avg: 0, max: 0 }
    };
  }

  formatBytes(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(chalk.yellow('Usage: node analyze-results.js <results-file>'));
    console.log(chalk.yellow('Example: node analyze-results.js artillery-report.json'));
    process.exit(1);
  }
  
  const analyzer = new LoadTestAnalyzer(path.join(__dirname, '../results'));
  analyzer.analyze(args[0]).catch(error => {
    console.error(chalk.red('Analysis failed:'), error);
    process.exit(1);
  });
}

module.exports = LoadTestAnalyzer;