import { LoadCalculationService } from '../load-calculation';
import { Decimal } from 'decimal.js';

describe('LoadCalculationService', () => {
  let service: LoadCalculationService;

  beforeEach(() => {
    service = new LoadCalculationService();
  });

  describe('Residential Load Calculations', () => {
    it('should calculate standard residential load correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [
          { name: 'Electric Range', va: 12000, quantity: 1 },
          { name: 'Dryer', va: 5000, quantity: 1 },
          { name: 'Water Heater', va: 4500, quantity: 1 },
          { name: 'Dishwasher', va: 1200, quantity: 1 }
        ],
        heating_va: 0,
        cooling_va: 3600,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // General lighting: 2000 sq ft × 3 VA/sq ft = 6000 VA
      expect(result.general_lighting_va).toBe(6000);
      
      // Small appliance circuits: 2 × 1500 VA = 3000 VA
      expect(result.small_appliance_va).toBe(3000);
      
      // Laundry circuit: 1500 VA
      expect(result.laundry_va).toBe(1500);
      
      // Demand factor applied to first 10,500 VA
      expect(result.demand_factor_applied).toBe(true);
      
      // Fixed appliances with 75% demand factor (4 appliances)
      const totalApplianceVa = 12000 + 5000 + 4500 + 1200;
      const expectedApplianceDemand = totalApplianceVa * 0.75;
      expect(result.appliance_loads.reduce((sum, app) => sum + app.demand_va, 0)).toBe(expectedApplianceDemand);
      
      // Cooling load
      expect(result.heating_cooling_va).toBe(3600);
      
      // NEC references should be included
      expect(result.necReferences).toContain('220.12');
      expect(result.necReferences).toContain('220.52');
      expect(result.necReferences).toContain('220.42');
      expect(result.necReferences).toContain('220.53');
      expect(result.necReferences).toContain('220.60');
      
      // Service size recommendations
      expect(result.recommended_service_240v).toBeGreaterThanOrEqual(100);
      expect(result.recommended_service_208v).toBeGreaterThanOrEqual(100);
    });

    it('should apply dwelling unit demand factors correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 5000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [],
        heating_va: 0,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // General loads: 5000×3 + 2×1500 + 1500 = 19,500 VA
      const generalLoads = 15000 + 3000 + 1500;
      
      // Demand factors: 
      // First 3000 VA at 100% = 3000 VA
      // Next 117,000 VA (3001-120,000) at 35% = (19,500-3000) × 0.35 = 5,775 VA
      // Total demand = 3000 + 5,775 = 8,775 VA
      const expectedDemand = 3000 + (generalLoads - 3000) * 0.35;
      
      expect(result.total_demand_va).toBeCloseTo(expectedDemand, 1);
    });

    it('should handle optional calculation method', async () => {
      const input = {
        calculation_type: 'OPTIONAL' as const,
        building_type: 'DWELLING' as const,
        square_footage: 1800,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [],
        heating_va: 5000,
        cooling_va: 4000,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // Optional method doesn't apply demand factors to general loads
      expect(result.demand_factor_applied).toBe(false);
      expect(result.demand_factor).toBe(1);
      
      // Heating vs cooling - larger load used
      expect(result.heating_cooling_va).toBe(5000);
    });
  });

  describe('Commercial Load Calculations', () => {
    it('should calculate office building load correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'OFFICE' as const,
        square_footage: 10000,
        small_appliance_circuits: 0,
        laundry_circuit: false,
        appliances: [],
        heating_va: 15000,
        cooling_va: 20000,
        largest_motor_va: 5000,
        other_loads_va: 2000
      };

      const result = await service.calculate(input);

      // General lighting: 10,000 sq ft × 3.5 VA/sq ft = 35,000 VA
      expect(result.general_lighting_va).toBe(35000);
      
      // No demand factor for non-dwelling
      expect(result.demand_factor_applied).toBe(false);
      
      // Cooling load (larger than heating)
      expect(result.heating_cooling_va).toBe(20000);
      
      // Motor load with 25% additional
      expect(result.largest_motor_va).toBe(5000);
      expect(result.motor_additional_va).toBe(1250);
      
      // Other loads
      expect(result.other_loads_va).toBe(2000);
      
      // Total: 35,000 + 20,000 + 5,000 + 1,250 + 2,000 = 63,250 VA
      expect(result.total_computed_va).toBe(63250);
    });

    it('should calculate warehouse load correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'WAREHOUSE' as const,
        square_footage: 50000,
        small_appliance_circuits: 0,
        laundry_circuit: false,
        appliances: [
          { name: 'Conveyor Motor', va: 10000, quantity: 2 },
          { name: 'Dock Lights', va: 2000, quantity: 5 }
        ],
        heating_va: 25000,
        cooling_va: 0,
        largest_motor_va: 10000,
        other_loads_va: 5000
      };

      const result = await service.calculate(input);

      // General lighting: 50,000 sq ft × 0.25 VA/sq ft = 12,500 VA
      expect(result.general_lighting_va).toBe(12500);
      
      // Fixed appliances (less than 4, no demand factor)
      const totalApplianceVa = (10000 * 2) + (2000 * 5);
      expect(result.appliance_loads.reduce((sum, app) => sum + app.demand_va, 0)).toBe(totalApplianceVa);
      
      // Heating load
      expect(result.heating_cooling_va).toBe(25000);
      
      // Motor calculations
      expect(result.motor_additional_va).toBe(2500); // 25% of 10,000 VA
    });

    it('should calculate restaurant load correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'RESTAURANT' as const,
        square_footage: 3000,
        small_appliance_circuits: 4,
        laundry_circuit: false,
        appliances: [
          { name: 'Grill', va: 8000, quantity: 1 },
          { name: 'Fryer', va: 6000, quantity: 2 },
          { name: 'Oven', va: 10000, quantity: 1 },
          { name: 'Refrigerator', va: 2000, quantity: 3 },
          { name: 'Dishwasher', va: 5000, quantity: 1 }
        ],
        heating_va: 15000,
        cooling_va: 20000,
        largest_motor_va: 3000,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // General lighting: 3,000 sq ft × 2 VA/sq ft = 6,000 VA
      expect(result.general_lighting_va).toBe(6000);
      
      // Small appliance circuits: 4 × 1500 VA = 6,000 VA
      expect(result.small_appliance_va).toBe(6000);
      
      // Fixed appliances with 75% demand factor (5 appliances)
      const totalApplianceVa = 8000 + (6000 * 2) + 10000 + (2000 * 3) + 5000;
      const expectedApplianceDemand = totalApplianceVa * 0.75;
      expect(result.appliance_loads.reduce((sum, app) => sum + app.demand_va, 0)).toBe(expectedApplianceDemand);
      
      // Each appliance should show 75% demand factor
      result.appliance_loads.forEach(appliance => {
        expect(appliance.demand_factor).toBe(0.75);
      });
    });
  });

  describe('Edge Cases and Validation', () => {
    it('should handle minimum values correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 1,
        small_appliance_circuits: 0,
        laundry_circuit: false,
        appliances: [],
        heating_va: 0,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      expect(result.general_lighting_va).toBe(3); // 1 sq ft × 3 VA/sq ft
      expect(result.total_computed_va).toBe(3);
      expect(result.recommended_service_240v).toBe(100); // Minimum service size
      expect(result.recommended_service_208v).toBe(100);
    });

    it('should handle maximum values correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'OFFICE' as const,
        square_footage: 1000000,
        small_appliance_circuits: 100,
        laundry_circuit: true,
        appliances: Array(20).fill(null).map((_, i) => ({
          name: `Equipment ${i + 1}`,
          va: 50000,
          quantity: 1
        })),
        heating_va: 500000,
        cooling_va: 600000,
        largest_motor_va: 100000,
        other_loads_va: 200000
      };

      const result = await service.calculate(input);

      // Very large load should recommend maximum service size
      expect(result.recommended_service_240v).toBe(3000); // Maximum in standard sizes
      expect(result.recommended_service_208v).toBe(3000);
      
      // 20 appliances should get 75% demand factor
      result.appliance_loads.forEach(appliance => {
        expect(appliance.demand_factor).toBe(0.75);
      });
    });

    it('should handle zero square footage', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 0,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [],
        heating_va: 5000,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 1000
      };

      const result = await service.calculate(input);

      expect(result.general_lighting_va).toBe(0);
      expect(result.small_appliance_va).toBe(3000);
      expect(result.laundry_va).toBe(1500);
      expect(result.total_computed_va).toBeGreaterThan(0);
    });

    it('should handle empty appliances array', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [],
        heating_va: 5000,
        cooling_va: 4000,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      expect(result.appliance_loads).toEqual([]);
      expect(result.necReferences).not.toContain('220.53'); // No appliance NEC reference
    });

    it('should handle undefined appliances', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        heating_va: 5000,
        cooling_va: 4000,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      expect(result.appliance_loads).toEqual([]);
    });
  });

  describe('NEC Compliance', () => {
    it('should apply correct demand factors for dwelling units', async () => {
      const testCases = [
        { generalLoads: 2500, expected: 2500 }, // Under 3000 VA - 100%
        { generalLoads: 10000, expected: 3000 + (7000 * 0.35) }, // First 3000 @ 100%, next @ 35%
        { generalLoads: 125000, expected: 3000 + (117000 * 0.35) + (5000 * 0.25) } // Over 120,000
      ];

      for (const testCase of testCases) {
        const squareFootage = Math.floor((testCase.generalLoads - 4500) / 3); // Accounting for small appliance and laundry
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: 'DWELLING' as const,
          square_footage: Math.max(1, squareFootage),
          small_appliance_circuits: 2,
          laundry_circuit: true,
          appliances: [],
          heating_va: 0,
          cooling_va: 0,
          largest_motor_va: 0,
          other_loads_va: 0
        };

        const result = await service.calculate(input);
        
        // Allow for small rounding differences
        expect(result.total_demand_va).toBeCloseTo(testCase.expected, 0);
      }
    });

    it('should apply 75% demand factor for 4 or more fixed appliances', async () => {
      const testCases = [
        { count: 3, expectedFactor: 1.0 },
        { count: 4, expectedFactor: 0.75 },
        { count: 10, expectedFactor: 0.75 }
      ];

      for (const testCase of testCases) {
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: 'DWELLING' as const,
          square_footage: 2000,
          small_appliance_circuits: 2,
          laundry_circuit: true,
          appliances: Array(testCase.count).fill(null).map((_, i) => ({
            name: `Appliance ${i + 1}`,
            va: 1000,
            quantity: 1
          })),
          heating_va: 0,
          cooling_va: 0,
          largest_motor_va: 0,
          other_loads_va: 0
        };

        const result = await service.calculate(input);
        
        result.appliance_loads.forEach(appliance => {
          expect(appliance.demand_factor).toBe(testCase.expectedFactor);
        });
      }
    });

    it('should select larger of heating or cooling load', async () => {
      const testCases = [
        { heating: 5000, cooling: 3000, expected: 5000 },
        { heating: 3000, cooling: 5000, expected: 5000 },
        { heating: 5000, cooling: 5000, expected: 5000 },
        { heating: 0, cooling: 3000, expected: 3000 },
        { heating: 3000, cooling: 0, expected: 3000 }
      ];

      for (const testCase of testCases) {
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: 'DWELLING' as const,
          square_footage: 2000,
          small_appliance_circuits: 2,
          laundry_circuit: true,
          appliances: [],
          heating_va: testCase.heating,
          cooling_va: testCase.cooling,
          largest_motor_va: 0,
          other_loads_va: 0
        };

        const result = await service.calculate(input);
        
        expect(result.heating_cooling_va).toBe(testCase.expected);
      }
    });

    it('should add 25% to largest motor load', async () => {
      const motorSizes = [1000, 5000, 10000, 50000];

      for (const motorSize of motorSizes) {
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: 'OFFICE' as const,
          square_footage: 5000,
          small_appliance_circuits: 0,
          laundry_circuit: false,
          appliances: [],
          heating_va: 0,
          cooling_va: 0,
          largest_motor_va: motorSize,
          other_loads_va: 0
        };

        const result = await service.calculate(input);
        
        expect(result.largest_motor_va).toBe(motorSize);
        expect(result.motor_additional_va).toBe(motorSize * 0.25);
      }
    });
  });

  describe('Decimal Precision', () => {
    it('should maintain precision for fractional values', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 1234.56,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [
          { name: 'Range', va: 12345.67, quantity: 1 }
        ],
        heating_va: 5678.9,
        cooling_va: 4321.1,
        largest_motor_va: 1111.11,
        other_loads_va: 999.99
      };

      const result = await service.calculate(input);

      // Check that decimal values are handled correctly
      expect(result.general_lighting_va).toBeCloseTo(1234.56 * 3, 2);
      expect(result.heating_cooling_va).toBeCloseTo(5678.9, 2);
      expect(result.motor_additional_va).toBeCloseTo(1111.11 * 0.25, 2);
      expect(result.other_loads_va).toBeCloseTo(999.99, 2);
    });

    it('should round amperage calculations appropriately', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2500,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [],
        heating_va: 5000,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // Amperage should be calculated and rounded to reasonable precision
      expect(result.required_amperage_240v).toBeGreaterThan(0);
      expect(result.required_amperage_208v).toBeGreaterThan(0);
      
      // 208V amperage should be higher than 240V
      expect(result.required_amperage_208v).toBeGreaterThan(result.required_amperage_240v);
    });
  });

  describe('Service Size Selection', () => {
    it('should select appropriate service sizes', async () => {
      const testCases = [
        { amperage: 50, expected: 100 },    // Minimum 100A service
        { amperage: 95, expected: 100 },
        { amperage: 101, expected: 125 },
        { amperage: 126, expected: 150 },
        { amperage: 195, expected: 200 },
        { amperage: 395, expected: 400 },
        { amperage: 2999, expected: 3000 },
        { amperage: 5000, expected: 3000 }  // Maximum available
      ];

      for (const testCase of testCases) {
        // Calculate VA needed for the desired amperage at 240V
        const requiredVA = testCase.amperage * 240;
        
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: 'OFFICE' as const,
          square_footage: 1,
          small_appliance_circuits: 0,
          laundry_circuit: false,
          appliances: [],
          heating_va: 0,
          cooling_va: 0,
          largest_motor_va: 0,
          other_loads_va: requiredVA - 3.5 // Account for general lighting
        };

        const result = await service.calculate(input);
        
        expect(result.recommended_service_240v).toBe(testCase.expected);
      }
    });
  });

  describe('Calculation Steps and References', () => {
    it('should include all calculation steps', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: [
          { name: 'Range', va: 12000, quantity: 1 },
          { name: 'Dryer', va: 5000, quantity: 1 },
          { name: 'Water Heater', va: 4500, quantity: 1 },
          { name: 'Dishwasher', va: 1200, quantity: 1 }
        ],
        heating_va: 5000,
        cooling_va: 3600,
        largest_motor_va: 1000,
        other_loads_va: 500
      };

      const result = await service.calculate(input);

      // Check that steps are included for each component
      const stepsText = result.calculation_steps.join(' ');
      
      expect(stepsText).toContain('General lighting');
      expect(stepsText).toContain('Small appliance circuits');
      expect(stepsText).toContain('Laundry circuit');
      expect(stepsText).toContain('Demand factor');
      expect(stepsText).toContain('Fixed appliances');
      expect(stepsText).toContain('Heating/Cooling');
      expect(stepsText).toContain('Largest motor');
      expect(stepsText).toContain('Total computed load');
      expect(stepsText).toContain('Required amperage');
      expect(stepsText).toContain('Recommended service');
    });

    it('should include unique NEC references', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'DWELLING' as const,
        square_footage: 2000,
        small_appliance_circuits: 2,
        laundry_circuit: true,
        appliances: Array(5).fill(null).map((_, i) => ({
          name: `Appliance ${i + 1}`,
          va: 1000,
          quantity: 1
        })),
        heating_va: 5000,
        cooling_va: 3600,
        largest_motor_va: 1000,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // Check for NEC references
      expect(result.necReferences).toContain('220.12');    // General lighting
      expect(result.necReferences).toContain('220.52');    // Small appliance
      expect(result.necReferences).toContain('220.52(B)'); // Laundry
      expect(result.necReferences).toContain('220.42');    // Demand factors
      expect(result.necReferences).toContain('220.53');    // Fixed appliances
      expect(result.necReferences).toContain('220.60');    // Heating/cooling
      expect(result.necReferences).toContain('220.50');    // Motors
      expect(result.necReferences).toContain('430.24');    // Motor additional
      expect(result.necReferences).toContain('230.42');    // Service size
      expect(result.necReferences).toContain('230.79');    // Service size
      
      // Should not have duplicates
      const uniqueRefs = [...new Set(result.necReferences)];
      expect(result.necReferences.length).toBe(uniqueRefs.length);
    });
  });

  describe('Building Type Variations', () => {
    const buildingTypes = [
      { type: 'DWELLING' as const, vaPerSqFt: 3 },
      { type: 'OFFICE' as const, vaPerSqFt: 3.5 },
      { type: 'STORE' as const, vaPerSqFt: 3 },
      { type: 'WAREHOUSE' as const, vaPerSqFt: 0.25 },
      { type: 'GARAGE' as const, vaPerSqFt: 0.5 },
      { type: 'HOSPITAL' as const, vaPerSqFt: 2 },
      { type: 'HOTEL' as const, vaPerSqFt: 2 },
      { type: 'SCHOOL' as const, vaPerSqFt: 3 },
      { type: 'RESTAURANT' as const, vaPerSqFt: 2 }
    ];

    buildingTypes.forEach(({ type, vaPerSqFt }) => {
      it(`should calculate ${type} building type correctly`, async () => {
        const input = {
          calculation_type: 'STANDARD' as const,
          building_type: type,
          square_footage: 1000,
          small_appliance_circuits: 0,
          laundry_circuit: false,
          appliances: [],
          heating_va: 0,
          cooling_va: 0,
          largest_motor_va: 0,
          other_loads_va: 0
        };

        const result = await service.calculate(input);

        expect(result.general_lighting_va).toBe(1000 * vaPerSqFt);
      });
    });
  });

  describe('Multiple Appliances with Quantities', () => {
    it('should handle appliances with quantities correctly', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'RESTAURANT' as const,
        square_footage: 3000,
        small_appliance_circuits: 0,
        laundry_circuit: false,
        appliances: [
          { name: 'Refrigerator', va: 1200, quantity: 3 },
          { name: 'Microwave', va: 1000, quantity: 2 },
          { name: 'Coffee Maker', va: 900, quantity: 4 }
        ],
        heating_va: 0,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // Check individual appliance calculations
      const fridge = result.appliance_loads.find(a => a.name === 'Refrigerator');
      expect(fridge?.va).toBe(3600); // 1200 × 3

      const microwave = result.appliance_loads.find(a => a.name === 'Microwave');
      expect(microwave?.va).toBe(2000); // 1000 × 2

      const coffee = result.appliance_loads.find(a => a.name === 'Coffee Maker');
      expect(coffee?.va).toBe(3600); // 900 × 4

      // Total VA before demand factor
      const totalApplianceVa = 3600 + 2000 + 3600;
      
      // Less than 4 appliances, no demand factor
      expect(result.appliance_loads[0].demand_factor).toBe(1.0);
    });

    it('should count appliances not quantities for demand factor', async () => {
      const input = {
        calculation_type: 'STANDARD' as const,
        building_type: 'RESTAURANT' as const,
        square_footage: 3000,
        small_appliance_circuits: 0,
        laundry_circuit: false,
        appliances: [
          { name: 'Refrigerator', va: 1200, quantity: 5 }, // This counts as 1 appliance type
          { name: 'Microwave', va: 1000, quantity: 3 },
          { name: 'Coffee Maker', va: 900, quantity: 2 },
          { name: 'Toaster', va: 800, quantity: 2 }
        ],
        heating_va: 0,
        cooling_va: 0,
        largest_motor_va: 0,
        other_loads_va: 0
      };

      const result = await service.calculate(input);

      // 4 appliance types, should get 75% demand factor
      result.appliance_loads.forEach(appliance => {
        expect(appliance.demand_factor).toBe(0.75);
      });
    });
  });
});