import React, { useEffect, useRef } from 'react';
import { X, Download, Printer, ExternalLink } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { FocusTrap, announce } from '../../utils/accessibility';

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  inspection: {
    id: string;
    qr_code_id: string;
    inspection_type: string;
    project?: {
      name: string;
      address: string;
    };
  };
}

export const QRCodeModalAccessible: React.FC<QRCodeModalProps> = ({ isOpen, onClose, inspection }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const focusTrapRef = useRef<FocusTrap | null>(null);
  const mobileUrl = `${window.location.origin}/inspection/mobile/${inspection.qr_code_id}`;

  // Use Google Charts QR code API
  const qrCodeUrl = `https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${encodeURIComponent(mobileUrl)}`;

  useEffect(() => {
    if (isOpen && modalRef.current) {
      focusTrapRef.current = new FocusTrap(modalRef.current);
      focusTrapRef.current.activate();
      announce('QR code modal opened');
    }

    return () => {
      if (focusTrapRef.current) {
        focusTrapRef.current.deactivate();
      }
    };
  }, [isOpen]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.download = `inspection-qr-${inspection.id}.png`;
    link.href = qrCodeUrl;
    link.click();
    announce('QR code download started');
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      announce('Unable to open print dialog', 'assertive');
      return;
    }
    
    printWindow.document.write(`
      <html>
        <head>
          <title>Inspection QR Code</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              padding: 20px;
            }
            h1, h2 {
              margin: 10px 0;
            }
            .qr-container {
              border: 2px solid #000;
              padding: 20px;
              margin: 20px 0;
            }
            .info {
              text-align: center;
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="info">
            <h1>${inspection.inspection_type.replace(/_/g, ' ')} Inspection</h1>
            ${inspection.project ? `
              <h2>${inspection.project.name}</h2>
              <p>${inspection.project.address}</p>
            ` : ''}
            <p>Scan this QR code to access the inspection checklist</p>
          </div>
          <div class="qr-container">
            <img src="${qrCodeUrl}" alt="QR Code for inspection ${inspection.id}" />
          </div>
          <p style="margin-top: 20px; font-size: 12px;">
            Inspection ID: ${inspection.id}<br>
            Generated: ${new Date().toLocaleString()}
          </p>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
    announce('Print dialog opened');
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="qr-modal-title"
      onKeyDown={handleKeyDown}
    >
      <Card 
        ref={modalRef}
        className="bg-white p-6 max-w-md w-full mx-4"
        role="document"
      >
        <div className="flex justify-between items-center mb-4">
          <h3 id="qr-modal-title" className="text-lg font-semibold">
            Inspection QR Code
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Close QR code modal"
          >
            <X className="h-5 w-5" aria-hidden="true" />
          </button>
        </div>

        <div className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            Scan this QR code to access the mobile inspection checklist
          </p>

          <div className="flex justify-center">
            <img
              src={qrCodeUrl}
              alt={`QR code for ${inspection.inspection_type.replace(/_/g, ' ')} inspection. Scan to access mobile checklist.`}
              className="border-2 border-gray-300 rounded-lg"
              width={300}
              height={300}
            />
          </div>

          <div className="flex gap-2 justify-center" role="group" aria-label="QR code actions">
            <Button 
              onClick={handleDownload} 
              variant="secondary" 
              size="sm"
              aria-label="Download QR code image"
            >
              <Download className="h-4 w-4 mr-2" aria-hidden="true" />
              Download
            </Button>
            <Button 
              onClick={handlePrint} 
              variant="secondary" 
              size="sm"
              aria-label="Print QR code"
            >
              <Printer className="h-4 w-4 mr-2" aria-hidden="true" />
              Print
            </Button>
          </div>

          <div className="space-y-2">
            <p className="text-xs text-gray-500">
              <span className="font-medium">Mobile URL:</span>
              <br />
              <span className="break-all">{mobileUrl}</span>
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                window.open(mobileUrl, '_blank');
                announce('Mobile inspection view opened in new tab');
              }}
              className="text-xs"
              aria-label="Open mobile inspection view in new tab"
            >
              <ExternalLink className="h-3 w-3 mr-1" aria-hidden="true" />
              Open in new tab
            </Button>
          </div>
        </div>

        {/* Hidden live region for screen reader announcements */}
        <div 
          role="status" 
          aria-live="polite" 
          aria-atomic="true" 
          className="sr-only"
        >
          QR code modal is open. Press Escape to close.
        </div>
      </Card>
    </div>
  );
};