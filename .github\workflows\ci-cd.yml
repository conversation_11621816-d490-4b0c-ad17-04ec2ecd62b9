name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  release:
    types: [created]

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '20'

jobs:
  # Code Quality Checks
  lint-and-test:
    name: Lint and <PERSON>
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [backend, frontend, agents, mobile]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT
      
      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run lint
        run: |
          cd app/${{ matrix.app }}
          pnpm lint
      
      - name: Run type check
        run: |
          cd app/${{ matrix.app }}
          pnpm typecheck
      
      - name: Run tests
        run: |
          cd app/${{ matrix.app }}
          pnpm test --passWithNoTests
      
      - name: Upload test coverage
        if: matrix.app != 'mobile'
        uses: codecov/codecov-action@v3
        with:
          file: ./app/${{ matrix.app }}/coverage/lcov.info
          flags: ${{ matrix.app }}

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: Run OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.9.0
        with:
          target: 'https://localhost:3000'
          allow_issue_writing: false
          
      - name: Run npm audit
        run: |
          pnpm audit --audit-level=moderate || true

  # Build Docker Images
  build-docker:
    name: Build Docker Images
    needs: [lint-and-test]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'release'
    strategy:
      matrix:
        service: [backend, frontend, agents]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.${{ matrix.service }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ github.sha }}
            BUILD_DATE=${{ github.event.head_commit.timestamp }}

  # Database Migrations
  migrate-database:
    name: Run Database Migrations
    needs: [build-docker]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Install dependencies
        run: |
          cd app/backend
          pnpm install
      
      - name: Run migrations (Staging)
        if: github.ref == 'refs/heads/develop'
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
        run: |
          cd app/backend
          npx prisma migrate deploy
      
      - name: Run migrations (Production)
        if: github.ref == 'refs/heads/main'
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
        run: |
          cd app/backend
          npx prisma migrate deploy

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    needs: [build-docker, migrate-database]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name electrical-staging --region us-east-1
      
      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k kubernetes/overlays/staging
          kubectl set image deployment/backend backend=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/backend:develop-${{ github.sha }} -n electrical-staging
          kubectl set image deployment/frontend frontend=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:develop-${{ github.sha }} -n electrical-staging
          kubectl set image deployment/agents agents=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/agents:develop-${{ github.sha }} -n electrical-staging
          kubectl rollout status deployment/backend -n electrical-staging
          kubectl rollout status deployment/frontend -n electrical-staging
          kubectl rollout status deployment/agents -n electrical-staging

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    needs: [build-docker, migrate-database]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name electrical-production --region us-east-1
      
      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k kubernetes/overlays/prod
          kubectl set image deployment/backend backend=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/backend:main-${{ github.sha }} -n electrical-production
          kubectl set image deployment/frontend frontend=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:main-${{ github.sha }} -n electrical-production
          kubectl set image deployment/agents agents=${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}/agents:main-${{ github.sha }} -n electrical-production
          kubectl rollout status deployment/backend -n electrical-production
          kubectl rollout status deployment/frontend -n electrical-production
          kubectl rollout status deployment/agents -n electrical-production
      
      - name: Run smoke tests
        run: |
          ./scripts/smoke-tests.sh production

  # Mobile App Build
  build-mobile:
    name: Build Mobile Apps
    needs: [lint-and-test]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    strategy:
      matrix:
        platform: [ios, android]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Install dependencies
        run: |
          cd app/mobile
          npm install
      
      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - name: Build Android
        if: matrix.platform == 'android'
        run: |
          cd app/mobile
          eas build --platform android --non-interactive --profile production
      
      - name: Build iOS
        if: matrix.platform == 'ios'
        run: |
          cd app/mobile
          eas build --platform ios --non-interactive --profile production

  # Create Release
  create-release:
    name: Create Release
    needs: [deploy-production, build-mobile]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Generate changelog
        id: changelog
        uses: mikepenz/release-changelog-builder-action@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Create Release Notes
        uses: softprops/action-gh-release@v1
        with:
          body: ${{ steps.changelog.outputs.changelog }}
          files: |
            ./dist/*
            ./app/mobile/build/*