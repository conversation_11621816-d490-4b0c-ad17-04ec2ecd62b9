import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
      manifest: {
        name: 'Electrical Contractor Pro',
        short_name: 'ElectricPro',
        description: 'Professional electrical contracting application',
        theme_color: '#1f2937',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'icon-192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icon-512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff,woff2}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api\./,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 300 // 5 minutes
              },
              cacheableResponse: {
                statuses: [0, 200]
              }
            }
          },
          // Cache calculation results
          {
            urlPattern: /\/api\/calculations\//,
            handler: 'CacheFirst',
            options: {
              cacheName: 'calculations-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 86400 // 24 hours
              }
            }
          },
          // Cache material prices
          {
            urlPattern: /\/api\/materials\/prices/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'material-prices-cache',
              expiration: {
                maxEntries: 200,
                maxAgeSeconds: 300 // 5 minutes
              }
            }
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@electrical/shared': path.resolve(__dirname, '../shared/src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      },
      '/socket.io': {
        target: 'http://localhost:3001',
        ws: true
      }
    }
  },
  build: {
    target: 'es2020',
    sourcemap: true,
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Improved manual chunks for better caching
        manualChunks: (id) => {
          // Core React dependencies
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react-vendor';
            }
            
            // UI libraries
            if (id.includes('@headlessui') || id.includes('clsx') || id.includes('tailwind-merge')) {
              return 'ui-vendor';
            }
            
            // State management
            if (id.includes('zustand') || id.includes('@tanstack/react-query')) {
              return 'state-vendor';
            }
            
            // Form handling
            if (id.includes('react-hook-form') || id.includes('@hookform')) {
              return 'form-vendor';
            }
            
            // Utilities
            if (id.includes('decimal.js') || id.includes('date-fns') || id.includes('zod')) {
              return 'utils-vendor';
            }
            
            // Charts and visualizations
            if (id.includes('recharts') || id.includes('d3')) {
              return 'charts-vendor';
            }
            
            // Virtual scrolling
            if (id.includes('@tanstack/react-virtual')) {
              return 'virtual-vendor';
            }
            
            // Icons
            if (id.includes('lucide-react') || id.includes('@heroicons')) {
              return 'icons-vendor';
            }
          }
          
          // Split by feature/page
          if (id.includes('src/pages/')) {
            if (id.includes('calculations')) return 'calculations-feature';
            if (id.includes('panels')) return 'panels-feature';
            if (id.includes('estimates')) return 'estimates-feature';
            if (id.includes('materials')) return 'materials-feature';
            if (id.includes('inspections')) return 'inspections-feature';
            if (id.includes('permits')) return 'permits-feature';
          }
          
          // Shared components
          if (id.includes('src/components/ui/')) return 'ui-components';
          if (id.includes('src/components/common/')) return 'common-components';
        },
        // Use content hash for better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `js/[name]-${facadeModuleId}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          } else if (/woff2?|ttf|otf|eot/i.test(ext)) {
            return `fonts/[name]-[hash][extname]`;
          } else if (ext === 'css') {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    // CSS code splitting
    cssCodeSplit: true,
    // Minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    }
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'zustand',
      'axios',
      'date-fns',
      'decimal.js'
    ],
    exclude: ['@electrical/shared']
  }
});