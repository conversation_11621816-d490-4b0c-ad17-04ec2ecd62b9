import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Layers, Plus, Trash2, AlertCircle, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';

const conductorSchema = z.object({
  type: z.enum(['THHN', 'XHHW', 'RHW', 'USE', 'BARE']),
  size: z.string(),
  count: z.number().int().positive(),
});

const conduitFillSchema = z.object({
  project_id: z.string().optional(),
  conduit_type: z.enum(['EMT', 'PVC', 'RIGID_METAL', 'FLEXIBLE_METAL', 'LIQUIDTIGHT']),
  conduit_size: z.enum(['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '3-1/2', '4', '5', '6']),
  conductors: z.array(conductorSchema).min(1),
});

type ConduitFillFormData = z.infer<typeof conduitFillSchema>;

const WIRE_SIZES = [
  '14', '12', '10', '8', '6', '4', '3', '2', '1', '1/0', '2/0', '3/0', '4/0',
  '250', '300', '350', '400', '500', '600', '700', '750', '800', '900', '1000'
];

export function ConduitFillCalculator() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ConduitFillFormData>({
    resolver: zodResolver(conduitFillSchema),
    defaultValues: {
      conduit_type: 'EMT',
      conduit_size: '3/4',
      conductors: [{ type: 'THHN', size: '12', count: 3 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'conductors',
  });

  const onSubmit = async (data: ConduitFillFormData) => {
    setIsCalculating(true);
    try {
      const response = await api.post('/calculations/conduit-fill', data);
      setResult(response.data);
      toast.success('Conduit fill calculation completed');
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Layers className="mr-2 h-5 w-5" />
          Conduit Fill Calculation (NEC Chapter 9, Table 1)
        </h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conduit Type
              </label>
              <select
                {...register('conduit_type')}
                className="mt-1 input"
              >
                <option value="EMT">EMT (Electrical Metallic Tubing)</option>
                <option value="PVC">PVC (Schedule 40 & 80)</option>
                <option value="RIGID_METAL">Rigid Metal Conduit</option>
                <option value="FLEXIBLE_METAL">Flexible Metal Conduit</option>
                <option value="LIQUIDTIGHT">Liquidtight Flexible</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conduit Size
              </label>
              <select
                {...register('conduit_size')}
                className="mt-1 input"
              >
                <option value="1/2">1/2"</option>
                <option value="3/4">3/4"</option>
                <option value="1">1"</option>
                <option value="1-1/4">1-1/4"</option>
                <option value="1-1/2">1-1/2"</option>
                <option value="2">2"</option>
                <option value="2-1/2">2-1/2"</option>
                <option value="3">3"</option>
                <option value="3-1/2">3-1/2"</option>
                <option value="4">4"</option>
                <option value="5">5"</option>
                <option value="6">6"</option>
              </select>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conductors
              </label>
              <button
                type="button"
                onClick={() => append({ type: 'THHN', size: '12', count: 1 })}
                className="btn-secondary text-sm"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Conductor
              </button>
            </div>

            <div className="space-y-3">
              {fields.map((field, index) => (
                <div key={field.id} className="flex gap-3 items-start">
                  <div className="flex-1">
                    <select
                      {...register(`conductors.${index}.type`)}
                      className="input"
                    >
                      <option value="THHN">THHN/THWN</option>
                      <option value="XHHW">XHHW</option>
                      <option value="RHW">RHW/RHW-2</option>
                      <option value="USE">USE/USE-2</option>
                      <option value="BARE">Bare Copper</option>
                    </select>
                  </div>
                  <div className="flex-1">
                    <select
                      {...register(`conductors.${index}.size`)}
                      className="input"
                    >
                      {WIRE_SIZES.map((size) => (
                        <option key={size} value={size}>
                          {size} AWG{size.includes('/') || parseInt(size) > 4 ? '' : '/kcmil'}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="w-24">
                    <input
                      type="number"
                      {...register(`conductors.${index}.count`, { valueAsNumber: true })}
                      className="input"
                      placeholder="Qty"
                      min="1"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="btn-secondary p-2"
                    disabled={fields.length === 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
              {errors.conductors && (
                <p className="text-sm text-red-600">Please add at least one conductor</p>
              )}
            </div>
          </div>

          <button
            type="submit"
            disabled={isCalculating}
            className="btn-primary w-full sm:w-auto"
          >
            {isCalculating ? (
              <>
                <div className="spinner mr-2" />
                Calculating...
              </>
            ) : (
              <>
                <Layers className="mr-2 h-4 w-4" />
                Calculate Conduit Fill
              </>
            )}
          </button>
        </form>

        {result && (
          <div className="mt-6 space-y-4">
            <div className={`${result.fillPercentage > 40 ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' : result.fillPercentage > 31 ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800' : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'} border rounded-md p-4`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className={`h-5 w-5 ${result.fillPercentage > 40 ? 'text-red-400' : result.fillPercentage > 31 ? 'text-yellow-400' : 'text-green-400'}`} />
                </div>
                <div className="ml-3">
                  <h3 className={`text-sm font-medium ${result.fillPercentage > 40 ? 'text-red-800 dark:text-red-200' : result.fillPercentage > 31 ? 'text-yellow-800 dark:text-yellow-200' : 'text-green-800 dark:text-green-200'}`}>
                    Conduit Fill Analysis
                  </h3>
                  <div className={`mt-2 text-sm ${result.fillPercentage > 40 ? 'text-red-700 dark:text-red-300' : result.fillPercentage > 31 ? 'text-yellow-700 dark:text-yellow-300' : 'text-green-700 dark:text-green-300'}`}>
                    <dl className="space-y-1">
                      <div className="flex justify-between">
                        <dt>Fill Percentage:</dt>
                        <dd className="font-bold text-lg">{result.fillPercentage.toFixed(1)}%</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Maximum Allowed:</dt>
                        <dd className="font-medium">{result.maxFillPercentage}%</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Status:</dt>
                        <dd className="font-medium">
                          {result.fillPercentage <= result.maxFillPercentage ? '✓ Compliant' : '✗ Exceeds Limit'}
                        </dd>
                      </div>
                    </dl>
                  </div>
                  {result.fillPercentage > result.maxFillPercentage && (
                    <p className="mt-2 text-sm font-medium text-red-800 dark:text-red-200">
                      ⚠️ Conduit fill exceeds NEC limits
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Fill Details
                </h4>
              </div>
              <div className="p-4">
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt>Conduit Area:</dt>
                    <dd>{result.conduitArea.toFixed(3)} sq in</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Total Conductor Area:</dt>
                    <dd>{result.totalConductorArea.toFixed(3)} sq in</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Available Area:</dt>
                    <dd>{result.availableArea.toFixed(3)} sq in</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Remaining Area:</dt>
                    <dd className={result.remainingArea < 0 ? 'text-red-600 dark:text-red-400' : ''}>
                      {result.remainingArea.toFixed(3)} sq in
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Conductor Breakdown
                </h4>
              </div>
              <div className="p-4">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead>
                    <tr>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Size
                      </th>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Qty
                      </th>
                      <th className="text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Area (sq in)
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {result.conductors.map((conductor: any, index: number) => (
                      <tr key={index}>
                        <td className="py-2 text-sm text-gray-900 dark:text-gray-300">
                          {conductor.type}
                        </td>
                        <td className="py-2 text-sm text-gray-900 dark:text-gray-300">
                          {conductor.size}
                        </td>
                        <td className="py-2 text-sm text-gray-900 dark:text-gray-300">
                          {conductor.count}
                        </td>
                        <td className="py-2 text-sm text-gray-900 dark:text-gray-300 text-right">
                          {(conductor.area * conductor.count).toFixed(4)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {result.recommendations && result.recommendations.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Recommendations
                </h4>
                <ul className="list-disc list-inside text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  {result.recommendations.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-md p-4">
              <div className="flex">
                <FileText className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    NEC References
                  </h4>
                  <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                    {result.necReferences.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}