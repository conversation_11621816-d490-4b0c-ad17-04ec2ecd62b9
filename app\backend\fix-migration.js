const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Attempting to fix migration issue...\n');

// Function to run command and handle errors
function runCommand(command, description) {
    console.log(`\n--- ${description} ---`);
    console.log(`Running: ${command}`);
    try {
        const output = execSync(command, { 
            encoding: 'utf-8',
            cwd: __dirname,
            stdio: 'inherit'
        });
        console.log('✓ Success');
        return true;
    } catch (error) {
        console.log('✗ Failed:', error.message);
        return false;
    }
}

// Step 1: Generate Prisma Client
runCommand('npx prisma generate', 'Generating Prisma Client');

// Step 2: Try to apply the migration with deploy (safer for production)
console.log('\nAttempting to apply migrations...');
if (!runCommand('npx prisma migrate deploy', 'Applying migrations with deploy')) {
    console.log('\nMigration deploy failed. Let\'s try to resolve it...');
    
    // Step 3: If deploy fails, try to mark the migration as applied
    console.log('\nTrying to resolve the migration manually...');
    
    // Option 1: Reset and reapply (only in development!)
    console.log('\n⚠️  WARNING: The following commands will reset your database!');
    console.log('Only proceed if this is a development environment and you have backups.\n');
    
    console.log('Option 1: Reset database and reapply all migrations');
    console.log('  npx prisma migrate reset --force');
    
    console.log('\nOption 2: Mark migration as applied without running it');
    console.log('  npx prisma migrate resolve --applied "20240115_performance_indexes"');
    
    console.log('\nOption 3: Create the indexes manually and then mark as applied');
    console.log('  1. Run the SQL commands manually');
    console.log('  2. Then run: npx prisma migrate resolve --applied "20240115_performance_indexes"');
    
    // Check what indexes already exist
    console.log('\n--- Checking existing indexes ---');
    try {
        const dbPath = path.join(__dirname, 'prisma', 'dev.db');
        const sqlite3 = require('sqlite3').verbose();
        const db = new sqlite3.Database(dbPath);
        
        db.all("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE '%idx%' ORDER BY name", (err, rows) => {
            if (!err && rows) {
                console.log('\nExisting indexes:');
                rows.forEach(row => console.log(`  - ${row.name}`));
            }
            db.close();
        });
    } catch (error) {
        console.log('Could not check existing indexes');
    }
}

console.log('\n--- Migration Fix Script Complete ---');
console.log('\nNext steps:');
console.log('1. Choose one of the options above based on your environment');
console.log('2. After fixing, run: npx prisma migrate status');
console.log('3. Then try to apply the security_hardening migration if needed');