# Electrical Contractor Mobile App

A professional React Native mobile application for electrical contractors to manage projects, perform calculations, track inspections, and handle documentation.

## Features

- **Project Management**: Create, track, and manage electrical projects
- **Electrical Calculations**: Built-in calculators for voltage drop, wire sizing, conduit fill, and load calculations
- **Inspection Tracking**: Schedule and document inspections with photo support
- **QR/Barcode Scanning**: Scan equipment and material codes
- **Offline Support**: Work offline with automatic sync when connection is restored
- **Secure Authentication**: JWT-based authentication with refresh tokens
- **Dark Mode Support**: Toggle between light and dark themes

## Tech Stack

- **React Native 0.73.2** with TypeScript
- **React Navigation 6** for routing
- **Native Base 3** for UI components
- **Redux Toolkit** for state management
- **React Query** for server state and caching
- **React Native Vision Camera** for barcode/QR scanning
- **AsyncStorage** for persistent storage
- **React Native Reanimated** for animations

## Prerequisites

- Node.js 18+
- React Native development environment set up:
  - For iOS: Xcode 14+ and CocoaPods
  - For Android: Android Studio and Android SDK
- yarn or npm

## Installation

1. Clone the repository and navigate to the mobile app directory:
```bash
cd app/mobile
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Install iOS dependencies (macOS only):
```bash
cd ios && pod install && cd ..
```

4. Create environment file:
```bash
cp .env.example .env
```

5. Update the `.env` file with your configuration:
```
API_URL=http://your-api-url/api
API_TIMEOUT=30000
NODE_ENV=development
```

## Running the App

### Development

**iOS:**
```bash
npm run ios
# or
yarn ios
```

**Android:**
```bash
npm run android
# or
yarn android
```

**Metro bundler:**
```bash
npm start
# or
yarn start
```

### Production Build

**iOS:**
1. Open `ios/ElectricalContractor.xcworkspace` in Xcode
2. Select your target device
3. Product → Archive

**Android:**
```bash
cd android
./gradlew assembleRelease
# APK will be in android/app/build/outputs/apk/release/
```

## Project Structure

```
src/
├── components/        # Reusable UI components
│   ├── common/       # Common components (LoadingScreen, ErrorBoundary, etc.)
│   ├── forms/        # Form components
│   └── ui/           # Basic UI components
├── navigation/       # Navigation configuration
├── screens/          # Screen components
│   ├── auth/        # Authentication screens
│   ├── dashboard/   # Dashboard screen
│   ├── projects/    # Project management screens
│   └── ...
├── services/        # API services and external integrations
├── store/           # Redux store configuration
│   └── slices/      # Redux slices
├── types/           # TypeScript type definitions
├── utils/           # Utility functions and helpers
└── hooks/           # Custom React hooks
```

## Key Features Implementation

### Offline Support
The app uses Redux Persist with AsyncStorage to maintain state offline. Network requests are queued when offline and synced when connection is restored.

### Barcode/QR Scanning
Implemented using React Native Vision Camera for high-performance scanning of equipment labels and QR codes.

### State Management
- **Redux Toolkit** for global state (auth, projects, UI)
- **React Query** for server state and caching
- **Redux Persist** for offline persistence

### Navigation
- Stack navigation for main flows
- Tab navigation for primary sections
- Modal presentation for calculators and image viewers

## Testing

Run unit tests:
```bash
npm test
# or
yarn test
```

Run linting:
```bash
npm run lint
# or
yarn lint
```

## Troubleshooting

### Common Issues

1. **Metro bundler issues:**
```bash
npx react-native start --reset-cache
```

2. **iOS build failures:**
```bash
cd ios && pod deintegrate && pod install
```

3. **Android build failures:**
```bash
cd android && ./gradlew clean
```

## Performance Optimization

- Uses React Native's VirtualizedList for large data sets
- Implements lazy loading for images
- Utilizes memoization for expensive calculations
- Optimistic updates for better UX

## Security

- JWT tokens stored securely in AsyncStorage
- API requests use HTTPS
- Sensitive data encrypted at rest
- Biometric authentication support (optional)

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests and linting
4. Submit a pull request

## License

[Your License Here]