const blessed = require('blessed');
const contrib = require('blessed-contrib');
const axios = require('axios');
const si = require('systeminformation');

class LoadTestDashboard {
  constructor() {
    this.screen = blessed.screen({
      smartCSR: true,
      title: 'Load Test Dashboard'
    });
    
    this.grid = new contrib.grid({
      rows: 12,
      cols: 12,
      screen: this.screen
    });
    
    this.metrics = {
      responseTime: [],
      requestRate: [],
      errorRate: [],
      cpu: [],
      memory: [],
      activeUsers: 0,
      totalRequests: 0,
      totalErrors: 0
    };
    
    this.setupWidgets();
    this.setupEventHandlers();
  }

  setupWidgets() {
    // Response Time Chart
    this.responseTimeChart = this.grid.set(0, 0, 4, 6, contrib.line, {
      style: {
        line: 'yellow',
        text: 'green',
        baseline: 'black'
      },
      label: 'Response Time (ms)',
      showLegend: true
    });
    
    // Request Rate Chart
    this.requestRateChart = this.grid.set(0, 6, 4, 6, contrib.line, {
      style: {
        line: 'green',
        text: 'green',
        baseline: 'black'
      },
      label: 'Requests/Second',
      showLegend: true
    });
    
    // System Resources
    this.cpuGauge = this.grid.set(4, 0, 2, 3, contrib.gauge, {
      label: 'CPU Usage %',
      stroke: 'green',
      fill: 'white'
    });
    
    this.memoryGauge = this.grid.set(4, 3, 2, 3, contrib.gauge, {
      label: 'Memory Usage %',
      stroke: 'green',
      fill: 'white'
    });
    
    // Error Rate
    this.errorRateChart = this.grid.set(4, 6, 2, 6, contrib.line, {
      style: {
        line: 'red',
        text: 'red',
        baseline: 'black'
      },
      label: 'Error Rate %',
      showLegend: false
    });
    
    // Stats Table
    this.statsTable = this.grid.set(6, 0, 3, 6, contrib.table, {
      keys: true,
      fg: 'white',
      selectedFg: 'white',
      selectedBg: 'blue',
      interactive: false,
      label: 'Test Statistics',
      width: '100%',
      height: '100%',
      border: { type: 'line', fg: 'cyan' },
      columnSpacing: 1,
      columnWidth: [20, 20]
    });
    
    // Log Window
    this.logWindow = this.grid.set(6, 6, 3, 6, contrib.log, {
      fg: 'green',
      selectedFg: 'green',
      label: 'Test Log',
      border: { type: 'line', fg: 'cyan' }
    });
    
    // Active Endpoints
    this.endpointsTable = this.grid.set(9, 0, 3, 12, contrib.table, {
      keys: true,
      fg: 'white',
      selectedFg: 'white',
      selectedBg: 'blue',
      interactive: false,
      label: 'Endpoint Performance',
      width: '100%',
      height: '100%',
      border: { type: 'line', fg: 'cyan' },
      columnSpacing: 1,
      columnWidth: [30, 10, 10, 10, 10, 10]
    });
    
    this.screen.render();
  }

  setupEventHandlers() {
    // Quit on Escape, q, or Control-C
    this.screen.key(['escape', 'q', 'C-c'], () => {
      process.exit(0);
    });
    
    // Refresh on 'r'
    this.screen.key(['r'], () => {
      this.refresh();
    });
  }

  async updateMetrics() {
    try {
      // Get system metrics
      const cpuData = await si.currentLoad();
      const memData = await si.mem();
      
      this.metrics.cpu.push(cpuData.currentLoad);
      this.metrics.memory.push((memData.used / memData.total) * 100);
      
      // Keep only last 60 data points
      if (this.metrics.cpu.length > 60) this.metrics.cpu.shift();
      if (this.metrics.memory.length > 60) this.metrics.memory.shift();
      
      // Update gauges
      this.cpuGauge.setPercent(Math.round(cpuData.currentLoad));
      this.memoryGauge.setPercent(Math.round((memData.used / memData.total) * 100));
      
      // Get test metrics from Artillery or k6
      await this.updateTestMetrics();
      
      // Update charts
      this.updateCharts();
      
      // Update tables
      this.updateTables();
      
      // Add log entry
      this.logWindow.log(`[${new Date().toLocaleTimeString()}] Metrics updated`);
      
      this.screen.render();
    } catch (error) {
      this.logWindow.log(`Error: ${error.message}`);
    }
  }

  async updateTestMetrics() {
    // This would connect to Artillery or k6 metrics endpoint
    // For demo, using mock data
    const mockResponseTime = 200 + Math.random() * 300;
    const mockRequestRate = 50 + Math.random() * 50;
    const mockErrorRate = Math.random() * 2;
    
    this.metrics.responseTime.push(mockResponseTime);
    this.metrics.requestRate.push(mockRequestRate);
    this.metrics.errorRate.push(mockErrorRate);
    
    // Keep only last 60 data points
    if (this.metrics.responseTime.length > 60) this.metrics.responseTime.shift();
    if (this.metrics.requestRate.length > 60) this.metrics.requestRate.shift();
    if (this.metrics.errorRate.length > 60) this.metrics.errorRate.shift();
    
    // Update counters
    this.metrics.totalRequests += Math.round(mockRequestRate);
    this.metrics.totalErrors += Math.round(mockRequestRate * mockErrorRate / 100);
    this.metrics.activeUsers = Math.round(50 + Math.random() * 50);
  }

  updateCharts() {
    // Response Time Chart
    this.responseTimeChart.setData([
      {
        title: 'p95',
        x: Array.from({ length: this.metrics.responseTime.length }, (_, i) => i.toString()),
        y: this.metrics.responseTime
      }
    ]);
    
    // Request Rate Chart
    this.requestRateChart.setData([
      {
        title: 'RPS',
        x: Array.from({ length: this.metrics.requestRate.length }, (_, i) => i.toString()),
        y: this.metrics.requestRate
      }
    ]);
    
    // Error Rate Chart
    this.errorRateChart.setData([
      {
        title: 'Error %',
        x: Array.from({ length: this.metrics.errorRate.length }, (_, i) => i.toString()),
        y: this.metrics.errorRate
      }
    ]);
  }

  updateTables() {
    // Stats Table
    const stats = [
      ['Metric', 'Value'],
      ['Active Users', this.metrics.activeUsers.toString()],
      ['Total Requests', this.metrics.totalRequests.toLocaleString()],
      ['Total Errors', this.metrics.totalErrors.toLocaleString()],
      ['Error Rate', `${(this.metrics.totalErrors / this.metrics.totalRequests * 100).toFixed(2)}%`],
      ['Avg Response Time', `${this.avgLast(this.metrics.responseTime).toFixed(0)}ms`],
      ['Avg Request Rate', `${this.avgLast(this.metrics.requestRate).toFixed(0)}/s`]
    ];
    
    this.statsTable.setData({
      headers: stats[0],
      data: stats.slice(1)
    });
    
    // Endpoints Table (mock data)
    const endpoints = [
      ['Endpoint', 'Requests', 'Errors', 'p50', 'p95', 'p99'],
      ['/api/calculations/voltage-drop', '15234', '12', '145ms', '289ms', '512ms'],
      ['/api/panels', '12456', '8', '89ms', '156ms', '234ms'],
      ['/api/materials/search', '9876', '23', '234ms', '456ms', '789ms'],
      ['/api/auth/login', '5432', '2', '56ms', '123ms', '189ms'],
      ['/api/exports/panel-schedule', '1234', '5', '1234ms', '2345ms', '3456ms']
    ];
    
    this.endpointsTable.setData({
      headers: endpoints[0],
      data: endpoints.slice(1)
    });
  }

  avgLast(arr, count = 10) {
    const slice = arr.slice(-count);
    return slice.length > 0 ? slice.reduce((a, b) => a + b, 0) / slice.length : 0;
  }

  refresh() {
    this.logWindow.log('Manual refresh triggered');
    this.updateMetrics();
  }

  start() {
    this.logWindow.log('Load Test Dashboard Started');
    this.logWindow.log('Press "q" to quit, "r" to refresh');
    
    // Update metrics every second
    setInterval(() => this.updateMetrics(), 1000);
    
    // Initial update
    this.updateMetrics();
  }
}

// Start dashboard
const dashboard = new LoadTestDashboard();
dashboard.start();