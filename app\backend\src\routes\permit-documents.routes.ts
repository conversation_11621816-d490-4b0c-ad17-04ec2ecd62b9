import { Router, Request, Response } from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { asyncHand<PERSON> } from '../utils/async-handler';
import { permitDocumentService } from '../services/permit-document.service';
import { z } from 'zod';
import multer from 'multer';
import path from 'path';

const router: Router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    cb(null, path.join(process.cwd(), 'uploads', 'permits'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.pdf', '.png', '.jpg', '.jpeg', '.doc', '.docx'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Validation schemas
const createPermitDocumentSchema = z.object({
  project_id: z.string().uuid(),
  document_type: z.enum([
    'PERMIT_APPLICATION',
    'LOAD_CALC_SUMMARY',
    'PANEL_SCHEDULE',
    'ONE_LINE_DIAGRAM',
    'SHORT_CIRCUIT_REPORT',
    'ARC_FLASH_REPORT',
    'INSPECTION_REQUEST',
    'AS_BUILT'
  ]),
  jurisdiction: z.string().min(1),
  jurisdiction_code: z.string().optional(),
  title: z.string().min(1),
  description: z.string().optional(),
  template_id: z.string().uuid().optional(),
  form_data: z.record(z.any()),
});

const updatePermitDocumentSchema = z.object({
  status: z.enum(['DRAFT', 'READY', 'SUBMITTED', 'APPROVED', 'REJECTED', 'EXPIRED']).optional(),
  form_data: z.record(z.any()).optional(),
  included_calculations: z.array(z.string().uuid()).optional(),
  included_panels: z.array(z.string().uuid()).optional(),
  attachments: z.array(z.string()).optional(),
  inspector_notes: z.string().optional(),
});

const submitPermitSchema = z.object({
  permit_number: z.string().optional(),
  submission_date: z.string().datetime(),
});

const updatePermitStatusSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  permit_number: z.string().optional(),
  approval_date: z.string().datetime().optional(),
  rejection_reason: z.string().optional(),
  expiration_date: z.string().datetime().optional(),
});

const digitalSignatureSchema = z.object({
  signature_data: z.string(), // Base64 encoded image
});

// Create permit document
router.post(
  '/',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const validatedData = createPermitDocumentSchema.parse(req.body);
    
    const document = await permitDocumentService.createPermitDocument({
      project_id: validatedData.project_id,
      document_type: validatedData.document_type,
      jurisdiction: validatedData.jurisdiction,
      jurisdiction_code: validatedData.jurisdiction_code,
      title: validatedData.title,
      description: validatedData.description,
      template_id: validatedData.template_id,
      form_data: validatedData.form_data,
      created_by: req.user!.id,
    });

    res.status(201).json(document);
  })
);

// Get all permit documents for a project
router.get(
  '/project/:projectId',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { projectId } = req.params;
    
    const documents = await permitDocumentService.getProjectPermitDocuments(projectId);
    
    res.json(documents);
  })
);

// Get latest version of a specific document type
router.get(
  '/project/:projectId/latest/:documentType',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { projectId, documentType } = req.params;
    
    const document = await permitDocumentService.getLatestDocument(projectId, documentType);
    
    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    res.json(document);
  })
);

// Update permit document
router.put(
  '/:id',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = updatePermitDocumentSchema.parse(req.body);
    
    const document = await permitDocumentService.updatePermitDocument(id, {
      ...validatedData,
      reviewed_by: req.user!.id,
    });

    res.json(document);
  })
);

// Submit permit document
router.post(
  '/:id/submit',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = submitPermitSchema.parse(req.body);
    
    const document = await permitDocumentService.submitPermitDocument(id, {
      permit_number: validatedData.permit_number,
      submission_date: new Date(validatedData.submission_date),
      submitted_by: req.user!.id,
    });

    res.json(document);
  })
);

// Generate permit application PDF
router.post(
  '/:id/generate-pdf',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const result = await permitDocumentService.generatePermitApplicationPDF(id);
    
    res.json({
      message: 'PDF generated successfully',
      ...result,
    });
  })
);

// Generate load calculation PDF
router.post(
  '/generate-load-calc-pdf',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { project_id, panel_id } = req.body;
    
    if (!project_id || !panel_id) {
      return res.status(400).json({ error: 'project_id and panel_id are required' });
    }
    
    const result = await permitDocumentService.generateLoadCalculationPDF(project_id, panel_id);
    
    res.json({
      message: 'Load calculation PDF generated successfully',
      ...result,
    });
  })
);

// Generate panel schedule PDF
router.post(
  '/generate-panel-schedule-pdf',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { panel_id } = req.body;
    
    if (!panel_id) {
      return res.status(400).json({ error: 'panel_id is required' });
    }
    
    const result = await permitDocumentService.generatePanelSchedulePDF(panel_id);
    
    res.json({
      message: 'Panel schedule PDF generated successfully',
      ...result,
    });
  })
);

// Generate inspection request PDF
router.post(
  '/:id/generate-inspection-pdf',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const result = await permitDocumentService.generateInspectionRequestPDF(id);
    
    res.json({
      message: 'Inspection request PDF generated successfully',
      ...result,
    });
  })
);

// Get jurisdiction templates
router.get(
  '/templates',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { state, document_type } = req.query;
    
    const templates = await permitDocumentService.getJurisdictionTemplates(
      state as string | undefined,
      document_type as string | undefined
    );
    
    res.json(templates);
  })
);

// Compile calculation reports for a project
router.get(
  '/project/:projectId/compile-reports',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { projectId } = req.params;
    
    const reports = await permitDocumentService.compileCalculationReports(projectId);
    
    res.json(reports);
  })
);

// Add digital signature
router.post(
  '/:id/sign',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = digitalSignatureSchema.parse(req.body);
    
    const document = await permitDocumentService.addDigitalSignature(id, {
      signed_by: req.user!.name,
      signature_data: validatedData.signature_data,
    });

    res.json(document);
  })
);

// Update permit status (admin only)
router.put(
  '/:id/status',
  authenticate,
  authorize(['admin']),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = updatePermitStatusSchema.parse(req.body);
    
    const data: any = {
      status: validatedData.status,
      permit_number: validatedData.permit_number,
      rejection_reason: validatedData.rejection_reason,
    };
    
    if (validatedData.approval_date) {
      data.approval_date = new Date(validatedData.approval_date);
    }
    if (validatedData.expiration_date) {
      data.expiration_date = new Date(validatedData.expiration_date);
    }
    
    const document = await permitDocumentService.updatePermitStatus(id, data);

    res.json(document);
  })
);

// Upload attachment
router.post(
  '/:id/attachments',
  authenticate,
  upload.array('files', 5),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }
    
    // Get current document
    const document = await permitDocumentService.getLatestDocument(id, '');
    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    // Update attachments
    const currentAttachments = document.attachments ? JSON.parse(document.attachments) : [];
    const newAttachments = files.map(file => file.path);
    
    await permitDocumentService.updatePermitDocument(id, {
      attachments: [...currentAttachments, ...newAttachments],
    });
    
    res.json({
      message: 'Files uploaded successfully',
      files: files.map(f => ({
        filename: f.filename,
        size: f.size,
        path: f.path,
      })),
    });
  })
);

// Download generated PDF
router.get(
  '/:id/download',
  authenticate,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    // This would need to be implemented to actually serve the PDF file
    // For now, we'll just return the file info
    res.json({
      message: 'Download endpoint - to be implemented',
      documentId: id,
    });
  })
);

export default router;