import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { CheckCircle, XCircle, AlertCircle, Camera, Save } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import inspectionService, { InspectionChecklist as InspectionChecklistType } from '../../services/inspectionService';

export const InspectionMobileView: React.FC = () => {
  const { qrCodeId } = useParams<{ qrCodeId: string }>();
  const [inspection, setInspection] = useState<InspectionChecklistType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('');

  useEffect(() => {
    if (qrCodeId) {
      loadInspection();
    }
  }, [qrCodeId]);

  const loadInspection = async () => {
    if (!qrCodeId) return;
    
    try {
      setLoading(true);
      const data = await inspectionService.getInspectionByQRCode(qrCodeId);
      setInspection(data);
      
      // Set first category as active
      const categories = [...new Set(data.checklist_items.map(item => item.category))];
      if (categories.length > 0) {
        setActiveCategory(categories[0]);
      }
    } catch (err) {
      setError('Invalid QR code or inspection not found');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleItemStatusChange = async (itemId: string, status: string) => {
    if (!inspection) return;

    try {
      const updatedItem = await inspectionService.updateChecklistItem(itemId, { status });
      
      // Update local state
      setInspection({
        ...inspection,
        checklist_items: inspection.checklist_items.map(item =>
          item.id === itemId ? { ...item, ...updatedItem } : item
        )
      });
    } catch (err) {
      console.error('Failed to update item status:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading inspection...</p>
        </div>
      </div>
    );
  }

  if (error || !inspection) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <Card className="p-6 max-w-sm mx-auto">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-center text-gray-700">{error || 'Inspection not found'}</p>
        </Card>
      </div>
    );
  }

  const categories = [...new Set(inspection.checklist_items.map(item => item.category))];
  const activeItems = inspection.checklist_items.filter(item => item.category === activeCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 sticky top-0 z-10">
        <h1 className="text-xl font-bold">
          {inspection.inspection_type.replace(/_/g, ' ')} Inspection
        </h1>
        <p className="text-blue-100">
          {inspection.project?.customer?.name} - {inspection.project?.name}
        </p>
      </div>

      {/* Category Tabs */}
      <div className="bg-white shadow-sm sticky top-16 z-10 overflow-x-auto">
        <div className="flex space-x-1 p-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-lg whitespace-nowrap text-sm font-medium transition-colors ${
                activeCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {category.replace(/_/g, ' ')}
            </button>
          ))}
        </div>
      </div>

      {/* Checklist Items */}
      <div className="p-4 space-y-4">
        {activeItems.map((item) => (
          <Card key={item.id} className="p-4">
            <div className="space-y-3">
              <div>
                <p className="font-medium text-gray-900">{item.description}</p>
                {item.nec_reference && (
                  <p className="text-sm text-gray-500 mt-1">NEC {item.nec_reference}</p>
                )}
              </div>

              {/* Status Buttons */}
              <div className="grid grid-cols-3 gap-2">
                <Button
                  size="sm"
                  variant={item.status === 'PASS' ? 'default' : 'outline'}
                  className={item.status === 'PASS' ? 'bg-green-600' : ''}
                  onClick={() => handleItemStatusChange(item.id, 'PASS')}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Pass
                </Button>
                <Button
                  size="sm"
                  variant={item.status === 'FAIL' ? 'default' : 'outline'}
                  className={item.status === 'FAIL' ? 'bg-red-600' : ''}
                  onClick={() => handleItemStatusChange(item.id, 'FAIL')}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Fail
                </Button>
                <Button
                  size="sm"
                  variant={item.status === 'NA' ? 'default' : 'outline'}
                  className={item.status === 'NA' ? 'bg-gray-600' : ''}
                  onClick={() => handleItemStatusChange(item.id, 'NA')}
                >
                  N/A
                </Button>
              </div>

              {/* Photo Button */}
              {item.photo_required && (
                <Button variant="secondary" size="sm" className="w-full">
                  <Camera className="h-4 w-4 mr-2" />
                  Add Photo {item.photos_attached > 0 && `(${item.photos_attached})`}
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* Bottom Progress Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="mb-2 flex justify-between text-sm">
          <span>Progress</span>
          <span>
            {inspection.checklist_items.filter(i => i.status !== 'NOT_INSPECTED').length} / {inspection.checklist_items.length}
          </span>
        </div>
        <div className="bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all"
            style={{
              width: `${(inspection.checklist_items.filter(i => i.status !== 'NOT_INSPECTED').length / inspection.checklist_items.length) * 100}%`
            }}
          />
        </div>
      </div>
    </div>
  );
};