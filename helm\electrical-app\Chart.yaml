apiVersion: v2
name: electrical-app
description: A comprehensive electrical contracting application
type: application
version: 1.0.0
appVersion: "1.0.0"
keywords:
  - electrical
  - contracting
  - nodejs
  - react
  - postgresql
home: https://github.com/yourcompany/electrical-app
sources:
  - https://github.com/yourcompany/electrical-app
maintainers:
  - name: Your Name
    email: <EMAIL>
dependencies:
  - name: postgresql
    version: "12.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: "17.x.x"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: neo4j
    version: "5.x.x"
    repository: https://neo4j.github.io/helm-charts
    condition: neo4j.enabled