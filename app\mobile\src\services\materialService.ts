import AsyncStorage from '@react-native-async-storage/async-storage';
import { Material, MaterialTakeoff } from '../types/electrical';
import { secureApi } from './secureApi';

const CACHE_KEY_PREFIX = 'material_cache_';
const PRICE_CACHE_KEY = 'material_prices_cache';
const TAKEOFF_CACHE_KEY = 'material_takeoffs_';

class MaterialService {
  // Search materials by query (SKU, name, or barcode)
  async searchMaterials(query: string): Promise<Material[]> {
    try {
      const response = await secureApi.get('/materials/search', {
        params: { q: query }
      });
      
      const materials = response.data;
      
      // Cache materials for offline use
      for (const material of materials) {
        await this.cacheMaterial(material);
      }
      
      return materials;
    } catch (error) {
      // If offline, search in cache
      return this.searchCachedMaterials(query);
    }
  }

  // Get material by ID
  async getMaterial(materialId: string): Promise<Material> {
    try {
      const response = await secureApi.get(`/materials/${materialId}`);
      const material = response.data;
      await this.cacheMaterial(material);
      return material;
    } catch (error) {
      const cached = await this.getCachedMaterial(materialId);
      if (cached) return cached;
      throw error;
    }
  }

  // Get real-time price for material
  async getMaterialPrice(materialId: string): Promise<number> {
    try {
      const response = await secureApi.get(`/materials/${materialId}/price`);
      const price = response.data.price;
      
      // Cache price with timestamp
      await this.cacheMaterialPrice(materialId, price);
      
      return price;
    } catch (error) {
      // Return cached price if available
      const cachedPrice = await this.getCachedPrice(materialId);
      if (cachedPrice !== null) return cachedPrice;
      throw error;
    }
  }

  // Check inventory status
  async checkInventory(materialId: string, locationId?: string): Promise<{
    inStock: boolean;
    quantity: number;
    locations: Array<{ location: string; quantity: number }>;
  }> {
    try {
      const response = await secureApi.get(`/materials/${materialId}/inventory`, {
        params: { locationId }
      });
      return response.data;
    } catch (error) {
      // Return cached inventory status if offline
      const material = await this.getCachedMaterial(materialId);
      if (material) {
        return {
          inStock: material.inStock,
          quantity: material.stockLevel || 0,
          locations: []
        };
      }
      throw error;
    }
  }

  // Save material takeoff
  async saveTakeoff(takeoff: MaterialTakeoff): Promise<MaterialTakeoff> {
    try {
      const response = await secureApi.post('/materials/takeoffs', takeoff);
      const savedTakeoff = response.data;
      await this.cacheTakeoff(savedTakeoff);
      return savedTakeoff;
    } catch (error) {
      // Save to offline queue
      await this.queueOfflineTakeoff(takeoff);
      throw error;
    }
  }

  // Get takeoffs for project
  async getProjectTakeoffs(projectId: string): Promise<MaterialTakeoff[]> {
    try {
      const response = await secureApi.get(`/projects/${projectId}/takeoffs`);
      const takeoffs = response.data;
      
      // Cache all takeoffs
      for (const takeoff of takeoffs) {
        await this.cacheTakeoff(takeoff);
      }
      
      return takeoffs;
    } catch (error) {
      return this.getCachedProjectTakeoffs(projectId);
    }
  }

  // Cache management
  private async cacheMaterial(material: Material): Promise<void> {
    const key = `${CACHE_KEY_PREFIX}${material.id}`;
    await AsyncStorage.setItem(key, JSON.stringify({
      ...material,
      cachedAt: new Date().toISOString()
    }));
  }

  private async getCachedMaterial(materialId: string): Promise<Material | null> {
    const key = `${CACHE_KEY_PREFIX}${materialId}`;
    const cached = await AsyncStorage.getItem(key);
    return cached ? JSON.parse(cached) : null;
  }

  private async searchCachedMaterials(query: string): Promise<Material[]> {
    const allKeys = await AsyncStorage.getAllKeys();
    const materialKeys = allKeys.filter(key => key.startsWith(CACHE_KEY_PREFIX));
    
    const materials: Material[] = [];
    const searchQuery = query.toLowerCase();
    
    for (const key of materialKeys) {
      const cached = await AsyncStorage.getItem(key);
      if (cached) {
        const material = JSON.parse(cached) as Material;
        if (
          material.sku.toLowerCase().includes(searchQuery) ||
          material.name.toLowerCase().includes(searchQuery)
        ) {
          materials.push(material);
        }
      }
    }
    
    return materials;
  }

  private async cacheMaterialPrice(materialId: string, price: number): Promise<void> {
    const priceCache = await this.getPriceCache();
    priceCache[materialId] = {
      price,
      timestamp: new Date().toISOString()
    };
    await AsyncStorage.setItem(PRICE_CACHE_KEY, JSON.stringify(priceCache));
  }

  private async getCachedPrice(materialId: string): Promise<number | null> {
    const priceCache = await this.getPriceCache();
    const cached = priceCache[materialId];
    
    if (cached) {
      // Check if price is less than 24 hours old
      const cacheAge = Date.now() - new Date(cached.timestamp).getTime();
      if (cacheAge < 24 * 60 * 60 * 1000) {
        return cached.price;
      }
    }
    
    return null;
  }

  private async getPriceCache(): Promise<Record<string, { price: number; timestamp: string }>> {
    const cached = await AsyncStorage.getItem(PRICE_CACHE_KEY);
    return cached ? JSON.parse(cached) : {};
  }

  private async cacheTakeoff(takeoff: MaterialTakeoff): Promise<void> {
    const key = `${TAKEOFF_CACHE_KEY}${takeoff.id}`;
    await AsyncStorage.setItem(key, JSON.stringify(takeoff));
  }

  private async getCachedProjectTakeoffs(projectId: string): Promise<MaterialTakeoff[]> {
    const allKeys = await AsyncStorage.getAllKeys();
    const takeoffKeys = allKeys.filter(key => key.startsWith(TAKEOFF_CACHE_KEY));
    
    const takeoffs: MaterialTakeoff[] = [];
    
    for (const key of takeoffKeys) {
      const cached = await AsyncStorage.getItem(key);
      if (cached) {
        const takeoff = JSON.parse(cached) as MaterialTakeoff;
        if (takeoff.projectId === projectId) {
          takeoffs.push(takeoff);
        }
      }
    }
    
    return takeoffs;
  }

  private async queueOfflineTakeoff(takeoff: MaterialTakeoff): Promise<void> {
    const queueKey = 'material_takeoff_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    const takeoffs = queue ? JSON.parse(queue) : [];
    
    takeoffs.push({
      ...takeoff,
      id: `offline_${Date.now()}`,
      queuedAt: new Date().toISOString()
    });
    
    await AsyncStorage.setItem(queueKey, JSON.stringify(takeoffs));
  }

  // Sync offline takeoffs
  async syncOfflineTakeoffs(): Promise<void> {
    const queueKey = 'material_takeoff_queue';
    const queue = await AsyncStorage.getItem(queueKey);
    if (!queue) return;

    const takeoffs = JSON.parse(queue);
    const failedTakeoffs = [];

    for (const takeoff of takeoffs) {
      try {
        await secureApi.post('/materials/takeoffs', takeoff);
      } catch (error) {
        failedTakeoffs.push(takeoff);
      }
    }

    if (failedTakeoffs.length > 0) {
      await AsyncStorage.setItem(queueKey, JSON.stringify(failedTakeoffs));
    } else {
      await AsyncStorage.removeItem(queueKey);
    }
  }

  // Clear cache
  async clearCache(): Promise<void> {
    const allKeys = await AsyncStorage.getAllKeys();
    const materialKeys = allKeys.filter(key => 
      key.startsWith(CACHE_KEY_PREFIX) || 
      key.startsWith(TAKEOFF_CACHE_KEY) ||
      key === PRICE_CACHE_KEY
    );
    
    await AsyncStorage.multiRemove(materialKeys);
  }
}

export const materialService = new MaterialService();