import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { MessageBus, MessageType, TaskRequest } from '../infrastructure/message-bus';
import { z } from 'zod';

// Task orchestration schema
const orchestrationSchema = z.object({
  workflow: z.string(),
  context: z.record(z.any()),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
});

// Agent assignment schema
const agentAssignmentSchema = z.object({
  taskType: z.string(),
  preferredAgent: z.string().optional(),
  fallbackAgents: z.array(z.string()).optional(),
});

// Workflow definition
interface WorkflowStep {
  id: string;
  agent: string;
  task: string;
  dependencies: string[];
  input: (context: any) => any;
  output?: string; // Key to store result in context
}

// Predefined workflows for electrical contracting
const WORKFLOWS: Record<string, WorkflowStep[]> = {
  'create-estimate': [
    {
      id: 'analyze-requirements',
      agent: 'research',
      task: 'analyze-project-requirements',
      dependencies: [],
      input: (ctx) => ({ description: ctx.projectDescription }),
      output: 'requirements',
    },
    {
      id: 'calculate-loads',
      agent: 'backend-database',
      task: 'calculate-electrical-loads',
      dependencies: ['analyze-requirements'],
      input: (ctx) => ({ requirements: ctx.requirements }),
      output: 'loadCalculations',
    },
    {
      id: 'lookup-materials',
      agent: 'research',
      task: 'lookup-material-prices',
      dependencies: ['calculate-loads'],
      input: (ctx) => ({ materials: ctx.loadCalculations.materials }),
      output: 'materialPrices',
    },
    {
      id: 'generate-estimate',
      agent: 'backend-database',
      task: 'generate-estimate',
      dependencies: ['lookup-materials'],
      input: (ctx) => ({
        calculations: ctx.loadCalculations,
        materials: ctx.materialPrices,
      }),
      output: 'estimate',
    },
  ],
  'nec-compliance-check': [
    {
      id: 'extract-parameters',
      agent: 'coding',
      task: 'extract-calculation-parameters',
      dependencies: [],
      input: (ctx) => ({ design: ctx.electricalDesign }),
      output: 'parameters',
    },
    {
      id: 'check-compliance',
      agent: 'research',
      task: 'check-nec-compliance',
      dependencies: ['extract-parameters'],
      input: (ctx) => ({ parameters: ctx.parameters }),
      output: 'complianceReport',
    },
    {
      id: 'suggest-corrections',
      agent: 'debugging',
      task: 'suggest-compliance-fixes',
      dependencies: ['check-compliance'],
      input: (ctx) => ({ report: ctx.complianceReport }),
      output: 'corrections',
    },
  ],
  'optimize-ui-component': [
    {
      id: 'analyze-performance',
      agent: 'frontend',
      task: 'optimize-performance',
      dependencies: [],
      input: (ctx) => ({
        componentPath: ctx.componentPath,
        metrics: ctx.targetMetrics,
        optimizationTypes: ctx.optimizationTypes || ['memo', 'lazy'],
      }),
      output: 'performanceAnalysis',
    },
    {
      id: 'validate-accessibility',
      agent: 'frontend',
      task: 'validate-accessibility',
      dependencies: ['analyze-performance'],
      input: (ctx) => ({
        componentPath: ctx.componentPath,
        wcagLevel: ctx.wcagLevel || 'AA',
      }),
      output: 'accessibilityReport',
    },
    {
      id: 'generate-tests',
      agent: 'frontend',
      task: 'generate-component-tests',
      dependencies: ['validate-accessibility'],
      input: (ctx) => ({
        componentPath: ctx.componentPath,
        testType: 'unit',
        coverage: 80,
      }),
      output: 'componentTests',
    },
  ],
  'create-estimation-form': [
    {
      id: 'design-state',
      agent: 'frontend',
      task: 'manage-ui-state',
      dependencies: [],
      input: (ctx) => ({
        stateType: 'zustand',
        componentPath: 'EstimationForm',
        stateRequirements: {
          data: ['materials', 'labor', 'customer', 'calculations'],
          actions: ['addMaterial', 'updateLabor', 'calculateTotal'],
          persistence: true,
          realtime: false,
        },
      }),
      output: 'stateManagement',
    },
    {
      id: 'create-validation',
      agent: 'frontend',
      task: 'generate-form-validation',
      dependencies: ['design-state'],
      input: (ctx) => ({
        formStructure: ctx.formStructure,
        validationLibrary: 'zod',
        includeRealtimeValidation: true,
      }),
      output: 'formValidation',
    },
    {
      id: 'add-error-handling',
      agent: 'frontend',
      task: 'recommend-error-boundaries',
      dependencies: ['create-validation'],
      input: (ctx) => ({
        componentPath: 'EstimationForm',
        errorTypes: ['async', 'network', 'render'],
        fallbackStrategy: 'retry',
      }),
      output: 'errorHandling',
    },
  ],
};

export class ProjectManagerAgent extends BaseAgent {
  private activeWorkflows: Map<string, {
    workflow: WorkflowStep[];
    context: any;
    completedSteps: Set<string>;
    status: 'running' | 'completed' | 'failed';
  }> = new Map();
  
  private agentRegistry: Map<string, {
    type: string;
    capabilities: string[];
    workload: number;
    lastSeen: Date;
  }> = new Map();

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'orchestrate-workflow',
        description: 'Orchestrate multi-agent workflows',
        inputSchema: orchestrationSchema,
      },
      {
        name: 'assign-task',
        description: 'Assign task to best available agent',
        inputSchema: agentAssignmentSchema,
      },
      {
        name: 'monitor-progress',
        description: 'Monitor workflow and task progress',
      },
      {
        name: 'prioritize-tasks',
        description: 'Manage task queue and priorities',
      },
    ];

    super({
      ...config,
      capabilities,
    });
  }

  protected async onInitialize(): Promise<void> {
    // Listen for agent status updates
    this.messageBus.on('agent:registered', this.handleAgentRegistered.bind(this));
    this.messageBus.on('agent:unregistered', this.handleAgentUnregistered.bind(this));
    this.messageBus.on('agent:status', this.handleAgentStatus.bind(this));
    
    // Load workflow history from memory
    const workflowHistory = await this.retrieveKnowledge(['workflow', 'history'], 10);
    if (workflowHistory.length > 0) {
      await this.log('Loaded workflow history', { 
        level: 'info', 
        count: workflowHistory.length 
      });
    }
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'orchestrate-workflow':
        return this.orchestrateWorkflow(data);
      case 'assign-task':
        return this.assignTask(data);
      case 'monitor-progress':
        return this.getWorkflowProgress();
      case 'prioritize-tasks':
        return this.prioritizeTasks();
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Orchestrate a predefined workflow
  private async orchestrateWorkflow(data: z.infer<typeof orchestrationSchema>): Promise<any> {
    const { workflow: workflowName, context, priority } = data;
    
    // Get workflow definition
    const workflow = WORKFLOWS[workflowName];
    if (!workflow) {
      throw new Error(`Unknown workflow: ${workflowName}`);
    }
    
    const workflowId = `wf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Initialize workflow state
    this.activeWorkflows.set(workflowId, {
      workflow,
      context: { ...context },
      completedSteps: new Set(),
      status: 'running',
    });
    
    await this.log('Starting workflow', {
      level: 'info',
      workflowId,
      workflowName,
      steps: workflow.length,
    });
    
    // Execute workflow
    try {
      await this.executeWorkflow(workflowId, priority);
      
      const result = this.activeWorkflows.get(workflowId)!.context;
      
      // Store successful workflow in memory
      await this.storeKnowledge(
        {
          workflowName,
          workflowId,
          result,
          duration: Date.now() - parseInt(workflowId.split('-')[1]),
        },
        ['workflow', 'history', workflowName, 'success'],
        0.7
      );
      
      return {
        workflowId,
        status: 'completed',
        result,
      };
    } catch (error) {
      // Store failed workflow in memory
      await this.storeKnowledge(
        {
          workflowName,
          workflowId,
          error: (error as Error).message,
          context,
        },
        ['workflow', 'history', workflowName, 'failure'],
        0.9 // High importance for failures
      );
      
      throw error;
    }
  }

  // Execute workflow steps
  private async executeWorkflow(
    workflowId: string, 
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  ): Promise<void> {
    const workflowState = this.activeWorkflows.get(workflowId);
    if (!workflowState) return;
    
    const { workflow, context, completedSteps } = workflowState;
    
    // Find ready steps (dependencies satisfied)
    const readySteps = workflow.filter(step => 
      !completedSteps.has(step.id) &&
      step.dependencies.every(dep => completedSteps.has(dep))
    );
    
    if (readySteps.length === 0 && completedSteps.size < workflow.length) {
      throw new Error('Workflow deadlock detected');
    }
    
    // Execute ready steps in parallel
    const stepPromises = readySteps.map(async step => {
      try {
        await this.log('Executing workflow step', {
          level: 'debug',
          workflowId,
          stepId: step.id,
          agent: step.agent,
          task: step.task,
        });
        
        // Get input for step
        const input = step.input(context);
        
        // Find best agent for the task
        const agent = await this.findBestAgent(step.agent, step.task);
        if (!agent) {
          throw new Error(`No available agent for ${step.agent}:${step.task}`);
        }
        
        // Request task execution
        const result = await this.requestTask(
          agent,
          step.task,
          input,
          priority
        );
        
        // Store result in context
        if (step.output) {
          context[step.output] = result;
        }
        
        // Mark step as completed
        completedSteps.add(step.id);
        
        await this.log('Completed workflow step', {
          level: 'debug',
          workflowId,
          stepId: step.id,
          hasOutput: !!step.output,
        });
      } catch (error) {
        workflowState.status = 'failed';
        throw new Error(`Step ${step.id} failed: ${(error as Error).message}`);
      }
    });
    
    // Wait for all ready steps to complete
    await Promise.all(stepPromises);
    
    // Continue with next steps if any remain
    if (completedSteps.size < workflow.length) {
      await this.executeWorkflow(workflowId, priority);
    } else {
      workflowState.status = 'completed';
    }
  }

  // Find best available agent for a task
  private async findBestAgent(agentType: string, taskType: string): Promise<string | null> {
    const availableAgents = Array.from(this.agentRegistry.entries())
      .filter(([id, info]) => 
        info.type === agentType &&
        info.capabilities.includes(taskType) &&
        info.workload < 5 // Max concurrent tasks
      )
      .sort((a, b) => a[1].workload - b[1].workload);
    
    if (availableAgents.length === 0) {
      // Try to wake up an offline agent
      await this.log('No available agents, attempting to wake agent', {
        level: 'warn',
        agentType,
        taskType,
      });
      
      // Broadcast wake request
      const wakeRequest = MessageBus.createTaskRequest(
        this.id,
        undefined, // Broadcast
        'wake-agent',
        { agentType, taskType },
        'HIGH'
      );
      
      await this.messageBus.send(wakeRequest);
      
      // Wait a bit for agent to come online
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Try again
      return this.findBestAgent(agentType, taskType);
    }
    
    // Return agent with lowest workload
    const [agentId, info] = availableAgents[0];
    info.workload++;
    
    return agentId;
  }

  // Assign a specific task to an agent
  private async assignTask(data: z.infer<typeof agentAssignmentSchema>): Promise<any> {
    const { taskType, preferredAgent, fallbackAgents } = data;
    
    // Try preferred agent first
    if (preferredAgent && this.agentRegistry.has(preferredAgent)) {
      const agent = this.agentRegistry.get(preferredAgent)!;
      if (agent.capabilities.includes(taskType)) {
        return {
          assignedAgent: preferredAgent,
          taskType,
          status: 'assigned',
        };
      }
    }
    
    // Try fallback agents
    if (fallbackAgents) {
      for (const agentId of fallbackAgents) {
        if (this.agentRegistry.has(agentId)) {
          const agent = this.agentRegistry.get(agentId)!;
          if (agent.capabilities.includes(taskType)) {
            return {
              assignedAgent: agentId,
              taskType,
              status: 'assigned',
            };
          }
        }
      }
    }
    
    // Find any capable agent
    for (const [agentId, agent] of this.agentRegistry) {
      if (agent.capabilities.includes(taskType)) {
        return {
          assignedAgent: agentId,
          taskType,
          status: 'assigned',
        };
      }
    }
    
    throw new Error(`No agent available for task: ${taskType}`);
  }

  // Get workflow progress
  private getWorkflowProgress(): any {
    const progress = Array.from(this.activeWorkflows.entries()).map(([id, state]) => ({
      workflowId: id,
      totalSteps: state.workflow.length,
      completedSteps: state.completedSteps.size,
      progress: (state.completedSteps.size / state.workflow.length) * 100,
      status: state.status,
    }));
    
    return { workflows: progress };
  }

  // Prioritize tasks based on importance and deadlines
  private async prioritizeTasks(): Promise<any> {
    // Get recent task history
    const taskHistory = await this.retrieveKnowledge(['task', 'history'], 20);
    
    // Analyze patterns
    const taskPatterns = this.analyzeTaskPatterns(taskHistory);
    
    // Generate recommendations
    const recommendations = {
      highPriority: taskPatterns.critical,
      normalPriority: taskPatterns.normal,
      lowPriority: taskPatterns.low,
      suggestedOptimizations: this.generateOptimizations(taskPatterns),
    };
    
    return recommendations;
  }

  // Analyze task patterns from history
  private analyzeTaskPatterns(history: any[]): any {
    const patterns = {
      critical: [] as string[],
      normal: [] as string[],
      low: [] as string[],
      failureRate: {} as Record<string, number>,
      avgDuration: {} as Record<string, number>,
    };
    
    // Analyze task history
    history.forEach(item => {
      const content = item.content;
      if (content.task) {
        // Track failure rates
        if (!patterns.failureRate[content.task]) {
          patterns.failureRate[content.task] = 0;
        }
        if (!content.success) {
          patterns.failureRate[content.task]++;
        }
        
        // Track durations
        if (content.duration) {
          if (!patterns.avgDuration[content.task]) {
            patterns.avgDuration[content.task] = 0;
          }
          patterns.avgDuration[content.task] += content.duration;
        }
      }
    });
    
    // Categorize by priority
    Object.entries(patterns.failureRate).forEach(([task, failures]) => {
      if (failures > 2) {
        patterns.critical.push(task);
      } else if (failures > 0) {
        patterns.normal.push(task);
      } else {
        patterns.low.push(task);
      }
    });
    
    return patterns;
  }

  // Generate optimization suggestions
  private generateOptimizations(patterns: any): string[] {
    const suggestions: string[] = [];
    
    // Suggest fixing high-failure tasks
    patterns.critical.forEach((task: string) => {
      suggestions.push(`Investigate high failure rate for task: ${task}`);
    });
    
    // Suggest parallelization for slow tasks
    Object.entries(patterns.avgDuration).forEach(([task, duration]) => {
      if ((duration as number) > 5000) {
        suggestions.push(`Consider parallelizing or optimizing task: ${task}`);
      }
    });
    
    return suggestions;
  }

  // Handle agent registration
  private handleAgentRegistered(data: { agentId: string; agentType: string }): void {
    this.agentRegistry.set(data.agentId, {
      type: data.agentType,
      capabilities: [],
      workload: 0,
      lastSeen: new Date(),
    });
  }

  // Handle agent unregistration
  private handleAgentUnregistered(data: { agentId: string }): void {
    this.agentRegistry.delete(data.agentId);
  }

  // Handle agent status updates
  private handleAgentStatus(data: { agentId: string; status: string }): void {
    const agent = this.agentRegistry.get(data.agentId);
    if (agent) {
      agent.lastSeen = new Date();
    }
  }

  // Handle broadcast messages to update agent registry
  protected async handleBroadcast(message: any): Promise<void> {
    if (message.type === MessageType.AGENT_READY) {
      const { agentType, capabilities, status } = message.payload;
      this.agentRegistry.set(message.from, {
        type: agentType,
        capabilities,
        workload: status === 'BUSY' ? 1 : 0,
        lastSeen: new Date(),
      });
    } else if (message.type === MessageType.TASK_RESPONSE && message.from !== this.id) {
      // Decrease workload when task completes
      const agent = this.agentRegistry.get(message.from);
      if (agent && agent.workload > 0) {
        agent.workload--;
      }
    }
  }
}