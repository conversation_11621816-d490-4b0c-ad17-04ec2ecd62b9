const si = require('systeminformation');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { table } = require('table');
const chalk = require('chalk');
const winston = require('winston');

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: path.join(__dirname, '../results/resource-monitor.log') 
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration
const config = {
  apiUrl: process.env.API_BASE_URL || 'http://localhost:3000/api',
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  monitorInterval: parseInt(process.env.MONITOR_INTERVAL) || 5000,
  databaseUrl: process.env.DATABASE_URL,
  thresholds: {
    cpu: 80,
    memory: 85,
    disk: 90,
    responseTime: 1000,
    errorRate: 5
  }
};

// Metrics storage
const metrics = {
  samples: [],
  alerts: [],
  startTime: Date.now()
};

// Resource monitoring functions
async function getCPUMetrics() {
  const cpuData = await si.currentLoad();
  const cpuTemp = await si.cpuTemperature();
  
  return {
    usage: cpuData.currentLoad,
    cores: cpuData.cpus.map(cpu => ({
      core: cpu.core,
      usage: cpu.load
    })),
    temperature: cpuTemp.main || null,
    processes: cpuData.currentLoadSystem,
    idle: cpuData.currentLoadIdle
  };
}

async function getMemoryMetrics() {
  const mem = await si.mem();
  
  return {
    total: mem.total,
    used: mem.used,
    free: mem.free,
    active: mem.active,
    available: mem.available,
    usage: (mem.used / mem.total) * 100,
    swapUsed: mem.swapused,
    swapUsage: mem.swaptotal > 0 ? (mem.swapused / mem.swaptotal) * 100 : 0
  };
}

async function getDiskMetrics() {
  const disks = await si.fsSize();
  
  return disks.map(disk => ({
    fs: disk.fs,
    mount: disk.mount,
    total: disk.size,
    used: disk.used,
    available: disk.available,
    usage: disk.use
  }));
}

async function getNetworkMetrics() {
  const network = await si.networkStats();
  
  return network.map(iface => ({
    interface: iface.iface,
    rxBytes: iface.rx_bytes,
    txBytes: iface.tx_bytes,
    rxPerSec: iface.rx_sec,
    txPerSec: iface.tx_sec,
    rxErrors: iface.rx_errors,
    txErrors: iface.tx_errors
  }));
}

async function getProcessMetrics() {
  const processes = await si.processes();
  const topProcesses = processes.list
    .sort((a, b) => b.cpu - a.cpu)
    .slice(0, 10);
  
  return {
    total: processes.all,
    running: processes.running,
    blocked: processes.blocked,
    sleeping: processes.sleeping,
    top: topProcesses.map(p => ({
      pid: p.pid,
      name: p.name,
      cpu: p.cpu,
      memory: p.mem,
      state: p.state
    }))
  };
}

async function getApplicationMetrics() {
  const metrics = {
    api: {
      healthy: false,
      responseTime: null,
      activeConnections: null
    },
    database: {
      connected: false,
      activeConnections: null,
      poolSize: null
    },
    redis: {
      connected: false,
      memory: null,
      connections: null
    }
  };
  
  // Check API health
  try {
    const start = Date.now();
    const response = await axios.get(config.apiUrl.replace('/api', '/health'), {
      timeout: 5000
    });
    metrics.api.healthy = response.status === 200;
    metrics.api.responseTime = Date.now() - start;
    
    // Get API metrics if available
    if (response.data.metrics) {
      metrics.api.activeConnections = response.data.metrics.activeConnections;
    }
  } catch (error) {
    logger.error('API health check failed:', error.message);
  }
  
  // Check database metrics
  if (config.databaseUrl) {
    try {
      const dbMetricsResponse = await axios.get(`${config.apiUrl}/admin/database/metrics`, {
        timeout: 5000
      });
      if (dbMetricsResponse.data) {
        metrics.database.connected = true;
        metrics.database.activeConnections = dbMetricsResponse.data.activeConnections;
        metrics.database.poolSize = dbMetricsResponse.data.poolSize;
      }
    } catch (error) {
      logger.error('Database metrics fetch failed:', error.message);
    }
  }
  
  // Check Redis metrics
  try {
    const redisResponse = await axios.get(`${config.apiUrl}/admin/redis/info`, {
      timeout: 5000
    });
    if (redisResponse.data) {
      metrics.redis.connected = true;
      metrics.redis.memory = redisResponse.data.used_memory_human;
      metrics.redis.connections = redisResponse.data.connected_clients;
    }
  } catch (error) {
    logger.error('Redis metrics fetch failed:', error.message);
  }
  
  return metrics;
}

async function collectMetrics() {
  const timestamp = new Date();
  
  try {
    const [cpu, memory, disk, network, processes, app] = await Promise.all([
      getCPUMetrics(),
      getMemoryMetrics(),
      getDiskMetrics(),
      getNetworkMetrics(),
      getProcessMetrics(),
      getApplicationMetrics()
    ]);
    
    const sample = {
      timestamp,
      cpu,
      memory,
      disk,
      network,
      processes,
      application: app
    };
    
    metrics.samples.push(sample);
    
    // Keep only last hour of samples
    const oneHourAgo = Date.now() - 3600000;
    metrics.samples = metrics.samples.filter(s => 
      new Date(s.timestamp).getTime() > oneHourAgo
    );
    
    // Check thresholds and generate alerts
    checkThresholds(sample);
    
    // Display current metrics
    displayMetrics(sample);
    
    // Log metrics
    logger.info('Metrics collected', { sample });
    
  } catch (error) {
    logger.error('Error collecting metrics:', error);
  }
}

function checkThresholds(sample) {
  const alerts = [];
  
  // CPU threshold
  if (sample.cpu.usage > config.thresholds.cpu) {
    alerts.push({
      level: 'warning',
      metric: 'CPU',
      value: sample.cpu.usage.toFixed(2),
      threshold: config.thresholds.cpu,
      message: `CPU usage ${sample.cpu.usage.toFixed(2)}% exceeds threshold of ${config.thresholds.cpu}%`
    });
  }
  
  // Memory threshold
  if (sample.memory.usage > config.thresholds.memory) {
    alerts.push({
      level: 'warning',
      metric: 'Memory',
      value: sample.memory.usage.toFixed(2),
      threshold: config.thresholds.memory,
      message: `Memory usage ${sample.memory.usage.toFixed(2)}% exceeds threshold of ${config.thresholds.memory}%`
    });
  }
  
  // Disk threshold
  sample.disk.forEach(disk => {
    if (disk.usage > config.thresholds.disk) {
      alerts.push({
        level: 'warning',
        metric: 'Disk',
        value: disk.usage.toFixed(2),
        threshold: config.thresholds.disk,
        message: `Disk ${disk.mount} usage ${disk.usage.toFixed(2)}% exceeds threshold of ${config.thresholds.disk}%`
      });
    }
  });
  
  // API response time
  if (sample.application.api.responseTime > config.thresholds.responseTime) {
    alerts.push({
      level: 'warning',
      metric: 'API Response Time',
      value: sample.application.api.responseTime,
      threshold: config.thresholds.responseTime,
      message: `API response time ${sample.application.api.responseTime}ms exceeds threshold of ${config.thresholds.responseTime}ms`
    });
  }
  
  // Process alerts
  if (alerts.length > 0) {
    alerts.forEach(alert => {
      logger.warn('Threshold exceeded', alert);
      metrics.alerts.push({ ...alert, timestamp: sample.timestamp });
    });
  }
}

function displayMetrics(sample) {
  console.clear();
  
  console.log(chalk.cyan.bold('\\n=== SYSTEM RESOURCE MONITOR ==='));
  console.log(chalk.gray(`Time: ${sample.timestamp.toLocaleString()}`));
  console.log(chalk.gray(`Uptime: ${formatDuration(Date.now() - metrics.startTime)}`));
  
  // CPU Table
  console.log(chalk.yellow.bold('\\nCPU Metrics:'));
  const cpuData = [
    ['Metric', 'Value'],
    ['Overall Usage', `${sample.cpu.usage.toFixed(2)}%`],
    ['System Load', `${sample.cpu.processes.toFixed(2)}%`],
    ['Temperature', sample.cpu.temperature ? `${sample.cpu.temperature}°C` : 'N/A']
  ];
  console.log(table(cpuData, { singleLine: true }));
  
  // Memory Table
  console.log(chalk.green.bold('\\nMemory Metrics:'));
  const memData = [
    ['Metric', 'Value'],
    ['Total', formatBytes(sample.memory.total)],
    ['Used', `${formatBytes(sample.memory.used)} (${sample.memory.usage.toFixed(2)}%)`],
    ['Available', formatBytes(sample.memory.available)],
    ['Swap Used', `${formatBytes(sample.memory.swapUsed)} (${sample.memory.swapUsage.toFixed(2)}%)`]
  ];
  console.log(table(memData, { singleLine: true }));
  
  // Application Metrics
  console.log(chalk.blue.bold('\\nApplication Metrics:'));
  const appData = [
    ['Service', 'Status', 'Details'],
    [
      'API', 
      sample.application.api.healthy ? chalk.green('✓ Healthy') : chalk.red('✗ Down'),
      `Response: ${sample.application.api.responseTime || 'N/A'}ms`
    ],
    [
      'Database', 
      sample.application.database.connected ? chalk.green('✓ Connected') : chalk.red('✗ Disconnected'),
      `Connections: ${sample.application.database.activeConnections || 'N/A'}/${sample.application.database.poolSize || 'N/A'}`
    ],
    [
      'Redis', 
      sample.application.redis.connected ? chalk.green('✓ Connected') : chalk.red('✗ Disconnected'),
      `Memory: ${sample.application.redis.memory || 'N/A'}, Clients: ${sample.application.redis.connections || 'N/A'}`
    ]
  ];
  console.log(table(appData, { singleLine: true }));
  
  // Recent Alerts
  if (metrics.alerts.length > 0) {
    console.log(chalk.red.bold('\\nRecent Alerts:'));
    const recentAlerts = metrics.alerts.slice(-5);
    recentAlerts.forEach(alert => {
      console.log(chalk.red(`  • ${alert.message}`));
    });
  }
  
  // Top Processes
  console.log(chalk.magenta.bold('\\nTop Processes (by CPU):'));
  const processData = [
    ['PID', 'Name', 'CPU %', 'Memory %']
  ];
  sample.processes.top.slice(0, 5).forEach(p => {
    processData.push([
      p.pid,
      p.name.substring(0, 20),
      `${p.cpu.toFixed(2)}%`,
      `${p.memory.toFixed(2)}%`
    ]);
  });
  console.log(table(processData, { singleLine: true }));
}

// Helper functions
function formatBytes(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
}

function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days}d ${hours % 24}h`;
  if (hours > 0) return `${hours}h ${minutes % 60}m`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
}

// Export metrics function
async function exportMetrics() {
  const exportPath = path.join(__dirname, '../results', `resource-metrics-${Date.now()}.json`);
  
  const exportData = {
    startTime: new Date(metrics.startTime).toISOString(),
    endTime: new Date().toISOString(),
    duration: Date.now() - metrics.startTime,
    samples: metrics.samples,
    alerts: metrics.alerts,
    summary: calculateSummary()
  };
  
  fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
  logger.info(`Metrics exported to ${exportPath}`);
}

function calculateSummary() {
  if (metrics.samples.length === 0) return null;
  
  const cpuValues = metrics.samples.map(s => s.cpu.usage);
  const memoryValues = metrics.samples.map(s => s.memory.usage);
  const responseTimeValues = metrics.samples
    .map(s => s.application.api.responseTime)
    .filter(v => v !== null);
  
  return {
    cpu: {
      avg: average(cpuValues),
      max: Math.max(...cpuValues),
      min: Math.min(...cpuValues)
    },
    memory: {
      avg: average(memoryValues),
      max: Math.max(...memoryValues),
      min: Math.min(...memoryValues)
    },
    responseTime: {
      avg: average(responseTimeValues),
      max: Math.max(...responseTimeValues),
      min: Math.min(...responseTimeValues)
    },
    totalAlerts: metrics.alerts.length
  };
}

function average(arr) {
  return arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
}

// Main monitoring loop
async function startMonitoring() {
  logger.info('Starting resource monitoring...');
  logger.info(`Monitoring interval: ${config.monitorInterval}ms`);
  
  // Initial collection
  await collectMetrics();
  
  // Set up periodic collection
  const interval = setInterval(collectMetrics, config.monitorInterval);
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    logger.info('\\nShutting down monitor...');
    clearInterval(interval);
    await exportMetrics();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    clearInterval(interval);
    await exportMetrics();
    process.exit(0);
  });
}

// Start monitoring
if (require.main === module) {
  startMonitoring();
}

module.exports = {
  collectMetrics,
  exportMetrics,
  metrics
};