import { api } from './api';

export interface ShortCircuitInput {
  utilityVoltage: number;
  utilityFaultCurrent: number;
  utilityXRRatio: number;
  transformerKva?: number;
  transformerImpedance?: number;
  transformerXRRatio?: number;
  transformerPrimaryV?: number;
  transformerSecondaryV?: number;
  transformerType?: string;
  conductorLength: number;
  conductorSize: string;
  conductorMaterial: string;
  conductorType: string;
  conductorsPerPhase: number;
  conduitType: string;
  includeMotorContribution: boolean;
  motorHpTotal?: number;
  motorContributionMultiplier?: number;
}

export interface ShortCircuitCalculation {
  id: string;
  panel_id: string;
  calculation_method: string;
  utility_voltage: number;
  utility_fault_current: number;
  utility_x_r_ratio: number;
  symmetrical_fault_3ph: number;
  symmetrical_fault_lg: number;
  symmetrical_fault_ll: number;
  asymmetrical_fault_3ph: number;
  peak_fault_current: number;
  total_x_r_ratio: number;
  bus_bracing_adequate: boolean;
  main_breaker_adequate: boolean;
  branch_breaker_adequate: boolean;
  calculation_notes: string;
  assumptions: string;
  calculated_by: string;
  calculation_date: string;
  panel?: {
    id: string;
    name: string;
    location: string;
    voltage_system: string;
    ampere_rating: number;
    bus_rating: number;
    main_breaker_size: number | null;
  };
}

export interface PanelRequiringAnalysis {
  id: string;
  name: string;
  location: string;
  voltage_system: string;
  ampere_rating: number;
  hasCalculation: boolean;
  lastCalculationDate: string | null;
  calculationExpired: boolean;
}

export interface BreakerRecommendation {
  manufacturer: string;
  model: string;
  rating: number;
  interruptingCapacity: number;
  timeCurrentCurve: string;
  price?: number;
}

export interface SeriesRating {
  upstreamBreaker: string;
  downstreamBreaker: string;
  combinationRating: number;
  approved: boolean;
}

export interface EquipmentRecommendations {
  mainBreaker: BreakerRecommendation[];
  branchBreakers: BreakerRecommendation[];
  seriesRatings: SeriesRating[];
}

class ShortCircuitService {
  /**
   * Perform a new short circuit calculation
   */
  async calculate(panelId: string, input: ShortCircuitInput): Promise<ShortCircuitCalculation> {
    const response = await api.post('/short-circuit/calculate', {
      panelId,
      ...input,
    });
    return response.data;
  }
  
  /**
   * Get all calculations for a panel
   */
  async getCalculationsByPanel(panelId: string): Promise<ShortCircuitCalculation[]> {
    const response = await api.get(`/short-circuit/panel/${panelId}`);
    return response.data;
  }
  
  /**
   * Get the latest calculation for a panel
   */
  async getLatestCalculation(panelId: string): Promise<ShortCircuitCalculation | null> {
    const response = await api.get(`/short-circuit/panel/${panelId}/latest`);
    return response.data;
  }
  
  /**
   * Get a specific calculation by ID
   */
  async getCalculationById(calculationId: string): Promise<ShortCircuitCalculation> {
    const response = await api.get(`/short-circuit/${calculationId}`);
    return response.data;
  }
  
  /**
   * Get all panels in a project that need short circuit analysis
   */
  async getPanelsRequiringAnalysis(projectId: string): Promise<PanelRequiringAnalysis[]> {
    const response = await api.get(`/short-circuit/project/${projectId}/required`);
    return response.data;
  }
  
  /**
   * Get equipment recommendations based on fault current
   */
  async getEquipmentRecommendations(calculationId: string): Promise<EquipmentRecommendations> {
    const response = await api.get(`/short-circuit/${calculationId}/equipment`);
    return response.data;
  }
  
  /**
   * Generate report data for a calculation
   */
  async generateReport(calculationId: string): Promise<{
    calculation: ShortCircuitCalculation;
    summary: {
      adequacyStatus: 'adequate' | 'inadequate';
      criticalFindings: string[];
      recommendations: string[];
    };
    chartData: {
      faultCurrentComparison: Array<{ phase: string; fault: number; rating: number }>;
      xrRatioBreakdown: Array<{ component: string; xrRatio: number }>;
    };
  }> {
    const response = await api.get(`/short-circuit/${calculationId}/report`);
    return response.data;
  }
  
  /**
   * Mark a calculation as reviewed
   */
  async markAsReviewed(calculationId: string): Promise<ShortCircuitCalculation> {
    const response = await api.put(`/short-circuit/${calculationId}/review`);
    return response.data;
  }
  
  /**
   * Delete a calculation (admin only)
   */
  async deleteCalculation(calculationId: string): Promise<void> {
    await api.delete(`/short-circuit/${calculationId}`);
  }
  
  /**
   * Validate input parameters without saving
   */
  async validateInput(input: ShortCircuitInput): Promise<{ valid: boolean; data?: any; error?: string; details?: any[] }> {
    const response = await api.post('/short-circuit/validate', input);
    return response.data;
  }
}

export const shortCircuitService = new ShortCircuitService();