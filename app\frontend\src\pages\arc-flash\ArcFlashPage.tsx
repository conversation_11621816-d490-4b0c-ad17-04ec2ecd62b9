import React from 'react';
import { useParams } from 'react-router-dom';
import { ArcFlashCalculator } from '../../components/arc-flash/ArcFlashCalculator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { Zap, AlertTriangle } from 'lucide-react';

export const ArcFlashPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  
  if (!projectId) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No project selected. Please select a project from the projects page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Zap className="h-8 w-8" />
          Arc Flash Analysis
        </h1>
        <p className="text-muted-foreground mt-2">
          Perform IEEE 1584-2018 compliant arc flash hazard calculations
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <ArcFlashCalculator projectId={projectId} />
        </div>
        
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>About Arc Flash Analysis</CardTitle>
              <CardDescription>IEEE 1584-2018 Standard</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-semibold">Purpose</h4>
                <p className="text-muted-foreground">
                  Arc flash analysis determines the incident energy and arc flash boundary
                  to ensure worker safety when working on or near energized equipment.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold">Key Outputs</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Incident energy levels (cal/cm²)</li>
                  <li>Arc flash boundary distance</li>
                  <li>PPE category requirements</li>
                  <li>Approach boundaries</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold">Validity Period</h4>
                <p className="text-muted-foreground">
                  Arc flash studies should be updated every 5 years or when major
                  system changes occur.
                </p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>PPE Categories</CardTitle>
              <CardDescription>Per NFPA 70E</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="font-semibold">Category 1:</span>
                <span>≤ 4 cal/cm²</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Category 2:</span>
                <span>≤ 8 cal/cm²</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Category 3:</span>
                <span>≤ 25 cal/cm²</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Category 4:</span>
                <span>≤ 40 cal/cm²</span>
              </div>
              <div className="pt-2 border-t">
                <p className="text-destructive font-semibold">
                  {'>'} 40 cal/cm²: No safe PPE exists
                </p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Safety Warning</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Arc flash calculations must be performed by qualified personnel.
                  Always verify results and follow NFPA 70E safety procedures.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};