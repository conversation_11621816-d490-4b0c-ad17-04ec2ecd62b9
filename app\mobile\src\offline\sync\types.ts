export interface SyncOptions {
  automatic?: boolean;
  pushOnly?: boolean;
  pullOnly?: boolean;
  forceOffline?: boolean;
  wifiOnly?: boolean;
  conflictStrategy?: ConflictStrategy;
  maxRetries?: number;
  limit?: number;
  filter?: {
    entityType?: string;
    entityId?: string;
    since?: Date;
  };
}

export interface SyncResult {
  success: boolean;
  syncedItems?: number;
  failedItems?: number;
  conflicts?: ConflictData[];
  duration?: number;
  error?: string;
}

export interface SyncStatus {
  isSyncing: boolean;
  progress: number;
  message: string;
  error?: string;
  lastSyncTime?: number;
}

export interface SyncQueueItem {
  entityType: string;
  entityId: string;
  action: 'create' | 'update' | 'delete';
  priority?: number;
  metadata?: Record<string, any>;
}

export type ConflictStrategy = 
  | 'last-write-wins' 
  | 'first-write-wins' 
  | 'manual' 
  | 'merge';

export interface ConflictData {
  entityType: string;
  entityId: string;
  localData: any;
  remoteData: any;
  strategy?: ConflictStrategy;
}

export interface ConflictResolution {
  action: 'use-local' | 'use-remote' | 'retry' | 'defer';
  data?: any;
  conflictId?: string;
}

export interface StorageStats {
  totalSpace: number;
  usedSpace: number;
  freeSpace: number;
  cacheSize: number;
  databaseSize: number;
  photosSize: number;
}