import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { api } from '../../services/api';
import { AlertTriangle, Shield, Zap, FileText, CheckCircle } from 'lucide-react';

interface Panel {
  id: string;
  name: string;
  voltage_system: string;
  location: string;
}

interface ArcFlashResult {
  arcingCurrentMin: number;
  arcingCurrentMax: number;
  incidentEnergyMin: number;
  incidentEnergyMax: number;
  arcFlashBoundary: number;
  ppeCategory: number;
  ppeMinArcRating: number;
  shockHazardVoltage: number;
  limitedApproach: number;
  restrictedApproach: number;
  enclosureCorrection: number;
  reducedArcingCurrent?: number;
  reducedIncidentEnergy?: number;
  hazardRiskCategory: number;
  recommendedPpe: string[];
  safetyNotes: string[];
}

interface ArcFlashCalculatorProps {
  projectId: string;
}

export const ArcFlashCalculator: React.FC<ArcFlashCalculatorProps> = ({ projectId }) => {
  const [panels, setPanels] = useState<Panel[]>([]);
  const [selectedPanel, setSelectedPanel] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ArcFlashResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    systemType: 'THREE_PHASE',
    groundingType: 'SOLIDLY_GROUNDED',
    frequency: 60,
    boltedFaultCurrent: '',
    faultClearingTime: '',
    equipmentType: 'PANELBOARD',
    electrodeConfiguration: 'VCB',
    enclosureWidth: '',
    enclosureHeight: '',
    enclosureDepth: '',
    conductorGap: '',
    workingDistance: '18', // Default 18 inches
  });
  
  // Load panels for project
  useEffect(() => {
    loadPanels();
  }, [projectId]);
  
  const loadPanels = async () => {
    try {
      const response = await api.get(`/panels/project/${projectId}`);
      setPanels(response.data.data);
    } catch (err) {
      console.error('Failed to load panels:', err);
    }
  };
  
  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  const getSystemVoltage = (voltageSystem: string): number => {
    const voltageMap: Record<string, number> = {
      '120/240V_1PH': 240,
      '208V_3PH': 208,
      '240V_3PH': 240,
      '480V_3PH': 480,
      '277/480V_3PH': 480,
    };
    return voltageMap[voltageSystem] || 240;
  };
  
  const handleCalculate = async () => {
    if (!selectedPanel) {
      setError('Please select a panel');
      return;
    }
    
    const panel = panels.find(p => p.id === selectedPanel);
    if (!panel) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const payload = {
        panelId: selectedPanel,
        systemVoltage: getSystemVoltage(panel.voltage_system),
        systemType: formData.systemType,
        groundingType: formData.groundingType,
        frequency: formData.frequency,
        boltedFaultCurrent: parseFloat(formData.boltedFaultCurrent),
        faultClearingTime: parseFloat(formData.faultClearingTime),
        equipmentType: formData.equipmentType,
        electrodeConfiguration: formData.electrodeConfiguration,
        enclosureWidth: parseFloat(formData.enclosureWidth),
        enclosureHeight: parseFloat(formData.enclosureHeight),
        enclosureDepth: parseFloat(formData.enclosureDepth),
        conductorGap: parseFloat(formData.conductorGap),
        workingDistance: parseFloat(formData.workingDistance),
      };
      
      const response = await api.post('/arc-flash/calculate', payload);
      setResult(response.data.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Calculation failed');
    } finally {
      setLoading(false);
    }
  };
  
  const getPPECategoryColor = (category: number): string => {
    switch (category) {
      case 0: return 'bg-green-500';
      case 1: return 'bg-yellow-500';
      case 2: return 'bg-orange-500';
      case 3: return 'bg-red-500';
      case 4: return 'bg-red-700';
      default: return 'bg-black';
    }
  };
  
  const getPPECategoryText = (category: number): string => {
    switch (category) {
      case 0: return 'Minimal PPE';
      case 1: return 'Category 1';
      case 2: return 'Category 2';
      case 3: return 'Category 3';
      case 4: return 'Category 4';
      default: return 'DANGER - No Safe PPE';
    }
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Arc Flash Calculator (IEEE 1584-2018)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Panel Selection */}
            <div className="md:col-span-2">
              <Label htmlFor="panel">Select Panel</Label>
              <Select value={selectedPanel} onValueChange={setSelectedPanel}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a panel" />
                </SelectTrigger>
                <SelectContent>
                  {panels.map(panel => (
                    <SelectItem key={panel.id} value={panel.id}>
                      {panel.name} - {panel.location} ({panel.voltage_system})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* System Configuration */}
            <div>
              <Label htmlFor="systemType">System Type</Label>
              <Select
                value={formData.systemType}
                onValueChange={(value) => handleInputChange('systemType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="THREE_PHASE">Three Phase</SelectItem>
                  <SelectItem value="SINGLE_PHASE">Single Phase</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="groundingType">Grounding Type</Label>
              <Select
                value={formData.groundingType}
                onValueChange={(value) => handleInputChange('groundingType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SOLIDLY_GROUNDED">Solidly Grounded</SelectItem>
                  <SelectItem value="RESISTANCE_GROUNDED">Resistance Grounded</SelectItem>
                  <SelectItem value="UNGROUNDED">Ungrounded</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Fault Current Data */}
            <div>
              <Label htmlFor="boltedFaultCurrent">
                Bolted Fault Current (A)
              </Label>
              <Input
                type="number"
                value={formData.boltedFaultCurrent}
                onChange={(e) => handleInputChange('boltedFaultCurrent', e.target.value)}
                placeholder="e.g., 20000"
              />
            </div>
            
            <div>
              <Label htmlFor="faultClearingTime">
                Fault Clearing Time (s)
              </Label>
              <Input
                type="number"
                step="0.01"
                value={formData.faultClearingTime}
                onChange={(e) => handleInputChange('faultClearingTime', e.target.value)}
                placeholder="e.g., 0.05"
              />
            </div>
            
            {/* Equipment Configuration */}
            <div>
              <Label htmlFor="equipmentType">Equipment Type</Label>
              <Select
                value={formData.equipmentType}
                onValueChange={(value) => handleInputChange('equipmentType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SWITCHGEAR">Switchgear</SelectItem>
                  <SelectItem value="PANELBOARD">Panelboard</SelectItem>
                  <SelectItem value="MCC">Motor Control Center</SelectItem>
                  <SelectItem value="CABLE">Cable</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="electrodeConfiguration">
                Electrode Configuration
              </Label>
              <Select
                value={formData.electrodeConfiguration}
                onValueChange={(value) => handleInputChange('electrodeConfiguration', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="VCB">VCB - Vertical in Box</SelectItem>
                  <SelectItem value="VCBB">VCBB - Vertical Terminated in Barrier</SelectItem>
                  <SelectItem value="HCB">HCB - Horizontal in Box</SelectItem>
                  <SelectItem value="VOA">VOA - Vertical Open Air</SelectItem>
                  <SelectItem value="HOA">HOA - Horizontal Open Air</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Enclosure Dimensions */}
            <div>
              <Label htmlFor="enclosureWidth">Enclosure Width (in)</Label>
              <Input
                type="number"
                value={formData.enclosureWidth}
                onChange={(e) => handleInputChange('enclosureWidth', e.target.value)}
                placeholder="e.g., 20"
              />
            </div>
            
            <div>
              <Label htmlFor="enclosureHeight">Enclosure Height (in)</Label>
              <Input
                type="number"
                value={formData.enclosureHeight}
                onChange={(e) => handleInputChange('enclosureHeight', e.target.value)}
                placeholder="e.g., 36"
              />
            </div>
            
            <div>
              <Label htmlFor="enclosureDepth">Enclosure Depth (in)</Label>
              <Input
                type="number"
                value={formData.enclosureDepth}
                onChange={(e) => handleInputChange('enclosureDepth', e.target.value)}
                placeholder="e.g., 8"
              />
            </div>
            
            <div>
              <Label htmlFor="conductorGap">Conductor Gap (in)</Label>
              <Input
                type="number"
                value={formData.conductorGap}
                onChange={(e) => handleInputChange('conductorGap', e.target.value)}
                placeholder="e.g., 1.25"
              />
            </div>
            
            {/* Working Distance */}
            <div className="md:col-span-2">
              <Label htmlFor="workingDistance">
                Working Distance (in) - Minimum 12"
              </Label>
              <Input
                type="number"
                value={formData.workingDistance}
                onChange={(e) => handleInputChange('workingDistance', e.target.value)}
                min="12"
                placeholder="e.g., 18"
              />
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="mt-6">
            <Button
              onClick={handleCalculate}
              disabled={loading || !selectedPanel}
              className="w-full"
            >
              {loading ? 'Calculating...' : 'Calculate Arc Flash'}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Arc Flash Analysis Results</span>
              <Badge className={`${getPPECategoryColor(result.ppeCategory)} text-white`}>
                {getPPECategoryText(result.ppeCategory)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="energy">Energy</TabsTrigger>
                <TabsTrigger value="ppe">PPE</TabsTrigger>
                <TabsTrigger value="safety">Safety</TabsTrigger>
              </TabsList>
              
              <TabsContent value="summary" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold">Critical Values</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Incident Energy:</span>
                        <span className="font-mono">
                          {Math.max(result.incidentEnergyMax, result.reducedIncidentEnergy || 0).toFixed(2)} cal/cm²
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Arc Flash Boundary:</span>
                        <span className="font-mono">
                          {(result.arcFlashBoundary / 25.4).toFixed(1)} inches
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>PPE Category:</span>
                        <span className="font-bold">{result.ppeCategory}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold">Approach Boundaries</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Limited:</span>
                        <span className="font-mono">
                          {(result.limitedApproach / 25.4).toFixed(0)} inches
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Restricted:</span>
                        <span className="font-mono">
                          {(result.restrictedApproach / 25.4).toFixed(0)} inches
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {result.hazardRiskCategory >= 4 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Extreme Hazard</AlertTitle>
                    <AlertDescription>
                      Incident energy exceeds safe working limits. De-energize equipment before work.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
              
              <TabsContent value="energy" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold">Arcing Current</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Minimum:</span>
                        <span className="font-mono">{result.arcingCurrentMin.toFixed(2)} kA</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Maximum:</span>
                        <span className="font-mono">{result.arcingCurrentMax.toFixed(2)} kA</span>
                      </div>
                      {result.reducedArcingCurrent && (
                        <div className="flex justify-between">
                          <span>Reduced (85%):</span>
                          <span className="font-mono">{result.reducedArcingCurrent.toFixed(2)} kA</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold">Incident Energy</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Minimum:</span>
                        <span className="font-mono">{result.incidentEnergyMin.toFixed(2)} cal/cm²</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Maximum:</span>
                        <span className="font-mono">{result.incidentEnergyMax.toFixed(2)} cal/cm²</span>
                      </div>
                      {result.reducedIncidentEnergy && (
                        <div className="flex justify-between">
                          <span>At Reduced Current:</span>
                          <span className="font-mono">{result.reducedIncidentEnergy.toFixed(2)} cal/cm²</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm">
                    <strong>Enclosure Correction Factor:</strong> {result.enclosureCorrection.toFixed(3)}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    This factor accounts for the effect of enclosure size on incident energy.
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="ppe" className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Required PPE (Category {result.ppeCategory})
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Minimum Arc Rating: {result.ppeMinArcRating.toFixed(1)} cal/cm²
                  </p>
                </div>
                
                <div className="space-y-2">
                  {result.recommendedPpe.map((item, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                      <span className="text-sm">{item}</span>
                    </div>
                  ))}
                </div>
                
                {result.ppeCategory >= 3 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>High Risk Work</AlertTitle>
                    <AlertDescription>
                      Energized work permit required. Ensure all PPE is properly maintained and tested.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
              
              <TabsContent value="safety" className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-semibold flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Safety Notes & Requirements
                  </h4>
                </div>
                
                <div className="space-y-2">
                  {result.safetyNotes.map((note, index) => (
                    <div key={index} className="p-3 bg-muted rounded-lg">
                      <p className="text-sm">{note}</p>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 p-4 border rounded-lg">
                  <h5 className="font-semibold mb-2">Label Information</h5>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Voltage: {result.shockHazardVoltage}V</div>
                    <div>Arc Flash Boundary: {(result.arcFlashBoundary / 25.4).toFixed(0)}"</div>
                    <div>Incident Energy: {Math.max(result.incidentEnergyMax, result.reducedIncidentEnergy || 0).toFixed(2)} cal/cm²</div>
                    <div>Working Distance: {formData.workingDistance}"</div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};