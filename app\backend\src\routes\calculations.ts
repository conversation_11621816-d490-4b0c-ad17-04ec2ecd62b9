import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../index';
import { authenticate, AuthRequest } from '../middleware/auth';
import { 
  LoadCalculationService,
  VoltageDropService,
  ConduitFillService,
  WireSizeService
} from '../services/calculations';
import { calculationQueue } from '../index';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Load calculation schema
const loadCalculationSchema = z.object({
  project_id: z.string().uuid().optional(),
  calculation_type: z.enum(['STANDARD', 'OPTIONAL', 'EXISTING']).default('STANDARD'),
  building_type: z.enum(['DWELLING', 'OFFICE', 'STORE', 'WAREHOUSE', 'GARAGE', 
                         'HOSPITAL', 'HOTEL', 'SCHOOL', 'RESTAURANT']),
  square_footage: z.number().positive(),
  small_appliance_circuits: z.number().int().min(2).default(2),
  laundry_circuit: z.boolean().default(true),
  appliances: z.array(z.object({
    name: z.string(),
    va: z.number().positive(),
    quantity: z.number().int().positive().default(1)
  })).optional(),
  heating_va: z.number().min(0).default(0),
  cooling_va: z.number().min(0).default(0),
  largest_motor_va: z.number().min(0).default(0),
  other_loads_va: z.number().min(0).default(0)
});

// Voltage drop calculation schema
const voltageDropSchema = z.object({
  project_id: z.string().uuid().optional(),
  circuit_name: z.string(),
  voltage: z.number().positive(),
  phase: z.enum(['1PH', '3PH']),
  amperage: z.number().positive(),
  distance: z.number().positive(), // One-way distance in feet
  conductor_size: z.string(),
  conductor_type: z.enum(['CU', 'AL']),
  conduit_type: z.enum(['STEEL', 'PVC', 'AL']),
  power_factor: z.number().min(0).max(1).default(0.9),
  ambient_temp_f: z.number().default(86) // 30°C
});

// Conduit fill calculation schema
const conduitFillSchema = z.object({
  conduit_type: z.enum(['EMT', 'RMC', 'PVC', 'LFMC', 'LFNC']),
  conduit_size: z.string(),
  conductors: z.array(z.object({
    size: z.string(),
    type: z.enum(['THHN', 'THWN', 'XHHW', 'NM', 'MC', 'USE']),
    quantity: z.number().int().positive()
  }))
});

// Wire size calculation schema
const wireSizeSchema = z.object({
  project_id: z.string().uuid().optional(),
  load_amps: z.number().positive(),
  voltage: z.number().positive(),
  phase: z.enum(['1PH', '3PH']),
  conductor_type: z.enum(['CU', 'AL']),
  ambient_temp_f: z.number().default(86),
  terminals_rated_75c: z.boolean().default(true),
  continuous_load: z.boolean().default(true),
  distance: z.number().positive().optional(),
  max_voltage_drop_percent: z.number().min(0).max(5).optional()
});

// Perform load calculation
router.post('/load', async (req: AuthRequest, res, next) => {
  try {
    const data = loadCalculationSchema.parse(req.body);
    
    // Perform calculation
    const service = new LoadCalculationService();
    const result = await service.calculate(data as any);
    
    // Log calculation
    await prisma.calculationLog.create({
      data: {
        calculation_type: 'LOAD_CALC',
        input_data: JSON.stringify(data),
        output_data: JSON.stringify(result),
        nec_references: JSON.stringify(result.necReferences),
        performed_by: req.user!.userId,
        project_id: data.project_id
      }
    });
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Perform voltage drop calculation
router.post('/voltage-drop', async (req: AuthRequest, res, next) => {
  try {
    const data = voltageDropSchema.parse(req.body);
    
    // Perform calculation
    const service = new VoltageDropService();
    const result = await service.calculate(data as any);
    
    // Log calculation
    await prisma.calculationLog.create({
      data: {
        calculation_type: 'VOLTAGE_DROP',
        input_data: JSON.stringify(data),
        output_data: JSON.stringify(result),
        nec_references: JSON.stringify(result.necReferences),
        performed_by: req.user!.userId,
        project_id: data.project_id
      }
    });
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Perform conduit fill calculation
router.post('/conduit-fill', async (req: AuthRequest, res, next) => {
  try {
    const data = conduitFillSchema.parse(req.body);
    
    // Perform calculation
    const service = new ConduitFillService();
    const result = await service.calculate(data as any);
    
    // Log calculation
    await prisma.calculationLog.create({
      data: {
        calculation_type: 'CONDUIT_FILL',
        input_data: JSON.stringify(data),
        output_data: JSON.stringify(result),
        nec_references: JSON.stringify(result.necReferences),
        performed_by: req.user!.userId
      }
    });
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Calculate wire size
router.post('/wire-size', async (req: AuthRequest, res, next) => {
  try {
    const data = wireSizeSchema.parse(req.body);
    
    // Perform calculation
    const service = new WireSizeService();
    const result = await service.calculate(data as any);
    
    // Log calculation
    await prisma.calculationLog.create({
      data: {
        calculation_type: 'WIRE_SIZE',
        input_data: JSON.stringify(data),
        output_data: JSON.stringify(result),
        nec_references: JSON.stringify(result.necReferences),
        performed_by: req.user!.userId,
        project_id: data.project_id
      }
    });
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Get calculation history
router.get('/history', async (req: AuthRequest, res, next) => {
  try {
    const querySchema = z.object({
      project_id: z.string().uuid().optional(),
      calculation_type: z.enum(['LOAD_CALC', 'VOLTAGE_DROP', 'CONDUIT_FILL', 
                                'WIRE_SIZE', 'BREAKER_SIZE', 'GROUNDING']).optional(),
      page: z.string().transform(Number).default('1'),
      limit: z.string().transform(Number).default('20')
    });
    
    const params = querySchema.parse(req.query);
    const skip = (params.page - 1) * params.limit;
    
    const where = {
      ...(params.project_id && { project_id: params.project_id }),
      ...(params.calculation_type && { calculation_type: params.calculation_type })
    };
    
    const [calculations, total] = await Promise.all([
      prisma.calculationLog.findMany({
        where,
        skip,
        take: params.limit,
        orderBy: { created_at: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          project: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }),
      prisma.calculationLog.count({ where })
    ]);
    
    res.json({
      data: calculations,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// Queue complex calculation
router.post('/queue', async (req: AuthRequest, res, next) => {
  try {
    const queueSchema = z.object({
      calculation_type: z.enum(['PANEL_SCHEDULE', 'SHORT_CIRCUIT', 'ARC_FLASH']),
      project_id: z.string().uuid(),
      parameters: z.record(z.any())
    });
    
    const data = queueSchema.parse(req.body);
    
    // Queue calculation job
    const job = await calculationQueue.add(data.calculation_type, {
      ...data,
      userId: req.user!.userId
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      }
    });
    
    res.json({
      message: 'Calculation queued',
      jobId: job.id,
      status: 'pending'
    });
  } catch (error) {
    next(error);
  }
});

// Get job status
router.get('/queue/:jobId', async (req: AuthRequest, res, next) => {
  try {
    const job = await calculationQueue.getJob(req.params.jobId);
    
    if (!job) {
      res.status(404).json({ error: 'Job not found' });
      return;
    }
    
    const state = await job.getState();
    const progress = job.progress;
    
    res.json({
      jobId: job.id,
      state,
      progress,
      data: job.data,
      returnValue: job.returnvalue,
      failedReason: job.failedReason
    });
  } catch (error) {
    next(error);
  }
});

export { router as calculationsRouter };