import axios from 'axios';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, subDays, subMonths } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

interface DateRange {
  start: Date;
  end: Date;
}

interface RevenueData {
  date: string;
  revenue: number;
  profit: number;
  margin: number;
  projectCount: number;
}

interface ProjectAnalytics {
  projectType: string;
  count: number;
  revenue: number;
  avgDuration: number;
  profitMargin: number;
}

interface LaborAnalytics {
  employeeId: string;
  employeeName: string;
  hoursWorked: number;
  regularHours: number;
  overtimeHours: number;
  productivity: number;
  costPerHour: number;
  totalCost: number;
}

interface MaterialAnalytics {
  materialId: string;
  materialName: string;
  category: string;
  totalUsed: number;
  totalCost: number;
  avgPrice: number;
  priceVariance: number;
  vendors: VendorPerformance[];
}

interface VendorPerformance {
  vendorName: string;
  orderCount: number;
  avgPrice: number;
  deliveryPerformance: number;
  qualityScore: number;
}

interface CalculationAnalytics {
  calculationType: string;
  count: number;
  avgCompletionTime: number;
  successRate: number;
  commonIssues: string[];
}

interface FinancialMetrics {
  cashFlow: number;
  accountsReceivable: number;
  accountsPayable: number;
  overdueInvoices: number;
  avgPaymentDays: number;
  grossMargin: number;
  netMargin: number;
}

interface PredictiveAnalytics {
  projectCompletionPredictions: ProjectPrediction[];
  materialPriceForecast: MaterialPriceForecast[];
  demandPatterns: DemandPattern[];
  riskScores: RiskAssessment[];
  churnPredictions: CustomerChurnPrediction[];
}

interface ProjectPrediction {
  projectId: string;
  projectName: string;
  predictedCompletion: Date;
  confidence: number;
  riskFactors: string[];
}

interface MaterialPriceForecast {
  materialId: string;
  materialName: string;
  currentPrice: number;
  predictedPrice30Days: number;
  predictedPrice90Days: number;
  trend: 'up' | 'down' | 'stable';
}

interface DemandPattern {
  period: string;
  demandLevel: number;
  projectTypes: string[];
  confidence: number;
}

interface RiskAssessment {
  category: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigationSteps: string[];
}

interface CustomerChurnPrediction {
  customerId: string;
  customerName: string;
  churnProbability: number;
  riskFactors: string[];
  lastProjectDate: Date;
  totalRevenue: number;
}

interface KPIMetrics {
  revenue: number;
  revenueGrowth: number;
  grossMargin: number;
  netMargin: number;
  customerAcquisitionCost: number;
  customerLifetimeValue: number;
  projectSuccessRate: number;
  avgProjectDuration: number;
  employeeUtilization: number;
  safetyIncidentRate: number;
}

class AnalyticsService {
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(endpoint: string, params?: unknown): string {
    return `${endpoint}:${JSON.stringify(params || {})}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data as T;
    }
    return null;
  }

  private setCache(key: string, data: unknown): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  async getKPIMetrics(): Promise<KPIMetrics> {
    const cacheKey = this.getCacheKey('kpi-metrics');
    const cached = this.getFromCache<KPIMetrics>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/kpi`);
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getRevenueAnalytics(period: 'daily' | 'weekly' | 'monthly' | 'yearly', range?: DateRange): Promise<RevenueData[]> {
    const params = {
      period,
      startDate: range?.start.toISOString(),
      endDate: range?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('revenue-analytics', params);
    const cached = this.getFromCache<RevenueData[]>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/revenue`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getProjectAnalytics(filters?: {
    projectType?: string;
    status?: string;
    dateRange?: DateRange;
  }): Promise<ProjectAnalytics[]> {
    const params = {
      ...filters,
      startDate: filters?.dateRange?.start.toISOString(),
      endDate: filters?.dateRange?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('project-analytics', params);
    const cached = this.getFromCache<ProjectAnalytics[]>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/projects`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getLaborAnalytics(dateRange?: DateRange): Promise<LaborAnalytics[]> {
    const params = {
      startDate: dateRange?.start.toISOString(),
      endDate: dateRange?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('labor-analytics', params);
    const cached = this.getFromCache<LaborAnalytics[]>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/labor`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getMaterialAnalytics(filters?: {
    category?: string;
    vendorId?: string;
    dateRange?: DateRange;
  }): Promise<MaterialAnalytics[]> {
    const params = {
      ...filters,
      startDate: filters?.dateRange?.start.toISOString(),
      endDate: filters?.dateRange?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('material-analytics', params);
    const cached = this.getFromCache<MaterialAnalytics[]>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/materials`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getCalculationAnalytics(dateRange?: DateRange): Promise<CalculationAnalytics[]> {
    const params = {
      startDate: dateRange?.start.toISOString(),
      endDate: dateRange?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('calculation-analytics', params);
    const cached = this.getFromCache<CalculationAnalytics[]>(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/calculations`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getFinancialMetrics(date?: Date): Promise<FinancialMetrics> {
    const params = {
      date: date?.toISOString() || new Date().toISOString(),
    };

    const cacheKey = this.getCacheKey('financial-metrics', params);
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/financial`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getPredictiveAnalytics(): Promise<PredictiveAnalytics> {
    const cacheKey = this.getCacheKey('predictive-analytics');
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/predictive`);
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getArcFlashAnalytics(dateRange?: DateRange): Promise<{
    totalCalculations: number;
    byPPECategory: { category: number; count: number; avgIncidentEnergy: number }[];
    byEquipmentType: { type: string; count: number; avgBoundary: number }[];
    safetyTrends: { date: string; incidents: number; highRiskCount: number }[];
  }> {
    const params = {
      startDate: dateRange?.start.toISOString(),
      endDate: dateRange?.end.toISOString(),
    };

    const cacheKey = this.getCacheKey('arc-flash-analytics', params);
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/arc-flash`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getLoadDistributionAnalytics(projectId?: string): Promise<{
    panelLoads: { panelId: string; panelName: string; loadPercentage: number; phaseBalance: number }[];
    loadByType: { loadType: string; totalLoad: number; percentage: number }[];
    peakDemandPatterns: { hour: number; avgLoad: number; peakLoad: number }[];
  }> {
    const params = { projectId };

    const cacheKey = this.getCacheKey('load-distribution', params);
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    const response = await axios.get(`${API_BASE_URL}/analytics/load-distribution`, { params });
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async exportAnalytics(
    reportType: string,
    format: 'pdf' | 'excel' | 'csv',
    filters?: {
      dateRange?: DateRange;
      projectType?: string;
      category?: string;
      vendorId?: string;
      projectId?: string;
    }
  ): Promise<Blob> {
    const response = await axios.post(
      `${API_BASE_URL}/analytics/export`,
      {
        reportType,
        format,
        filters,
      },
      {
        responseType: 'blob',
      }
    );
    return response.data;
  }

  // Utility methods for date ranges
  getDateRange(preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'last30' | 'last90'): DateRange {
    const now = new Date();
    switch (preset) {
      case 'today':
        return { start: startOfDay(now), end: endOfDay(now) };
      case 'week':
        return { start: startOfWeek(now), end: endOfWeek(now) };
      case 'month':
        return { start: startOfMonth(now), end: endOfMonth(now) };
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
        return { start: quarterStart, end: quarterEnd };
      case 'year':
        return { start: startOfYear(now), end: endOfYear(now) };
      case 'last30':
        return { start: subDays(now, 30), end: now };
      case 'last90':
        return { start: subDays(now, 90), end: now };
      default:
        return { start: startOfMonth(now), end: endOfMonth(now) };
    }
  }

  clearCache(): void {
    this.cache.clear();
  }
}

export const analyticsService = new AnalyticsService();
export type {
  DateRange,
  RevenueData,
  ProjectAnalytics,
  LaborAnalytics,
  MaterialAnalytics,
  VendorPerformance,
  CalculationAnalytics,
  FinancialMetrics,
  PredictiveAnalytics,
  ProjectPrediction,
  MaterialPriceForecast,
  DemandPattern,
  RiskAssessment,
  CustomerChurnPrediction,
  KPIMetrics,
};