{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": ["./tsconfig.json", "./*/tsconfig.json"]}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/strict-boolean-expressions": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/require-await": "error", "no-console": ["error", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "eqeqeq": ["error", "always"], "curly": ["error", "all"], "brace-style": ["error", "1tbs"], "no-throw-literal": "error", "no-return-await": "error", "require-await": "off"}, "overrides": [{"files": ["*.test.ts", "*.spec.ts"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off"}}], "ignorePatterns": ["dist/", "build/", "coverage/", "node_modules/"]}