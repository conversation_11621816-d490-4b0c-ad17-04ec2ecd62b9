import { Job } from 'bullmq';
import { prisma, io } from '../index';
import { Decimal } from 'decimal.js';

interface CalculationJobData {
  calculation_type: 'PANEL_SCHEDULE' | 'SHORT_CIRCUIT' | 'ARC_FLASH';
  project_id: string;
  parameters: Record<string, any>;
  userId: string;
}

export async function calculationWorker(job: Job<CalculationJobData>): Promise<any> {
  const { calculation_type, project_id, parameters, userId } = job.data;
  
  await job.updateProgress(0);
  
  let result: any;
  
  switch (calculation_type) {
    case 'PANEL_SCHEDULE':
      result = await calculatePanelSchedule(project_id, parameters, job);
      break;
    case 'SHORT_CIRCUIT':
      result = await calculateShortCircuit(project_id, parameters, job);
      break;
    case 'ARC_FLASH':
      result = await calculateArcFlash(project_id, parameters, job);
      break;
    default:
      throw new Error(`Unknown calculation type: ${calculation_type}`);
  }
  
  // Save calculation log
  await prisma.calculationLog.create({
    data: {
      calculation_type,
      input_data: JSON.stringify(parameters),
      output_data: JSON.stringify(result),
      nec_references: JSON.stringify(result.necReferences || []),
      performed_by: userId,
      project_id
    }
  });
  
  // Notify user
  io.to(`user:${userId}`).emit('calculation:complete', {
    jobId: job.id,
    calculation_type,
    project_id,
    result
  });
  
  return result;
}

async function calculatePanelSchedule(
  projectId: string, 
  parameters: any,
  job: Job
): Promise<any> {
  await job.updateProgress(10);
  
  // Get project electrical details
  const project = await prisma.project.findUnique({
    where: { id: projectId }
  });
  
  if (!project) {
    throw new Error('Project not found');
  }
  
  const panelData = parameters.panel || {};
  const circuits = parameters.circuits || [];
  
  await job.updateProgress(20);
  
  // Calculate loads for each circuit
  const circuitCalculations = [];
  let totalLoadA = new Decimal(0);
  let totalLoadB = new Decimal(0);
  let totalLoadC = new Decimal(0);
  
  for (let i = 0; i < circuits.length; i++) {
    const circuit = circuits[i];
    const load = new Decimal(circuit.load || 0);
    
    // Determine phase allocation (simplified)
    let phaseA = new Decimal(0);
    let phaseB = new Decimal(0);
    let phaseC = new Decimal(0);
    
    if (circuit.poles === 1) {
      // Single pole - alternate between phases
      if (i % 3 === 0) phaseA = load;
      else if (i % 3 === 1) phaseB = load;
      else phaseC = load;
    } else if (circuit.poles === 2) {
      // Double pole - split between two phases
      phaseA = load.dividedBy(2);
      phaseB = load.dividedBy(2);
    } else {
      // Three pole - split evenly
      phaseA = load.dividedBy(3);
      phaseB = load.dividedBy(3);
      phaseC = load.dividedBy(3);
    }
    
    totalLoadA = totalLoadA.plus(phaseA);
    totalLoadB = totalLoadB.plus(phaseB);
    totalLoadC = totalLoadC.plus(phaseC);
    
    circuitCalculations.push({
      circuit_number: circuit.number,
      description: circuit.description,
      load_va: load.toNumber(),
      breaker_size: circuit.breaker_size,
      poles: circuit.poles,
      phase_a_load: phaseA.toNumber(),
      phase_b_load: phaseB.toNumber(),
      phase_c_load: phaseC.toNumber()
    });
    
    await job.updateProgress(20 + (i / circuits.length) * 60);
  }
  
  // Calculate panel totals
  const totalLoad = totalLoadA.plus(totalLoadB).plus(totalLoadC);
  const averageLoad = totalLoad.dividedBy(3);
  const imbalance = Math.max(
    Math.abs(totalLoadA.minus(averageLoad).toNumber()),
    Math.abs(totalLoadB.minus(averageLoad).toNumber()),
    Math.abs(totalLoadC.minus(averageLoad).toNumber())
  );
  const imbalancePercent = new Decimal(imbalance).dividedBy(averageLoad).times(100);
  
  await job.updateProgress(90);
  
  const result = {
    panel_info: {
      name: panelData.name || 'Panel',
      voltage: project.voltage_system,
      main_breaker: panelData.main_breaker || project.service_size,
      spaces: panelData.spaces || 42
    },
    circuits: circuitCalculations,
    phase_loads: {
      phase_a: totalLoadA.toNumber(),
      phase_b: totalLoadB.toNumber(),
      phase_c: totalLoadC.toNumber(),
      total: totalLoad.toNumber()
    },
    load_balance: {
      average_load: averageLoad.toNumber(),
      max_imbalance: imbalance,
      imbalance_percent: imbalancePercent.toNumber()
    },
    utilization: {
      total_amps: totalLoad.dividedBy(
        project.voltage_system.includes('208') ? 208 : 
        project.voltage_system.includes('480') ? 480 : 240
      ).toNumber(),
      percent_used: totalLoad.dividedBy(project.service_size).times(100).toNumber()
    },
    necReferences: ['408.4', '408.30', '210.4(B)', '220.61']
  };
  
  await job.updateProgress(100);
  
  return result;
}

async function calculateShortCircuit(
  projectId: string,
  parameters: any,
  job: Job
): Promise<any> {
  // Simplified short circuit calculation
  await job.updateProgress(10);
  
  const transformerKva = new Decimal(parameters.transformer_kva || 500);
  const transformerImpedance = new Decimal(parameters.transformer_impedance || 0.05); // 5%
  const voltage = new Decimal(parameters.voltage || 480);
  const cableLength = new Decimal(parameters.cable_length || 100); // feet
  const cableSize = parameters.cable_size || '4/0';
  
  await job.updateProgress(30);
  
  // Calculate transformer fault current
  const transformerFla = transformerKva.times(1000).dividedBy(voltage.times(Math.sqrt(3)));
  const infiniteBusFault = transformerFla.dividedBy(transformerImpedance);
  
  await job.updateProgress(50);
  
  // Add cable impedance (simplified)
  const cableResistance = new Decimal(0.05); // ohms per 1000 ft (simplified)
  const cableReactance = new Decimal(0.04);
  const cableImpedance = Decimal.sqrt(
    cableResistance.pow(2).plus(cableReactance.pow(2))
  ).times(cableLength).dividedBy(1000);
  
  await job.updateProgress(70);
  
  // Calculate fault current at end of cable
  const totalImpedance = transformerImpedance.plus(
    cableImpedance.times(voltage).dividedBy(transformerKva.times(1000))
  );
  const faultCurrent = infiniteBusFault.times(transformerImpedance).dividedBy(totalImpedance);
  
  await job.updateProgress(90);
  
  const result = {
    transformer: {
      kva: transformerKva.toNumber(),
      impedance_percent: transformerImpedance.times(100).toNumber(),
      infinite_bus_fault: infiniteBusFault.toNumber()
    },
    cable: {
      length_ft: cableLength.toNumber(),
      size: cableSize,
      impedance: cableImpedance.toNumber()
    },
    fault_current: {
      symmetrical_rms: faultCurrent.toNumber(),
      asymmetrical_peak: faultCurrent.times(2.7).toNumber(), // X/R = 15 assumed
      x_r_ratio: 15
    },
    equipment_ratings: {
      recommended_aic: Math.ceil(faultCurrent.toNumber() / 1000) * 1000,
      minimum_aic: faultCurrent.times(1.25).toNumber()
    },
    necReferences: ['110.9', '110.10', '240.86']
  };
  
  await job.updateProgress(100);
  
  return result;
}

async function calculateArcFlash(
  projectId: string,
  parameters: any,
  job: Job
): Promise<any> {
  // Simplified arc flash calculation (IEEE 1584)
  await job.updateProgress(10);
  
  const voltage = new Decimal(parameters.voltage || 480);
  const faultCurrent = new Decimal(parameters.fault_current || 20000);
  const workingDistance = new Decimal(parameters.working_distance || 18); // inches
  const arcDuration = new Decimal(parameters.arc_duration || 0.1); // seconds
  const enclosureType = parameters.enclosure_type || 'PANEL'; // PANEL or OPEN
  
  await job.updateProgress(30);
  
  // Calculate arc current (simplified IEEE 1584)
  const k = enclosureType === 'PANEL' ? 0.85 : 1.0;
  const arcCurrent = faultCurrent.times(k);
  
  await job.updateProgress(50);
  
  // Calculate incident energy (cal/cm²)
  // Simplified calculation - real implementation would use full IEEE 1584
  const cf = voltage.lessThanOrEqualTo(1000) ? 1.0 : 1.5;
  const en = new Decimal(1.081).times(
    arcCurrent.pow(1.4).times(arcDuration).times(cf)
  ).dividedBy(workingDistance.pow(2));
  
  await job.updateProgress(70);
  
  // Determine PPE category
  let ppeCategory = 0;
  let ppeDescription = '';
  
  if (en.lessThanOrEqualTo(1.2)) {
    ppeCategory = 1;
    ppeDescription = 'Arc-rated clothing, min 4 cal/cm²';
  } else if (en.lessThanOrEqualTo(8)) {
    ppeCategory = 2;
    ppeDescription = 'Arc-rated clothing, min 8 cal/cm²';
  } else if (en.lessThanOrEqualTo(25)) {
    ppeCategory = 3;
    ppeDescription = 'Arc-rated clothing, min 25 cal/cm²';
  } else if (en.lessThanOrEqualTo(40)) {
    ppeCategory = 4;
    ppeDescription = 'Arc-rated clothing, min 40 cal/cm²';
  } else {
    ppeCategory = 5;
    ppeDescription = 'No safe PPE available - de-energize equipment';
  }
  
  // Calculate arc flash boundary
  const arcFlashBoundary = Decimal.sqrt(
    arcCurrent.times(arcDuration).times(610).dividedBy(1.2)
  );
  
  await job.updateProgress(90);
  
  const result = {
    input_parameters: {
      voltage: voltage.toNumber(),
      fault_current: faultCurrent.toNumber(),
      working_distance_in: workingDistance.toNumber(),
      arc_duration_sec: arcDuration.toNumber(),
      enclosure_type: enclosureType
    },
    arc_flash_results: {
      arc_current: arcCurrent.toNumber(),
      incident_energy_cal_cm2: en.toDecimalPlaces(2).toNumber(),
      arc_flash_boundary_in: arcFlashBoundary.toDecimalPlaces(1).toNumber(),
      ppe_category: ppeCategory,
      ppe_description: ppeDescription
    },
    label_info: {
      danger_level: en.greaterThan(40) ? 'DANGER' : 'WARNING',
      shock_hazard: `${voltage.toNumber()} Volts`,
      arc_flash_hazard: `${en.toDecimalPlaces(2).toNumber()} cal/cm²`,
      limited_approach: '42 inches',
      restricted_approach: '12 inches'
    },
    necReferences: ['110.16', '130.5', 'NFPA 70E Article 130']
  };
  
  await job.updateProgress(100);
  
  return result;
}