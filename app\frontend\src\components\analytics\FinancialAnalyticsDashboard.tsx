import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ComposedChart,
  Sankey,
  Treemap,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import type { FinancialMetrics } from '../../services/analyticsService';
import { format, subMonths } from 'date-fns';
import { DollarSign, TrendingUp, TrendingDown, AlertCircle, CreditCard, PiggyBank } from 'lucide-react';

interface FinancialAnalyticsDashboardProps {
  dateRange: { start: Date; end: Date };
}

const FinancialAnalyticsDashboard: React.FC<FinancialAnalyticsDashboardProps> = ({ dateRange }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');

  // Fetch financial metrics
  const { data: financialMetrics } = useQuery({
    queryKey: ['financial-metrics', dateRange],
    queryFn: () => analyticsService.getFinancialMetrics(dateRange.end),
  });

  // Fetch cash flow projections
  const { data: cashFlowData } = useQuery({
    queryKey: ['cash-flow-projections', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return Array.from({ length: 12 }, (_, i) => {
        const date = subMonths(new Date(), 11 - i);
        return {
          month: format(date, 'MMM'),
          inflow: Math.floor(Math.random() * 50000) + 150000,
          outflow: Math.floor(Math.random() * 40000) + 120000,
          netCashFlow: 0, // calculated below
          cumulativeCashFlow: 0, // calculated below
        };
      }).map((item, index, arr) => {
        item.netCashFlow = item.inflow - item.outflow;
        item.cumulativeCashFlow = arr.slice(0, index + 1).reduce((sum, m) => sum + m.netCashFlow, 0);
        return item;
      });
    },
  });

  // Fetch accounts receivable aging
  const { data: receivableAging } = useQuery({
    queryKey: ['receivable-aging', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { range: 'Current', amount: 185000, percentage: 45 },
        { range: '1-30 days', amount: 95000, percentage: 23 },
        { range: '31-60 days', amount: 68000, percentage: 17 },
        { range: '61-90 days', amount: 42000, percentage: 10 },
        { range: 'Over 90 days', amount: 20000, percentage: 5 },
      ];
    },
  });

  // Fetch budget vs actual analysis
  const { data: budgetVsActual } = useQuery({
    queryKey: ['budget-vs-actual', dateRange, selectedPeriod],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { category: 'Revenue', budgeted: 450000, actual: 468000, variance: 18000 },
        { category: 'Materials', budgeted: 180000, actual: 195000, variance: -15000 },
        { category: 'Labor', budgeted: 150000, actual: 142000, variance: 8000 },
        { category: 'Equipment', budgeted: 45000, actual: 48000, variance: -3000 },
        { category: 'Overhead', budgeted: 35000, actual: 32000, variance: 3000 },
        { category: 'Other', budgeted: 25000, actual: 28000, variance: -3000 },
      ];
    },
  });

  // Fetch invoice payment patterns
  const { data: paymentPatterns } = useQuery({
    queryKey: ['payment-patterns', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { dayRange: '0-15', count: 145, percentage: 35 },
        { dayRange: '16-30', count: 165, percentage: 40 },
        { dayRange: '31-45', count: 62, percentage: 15 },
        { dayRange: '46-60', count: 25, percentage: 6 },
        { dayRange: '60+', count: 16, percentage: 4 },
      ];
    },
  });

  // Fetch gross margin trends
  const { data: marginTrends } = useQuery({
    queryKey: ['margin-trends', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { month: 'Jan', grossMargin: 28.5, netMargin: 12.3, targetGross: 30, targetNet: 15 },
        { month: 'Feb', grossMargin: 29.2, netMargin: 13.1, targetGross: 30, targetNet: 15 },
        { month: 'Mar', grossMargin: 30.8, netMargin: 14.5, targetGross: 30, targetNet: 15 },
        { month: 'Apr', grossMargin: 31.2, netMargin: 15.2, targetGross: 30, targetNet: 15 },
        { month: 'May', grossMargin: 29.5, netMargin: 13.8, targetGross: 30, targetNet: 15 },
        { month: 'Jun', grossMargin: 30.5, netMargin: 14.9, targetGross: 30, targetNet: 15 },
      ];
    },
  });

  // Fetch expense breakdown
  const { data: expenseBreakdown } = useQuery({
    queryKey: ['expense-breakdown', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { name: 'Direct Costs', children: [
          { name: 'Materials', value: 195000 },
          { name: 'Labor', value: 142000 },
          { name: 'Subcontractors', value: 45000 },
        ]},
        { name: 'Indirect Costs', children: [
          { name: 'Equipment', value: 48000 },
          { name: 'Vehicles', value: 22000 },
          { name: 'Insurance', value: 18000 },
          { name: 'Office', value: 15000 },
        ]},
        { name: 'Administrative', children: [
          { name: 'Salaries', value: 65000 },
          { name: 'Marketing', value: 12000 },
          { name: 'Professional', value: 8000 },
          { name: 'Other', value: 10000 },
        ]},
      ];
    },
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const MetricCard = ({ title, value, change, icon: Icon, format: formatFn = (v: any) => v }: any) => {
    const isPositive = change >= 0;
    return (
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
              {formatFn(value)}
            </p>
            {change !== undefined && (
              <div className="mt-2 flex items-center">
                {isPositive ? (
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                  {isPositive ? '+' : ''}{formatPercentage(change)}
                </span>
                <span className="ml-2 text-sm text-gray-500">vs last period</span>
              </div>
            )}
          </div>
          <Icon className="h-8 w-8 text-gray-400" />
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Financial Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Cash Flow"
          value={financialMetrics?.cashFlow || 0}
          change={8.5}
          icon={DollarSign}
          format={formatCurrency}
        />
        <MetricCard
          title="Accounts Receivable"
          value={financialMetrics?.accountsReceivable || 0}
          change={-3.2}
          icon={CreditCard}
          format={formatCurrency}
        />
        <MetricCard
          title="Gross Margin"
          value={financialMetrics?.grossMargin || 0}
          change={2.1}
          icon={TrendingUp}
          format={formatPercentage}
        />
        <MetricCard
          title="Avg Payment Days"
          value={financialMetrics?.avgPaymentDays || 0}
          change={-5.8}
          icon={AlertCircle}
          format={(v: number) => `${v} days`}
        />
      </div>

      {/* Cash Flow Projections */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Cash Flow Analysis
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <ComposedChart data={cashFlowData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis yAxisId="left" tickFormatter={(value) => `$${value / 1000}k`} />
            <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `$${value / 1000}k`} />
            <Tooltip formatter={(value) => formatCurrency(value as number)} />
            <Legend />
            <Bar yAxisId="left" dataKey="inflow" fill="#10B981" name="Cash Inflow" />
            <Bar yAxisId="left" dataKey="outflow" fill="#EF4444" name="Cash Outflow" />
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="cumulativeCashFlow"
              stroke="#3B82F6"
              strokeWidth={2}
              name="Cumulative Cash Flow"
            />
          </ComposedChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Accounts Receivable Aging */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Accounts Receivable Aging
          </h3>
          <div className="space-y-3">
            {receivableAging?.map((item, index) => (
              <div key={item.range} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {item.range}
                    </span>
                    <span className="text-sm font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(item.amount)}
                    </span>
                  </div>
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        index === 0 ? 'bg-green-600' :
                        index === 1 ? 'bg-yellow-600' :
                        index === 2 ? 'bg-orange-600' :
                        'bg-red-600'
                      }`}
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Outstanding
              </span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">
                {formatCurrency(receivableAging?.reduce((sum, item) => sum + item.amount, 0) || 0)}
              </span>
            </div>
          </div>
        </Card>

        {/* Invoice Payment Patterns */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Invoice Payment Patterns
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={paymentPatterns} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="dayRange" />
              <YAxis />
              <Tooltip formatter={(value, name) => name === 'count' ? value : `${value}%`} />
              <Bar dataKey="percentage" fill="#3B82F6" name="Percentage">
                {paymentPatterns?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Budget vs Actual Analysis */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Budget vs Actual Analysis
          </h3>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Budgeted
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actual
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Variance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  % Variance
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {budgetVsActual?.map((item) => (
                <tr key={item.category}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatCurrency(item.budgeted)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatCurrency(item.actual)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${
                      item.variance >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(Math.abs(item.variance))}
                      {item.variance < 0 && ' over'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.variance >= 0 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {((item.variance / item.budgeted) * 100).toFixed(1)}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Gross Margin Trends */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Margin Trends
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={marginTrends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={(value) => `${value}%`} />
            <Tooltip formatter={(value) => `${value}%`} />
            <Legend />
            <Line type="monotone" dataKey="grossMargin" stroke="#3B82F6" strokeWidth={2} name="Gross Margin" />
            <Line type="monotone" dataKey="netMargin" stroke="#10B981" strokeWidth={2} name="Net Margin" />
            <Line type="monotone" dataKey="targetGross" stroke="#3B82F6" strokeDasharray="5 5" name="Target Gross" />
            <Line type="monotone" dataKey="targetNet" stroke="#10B981" strokeDasharray="5 5" name="Target Net" />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default FinancialAnalyticsDashboard;