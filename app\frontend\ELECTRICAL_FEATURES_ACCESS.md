# Electrical Features Access Guide

This guide shows how to access all electrical features in the application after fixing the navigation issues.

## Frontend URL
http://localhost:3002/

## Backend API URL
http://localhost:3001/api

## Test Credentials
If you need to create a test user:
- Email: <EMAIL>
- Password: password123

## Access Methods for Electrical Features

### 1. Panel Management
**Method A - From Projects List:**
1. Navigate to http://localhost:3002/projects
2. Click on any project card
3. You'll see the project detail page with all feature buttons
4. Click "Panel Management" button

**Method B - Direct URL:**
- Pattern: `/projects/{projectId}/panels`
- Example: http://localhost:3002/projects/1/panels

**Method C - From Project Card:**
1. Go to Projects page
2. Each project card has a "Panels" button at the bottom
3. Click it to go directly to panel management

### 2. Arc Flash Analysis
**Method A - From Project Detail Page:**
1. Navigate to a project detail page
2. Click "Arc Flash Analysis" button

**Method B - From Panel List:**
1. Go to any project's panel list
2. Click "Arc Flash Analysis" button in the top right

**Method C - Direct URL:**
- Pattern: `/projects/{projectId}/arc-flash`
- Example: http://localhost:3002/projects/1/arc-flash

### 3. Short Circuit Analysis
**Method A - From Project Detail Page:**
1. Navigate to a project detail page
2. Click "Short Circuit Analysis" button

**Method B - Direct URL:**
- Pattern: `/projects/{projectId}/short-circuit`
- Example: http://localhost:3002/projects/1/short-circuit

### 4. Permit Documents
**Method A - From Project Detail Page:**
1. Navigate to a project detail page
2. Click "Permit Documents" button

**Method B - From Project Card:**
1. Go to Projects page
2. Each project card has a "Permits" button at the bottom
3. Click it to go directly to permits

**Method C - Direct URL:**
- Pattern: `/projects/{projectId}/permits`
- Example: http://localhost:3002/projects/1/permits

### 5. Inspection Checklists
**Method A - From Project Detail Page:**
1. Navigate to a project detail page
2. Click "Inspections" button

**Method B - Direct URL:**
- Pattern: `/projects/{projectId}/inspections`
- Example: http://localhost:3002/projects/1/inspections

## Creating Test Data

If you need to create a test project to access these features:

1. **Register/Login:**
   - Go to http://localhost:3002/register
   - Create a new account
   - Or login with existing credentials

2. **Create a Project:**
   - Go to Projects page
   - Click "New Project" button
   - Fill in project details:
     - Name: "Test Electrical Project"
     - Type: Commercial
     - Address: "123 Main St"
     - City: "Your City"
     - State: "Your State"
     - Service Size: 200
     - Voltage System: "208Y/120"

3. **Access Features:**
   - After creating the project, click on it
   - You'll see the new Project Detail Page with all feature buttons
   - Each button leads to the respective electrical feature

## Feature Status

✅ **Fixed Issues:**
- Added ProjectDetailPage component for better navigation
- Updated routing to use ProjectDetailPage for individual projects
- Fixed authentication token reference in PanelList
- Created comprehensive navigation hub for all electrical features

✅ **Available Features:**
- Panel Management (create panels, circuits, schedules)
- Arc Flash Analysis
- Short Circuit Analysis
- Permit Document Generation
- Inspection Checklists
- Project Estimates

## API Endpoints

For direct API testing:
- Projects: GET http://localhost:3001/api/projects
- Panels: GET http://localhost:3001/api/projects/{projectId}/panels
- Arc Flash: POST http://localhost:3001/api/calculations/arc-flash
- Short Circuit: POST http://localhost:3001/api/calculations/short-circuit
- Permits: GET http://localhost:3001/api/projects/{projectId}/permits

## Troubleshooting

1. **Can't see projects:**
   - Make sure you're logged in
   - Check browser console for errors
   - Verify backend is running on port 3001

2. **Navigation not working:**
   - Clear browser cache
   - Check if frontend is running on correct port (3002)
   - Look for console errors

3. **API errors:**
   - Ensure backend is running
   - Check authentication token is being sent
   - Verify project ID exists in database