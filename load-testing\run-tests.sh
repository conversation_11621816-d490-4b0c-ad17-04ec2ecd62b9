#!/bin/bash

# Load Testing Execution Script
# This script orchestrates the complete load testing process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
RESULTS_DIR="./results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
TEST_TYPE=${1:-"load"}

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_warning "Dependencies not installed. Installing now..."
        npm install
    fi
    
    # Check if .env exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from example..."
        cp .env.example .env
        print_warning "Please configure .env file before running tests"
        exit 1
    fi
    
    # Check if test data exists
    if [ ! -f "./data/test-users.csv" ]; then
        print_warning "Test data not found. Generating..."
        node scripts/test-data-generator.js
    fi
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    mkdir -p "$RESULTS_DIR/reports"
    
    print_success "Prerequisites check completed"
}

start_monitoring() {
    print_header "Starting Resource Monitor"
    
    # Start resource monitor in background
    node scripts/monitor-resources.js > "$RESULTS_DIR/monitor_${TIMESTAMP}.log" 2>&1 &
    MONITOR_PID=$!
    
    print_success "Resource monitor started (PID: $MONITOR_PID)"
    
    # Wait for monitor to initialize
    sleep 3
}

run_load_test() {
    local test_scenario=$1
    local output_file="$RESULTS_DIR/${test_scenario}_${TIMESTAMP}.json"
    
    print_header "Running $test_scenario Test"
    
    case $test_scenario in
        "load")
            artillery run scenarios/full-load-test.yml \
                --output "$output_file" \
                --overrides "{\"config\":{\"statsInterval\":10}}"
            ;;
        "stress")
            artillery run scenarios/stress-test.yml \
                --output "$output_file"
            ;;
        "spike")
            artillery run scenarios/spike-test.yml \
                --output "$output_file"
            ;;
        "endurance")
            print_warning "Endurance test will run for 4 hours"
            artillery run scenarios/endurance-test.yml \
                --output "$output_file"
            ;;
        "k6-load")
            k6 run scenarios/k6-load-test.js \
                --out json="$output_file"
            ;;
        "k6-stress")
            k6 run scenarios/k6-stress-test.js \
                --out json="$output_file"
            ;;
        *)
            print_error "Unknown test type: $test_scenario"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_success "$test_scenario test completed successfully"
        echo "$output_file"
    else
        print_error "$test_scenario test failed"
        return 1
    fi
}

stop_monitoring() {
    print_header "Stopping Resource Monitor"
    
    if [ ! -z "$MONITOR_PID" ]; then
        kill -SIGINT $MONITOR_PID 2>/dev/null || true
        wait $MONITOR_PID 2>/dev/null || true
        print_success "Resource monitor stopped"
    fi
}

analyze_results() {
    local results_file=$1
    
    print_header "Analyzing Results"
    
    if [ -f "$results_file" ]; then
        node scripts/analyze-results.js "$(basename "$results_file")"
        print_success "Analysis completed"
    else
        print_error "Results file not found: $results_file"
        return 1
    fi
}

generate_report() {
    print_header "Generating Test Report"
    
    local report_file="$RESULTS_DIR/reports/test_report_${TIMESTAMP}.md"
    
    cat > "$report_file" << EOF
# Load Test Report

**Date:** $(date)
**Test Type:** $TEST_TYPE
**Duration:** $((END_TIME - START_TIME)) seconds

## Summary

### Test Configuration
- Target URL: $(grep API_BASE_URL .env | cut -d'=' -f2)
- Test Scenario: $TEST_TYPE
- Max Concurrent Users: $(grep MAX_CONCURRENT_USERS .env | cut -d'=' -f2)

### Key Metrics
EOF

    # Add metrics from analysis
    if [ -f "$RESULTS_DIR/reports/analysis-${TIMESTAMP}.json" ]; then
        # Extract key metrics and append to report
        print_success "Report generated: $report_file"
    fi
}

cleanup() {
    print_header "Cleanup"
    
    # Stop monitoring if still running
    stop_monitoring
    
    # Archive old results (keep last 30 days)
    find "$RESULTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_header "Electrical Contracting App - Load Testing Suite"
    echo "Test Type: $TEST_TYPE"
    echo "Timestamp: $TIMESTAMP"
    echo ""
    
    # Record start time
    START_TIME=$(date +%s)
    
    # Execute test phases
    check_prerequisites
    start_monitoring
    
    # Run the test
    RESULTS_FILE=$(run_load_test "$TEST_TYPE")
    
    # Record end time
    END_TIME=$(date +%s)
    
    # Stop monitoring before analysis
    stop_monitoring
    
    # Analyze results
    if [ ! -z "$RESULTS_FILE" ]; then
        sleep 2
        analyze_results "$RESULTS_FILE"
        generate_report
    fi
    
    print_header "Test Completed"
    echo "Duration: $((END_TIME - START_TIME)) seconds"
    echo "Results saved to: $RESULTS_DIR"
}

# Execute main function
main