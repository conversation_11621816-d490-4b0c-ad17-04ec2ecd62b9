const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('=== Migration Fix Script ===\n');

const runCommand = (command, description, throwOnError = false) => {
    console.log(`\n${description}...`);
    try {
        const output = execSync(command, { 
            encoding: 'utf-8', 
            cwd: __dirname,
            stdio: throwOnError ? 'pipe' : 'inherit'
        });
        if (throwOnError) {
            return output;
        }
        console.log('✓ Success');
        return true;
    } catch (error) {
        console.log('✗ Command failed:', error.message);
        if (throwOnError) {
            throw error;
        }
        return false;
    }
};

// Step 1: Check current status
console.log('Step 1: Checking current migration status');
try {
    const status = runCommand('npx prisma migrate status', 'Getting migration status', true);
    console.log(status);
} catch (error) {
    console.log('Migration status shows pending migrations (this is expected)');
}

// Step 2: Mark the performance indexes migration as applied
console.log('\nStep 2: Resolving performance indexes migration');
const resolved = runCommand(
    'npx prisma migrate resolve --applied "20240115_performance_indexes"',
    'Marking 20240115_performance_indexes as applied'
);

if (!resolved) {
    console.log('Note: This might fail if the migration is already resolved or doesn\'t exist in the database');
}

// Step 3: Fix the security hardening migration
console.log('\nStep 3: Fixing security hardening migration');
const badMigrationPath = path.join(__dirname, 'prisma', 'migrations', '20240116_security_hardening', 'migration.sql');
const fixedMigrationPath = path.join(__dirname, 'prisma', 'migrations', '20240116_security_hardening', 'migration-fixed.sql');
const backupPath = badMigrationPath + '.backup';

if (fs.existsSync(fixedMigrationPath)) {
    // Backup original if not already backed up
    if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(badMigrationPath, backupPath);
        console.log('✓ Backed up original migration to:', path.basename(backupPath));
    }
    
    // Replace with fixed version
    fs.copyFileSync(fixedMigrationPath, badMigrationPath);
    console.log('✓ Replaced migration.sql with fixed version');
} else {
    console.log('✗ Fixed migration file not found at:', fixedMigrationPath);
}

// Step 4: Deploy all migrations
console.log('\nStep 4: Deploying all migrations');
const deployed = runCommand('npx prisma migrate deploy', 'Running prisma migrate deploy');

// Step 5: Generate Prisma client
console.log('\nStep 5: Generating Prisma client');
runCommand('npx prisma generate', 'Generating Prisma client');

// Step 6: Final status check
console.log('\nStep 6: Final migration status check');
try {
    const finalStatus = runCommand('npx prisma migrate status', 'Checking final status', true);
    console.log(finalStatus);
    console.log('\n✅ Migration fix process completed!');
} catch (error) {
    console.log('Final status check failed, but migrations may still be applied correctly');
}

// Summary
console.log('\n=== Summary ===');
console.log('1. Marked 20240115_performance_indexes as applied');
console.log('2. Fixed SQLite syntax errors in 20240116_security_hardening');
console.log('3. Deployed all migrations');
console.log('4. Generated Prisma client');
console.log('\nIf you still see errors, you may need to:');
console.log('- Run: npx prisma migrate reset --force (WARNING: This will delete all data)');
console.log('- Or manually check the database with a SQLite browser');