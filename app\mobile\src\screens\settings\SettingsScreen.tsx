import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';

const SettingsScreen: React.FC = () => {
  const dispatch = useDispatch();
  const [offlineMode, setOfflineMode] = React.useState(false);
  const [notifications, setNotifications] = React.useState(true);

  const handleLogout = async () => {
    await AsyncStorage.clear();
    // Add logout logic here
  };

  const settingsSections = [
    {
      title: 'Account',
      items: [
        {
          title: 'Profile',
          subtitle: 'Manage your profile information',
          onPress: () => {},
        },
        {
          title: 'Security',
          subtitle: 'PIN, biometrics, and session settings',
          onPress: () => {},
        },
      ],
    },
    {
      title: 'App Settings',
      items: [
        {
          title: 'Offline Mode',
          subtitle: 'Work without internet connection',
          toggle: true,
          value: offlineMode,
          onToggle: setOfflineMode,
        },
        {
          title: 'Notifications',
          subtitle: 'Push notifications for updates',
          toggle: true,
          value: notifications,
          onToggle: setNotifications,
        },
      ],
    },
    {
      title: 'Data',
      items: [
        {
          title: 'Sync Data',
          subtitle: 'Sync offline data with server',
          onPress: () => {},
        },
        {
          title: 'Clear Cache',
          subtitle: 'Remove cached data',
          onPress: () => {},
        },
      ],
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
      </View>

      {settingsSections.map((section, index) => (
        <View key={index} style={styles.section}>
          <Text style={styles.sectionTitle}>{section.title}</Text>
          {section.items.map((item, itemIndex) => (
            <TouchableOpacity
              key={itemIndex}
              style={styles.item}
              onPress={item.onPress}
              disabled={item.toggle}
            >
              <View style={styles.itemInfo}>
                <Text style={styles.itemTitle}>{item.title}</Text>
                <Text style={styles.itemSubtitle}>{item.subtitle}</Text>
              </View>
              {item.toggle ? (
                <Switch
                  value={item.value}
                  onValueChange={item.onToggle}
                  trackColor={{ false: '#767577', true: '#81b0ff' }}
                  thumbColor={item.value ? '#2196F3' : '#f4f3f4'}
                />
              ) : (
                <Text style={styles.arrow}>→</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      ))}

      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 40,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 10,
    paddingHorizontal: 20,
    textTransform: 'uppercase',
  },
  item: {
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 18,
    color: '#999',
  },
  logoutButton: {
    backgroundColor: '#f44336',
    margin: 20,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SettingsScreen;