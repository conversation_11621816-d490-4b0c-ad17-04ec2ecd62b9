import { Router, Request, Response } from 'express';
import { PrismaClient, Prisma } from '@prisma/client';
import { startOfMonth, endOfMonth, subDays, subMonths } from 'date-fns';
import { authenticate } from '../middleware/auth';
import { asyncHandler } from '../utils/async-handler';
import {
  ProjectFilters,
  MaterialFilters,
  KPIMetrics,
  RevenueData,
  ProjectAnalytics,
  LaborAnalytics,
  MaterialAnalytics,
  CalculationAnalytics,
  ProjectCompletionPrediction,
  MaterialPriceForecast,
  DemandPattern,
  RiskAssessment,
  CustomerChurnPrediction,
  PPECategoryGroup,
  EquipmentTypeGroup,
  SafetyTrend,
  PanelLoad,
  LoadByType,
  PeakDemandPattern,
  ExportFilters
} from '../types/analytics.types';

const router: Router = Router();
const prisma = new PrismaClient();

// Apply authentication to all analytics routes
router.use(authenticate);

// Get KPI metrics
router.get('/kpi', asyncHandler(async (_req: Request, res: Response) => {
  const now = new Date();
  const lastMonth = subMonths(now, 1);

  // Calculate current period metrics
  const currentMetrics = await calculateKPIMetrics(startOfMonth(now), endOfMonth(now));
  const previousMetrics = await calculateKPIMetrics(startOfMonth(lastMonth), endOfMonth(lastMonth));

  // Calculate growth rates
  const revenueGrowth = previousMetrics.revenue > 0 
    ? (currentMetrics.revenue - previousMetrics.revenue) / previousMetrics.revenue 
    : 0;

  res.json({
    revenue: currentMetrics.revenue,
    revenueGrowth,
    grossMargin: currentMetrics.grossMargin,
    netMargin: currentMetrics.netMargin,
    customerAcquisitionCost: currentMetrics.customerAcquisitionCost,
    customerLifetimeValue: currentMetrics.customerLifetimeValue,
    projectSuccessRate: currentMetrics.projectSuccessRate,
    avgProjectDuration: currentMetrics.avgProjectDuration,
    employeeUtilization: currentMetrics.employeeUtilization,
    safetyIncidentRate: currentMetrics.safetyIncidentRate,
  });
}));

// Get revenue analytics
router.get('/revenue', asyncHandler(async (req: Request, res: Response) => {
  const { period, startDate, endDate } = req.query;
  const start = startDate ? new Date(startDate as string) : subDays(new Date(), 30);
  const end = endDate ? new Date(endDate as string) : new Date();

  const projects = await prisma.project.findMany({
    where: {
      created_at: {
        gte: start,
        lte: end,
      },
      // Active projects only
    },
    include: {
      estimates: {
        where: {
          status: 'APPROVED',
        },
      },
    },
  });

  // Group by period and calculate metrics
  const revenueData = groupRevenueByPeriod(projects as any, period as string);

  res.json(revenueData);
}));

// Get project analytics
router.get('/projects', asyncHandler(async (req: Request, res: Response) => {
  const { projectType, status, startDate, endDate } = req.query;
  const filters: ProjectFilters = { deleted_at: null };

  if (projectType) filters.type = projectType as string;
  if (status) filters.status = status as string;
  if (startDate && endDate) {
    filters.created_at = {
      gte: new Date(startDate as string),
      lte: new Date(endDate as string),
    };
  }

  const projects = await prisma.project.findMany({
    where: filters,
    include: {
      estimates: {
        where: {
          status: 'APPROVED',
        },
        include: {
          material_items: true,
          labor_items: true,
        },
      },
    },
  });

  // Calculate analytics by project type
  const analytics = calculateProjectAnalytics(projects);

  res.json(analytics);
}));

// Get labor analytics
router.get('/labor', asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate } = req.query;
  const start = startDate ? new Date(startDate as string) : startOfMonth(new Date());
  const end = endDate ? new Date(endDate as string) : endOfMonth(new Date());

  const laborItems = await prisma.laborItem.findMany({
    where: {
      created_at: {
        gte: start,
        lte: end,
      },
      estimate: {
        status: 'APPROVED',
      },
    },
    include: {
      estimate: {
        include: {
          project: true,
        },
      },
    },
  });

  // Group by employee/trade and calculate metrics
  const laborAnalytics = calculateLaborAnalytics(laborItems);

  res.json(laborAnalytics);
}));

// Get material analytics
router.get('/materials', asyncHandler(async (req: Request, res: Response) => {
  const { category, vendorId: _vendorId, startDate, endDate } = req.query;
  const filters: MaterialFilters = {};

  if (category) filters.category = category as string;
  if (startDate && endDate) {
    filters.created_at = {
      gte: new Date(startDate as string),
      lte: new Date(endDate as string),
    };
  }

  const materialItems = await prisma.materialItem.findMany({
    where: {
      ...filters,
      estimate: {
        status: 'APPROVED',
      },
    },
    include: {
      estimate: {
        include: {
          project: true,
        },
      },
    },
  });

  // Calculate material analytics
  const analytics = calculateMaterialAnalytics(materialItems);

  res.json(analytics);
}));

// Get calculation analytics
router.get('/calculations', asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate } = req.query;
  const filters: Prisma.CalculationLogWhereInput = {};

  if (startDate && endDate) {
    filters.created_at = {
      gte: new Date(startDate as string),
      lte: new Date(endDate as string),
    };
  }

  const calculations = await prisma.calculationLog.findMany({
    where: filters,
    include: {
      user: true,
      project: true,
    },
  });

  // Group by calculation type and analyze
  const analytics = calculateCalculationAnalytics(calculations);

  res.json(analytics);
}));

// Get financial metrics
router.get('/financial', asyncHandler(async (req: Request, res: Response) => {
  const { date } = req.query;
  const targetDate = date ? new Date(date as string) : new Date();

  // Mock financial metrics - replace with actual calculations
  const metrics = {
    cashFlow: 125000,
    accountsReceivable: 410000,
    accountsPayable: 180000,
    overdueInvoices: 62000,
    avgPaymentDays: 28,
    grossMargin: 0.305,
    netMargin: 0.145,
  };

  res.json(metrics);
}));

// Get predictive analytics
router.get('/predictive', asyncHandler(async (_req: Request, res: Response) => {
  // Mock predictive analytics - replace with ML model predictions
  const predictiveData = {
    projectCompletionPredictions: await predictProjectCompletions(),
    materialPriceForecast: await forecastMaterialPrices(),
    demandPatterns: await analyzeDemandPatterns(),
    riskScores: await assessRisks(),
    churnPredictions: await predictCustomerChurn(),
  };

  res.json(predictiveData);
}));

// Get arc flash analytics
router.get('/arc-flash', asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate } = req.query;
  const filters: Prisma.ArcFlashCalculationWhereInput = {};

  if (startDate && endDate) {
    filters.created_at = {
      gte: new Date(startDate as string),
      lte: new Date(endDate as string),
    };
  }

  const arcFlashCalcs = await prisma.arcFlashCalculation.findMany({
    where: filters,
    include: {
      panel: {
        include: {
          project: true,
        },
      },
    },
  });

  const analytics = {
    totalCalculations: arcFlashCalcs.length,
    byPPECategory: groupByPPECategory(arcFlashCalcs),
    byEquipmentType: groupByEquipmentType(arcFlashCalcs),
    safetyTrends: analyzeSafetyTrends(arcFlashCalcs),
  };

  res.json(analytics);
}));

// Get load distribution analytics
router.get('/load-distribution', asyncHandler(async (req: Request, res: Response) => {
  const { projectId } = req.query;
  const filters: Prisma.PanelWhereInput = {};

  if (projectId && typeof projectId === 'string') {
    filters.project_id = projectId;
  }

  const panels = await prisma.panel.findMany({
    where: filters,
    include: {
      circuits: true,
      load_calculations: {
        orderBy: {
          calculation_date: 'desc',
        },
        take: 1,
      },
    },
  });

  const analytics = {
    panelLoads: calculatePanelLoads(panels),
    loadByType: calculateLoadByType(panels),
    peakDemandPatterns: analyzePeakDemandPatterns(panels),
  };

  res.json(analytics);
}));

// Export analytics data
router.post('/export', asyncHandler(async (req: Request, res: Response) => {
  const { reportType, format, filters } = req.body;

  // Generate export based on report type and format
  const exportData = await generateExport(reportType, format, filters);

  res.setHeader('Content-Type', getContentType(format));
  res.setHeader('Content-Disposition', `attachment; filename=analytics-${reportType}-${Date.now()}.${format}`);
  res.send(exportData);
}));

// Helper functions
async function calculateKPIMetrics(startDate: Date, endDate: Date): Promise<KPIMetrics> {
  // Mock implementation - replace with actual calculations
  return {
    revenue: 468000,
    grossMargin: 0.305,
    netMargin: 0.145,
    customerAcquisitionCost: 2500,
    customerLifetimeValue: 45000,
    projectSuccessRate: 0.92,
    avgProjectDuration: 28,
    employeeUtilization: 0.85,
    safetyIncidentRate: 0.2,
  };
}

function groupRevenueByPeriod(projects: Prisma.ProjectGetPayload<{
  include: { estimates: true }
}>[], period: string): RevenueData[] {
  // Mock implementation - group projects by period and calculate revenue
  return projects.map(project => ({
    date: project.created_at,
    revenue: project.estimates.reduce((sum: number, est) => sum + (est.total_amount ?? 0), 0),
    profit: project.estimates.reduce((sum: number, est) => sum + ((est.total_amount ?? 0) * (est.profit_margin ?? 0) / 100), 0),
    margin: project.estimates[0]?.profit_margin || 0,
    projectCount: 1,
  }));
}

function calculateProjectAnalytics(projects: Prisma.ProjectGetPayload<{
  include: { estimates: { include: { material_items: true; labor_items: true } } }
}>[]): ProjectAnalytics[] {
  // Group projects by type and calculate metrics
  const grouped = projects.reduce<Record<string, ProjectAnalytics>>((acc, project) => {
    if (!acc[project.type]) {
      acc[project.type] = {
        projectType: project.type,
        count: 0,
        revenue: 0,
        totalDuration: 0,
        totalProfit: 0,
        avgDuration: 0,
        profitMargin: 0
      };
    }
    
    const revenue = project.estimates.reduce((sum: number, est) => sum + (est.total_amount ?? 0), 0);
    const profit = project.estimates.reduce((sum: number, est) => sum + ((est.total_amount ?? 0) * (est.profit_margin ?? 0) / 100), 0);
    
    acc[project.type].count++;
    acc[project.type].revenue += revenue;
    acc[project.type].totalProfit += profit;
    acc[project.type].totalDuration += 30; // Mock duration
    
    return acc;
  }, {});

  return Object.values(grouped).map((group) => ({
    ...group,
    avgDuration: group.totalDuration / group.count,
    profitMargin: (group.totalProfit / group.revenue) * 100,
  }));
}

function calculateLaborAnalytics(laborItems: Prisma.LaborItemGetPayload<{
  include: { estimate: { include: { project: true } } }
}>[]): LaborAnalytics[] {
  // Mock implementation - calculate labor analytics
  return [
    {
      employeeId: 'EMP001',
      employeeName: 'John Smith',
      hoursWorked: 168,
      regularHours: 160,
      overtimeHours: 8,
      productivity: 92,
      costPerHour: 45,
      totalCost: 7920,
    },
    // Add more mock data
  ];
}

function calculateMaterialAnalytics(materialItems: Prisma.MaterialItemGetPayload<{
  include: { estimate: { include: { project: true } } }
}>[]): MaterialAnalytics[] {
  // Mock implementation - calculate material analytics
  return materialItems.reduce((acc: MaterialAnalytics[], item) => {
    const existing = acc.find(m => m.materialId === item.catalog_number);
    if (existing) {
      existing.totalUsed += item.quantity;
      existing.totalCost += item.total_amount;
    } else {
      acc.push({
        materialId: item.catalog_number,
        materialName: item.description,
        category: item.category,
        totalUsed: item.quantity,
        totalCost: item.total_amount,
        avgPrice: item.unit_cost,
        priceVariance: 0,
        vendors: [],
      });
    }
    return acc;
  }, []);
}

function calculateCalculationAnalytics(calculations: Prisma.CalculationLogGetPayload<{
  include: { user: true; project: true }
}>[]): CalculationAnalytics[] {
  // Group by calculation type and analyze
  const grouped = calculations.reduce((acc: Record<string, CalculationAnalytics>, calc) => {
    if (!acc[calc.calculation_type]) {
      acc[calc.calculation_type] = {
        calculationType: calc.calculation_type,
        count: 0,
        totalTime: 0,
        successCount: 0,
        avgCompletionTime: 0,
        successRate: 0,
        commonIssues: []
      };
    }
    
    acc[calc.calculation_type].count++;
    acc[calc.calculation_type].totalTime += 120; // Mock completion time
    acc[calc.calculation_type].successCount++;
    
    return acc;
  }, {});

  return Object.values(grouped).map((group) => ({
    ...group,
    avgCompletionTime: group.totalTime / group.count,
    successRate: (group.successCount / group.count) * 100,
    commonIssues: group.commonIssues,
  }));
}

async function predictProjectCompletions(): Promise<ProjectCompletionPrediction[]> {
  // Mock implementation - replace with ML model
  const projects = await prisma.project.findMany({
    where: {
      status: 'IN_PROGRESS',
    },
    take: 10,
  });

  return projects.map(project => ({
    projectId: project.id,
    projectName: project.name,
    predictedCompletion: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000),
    confidence: Math.floor(Math.random() * 30) + 70,
    riskFactors: ['Weather delays', 'Material shortage'].slice(0, Math.floor(Math.random() * 3)),
  }));
}

async function forecastMaterialPrices(): Promise<MaterialPriceForecast[]> {
  // Mock implementation - replace with ML model
  const materials = await prisma.material.findMany({
    take: 10,
  });

  return materials.map(material => ({
    materialId: material.id,
    materialName: material.name,
    currentPrice: material.current_price || 0,
    predictedPrice30Days: (material.current_price || 0) * (1 + (Math.random() * 0.2 - 0.1)),
    predictedPrice90Days: (material.current_price || 0) * (1 + (Math.random() * 0.3 - 0.1)),
    trend: Math.random() > 0.5 ? 'up' : 'down',
  }));
}

async function analyzeDemandPatterns(): Promise<DemandPattern[]> {
  // Mock implementation
  return [
    {
      period: 'Next 30 days',
      demandLevel: 85,
      projectTypes: ['Residential', 'Commercial'],
      confidence: 88,
    },
    {
      period: 'Next 60 days',
      demandLevel: 72,
      projectTypes: ['Commercial', 'Industrial'],
      confidence: 75,
    },
    {
      period: 'Next 90 days',
      demandLevel: 90,
      projectTypes: ['Residential'],
      confidence: 82,
    },
  ];
}

async function assessRisks(): Promise<RiskAssessment[]> {
  // Mock implementation
  return [
    {
      category: 'Supply Chain',
      riskLevel: 'medium',
      description: 'Potential material shortages due to supplier delays',
      mitigationSteps: ['Maintain safety stock', 'Diversify suppliers'],
    },
    {
      category: 'Labor',
      riskLevel: 'low',
      description: 'Adequate skilled labor availability',
      mitigationSteps: ['Continue training programs', 'Maintain competitive wages'],
    },
    {
      category: 'Financial',
      riskLevel: 'medium',
      description: 'Increasing accounts receivable aging',
      mitigationSteps: ['Implement stricter collection policies', 'Offer early payment discounts'],
    },
  ];
}

async function predictCustomerChurn(): Promise<CustomerChurnPrediction[]> {
  // Mock implementation - replace with ML model
  const customers = await prisma.customer.findMany({
    include: {
      projects: {
        orderBy: {
          created_at: 'desc',
        },
        take: 1,
      },
    },
    take: 10,
  });

  return customers.map(customer => ({
    customerId: customer.id,
    customerName: customer.name,
    churnProbability: Math.floor(Math.random() * 100),
    riskFactors: ['No recent projects', 'Payment delays'].slice(0, Math.floor(Math.random() * 3)),
    lastProjectDate: customer.projects[0]?.created_at || new Date(),
    totalRevenue: Math.floor(Math.random() * 500000) + 50000,
  }));
}

function groupByPPECategory(calculations: Prisma.ArcFlashCalculationGetPayload<{
  include: { panel: { include: { project: true } } }
}>[]): PPECategoryGroup[] {
  const grouped = calculations.reduce((acc: Record<string, PPECategoryGroup>, calc) => {
    if (!acc[calc.ppe_category]) {
      acc[calc.ppe_category] = {
        category: calc.ppe_category.toString(),
        count: 0,
        totalIncidentEnergy: 0,
        avgIncidentEnergy: 0
      };
    }
    
    acc[calc.ppe_category].count++;
    acc[calc.ppe_category].totalIncidentEnergy += calc.incident_energy_max;
    
    return acc;
  }, {});

  return Object.values(grouped).map((group) => ({
    ...group,
    avgIncidentEnergy: group.totalIncidentEnergy / group.count,
  }));
}

function groupByEquipmentType(calculations: Prisma.ArcFlashCalculationGetPayload<{
  include: { panel: { include: { project: true } } }
}>[]): EquipmentTypeGroup[] {
  const grouped = calculations.reduce((acc: Record<string, EquipmentTypeGroup>, calc) => {
    if (!acc[calc.equipment_type]) {
      acc[calc.equipment_type] = {
        type: calc.equipment_type,
        count: 0,
        totalBoundary: 0,
        avgBoundary: 0
      };
    }
    
    acc[calc.equipment_type].count++;
    acc[calc.equipment_type].totalBoundary += calc.arc_flash_boundary;
    
    return acc;
  }, {});

  return Object.values(grouped).map((group) => ({
    ...group,
    avgBoundary: group.totalBoundary / group.count,
  }));
}

function analyzeSafetyTrends(calculations: Prisma.ArcFlashCalculationGetPayload<{
  include: { panel: { include: { project: true } } }
}>[]): SafetyTrend[] {
  // Mock implementation
  return [
    { date: '2024-01-01', incidents: 0, highRiskCount: 2 },
    { date: '2024-02-01', incidents: 1, highRiskCount: 3 },
    { date: '2024-03-01', incidents: 0, highRiskCount: 1 },
  ];
}

function calculatePanelLoads(panels: Prisma.PanelGetPayload<{
  include: { circuits: true; load_calculations: true }
}>[]): PanelLoad[] {
  return panels.map(panel => {
    const latestCalc = panel.load_calculations[0];
    return {
      panelId: panel.id,
      panelName: panel.name,
      loadPercentage: latestCalc ? (latestCalc.load_percentage || 0) : 0,
      phaseBalance: latestCalc ? (latestCalc.phase_imbalance_percent || 0) : 0,
    };
  });
}

function calculateLoadByType(panels: Prisma.PanelGetPayload<{
  include: { circuits: true; load_calculations: true }
}>[]): LoadByType[] {
  const loadByType: Record<string, number> = {};
  let totalLoad = 0;

  panels.forEach(panel => {
    panel.circuits.forEach((circuit) => {
      if (!loadByType[circuit.load_type]) {
        loadByType[circuit.load_type] = 0;
      }
      loadByType[circuit.load_type] += circuit.calculated_load;
      totalLoad += circuit.calculated_load;
    });
  });

  return Object.entries(loadByType).map(([loadType, load]) => ({
    loadType,
    totalLoad: load as number,
    percentage: ((load as number) / totalLoad) * 100,
  }));
}

function analyzePeakDemandPatterns(panels: Prisma.PanelGetPayload<{
  include: { circuits: true; load_calculations: true }
}>[]): PeakDemandPattern[] {
  // Mock implementation
  return Array.from({ length: 24 }, (_, hour) => ({
    hour,
    avgLoad: Math.random() * 1000 + 500,
    peakLoad: Math.random() * 1500 + 800,
  }));
}

async function generateExport(reportType: string, format: string, filters: ExportFilters): Promise<string | Buffer> {
  // Mock implementation - generate export data
  if (format === 'csv') {
    return 'Date,Revenue,Profit,Margin\n2024-01-01,150000,45000,30%\n';
  } else if (format === 'pdf') {
    return Buffer.from('PDF content');
  } else {
    return Buffer.from('Excel content');
  }
}

function getContentType(format: string) {
  switch (format) {
    case 'csv': return 'text/csv';
    case 'pdf': return 'application/pdf';
    case 'excel': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    default: return 'application/octet-stream';
  }
}

export default router;