# Electrical Contractor Mobile App - Feature Documentation

## Overview
This React Native mobile app provides comprehensive electrical-specific features for field electricians and contractors. All features are designed to work offline with automatic sync when connectivity is restored.

## Core Features

### 1. Electrical Calculators
All calculators comply with NEC (National Electrical Code) standards and work completely offline.

#### Load Calculator (NEC 220)
- **Dwelling Unit Calculations**: Square footage-based calculations with demand factors
- **Commercial Load Calculations**: Motor loads, lighting, and receptacle calculations
- **Fixed Appliances**: Range, dryer, water heater, HVAC loads
- **Automatic Demand Factors**: Applied per NEC Article 220
- **Real-time Results**: Instant calculation updates

#### Voltage Drop Calculator
- **Real-time Calculations**: Updates as you type
- **Wire Types**: Copper and aluminum options
- **Temperature Derating**: Automatic adjustment for ambient temperature
- **Visual Indicators**: Color-coded compliance status
- **NEC Compliance**: Shows 3% and 5% limits

#### Wire Size Calculator
- **Temperature Derating Chart**: Visual reference for derating factors
- **Conduit Fill Corrections**: Accounts for multiple conductors
- **Ampacity Tables**: NEC Table 310.16 reference
- **Insulation Types**: THHN, THWN, XHHW, USE
- **Recommended Sizing**: Highlights appropriate wire size

#### Conduit Fill Calculator
- **Visual Fill Gauge**: Animated percentage display
- **Multiple Wire Support**: Add unlimited wire combinations
- **Conduit Types**: EMT, PVC, Rigid, FMC, LFMC
- **NEC Compliance**: Chapter 9, Table 1 and Annex C
- **Fill Percentage Limits**: 53% (1 wire), 31% (2 wires), 40% (3+ wires)

### 2. Panel Management

#### Panel Schedule Viewer
- **Read-only Display**: View panel schedules safely
- **Circuit Status**: Visual indicators for on/off/tripped/maintenance
- **Load Monitoring**: Real-time load percentage with color coding
- **AFCI/GFCI Indicators**: Protection type badges
- **Pull-to-Refresh**: Update panel data

#### Circuit Status Updates
- **Quick Status Changes**: One-tap circuit status updates
- **Safety Warnings**: Automatic warnings for tripped circuits
- **Offline Queue**: Changes sync when online
- **Notes Support**: Add maintenance notes

#### QR Code Scanner
- **Quick Panel Access**: Scan panel QR codes
- **Offline Support**: Access cached panel data
- **Camera Integration**: Native camera scanning

### 3. Field Tools

#### Inspection Checklist
- **Photo Capture**: Document violations with photos
- **NEC Code References**: Built-in code citations
- **Status Tracking**: Pass/Fail/NA/Pending states
- **Category Organization**: Grouped by inspection type
- **Progress Tracking**: Visual completion percentage
- **Offline Storage**: All data saved locally

#### Material Takeoff Scanner
- **Barcode Scanning**: Scan material barcodes
- **Manual Search**: Search by SKU or name
- **Quantity Management**: Track quantities with waste factors
- **Real-time Pricing**: Current material costs
- **Running Total**: Automatic cost calculation
- **Export Support**: Generate takeoff reports

#### Quick Estimate Builder
- **Line Item Entry**: Add materials and labor
- **Overhead & Profit**: Configurable percentages
- **Labor Rate Setting**: Customizable hourly rates
- **Material Database**: Access to pricing data
- **PDF Generation**: Create professional estimates
- **Email Integration**: Send estimates to customers

#### Time Tracking
- **Background Tracking**: Continues when app is minimized
- **Task Categories**: Installation, troubleshooting, inspection, etc.
- **Daily Summary**: View total hours by project
- **Offline Storage**: Syncs when connected
- **Labor Reports**: Export for billing

### 4. Safety Features

#### Arc Flash Warning Display
- **PPE Requirements**: Category-specific equipment lists
- **Incident Energy**: cal/cm² calculations
- **Approach Boundaries**: Limited, restricted, prohibited
- **Visual Warnings**: Color-coded hazard levels
- **Equipment Details**: Voltage and equipment ID
- **Safety Procedures**: Step-by-step guidelines

#### PPE Requirements Viewer
- **Category Reference**: PPE Categories 1-4
- **Equipment Lists**: Required protection by category
- **Minimum Arc Ratings**: cal/cm² requirements
- **Visual Guide**: Equipment illustrations

#### Emergency Contact Quick Dial
- **One-Tap Calling**: Emergency numbers
- **Priority Ordering**: Critical contacts first
- **24/7 Availability**: Marked contacts
- **Project-Specific**: Custom contacts per job
- **Call Logging**: Track emergency calls

#### Safety Violation Reporter
- **Photo Documentation**: Capture violations
- **Severity Levels**: Low to critical
- **Location Tracking**: GPS coordinates
- **Corrective Actions**: Required fixes
- **Anonymous Reporting**: Optional anonymity

### 5. Real-time Features

#### Material Price Lookup
- **Current Pricing**: Live material costs
- **Price History**: Track price changes
- **Supplier Comparison**: Multiple vendors
- **Cached Prices**: 24-hour offline cache

#### Inventory Check
- **Stock Levels**: Real-time availability
- **Location Info**: Warehouse locations
- **Reserve Items**: Hold for pickup
- **Alternative Suggestions**: Similar items

#### Team Communication
- **Project Messages**: Team chat
- **Priority Levels**: Urgent notifications
- **File Sharing**: Photos and documents
- **Read Receipts**: Message status

#### Job Status Updates
- **Progress Tracking**: Completion percentage
- **Photo Updates**: Visual progress
- **Issue Reporting**: Problems and delays
- **Customer Notifications**: Auto-updates

## Technical Features

### Offline Support
- **AsyncStorage**: Local data persistence
- **Queue System**: Pending changes tracked
- **Auto-Sync**: Resumes when online
- **Conflict Resolution**: Smart merge strategies

### Security
- **Encrypted Storage**: Sensitive data protection
- **Session Management**: Auto-logout options
- **Biometric Support**: Fingerprint/Face ID
- **Data Isolation**: Project-based access

### Performance
- **Lazy Loading**: On-demand data fetch
- **Image Optimization**: Compressed photos
- **Background Processing**: Non-blocking operations
- **Memory Management**: Automatic cleanup

### Field-Friendly UI
- **Large Touch Targets**: 48dp minimum
- **High Contrast**: Outdoor visibility
- **Simple Navigation**: One-handed use
- **Quick Actions**: Common tasks accessible
- **Error Recovery**: Graceful failure handling

## Integration Points

### Backend API
- Secure REST endpoints
- WebSocket for real-time updates
- Offline queue synchronization
- Batch operations support

### External Services
- Material pricing APIs
- Weather services (for outdoor work)
- Mapping services (job locations)
- Email/SMS notifications

### Hardware Integration
- Camera (barcode/QR scanning)
- GPS (location tracking)
- Accelerometer (drop detection)
- Biometric sensors

## Usage Guidelines

### Best Practices
1. **Sync Regularly**: Connect to WiFi daily for updates
2. **Photo Management**: Clear old photos monthly
3. **Battery Usage**: Enable power saving for long days
4. **Data Backup**: Export important calculations

### Troubleshooting
- **Sync Issues**: Check network connectivity
- **Calculator Errors**: Verify input values
- **Scanner Problems**: Clean camera lens
- **Performance**: Clear app cache

## Future Enhancements
- Voice input for hands-free operation
- AR wire routing visualization
- Thermal camera integration
- Drone inspection support
- AI-powered code compliance checking