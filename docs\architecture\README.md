# Architecture Documentation

This directory contains the architectural documentation for the Electrical Contracting Application, including Architecture Decision Records (ADRs), system design documents, and technical specifications.

## 📚 Documentation Structure

### Architecture Decision Records (ADRs)
- [ADR-001: Microservices vs Monolith](./adr/ADR-001-microservices-vs-monolith.md)
- [ADR-002: Database Selection](./adr/ADR-002-database-selection.md)
- [ADR-003: Frontend Framework](./adr/ADR-003-frontend-framework.md)
- [ADR-004: Authentication Strategy](./adr/ADR-004-authentication-strategy.md)
- [ADR-005: Offline-First Mobile Architecture](./adr/ADR-005-offline-first-mobile.md)
- [ADR-006: AI Agent Architecture](./adr/ADR-006-ai-agent-architecture.md)
- [ADR-007: Event-Driven Architecture](./adr/ADR-007-event-driven-architecture.md)
- [ADR-008: Multi-Tenancy Strategy](./adr/ADR-008-multi-tenancy-strategy.md)

### System Design
- [High-Level Architecture](./system-design/high-level-architecture.md)
- [Data Flow Diagrams](./system-design/data-flow.md)
- [Security Architecture](./system-design/security-architecture.md)
- [Deployment Architecture](./system-design/deployment-architecture.md)

### Technical Specifications
- [API Design Principles](./specs/api-design.md)
- [Database Schema Design](./specs/database-design.md)
- [Caching Strategy](./specs/caching-strategy.md)
- [Performance Requirements](./specs/performance-requirements.md)

## 🏗️ System Overview

### Core Architecture Principles

1. **Modularity**: Loosely coupled components with clear interfaces
2. **Scalability**: Horizontal scaling capability for all services
3. **Reliability**: Fault tolerance and graceful degradation
4. **Security**: Defense in depth with multiple security layers
5. **Performance**: Sub-second response times for critical operations
6. **Maintainability**: Clear separation of concerns and comprehensive documentation

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                         Client Layer                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Web Client    │  Mobile Client  │      AI Agents              │
│   (React/TS)    │ (React Native)  │    (TypeScript)             │
└────────┬────────┴────────┬────────┴──────────┬──────────────────┘
         │                 │                   │
         └─────────────────┴───────────────────┘
                           │
                    ┌──────┴──────┐
                    │  API Gateway │
                    │   (Nginx)    │
                    └──────┬──────┘
                           │
         ┌─────────────────┼─────────────────────┐
         │                 │                     │
┌────────┴────────┐ ┌──────┴──────┐ ┌──────────┴──────────┐
│   Backend API   │ │  WebSocket  │ │   Agent Service     │
│  (Express/TS)   │ │  Server     │ │   (TypeScript)      │
└────────┬────────┘ └──────┬──────┘ └──────────┬──────────┘
         │                 │                     │
         └─────────────────┴─────────────────────┘
                           │
                    ┌──────┴──────┐
                    │Message Queue │
                    │  (BullMQ)    │
                    └──────┬──────┘
                           │
         ┌─────────────────┼─────────────────────┐
         │                 │                     │
┌────────┴────────┐ ┌──────┴──────┐ ┌──────────┴──────────┐
│   PostgreSQL    │ │    Redis    │ │      Neo4j          │
│  (Primary DB)   │ │   (Cache)   │ │  (Graph Store)      │
└─────────────────┘ └─────────────┘ └─────────────────────┘
```

### Technology Stack

#### Backend
- **Runtime**: Node.js 20+ (LTS)
- **Framework**: Express.js with TypeScript
- **ORM**: Prisma
- **Authentication**: JWT with refresh tokens
- **Validation**: Zod
- **API Documentation**: OpenAPI 3.0

#### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand (web), Redux Toolkit (mobile)
- **Data Fetching**: TanStack Query
- **Forms**: React Hook Form

#### Mobile
- **Framework**: React Native
- **Navigation**: React Navigation
- **Offline Storage**: TypeORM with SQLite
- **Background Tasks**: React Native Background Fetch

#### Infrastructure
- **Container**: Docker
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack
- **APM**: Sentry

### Key Design Decisions

1. **Modular Monolith**: Started with a well-structured monolith with clear module boundaries, allowing future extraction to microservices if needed.

2. **Event-Driven Communication**: Using event sourcing for audit trails and enabling real-time features through WebSockets.

3. **Offline-First Mobile**: Full offline functionality with intelligent sync and conflict resolution.

4. **AI Agent System**: Pluggable agent architecture allowing for different AI providers and custom agents.

5. **Multi-Tenant Ready**: Database schema and application logic designed for multi-tenancy from the start.

## 📊 Data Architecture

### Data Storage Strategy

1. **PostgreSQL**: Primary transactional data
   - Projects, users, calculations
   - ACID compliance for critical data
   - Row-level security for multi-tenancy

2. **Redis**: Caching and sessions
   - API response caching
   - Session storage
   - Real-time presence data
   - Pub/sub for events

3. **Neo4j**: Relationship data
   - User-project relationships
   - Material supply chains
   - Electrical system topology

4. **ChromaDB**: Vector storage
   - Document embeddings
   - Semantic search
   - AI agent memory

5. **S3/MinIO**: Object storage
   - Project photos
   - Generated reports
   - Backups

### Data Flow

```
User Action → API Gateway → Backend Service → Database
     ↓              ↓               ↓            ↓
  Analytics    Rate Limit      Business      Audit Log
                              Logic
                                ↓
                            Message Queue
                                ↓
                        Background Workers
                         (Calculations,
                          Notifications,
                          Report Generation)
```

## 🔒 Security Architecture

### Security Layers

1. **Network Security**
   - WAF (Web Application Firewall)
   - DDoS protection
   - SSL/TLS encryption
   - VPC isolation

2. **Application Security**
   - Input validation
   - SQL injection prevention
   - XSS protection
   - CSRF tokens

3. **Authentication & Authorization**
   - JWT with short expiry
   - Refresh token rotation
   - Role-based access control
   - API key management

4. **Data Security**
   - Encryption at rest
   - Encryption in transit
   - Field-level encryption for PII
   - Secure key management

### Security Compliance

- SOC 2 Type II
- GDPR compliant
- CCPA compliant
- Regular penetration testing
- Security audit logging

## 🚀 Deployment Architecture

### Environment Strategy

1. **Development**: Local Docker Compose
2. **Staging**: Kubernetes cluster (shared)
3. **Production**: Kubernetes cluster (dedicated)
4. **DR Site**: Standby cluster in different region

### Deployment Pipeline

```
Code Push → GitHub Actions → Build & Test → Container Registry
                                              ↓
                                         Staging Deploy
                                              ↓
                                         Integration Tests
                                              ↓
                                         Production Deploy
                                              ↓
                                         Smoke Tests
```

### Scaling Strategy

- **Horizontal Scaling**: All services designed for horizontal scaling
- **Auto-scaling**: Based on CPU, memory, and custom metrics
- **Database Scaling**: Read replicas and connection pooling
- **CDN**: Static assets and API caching at edge

## 📈 Performance Architecture

### Performance Goals

- API response time: < 200ms (p95)
- Page load time: < 2s (p95)
- Time to Interactive: < 3s
- Offline sync: < 30s for full project

### Performance Strategies

1. **Caching**
   - Redis for hot data
   - CDN for static assets
   - Browser caching
   - Service worker caching

2. **Database Optimization**
   - Query optimization
   - Proper indexing
   - Connection pooling
   - Materialized views

3. **Frontend Optimization**
   - Code splitting
   - Lazy loading
   - Image optimization
   - Virtual scrolling

4. **API Optimization**
   - Response compression
   - Field filtering
   - Pagination
   - Batch operations

## 🔄 Evolution Strategy

### Current State (v1.0)
- Modular monolith
- Single database
- Synchronous processing
- Basic caching

### Next Phase (v2.0)
- Extract critical services
- Add message queue
- Implement CQRS
- Enhanced caching

### Future Vision (v3.0)
- Full microservices
- Event sourcing
- GraphQL federation
- Global distribution

## 📚 Related Documentation

- [Development Guide](../developer/README.md)
- [API Documentation](../api/README.md)
- [Security Guide](./system-design/security-architecture.md)
- [Deployment Guide](../../DEPLOYMENT.md)

---

For architectural questions or proposals, please create an ADR following the template in `adr/template.md`.