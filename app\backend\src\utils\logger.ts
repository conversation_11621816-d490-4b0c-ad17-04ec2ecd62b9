import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston about the colors
winston.addColors(colors);

// Format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => {
      const { timestamp, level, message, ...extra } = info;
      
      // Handle object messages
      const msg = typeof message === 'object' 
        ? JSON.stringify(message, null, 2)
        : message;
      
      // Include extra fields if present
      const extraStr = Object.keys(extra).length 
        ? `\n${JSON.stringify(extra, null, 2)}`
        : '';
      
      return `${timestamp} [${level}]: ${msg}${extraStr}`;
    }
  ),
);

// Format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.uncolorize(),
  winston.format.json(),
);

// Define transports based on environment
const transports: winston.transport[] = [];

// Console transport (always enabled in development)
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );
}

// File transports
if (process.env.NODE_ENV === 'production') {
  // Error log
  transports.push(
    new winston.transports.File({
      filename: path.join('logs', 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
  
  // Combined log
  transports.push(
    new winston.transports.File({
      filename: path.join('logs', 'combined.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
  
  // Production console output (errors only)
  transports.push(
    new winston.transports.Console({
      level: 'error',
      format: consoleFormat,
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  transports,
});

// Create a stream object for Morgan HTTP logging
export const stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
export const logError = (
  message: string,
  error: unknown,
  context?: Record<string, any>
): void => {
  logger.error({
    message,
    error: error instanceof Error ? {
      message: error.message,
      stack: error.stack,
      name: error.name,
    } : error,
    ...context,
  });
};

export const logWarning = (
  message: string,
  context?: Record<string, any>
): void => {
  logger.warn({
    message,
    ...context,
  });
};

export const logInfo = (
  message: string,
  context?: Record<string, any>
): void => {
  logger.info({
    message,
    ...context,
  });
};

export const logDebug = (
  message: string,
  context?: Record<string, any>
): void => {
  logger.debug({
    message,
    ...context,
  });
};

// Export default logger
export default logger;