# Electrical Calculations Test Suite

This directory contains comprehensive unit tests for all electrical calculation services in the application. The test suite ensures 100% code coverage and validates NEC (National Electrical Code) compliance for all calculations.

## Test Coverage

### 1. Load Calculation Tests (`load-calculation.test.ts`)
Tests the electrical load calculation service with comprehensive coverage of:

- **Residential Load Calculations**
  - Standard dwelling unit calculations
  - Demand factor applications per NEC 220.42
  - Optional calculation methods
  
- **Commercial Load Calculations**
  - Office buildings
  - Warehouses
  - Restaurants
  - All building types per NEC Table 220.12
  
- **NEC Compliance**
  - Dwelling unit demand factors (Table 220.42)
  - Fixed appliance demand factors (220.53)
  - Heating/cooling load selection (220.60)
  - Motor load calculations (220.50)
  
- **Edge Cases**
  - Minimum/maximum values
  - Zero square footage
  - Empty appliance arrays
  - Decimal precision handling

### 2. Voltage Drop Tests (`voltage-drop.test.ts`)
Tests voltage drop calculations across various scenarios:

- **Basic Calculations**
  - Single-phase systems
  - Three-phase systems
  - Different voltage levels (120V, 208V, 240V, 277V, 480V)
  
- **Conductor Types**
  - Copper conductors
  - Aluminum conductors
  - All standard wire sizes
  
- **Temperature Effects**
  - Ambient temperature derating
  - NEC Table 310.15(B)(1) compliance
  
- **NEC Limits**
  - 3% branch circuit limit
  - 5% total limit
  - Custom voltage drop limits

### 3. Wire Size Tests (`wire-size.test.ts`)
Tests wire sizing calculations with:

- **Load Adjustments**
  - Continuous load factor (125%)
  - Temperature derating factors
  - Combined derating calculations
  
- **Voltage Drop Considerations**
  - Automatic upsizing for voltage drop
  - Distance-based calculations
  - Recommendation generation
  
- **Safety Factors**
  - Terminal temperature ratings
  - Parallel conductor recommendations
  - Spare capacity analysis

### 4. Conduit Fill Tests (`conduit-fill.test.ts`)
Tests conduit fill percentage calculations:

- **Fill Percentages**
  - 53% for 1 conductor
  - 31% for 2 conductors
  - 40% for more than 2 conductors
  
- **Mixed Installations**
  - Different wire sizes in same conduit
  - Branch circuit configurations
  - Feeder calculations
  
- **Conduit Types**
  - EMT (Electrical Metallic Tubing)
  - RMC (Rigid Metal Conduit)
  - PVC (Polyvinyl Chloride)
  - LFMC (Liquidtight Flexible Metal Conduit)
  - LFNC (Liquidtight Flexible Nonmetallic Conduit)

## Running Tests

### Run All Calculation Tests
```bash
npm run test:calculations
```

### Run Individual Test Suites
```bash
npm run test:load-calculation
npm run test:voltage-drop
npm run test:wire-size
npm run test:conduit-fill
```

### Run with Coverage
```bash
npm run test:coverage
```

### Generate HTML Coverage Report
```bash
npm run test:coverage:html
# Open coverage/index.html in browser
```

### Watch Mode (for development)
```bash
npm run test:watch
```

## Test Structure

Each test file follows a consistent structure:

1. **Setup**: Service initialization and mocks
2. **Basic Functionality**: Core calculation tests
3. **Variations**: Different input combinations
4. **Edge Cases**: Boundary conditions and error handling
5. **NEC Compliance**: Code requirement validation
6. **Recommendations**: User guidance generation

## Coverage Requirements

The test suite enforces 100% code coverage:
- Branches: 100%
- Functions: 100%
- Lines: 100%
- Statements: 100%

## Mock Services

- **CacheService**: Mocked to avoid external dependencies
- **Decimal.js**: Used for precise decimal calculations

## Test Data

Test cases include:
- Real-world scenarios from electrical installations
- NEC code examples
- Edge cases and boundary conditions
- Invalid input handling

## Adding New Tests

When adding new calculation services:

1. Create a new test file: `__tests__/[service-name].test.ts`
2. Follow the existing test structure
3. Ensure 100% coverage
4. Include NEC reference validation
5. Test all edge cases
6. Add recommendation scenarios

## Continuous Integration

The test suite is designed for CI/CD pipelines:

```bash
npm run test:ci
```

This runs tests with:
- CI mode enabled
- Coverage reporting
- Limited worker processes
- Fail on coverage threshold violations

## Debugging Tests

To debug a specific test:

1. Add `console.log` statements in the test
2. Run with `--verbose` flag
3. Use VS Code's Jest extension for debugging
4. Check the generated coverage report for uncovered lines

## Test Utilities

The `jest.setup.ts` file provides:
- Environment variable loading
- Console mocking
- Custom matchers
- Global test configuration

## Best Practices

1. **Descriptive Test Names**: Use clear, specific test descriptions
2. **Arrange-Act-Assert**: Follow AAA pattern in tests
3. **Test Data**: Use realistic values from actual electrical work
4. **Error Cases**: Always test error conditions
5. **NEC References**: Validate all code references are included
6. **Precision**: Use appropriate decimal precision for electrical values

## Maintenance

To maintain the test suite:

1. Update tests when NEC codes change
2. Add tests for new calculation features
3. Review coverage reports regularly
4. Keep test data current with industry standards
5. Document any special test considerations