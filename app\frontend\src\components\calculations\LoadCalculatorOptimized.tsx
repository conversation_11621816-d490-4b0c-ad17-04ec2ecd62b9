import { useState, useMemo, memo, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calculator, AlertCircle, FileText, Download } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { GENERAL_LIGHTING_LOADS } from '@electrical/shared';

const loadCalculationSchema = z.object({
  project_id: z.string().optional(),
  calculation_type: z.enum(['STANDARD', 'OPTIONAL', 'EXISTING']).default('STANDARD'),
  building_type: z.enum(Object.keys(GENERAL_LIGHTING_LOADS) as [keyof typeof GENERAL_LIGHTING_LOADS]),
  square_footage: z.number().positive().int(),
  small_appliance_circuits: z.number().int().min(2).default(2),
  laundry_circuit: z.boolean().default(true),
  heating_va: z.number().min(0).default(0),
  cooling_va: z.number().min(0).default(0),
  largest_motor_va: z.number().min(0).default(0),
  other_loads_va: z.number().min(0).default(0),
});

type LoadCalculationFormData = z.infer<typeof loadCalculationSchema>;

// Memoized result display component
const ResultDisplay = memo(({ result }: { result: any }) => {
  const downloadReport = useCallback(() => {
    const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `load-calculation-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [result]);

  return (
    <div className="mt-8 bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-lg font-medium text-gray-900 dark:text-white">
          Calculation Results
        </h4>
        <button
          onClick={downloadReport}
          className="btn-secondary flex items-center"
        >
          <Download className="h-4 w-4 mr-2" />
          Download Report
        </button>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            General Lighting Load
          </p>
          <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            {result.general_lighting_va?.toLocaleString()} VA
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Small Appliance Load
          </p>
          <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            {result.small_appliance_va?.toLocaleString()} VA
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total Connected Load
          </p>
          <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            {result.total_connected_va?.toLocaleString()} VA
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total Demand Load
          </p>
          <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            {result.total_demand_va?.toLocaleString()} VA
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Calculated Amperage
          </p>
          <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
            {result.calculated_amps?.toFixed(2)} A
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Recommended Service Size
          </p>
          <p className="mt-1 text-lg font-semibold text-primary-600">
            {result.recommended_service_size} A
          </p>
        </div>
      </div>

      {result.demand_factors_applied && (
        <div className="mt-6">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Demand Factors Applied:
          </h5>
          <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            {result.demand_factors_applied.map((factor: any, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-primary-600 mr-2">•</span>
                {factor.category}: {factor.factor * 100}% 
                {factor.nec_reference && ` (${factor.nec_reference})`}
              </li>
            ))}
          </ul>
        </div>
      )}

      {result.nec_references && (
        <div className="mt-4">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            NEC References:
          </h5>
          <div className="flex flex-wrap gap-2">
            {result.nec_references.map((ref: string) => (
              <span
                key={ref}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {ref}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

ResultDisplay.displayName = 'ResultDisplay';

// Memoized building type options
const BuildingTypeOptions = memo(() => {
  const options = useMemo(() => 
    Object.entries(GENERAL_LIGHTING_LOADS).map(([key, value]) => ({
      key,
      label: `${key.charAt(0) + key.slice(1).toLowerCase()} (${value} VA/sq ft)`,
      value
    })),
    []
  );

  return (
    <>
      {options.map(({ key, label }) => (
        <option key={key} value={key}>
          {label}
        </option>
      ))}
    </>
  );
});

BuildingTypeOptions.displayName = 'BuildingTypeOptions';

export function LoadCalculatorOptimized() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoadCalculationFormData>({
    resolver: zodResolver(loadCalculationSchema),
    defaultValues: {
      calculation_type: 'STANDARD',
      building_type: 'DWELLING',
      small_appliance_circuits: 2,
      laundry_circuit: true,
      heating_va: 0,
      cooling_va: 0,
      largest_motor_va: 0,
      other_loads_va: 0,
    },
  });

  const buildingType = watch('building_type');
  const squareFootage = watch('square_footage');

  // Memoized estimated load calculation for preview
  const estimatedLoad = useMemo(() => {
    if (!squareFootage || !buildingType) return null;
    
    const lightingLoad = GENERAL_LIGHTING_LOADS[buildingType] * squareFootage;
    const smallApplianceLoad = 3000; // 1500VA x 2 circuits minimum
    const laundryLoad = 1500;
    
    return {
      lighting: lightingLoad,
      smallAppliance: smallApplianceLoad,
      laundry: laundryLoad,
      subtotal: lightingLoad + smallApplianceLoad + laundryLoad
    };
  }, [squareFootage, buildingType]);

  const onSubmit = useCallback(async (data: LoadCalculationFormData) => {
    setIsCalculating(true);
    try {
      const response = await api.post('/calculations/load', data);
      setResult(response.data);
      toast.success('Load calculation completed');
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  }, []);

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Calculator className="mr-2 h-5 w-5" />
          Load Calculation (NEC Article 220)
        </h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Calculation Type
              </label>
              <select
                {...register('calculation_type')}
                className="mt-1 input"
              >
                <option value="STANDARD">Standard Method</option>
                <option value="OPTIONAL">Optional Method</option>
                <option value="EXISTING">Existing Dwelling</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Building Type
              </label>
              <select
                {...register('building_type')}
                className="mt-1 input"
              >
                <BuildingTypeOptions />
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Square Footage
              </label>
              <input
                type="number"
                {...register('square_footage', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="2000"
              />
              {errors.square_footage && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.square_footage.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Small Appliance Circuits
              </label>
              <input
                type="number"
                {...register('small_appliance_circuits', { valueAsNumber: true })}
                className="mt-1 input"
                min="2"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Minimum 2 required (NEC 220.52(A))
              </p>
            </div>

            <div className="sm:col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('laundry_circuit')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Include laundry circuit (1500 VA)
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Heating Load (VA)
              </label>
              <input
                type="number"
                {...register('heating_va', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Cooling Load (VA)
              </label>
              <input
                type="number"
                {...register('cooling_va', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Largest Motor (VA)
              </label>
              <input
                type="number"
                {...register('largest_motor_va', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Other Loads (VA)
              </label>
              <input
                type="number"
                {...register('other_loads_va', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="0"
              />
            </div>
          </div>

          {estimatedLoad && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div className="ml-3 text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200">
                    Estimated Load Preview
                  </p>
                  <p className="mt-1 text-blue-700 dark:text-blue-300">
                    General Lighting: {estimatedLoad.lighting.toLocaleString()} VA<br />
                    Small Appliances: {estimatedLoad.smallAppliance.toLocaleString()} VA<br />
                    Laundry: {estimatedLoad.laundry.toLocaleString()} VA<br />
                    Subtotal: {estimatedLoad.subtotal.toLocaleString()} VA
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isCalculating}
              className="btn-primary"
            >
              {isCalculating ? (
                <>
                  <span className="spinner mr-2" />
                  Calculating...
                </>
              ) : (
                <>
                  <Calculator className="mr-2 h-5 w-5" />
                  Calculate Load
                </>
              )}
            </button>
          </div>
        </form>

        {result && <ResultDisplay result={result} />}
      </div>
    </div>
  );
}