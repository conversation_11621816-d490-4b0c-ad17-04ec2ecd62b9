import { useState, useEffect, useCallback } from 'react';
import { Search, Package, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { Material } from '@electrical/shared';
import { debounce } from '../../utils/debounce';

export function MaterialSearch() {
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState<string>('ALL');
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);

  const searchMaterials = useCallback(
    debounce(async (term: string, cat: string) => {
      if (!term && cat === 'ALL') {
        setMaterials([]);
        return;
      }

      setLoading(true);
      try {
        const params = new URLSearchParams();
        if (term) params.append('search', term);
        if (cat !== 'ALL') params.append('category', cat);
        
        const response = await api.get(`/materials?${params.toString()}`);
        setMaterials(response.data);
      } catch (error) {
        toast.error('Failed to search materials');
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    searchMaterials(searchTerm, category);
  }, [searchTerm, category, searchMaterials]);

  const formatPrice = (price: number | null) => {
    if (price === null) return 'N/A';
    return `$${price.toFixed(2)}`;
  };

  const getPriceChangeIcon = (change: number | null) => {
    if (!change) return null;
    if (change > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    return <TrendingDown className="h-4 w-4 text-green-500" />;
  };

  const getPriceChangeColor = (change: number | null) => {
    if (!change) return '';
    return change > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400';
  };

  return (
    <div>
      <div className="mb-6 space-y-4">
        <div className="flex space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search materials by name, SKU, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 input"
              />
            </div>
          </div>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="input w-48"
          >
            <option value="ALL">All Categories</option>
            <option value="WIRE">Wire & Cable</option>
            <option value="CONDUIT">Conduit & Fittings</option>
            <option value="BREAKERS">Breakers & Panels</option>
            <option value="OUTLETS">Outlets & Switches</option>
            <option value="LIGHTING">Lighting</option>
            <option value="TOOLS">Tools</option>
            <option value="SAFETY">Safety Equipment</option>
            <option value="OTHER">Other</option>
          </select>
        </div>
      </div>

      {loading && (
        <div className="flex justify-center items-center h-64">
          <div className="spinner" />
        </div>
      )}

      {!loading && materials.length === 0 && (searchTerm || category !== 'ALL') && (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          No materials found matching your search criteria
        </div>
      )}

      {!loading && materials.length > 0 && (
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {materials.map((material) => (
            <div
              key={material.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setSelectedMaterial(material)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <Package className="h-5 w-5 text-gray-400 mr-2" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {material.name}
                      </h3>
                    </div>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      SKU: {material.sku} | {material.manufacturer}
                    </p>
                    {material.description && (
                      <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                        {material.description}
                      </p>
                    )}
                  </div>
                  <div className="ml-4 text-right">
                    <div className="flex items-center justify-end">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="text-xl font-bold text-gray-900 dark:text-white">
                        {formatPrice(material.current_price)}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                        /{material.unit}
                      </span>
                    </div>
                    {material.price_change_24h !== null && (
                      <div className={`flex items-center justify-end mt-1 ${getPriceChangeColor(material.price_change_24h)}`}>
                        {getPriceChangeIcon(material.price_change_24h)}
                        <span className="text-sm ml-1">
                          {Math.abs(material.price_change_24h).toFixed(2)}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="mt-4 flex items-center justify-between text-sm">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {material.category}
                  </span>
                  {material.in_stock && (
                    <span className="text-green-600 dark:text-green-400">In Stock</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {selectedMaterial && (
        <MaterialDetail
          material={selectedMaterial}
          onClose={() => setSelectedMaterial(null)}
        />
      )}
    </div>
  );
}

interface MaterialDetailProps {
  material: Material;
  onClose: () => void;
}

function MaterialDetail({ material, onClose }: MaterialDetailProps) {
  const [priceHistory, setPriceHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  useEffect(() => {
    fetchPriceHistory();
  }, [material.id]);

  const fetchPriceHistory = async () => {
    try {
      const response = await api.get(`/materials/${material.id}/price-history`);
      setPriceHistory(response.data);
    } catch (error) {
      toast.error('Failed to load price history');
    } finally {
      setLoadingHistory(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Material Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {material.name}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {material.manufacturer} | SKU: {material.sku}
              </p>
              {material.description && (
                <p className="mt-4 text-gray-600 dark:text-gray-300">
                  {material.description}
                </p>
              )}
              
              <dl className="mt-6 space-y-3">
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</dt>
                  <dd className="text-sm text-gray-900 dark:text-white">{material.category}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Unit</dt>
                  <dd className="text-sm text-gray-900 dark:text-white">{material.unit}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Price</dt>
                  <dd className="text-lg font-semibold text-gray-900 dark:text-white">
                    ${material.current_price?.toFixed(2) || 'N/A'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Availability</dt>
                  <dd className="text-sm">
                    {material.in_stock ? (
                      <span className="text-green-600 dark:text-green-400">In Stock</span>
                    ) : (
                      <span className="text-red-600 dark:text-red-400">Out of Stock</span>
                    )}
                  </dd>
                </div>
              </dl>
            </div>

            <div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Price History (Last 30 Days)
              </h4>
              {loadingHistory ? (
                <div className="flex justify-center items-center h-48">
                  <div className="spinner" />
                </div>
              ) : priceHistory.length > 0 ? (
                <div className="space-y-2">
                  {priceHistory.slice(0, 10).map((record, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">
                        {new Date(record.date).toLocaleDateString()}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        ${record.price.toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  No price history available
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}