# Multi-stage Dockerfile for Backend
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat python3 make g++
WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy dependency files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY app/backend/package.json ./app/backend/
COPY app/agents/package.json ./app/agents/
COPY shared/package.json ./shared/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/app/backend/node_modules ./app/backend/node_modules
COPY --from=deps /app/app/agents/node_modules ./app/agents/node_modules
COPY --from=deps /app/shared/node_modules ./shared/node_modules

# Copy source code
COPY . .

# Generate Prisma client
WORKDIR /app/app/backend
RUN npx prisma generate

# Build backend
RUN pnpm build

# Production image, copy all the files and run
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Install production dependencies only
RUN apk add --no-cache dumb-init

# Copy necessary files
COPY --from=builder --chown=nodejs:nodejs /app/app/backend/dist ./dist
COPY --from=builder --chown=nodejs:nodejs /app/app/backend/prisma ./prisma
COPY --from=builder --chown=nodejs:nodejs /app/app/backend/node_modules ./node_modules
COPY --from=builder --chown=nodejs:nodejs /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder --chown=nodejs:nodejs /app/app/backend/package.json ./package.json

# Copy shared module
COPY --from=builder --chown=nodejs:nodejs /app/shared/dist /app/shared/dist
COPY --from=builder --chown=nodejs:nodejs /app/shared/package.json /app/shared/package.json

USER nodejs

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1); })"

# Use dumb-init to handle signals
ENTRYPOINT ["dumb-init", "--"]

CMD ["node", "dist/index.js"]