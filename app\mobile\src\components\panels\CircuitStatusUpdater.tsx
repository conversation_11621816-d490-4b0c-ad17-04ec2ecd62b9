import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Switch,
} from 'react-native';
import { Circuit } from '../../types/electrical';
import { panelService } from '../../services/panelService';

interface Props {
  circuit: Circuit;
  panelId: string;
  onUpdate: (circuit: Circuit) => void;
  onClose: () => void;
}

export const CircuitStatusUpdater: React.FC<Props> = ({
  circuit,
  panelId,
  onUpdate,
  onClose,
}) => {
  const [status, setStatus] = useState<Circuit['status']>(circuit.status);
  const [notes, setNotes] = useState(circuit.notes || '');
  const [updating, setUpdating] = useState(false);

  const handleStatusUpdate = async () => {
    try {
      setUpdating(true);
      const updatedCircuit = await panelService.updateCircuitStatus(
        panelId,
        circuit.id,
        status
      );
      
      // Update notes if changed
      if (notes !== circuit.notes) {
        // This would call an API to update notes
        updatedCircuit.notes = notes;
      }

      onUpdate(updatedCircuit);
      Alert.alert('Success', 'Circuit status updated successfully');
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to update circuit status. Changes saved for offline sync.');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (statusType: Circuit['status']) => {
    switch (statusType) {
      case 'on': return '#4CAF50';
      case 'off': return '#9E9E9E';
      case 'tripped': return '#f44336';
      case 'maintenance': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Update Circuit Status</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.circuitInfo}>
            <View style={styles.circuitHeader}>
              <View style={[styles.circuitNumber, { backgroundColor: getStatusColor(circuit.status) }]}>
                <Text style={styles.circuitNumberText}>{circuit.number}</Text>
              </View>
              <View style={styles.circuitDetails}>
                <Text style={styles.circuitName}>{circuit.name}</Text>
                <Text style={styles.circuitSpecs}>
                  {circuit.amperage}A • {circuit.voltage}V • {circuit.poles}P
                </Text>
              </View>
            </View>

            <View style={styles.protectionInfo}>
              {circuit.afci && (
                <View style={styles.protectionBadge}>
                  <Text style={styles.protectionText}>AFCI Protected</Text>
                </View>
              )}
              {circuit.gfci && (
                <View style={styles.protectionBadge}>
                  <Text style={styles.protectionText}>GFCI Protected</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.statusSection}>
            <Text style={styles.sectionTitle}>Circuit Status</Text>
            <View style={styles.statusOptions}>
              {(['on', 'off', 'tripped', 'maintenance'] as const).map((statusOption) => (
                <TouchableOpacity
                  key={statusOption}
                  style={[
                    styles.statusOption,
                    status === statusOption && styles.statusOptionActive,
                    { borderColor: getStatusColor(statusOption) },
                  ]}
                  onPress={() => setStatus(statusOption)}
                >
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: getStatusColor(statusOption) },
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusOptionText,
                      status === statusOption && styles.statusOptionTextActive,
                    ]}
                  >
                    {statusOption.charAt(0).toUpperCase() + statusOption.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.notesSection}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <TextInput
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add notes about this circuit..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.warningSection}>
            {status === 'tripped' && (
              <View style={styles.warningBox}>
                <Text style={styles.warningTitle}>⚠️ Circuit Tripped</Text>
                <Text style={styles.warningText}>
                  Before resetting, investigate the cause of the trip. Check for:
                  • Overloaded circuit
                  • Short circuit
                  • Ground fault
                  • Faulty equipment
                </Text>
              </View>
            )}
            {status === 'maintenance' && (
              <View style={styles.warningBox}>
                <Text style={styles.warningTitle}>🔧 Maintenance Mode</Text>
                <Text style={styles.warningText}>
                  Circuit is locked out for maintenance. Ensure proper lockout/tagout procedures are followed.
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.updateButton]}
              onPress={handleStatusUpdate}
              disabled={updating}
            >
              <Text style={styles.updateButtonText}>
                {updating ? 'Updating...' : 'Update Status'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 24,
    color: '#666',
  },
  circuitInfo: {
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  circuitHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circuitNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circuitNumberText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  circuitDetails: {
    marginLeft: 15,
    flex: 1,
  },
  circuitName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
  },
  circuitSpecs: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  protectionInfo: {
    flexDirection: 'row',
    marginTop: 10,
  },
  protectionBadge: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
    marginRight: 10,
  },
  protectionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  statusOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statusOption: {
    width: '48%',
    padding: 15,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusOptionActive: {
    backgroundColor: '#f0f0f0',
  },
  statusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 10,
  },
  statusOptionText: {
    fontSize: 16,
    color: '#666',
  },
  statusOptionTextActive: {
    fontWeight: 'bold',
    color: '#333',
  },
  notesSection: {
    padding: 20,
    paddingTop: 0,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    fontSize: 14,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  warningSection: {
    paddingHorizontal: 20,
  },
  warningBox: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E65100',
    marginBottom: 5,
  },
  warningText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  updateButton: {
    backgroundColor: '#2196F3',
    marginLeft: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
  },
  updateButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: 'bold',
  },
});