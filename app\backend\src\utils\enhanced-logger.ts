import winston from 'winston';
import path from 'path';
import { Request } from 'express';
import { StructuredError, ErrorContext } from './structured-error';
import { v4 as uuidv4 } from 'uuid';

// Extended log levels with more granularity
const levels = {
  fatal: 0,
  error: 1,
  warn: 2,
  info: 3,
  http: 4,
  debug: 5,
  trace: 6,
};

const colors = {
  fatal: 'red bold',
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
  trace: 'gray',
};

winston.addColors(colors);

// Create logs directory if it doesn't exist
import fs from 'fs';
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for structured logging
const structuredFormat = winston.format.printf((info) => {
  const {
    timestamp,
    level,
    message,
    correlationId,
    requestId,
    userId,
    errorId,
    ...meta
  } = info;

  const log = {
    timestamp,
    level,
    message,
    correlationId,
    requestId,
    userId,
    errorId,
    ...meta,
  };

  return JSON.stringify(log);
});

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const {
      timestamp,
      level,
      message,
      correlationId,
      requestId,
      duration,
      ...extra
    } = info;

    let output = `${timestamp} [${level}]`;
    
    if (correlationId) output += ` [${correlationId}]`;
    if (requestId) output += ` [${requestId}]`;
    if (duration) output += ` [${duration}ms]`;
    
    output += `: ${message}`;

    // Add extra fields if present
    const extraKeys = Object.keys(extra);
    if (extraKeys.length > 0) {
      output += '\n' + JSON.stringify(extra, null, 2);
    }

    return output;
  })
);

// Create transports
const transports: winston.transport[] = [];

if (process.env.NODE_ENV === 'production') {
  // Fatal errors log
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'fatal.log'),
      level: 'fatal',
      format: structuredFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true,
    })
  );

  // Error log
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: structuredFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true,
    })
  );

  // Combined log
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: structuredFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 20,
      tailable: true,
    })
  );

  // Audit log for security events
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'audit.log'),
      format: structuredFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 30,
      tailable: true,
    })
  );

  // Performance log
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'performance.log'),
      format: structuredFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true,
    })
  );

  // Console output for production (errors only)
  transports.push(
    new winston.transports.Console({
      level: 'error',
      format: consoleFormat,
    })
  );
} else {
  // Development console output
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );

  // Development file logs
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, 'development.log'),
      format: structuredFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  levels,
  transports,
  exitOnError: false,
});

// Enhanced logger class with context management
export class EnhancedLogger {
  private context: Record<string, any> = {};
  private correlationId?: string;
  private requestId?: string;

  constructor(context?: Record<string, any>) {
    this.context = context || {};
  }

  /**
   * Set correlation ID for tracking requests across services
   */
  setCorrelationId(correlationId: string): void {
    this.correlationId = correlationId;
  }

  /**
   * Set request ID for tracking individual requests
   */
  setRequestId(requestId: string): void {
    this.requestId = requestId;
  }

  /**
   * Create a child logger with additional context
   */
  child(context: Record<string, any>): EnhancedLogger {
    const childLogger = new EnhancedLogger({
      ...this.context,
      ...context,
    });
    if (this.correlationId) childLogger.setCorrelationId(this.correlationId);
    if (this.requestId) childLogger.setRequestId(this.requestId);
    return childLogger;
  }

  /**
   * Extract context from Express request
   */
  fromRequest(req: Request): EnhancedLogger {
    const correlationId = (req.headers['x-correlation-id'] as string) || uuidv4();
    const requestId = uuidv4();

    const childLogger = this.child({
      method: req.method,
      path: req.path,
      ip: req.ip || req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      userId: (req as any).user?.id,
      sessionId: (req as any).session?.id,
    });

    childLogger.setCorrelationId(correlationId);
    childLogger.setRequestId(requestId);

    // Store in request for later use
    (req as any).correlationId = correlationId;
    (req as any).requestId = requestId;
    (req as any).logger = childLogger;

    return childLogger;
  }

  /**
   * Log methods
   */
  fatal(message: string, meta?: Record<string, any>): void {
    logger.log('fatal', message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  error(message: string, error?: Error | StructuredError, meta?: Record<string, any>): void {
    const logData: Record<string, any> = {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    };

    if (error instanceof StructuredError) {
      logData.errorId = error.id;
      logData.errorCode = error.code;
      logData.errorCategory = error.category;
      logData.errorDetails = error.toJSON();
    } else if (error instanceof Error) {
      logData.errorMessage = error.message;
      logData.errorStack = error.stack;
      logData.errorType = error.constructor.name;
    }

    logger.error(message, logData);
  }

  warn(message: string, meta?: Record<string, any>): void {
    logger.warn(message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  info(message: string, meta?: Record<string, any>): void {
    logger.info(message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  http(message: string, meta?: Record<string, any>): void {
    logger.http(message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  debug(message: string, meta?: Record<string, any>): void {
    logger.debug(message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  trace(message: string, meta?: Record<string, any>): void {
    logger.log('trace', message, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
    });
  }

  /**
   * Log audit events for security tracking
   */
  audit(action: string, meta?: Record<string, any>): void {
    logger.info(`AUDIT: ${action}`, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
      auditAction: action,
      auditTimestamp: new Date().toISOString(),
    });
  }

  /**
   * Log performance metrics
   */
  performance(operation: string, duration: number, meta?: Record<string, any>): void {
    logger.info(`PERFORMANCE: ${operation}`, {
      ...this.context,
      ...meta,
      correlationId: this.correlationId,
      requestId: this.requestId,
      operation,
      duration,
      performanceTimestamp: new Date().toISOString(),
    });
  }
}

// Create default logger instance
export const enhancedLogger = new EnhancedLogger();

// Morgan stream for HTTP logging
export const httpLogStream = {
  write: (message: string) => {
    enhancedLogger.http(message.trim());
  },
};

// Helper function to measure async operation performance
export async function measurePerformance<T>(
  operation: string,
  fn: () => Promise<T>,
  logger: EnhancedLogger = enhancedLogger
): Promise<T> {
  const start = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - start;
    logger.performance(operation, duration, { status: 'success' });
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logger.performance(operation, duration, { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' });
    throw error;
  }
}

export default enhancedLogger;