import Redis from 'ioredis';
import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import { config } from '../config';

// Initialize Redis connection
export let redis: Redis | null = null;

export async function initializeRedis(): Promise<void> {
  try {
    redis = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      maxRetriesPerRequest: 1,
      enableOfflineQueue: false,
      retryStrategy: () => null // Don't retry in dev
    });
    
    // Test connection
    await redis.ping();
    console.log('Redis connected successfully');
  } catch (error) {
    console.warn('Redis connection failed, running without cache:', (error as Error).message);
    if (redis) {
      try {
        redis.disconnect();
      } catch (err) {
        // Ignore errors during disconnect
      }
    }
    redis = null;
  }
}

export function getRedis(): Redis | null {
  return redis;
}