@echo off
echo ============================================================
echo       Electrical Contracting Application Startup
echo ============================================================
echo.
echo You need to run TWO commands in SEPARATE terminals:
echo.
echo STEP 1: Open a NEW terminal/command prompt and run:
echo         start-backend-only.bat
echo.
echo STEP 2: Open ANOTHER terminal/command prompt and run:
echo         start-frontend-only.bat
echo.
echo ============================================================
echo.
echo Would you like me to open both terminals for you? (Y/N)
choice /C YN /N /M ">"

if errorlevel 2 goto manual
if errorlevel 1 goto auto

:auto
echo Opening terminals...
start "Backend Server" cmd /k start-backend-only.bat
timeout /t 3 /nobreak > nul
start "Frontend Server" cmd /k start-frontend-only.bat
echo.
echo Both servers are starting in separate windows!
echo.
echo Access the application at: http://localhost:3000
echo.
pause
exit

:manual
echo.
echo Please manually run the following in separate terminals:
echo 1. start-backend-only.bat
echo 2. start-frontend-only.bat
echo.
pause