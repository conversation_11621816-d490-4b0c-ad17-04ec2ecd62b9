version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: electrical_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  neo4j:
    image: neo4j:5
    container_name: electrical_neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/electrical123
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "electrical123", "MATCH () RETURN count(*) LIMIT 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: PostgreSQL for future migration from SQLite
  postgres:
    image: postgres:16-alpine
    container_name: electrical_postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=electrical
      - POSTGRES_PASSWORD=electrical123
      - POSTGRES_DB=electrical_contracting
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U electrical"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ChromaDB for vector storage
  chromadb:
    image: chromadb/chroma:latest
    container_name: electrical_chromadb
    ports:
      - "8000:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - IS_PERSISTENT=TRUE

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2
    container_name: electrical_influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=electrical
      - DOCKER_INFLUXDB_INIT_PASSWORD=electrical123
      - DOCKER_INFLUXDB_INIT_ORG=electrical_contracting
      - DOCKER_INFLUXDB_INIT_BUCKET=metrics
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=electrical_token_123
    volumes:
      - influxdb_data:/var/lib/influxdb2

volumes:
  redis_data:
  neo4j_data:
  neo4j_logs:
  postgres_data:
  chromadb_data:
  influxdb_data: