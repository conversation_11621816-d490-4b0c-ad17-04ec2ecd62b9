import { Repository, DeepPartial, FindManyOptions, FindOneOptions } from 'typeorm';
import { getDatabaseConnection } from '../database/connection';
import { BaseEntity } from '../database/entities';
import { SyncQueue } from '../sync/SyncQueue';
import { v4 as uuidv4 } from 'uuid';

export abstract class BaseRepository<T extends BaseEntity> {
  protected repository: Repository<T>;
  protected syncQueue: SyncQueue;
  protected entityName: string;

  constructor(entityName: string) {
    this.entityName = entityName;
    this.syncQueue = new SyncQueue();
    this.initializeRepository();
  }

  private async initializeRepository() {
    const db = await getDatabaseConnection();
    this.repository = db.getRepository<T>(this.entityName);
  }

  async findAll(options?: FindManyOptions<T>): Promise<T[]> {
    await this.ensureRepository();
    return this.repository.find({
      ...options,
      where: {
        ...options?.where,
        isDeleted: false,
      },
    });
  }

  async findById(id: string, options?: FindOneOptions<T>): Promise<T | null> {
    await this.ensureRepository();
    return this.repository.findOne({
      ...options,
      where: {
        ...options?.where,
        id,
        isDeleted: false,
      },
    });
  }

  async create(data: DeepPartial<T>): Promise<T> {
    await this.ensureRepository();
    
    const entity = this.repository.create({
      ...data,
      id: uuidv4(),
      version: 1,
      syncStatus: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const saved = await this.repository.save(entity);

    // Add to sync queue
    await this.syncQueue.addToQueue({
      entityType: this.entityName,
      entityId: saved.id,
      action: 'create',
      priority: 1,
    });

    return saved;
  }

  async update(id: string, data: DeepPartial<T>): Promise<T | null> {
    await this.ensureRepository();
    
    const existing = await this.findById(id);
    if (!existing) {
      return null;
    }

    const updated = await this.repository.save({
      ...existing,
      ...data,
      id,
      version: existing.version + 1,
      updatedAt: new Date(),
      syncStatus: 'pending',
    });

    // Add to sync queue
    await this.syncQueue.addToQueue({
      entityType: this.entityName,
      entityId: id,
      action: 'update',
      priority: 2,
    });

    return updated;
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureRepository();
    
    const existing = await this.findById(id);
    if (!existing) {
      return false;
    }

    // Soft delete
    await this.repository.save({
      ...existing,
      isDeleted: true,
      updatedAt: new Date(),
      syncStatus: 'pending',
    });

    // Add to sync queue
    await this.syncQueue.addToQueue({
      entityType: this.entityName,
      entityId: id,
      action: 'delete',
      priority: 3,
    });

    return true;
  }

  async bulkCreate(items: DeepPartial<T>[]): Promise<T[]> {
    await this.ensureRepository();
    
    const entities = items.map(item => this.repository.create({
      ...item,
      id: uuidv4(),
      version: 1,
      syncStatus: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    const saved = await this.repository.save(entities);

    // Add all to sync queue
    for (const entity of saved) {
      await this.syncQueue.addToQueue({
        entityType: this.entityName,
        entityId: entity.id,
        action: 'create',
        priority: 1,
      });
    }

    return saved;
  }

  async search(query: string, fields: string[]): Promise<T[]> {
    await this.ensureRepository();
    
    const qb = this.repository.createQueryBuilder('entity');
    qb.where('entity.isDeleted = :isDeleted', { isDeleted: false });

    if (query && fields.length > 0) {
      const conditions = fields.map(field => 
        `LOWER(entity.${field}) LIKE :query`
      ).join(' OR ');
      
      qb.andWhere(`(${conditions})`, { query: `%${query.toLowerCase()}%` });
    }

    return qb.getMany();
  }

  async count(options?: FindManyOptions<T>): Promise<number> {
    await this.ensureRepository();
    return this.repository.count({
      ...options,
      where: {
        ...options?.where,
        isDeleted: false,
      },
    });
  }

  async findBySyncStatus(status: 'pending' | 'synced' | 'conflict'): Promise<T[]> {
    await this.ensureRepository();
    return this.repository.find({
      where: {
        syncStatus: status,
        isDeleted: false,
      },
    });
  }

  async markAsSynced(id: string, remoteId: string): Promise<void> {
    await this.ensureRepository();
    await this.repository.update(id, {
      syncStatus: 'synced',
      remoteId,
      lastSyncedAt: new Date().toISOString(),
    } as any);
  }

  async markAsConflict(id: string, conflictData: any): Promise<void> {
    await this.ensureRepository();
    await this.repository.update(id, {
      syncStatus: 'conflict',
      conflictData: JSON.stringify(conflictData),
    } as any);
  }

  protected async ensureRepository() {
    if (!this.repository) {
      await this.initializeRepository();
    }
  }

  async forceSyncEntity(id: string): Promise<void> {
    const entity = await this.findById(id);
    if (entity) {
      await this.syncQueue.addToQueue({
        entityType: this.entityName,
        entityId: id,
        action: 'update',
        priority: 0, // High priority
      });
    }
  }

  async getLocalChanges(): Promise<T[]> {
    await this.ensureRepository();
    return this.repository.find({
      where: {
        syncStatus: 'pending',
        isDeleted: false,
      },
      order: {
        updatedAt: 'ASC',
      },
    });
  }
}