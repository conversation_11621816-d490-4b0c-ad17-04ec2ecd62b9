import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

const jurisdictionTemplates = [
  {
    jurisdiction_name: 'City of Los Angeles',
    jurisdiction_code: 'LA_CITY',
    state: 'CA',
    document_type: 'PERMIT_APPLICATION',
    template_name: 'Electrical Permit Application',
    template_version: '2024.1',
    effective_date: new Date('2024-01-01'),
    form_fields: JSON.stringify({
      sections: [
        {
          name: 'applicant_info',
          title: 'Applicant Information',
          fields: [
            { name: 'applicant_name', type: 'text', required: true, label: 'Applicant Name' },
            { name: 'applicant_license', type: 'text', required: true, label: 'C-10 License Number' },
            { name: 'applicant_phone', type: 'tel', required: true, label: 'Phone Number' },
            { name: 'applicant_email', type: 'email', required: true, label: 'Email Address' },
          ]
        },
        {
          name: 'property_info',
          title: 'Property Information',
          fields: [
            { name: 'property_address', type: 'text', required: true, label: 'Street Address' },
            { name: 'property_apn', type: 'text', required: false, label: 'Assessor Parcel Number' },
            { name: 'property_owner', type: 'text', required: true, label: 'Property Owner Name' },
            { name: 'property_type', type: 'select', required: true, label: 'Property Type', options: ['Residential', 'Commercial', 'Industrial'] },
          ]
        },
        {
          name: 'project_info',
          title: 'Project Information',
          fields: [
            { name: 'project_type', type: 'select', required: true, label: 'Work Type', options: ['New Construction', 'Alteration', 'Addition', 'Repair'] },
            { name: 'project_description', type: 'textarea', required: true, label: 'Description of Work' },
            { name: 'estimated_cost', type: 'number', required: true, label: 'Estimated Cost' },
            { name: 'square_footage', type: 'number', required: false, label: 'Square Footage' },
          ]
        },
        {
          name: 'electrical_details',
          title: 'Electrical System Details',
          fields: [
            { name: 'service_size', type: 'number', required: true, label: 'Service Size (Amps)' },
            { name: 'voltage_system', type: 'select', required: true, label: 'Voltage System', options: ['120/240V 1PH', '208V 3PH', '480V 3PH'] },
            { name: 'panel_count', type: 'number', required: true, label: 'Number of Panels' },
            { name: 'has_solar', type: 'checkbox', required: false, label: 'Solar PV System' },
            { name: 'has_battery', type: 'checkbox', required: false, label: 'Battery Storage System' },
          ]
        }
      ]
    }),
    required_documents: JSON.stringify([
      'Plot plan showing electrical service location',
      'One-line electrical diagram',
      'Load calculations per NEC Article 220',
      'Panel schedules',
      'Manufacturers specifications for equipment'
    ]),
    fee_schedule: JSON.stringify({
      base_fee: 200,
      per_outlet: 0.50,
      per_switch: 0.50,
      per_fixture: 1.00,
      service_upgrade: {
        '100A': 150,
        '200A': 200,
        '400A': 300,
        '600A+': 500
      },
      minimum_fee: 200
    }),
    nec_edition: '2023',
    local_amendments: JSON.stringify([
      'LAMC 91.8703.2.1 - GFCI protection required for all kitchen receptacles',
      'LAMC 91.8703.3 - Arc-fault protection required for all dwelling unit circuits',
    ]),
    department_name: 'Los Angeles Department of Building and Safety',
    department_phone: '(*************',
    department_email: '<EMAIL>',
    submission_url: 'https://www.ladbsservices2.lacity.org/',
  },
  {
    jurisdiction_name: 'Miami-Dade County',
    jurisdiction_code: 'MIAMI_DADE',
    state: 'FL',
    document_type: 'PERMIT_APPLICATION',
    template_name: 'Electrical Permit Application',
    template_version: '2024.1',
    effective_date: new Date('2024-01-01'),
    form_fields: JSON.stringify({
      sections: [
        {
          name: 'applicant_info',
          title: 'Applicant Information',
          fields: [
            { name: 'applicant_name', type: 'text', required: true, label: 'Applicant Name' },
            { name: 'applicant_license', type: 'text', required: true, label: 'EC/ER License Number' },
            { name: 'applicant_qualifier', type: 'text', required: true, label: 'Qualifier Name' },
            { name: 'applicant_phone', type: 'tel', required: true, label: 'Phone Number' },
            { name: 'applicant_email', type: 'email', required: true, label: 'Email Address' },
          ]
        },
        {
          name: 'property_info',
          title: 'Property Information',
          fields: [
            { name: 'property_address', type: 'text', required: true, label: 'Street Address' },
            { name: 'property_folio', type: 'text', required: true, label: 'Folio Number' },
            { name: 'property_owner', type: 'text', required: true, label: 'Property Owner' },
            { name: 'property_use', type: 'select', required: true, label: 'Occupancy Type', options: ['Single Family', 'Multi-Family', 'Commercial', 'Industrial'] },
            { name: 'flood_zone', type: 'text', required: true, label: 'Flood Zone' },
          ]
        },
        {
          name: 'electrical_details',
          title: 'Electrical Installation Details',
          fields: [
            { name: 'service_size', type: 'number', required: true, label: 'Service Size (Amps)' },
            { name: 'service_type', type: 'select', required: true, label: 'Service Type', options: ['Overhead', 'Underground'] },
            { name: 'meter_location', type: 'text', required: true, label: 'Meter Location' },
            { name: 'grounding_electrode', type: 'select', required: true, label: 'Grounding Electrode', options: ['Ground Rod', 'CEE/Ufer', 'Water Pipe'] },
            { name: 'surge_protection', type: 'checkbox', required: false, label: 'Type 1 or 2 SPD Required' },
          ]
        }
      ]
    }),
    required_documents: JSON.stringify([
      'Site plan with electrical service location',
      'Electrical riser diagram',
      'Load calculations',
      'Panel schedules',
      'Product approval documents (NOA for hurricane zones)'
    ]),
    fee_schedule: JSON.stringify({
      base_fee: 155,
      per_circuit: 2.00,
      service_change: {
        '0-200A': 155,
        '201-400A': 250,
        '401-800A': 400,
        '801A+': 600
      },
      reinspection_fee: 155
    }),
    nec_edition: '2023',
    local_amendments: JSON.stringify([
      'FBC 701.2 - Emergency disconnects required for one- and two-family dwellings',
      'Miami-Dade County Code - Type 2 SPD required for all new services',
    ]),
    department_name: 'Miami-Dade County Permitting and Inspection Center',
    department_phone: '(*************',
    department_email: '<EMAIL>',
    submission_url: 'https://www.miamidade.gov/permits/',
  },
  {
    jurisdiction_name: 'City of Chicago',
    jurisdiction_code: 'CHICAGO',
    state: 'IL',
    document_type: 'PERMIT_APPLICATION',
    template_name: 'Electrical Permit Application',
    template_version: '2024.1',
    effective_date: new Date('2024-01-01'),
    form_fields: JSON.stringify({
      sections: [
        {
          name: 'contractor_info',
          title: 'Electrical Contractor Information',
          fields: [
            { name: 'contractor_name', type: 'text', required: true, label: 'Contractor Business Name' },
            { name: 'contractor_license', type: 'text', required: true, label: 'City License Number' },
            { name: 'contractor_insurance', type: 'text', required: true, label: 'Insurance Policy Number' },
            { name: 'supervising_electrician', type: 'text', required: true, label: 'Supervising Electrician' },
            { name: 'supervisor_license', type: 'text', required: true, label: 'Supervisor License #' },
          ]
        },
        {
          name: 'building_info',
          title: 'Building Information',
          fields: [
            { name: 'building_address', type: 'text', required: true, label: 'Building Address' },
            { name: 'building_pin', type: 'text', required: true, label: 'PIN Number' },
            { name: 'building_type', type: 'select', required: true, label: 'Building Type', options: ['Single Family', 'Two-Flat', 'Multi-Unit', 'Commercial', 'Industrial'] },
            { name: 'building_stories', type: 'number', required: true, label: 'Number of Stories' },
            { name: 'building_units', type: 'number', required: true, label: 'Number of Units' },
          ]
        },
        {
          name: 'work_scope',
          title: 'Scope of Work',
          fields: [
            { name: 'work_type', type: 'checkbox_group', required: true, label: 'Type of Work', options: ['Service Upgrade', 'Panel Replacement', 'Rewire', 'New Circuits', 'Temporary Power'] },
            { name: 'service_size', type: 'number', required: false, label: 'New Service Size (if applicable)' },
            { name: 'panel_locations', type: 'textarea', required: true, label: 'Panel Locations' },
            { name: 'special_conditions', type: 'textarea', required: false, label: 'Special Conditions' },
          ]
        }
      ]
    }),
    required_documents: JSON.stringify([
      'Electrical plans sealed by licensed architect or engineer',
      'Load calculation worksheet',
      'ComEd service application (for service upgrades)',
      'Contractor license and insurance certificates'
    ]),
    fee_schedule: JSON.stringify({
      minimum_fee: 250,
      service_entrance: {
        '0-200A': 250,
        '201-600A': 500,
        '601-1200A': 750,
        '1201A+': 1000
      },
      per_unit_fee: 50,
      per_floor_fee: 25
    }),
    nec_edition: '2020',
    local_amendments: JSON.stringify([
      'Chicago Electrical Code 18-27 - Conduit required for all wiring',
      'CEC 18-27-410.6 - No NM cable permitted',
      'CEC 18-27-700.5 - Emergency lighting requirements',
    ]),
    department_name: 'Chicago Department of Buildings',
    department_phone: '(*************',
    department_email: '<EMAIL>',
    submission_url: 'https://www.chicago.gov/permits',
  },
  {
    jurisdiction_name: 'State of Texas',
    jurisdiction_code: 'TEXAS_STATE',
    state: 'TX',
    document_type: 'PERMIT_APPLICATION',
    template_name: 'State Electrical Permit Application',
    template_version: '2024.1',
    effective_date: new Date('2024-01-01'),
    form_fields: JSON.stringify({
      sections: [
        {
          name: 'license_info',
          title: 'License Information',
          fields: [
            { name: 'master_license', type: 'text', required: true, label: 'Master Electrician License #' },
            { name: 'contractor_license', type: 'text', required: true, label: 'Electrical Contractor License #' },
            { name: 'business_name', type: 'text', required: true, label: 'Business Name' },
            { name: 'business_address', type: 'text', required: true, label: 'Business Address' },
          ]
        },
        {
          name: 'job_info',
          title: 'Job Information',
          fields: [
            { name: 'job_address', type: 'text', required: true, label: 'Job Site Address' },
            { name: 'job_county', type: 'text', required: true, label: 'County' },
            { name: 'job_value', type: 'number', required: true, label: 'Total Job Value' },
            { name: 'job_description', type: 'textarea', required: true, label: 'Description of Work' },
          ]
        }
      ]
    }),
    required_documents: JSON.stringify([
      'Copy of state electrical license',
      'Proof of insurance',
      'One-line diagram for commercial projects',
      'Load calculations for service changes'
    ]),
    fee_schedule: JSON.stringify({
      base_fee: 50,
      value_based: [
        { min: 0, max: 5000, fee: 50 },
        { min: 5001, max: 25000, fee: 100 },
        { min: 25001, max: 100000, fee: 200 },
        { min: 100001, max: null, fee: 300 }
      ]
    }),
    nec_edition: '2023',
    department_name: 'Texas Department of Licensing and Regulation',
    department_phone: '(*************',
    department_email: '<EMAIL>',
    submission_url: 'https://www.tdlr.texas.gov/',
  },
  // Inspection Request Templates
  {
    jurisdiction_name: 'City of Los Angeles',
    jurisdiction_code: 'LA_CITY',
    state: 'CA',
    document_type: 'INSPECTION_REQUEST',
    template_name: 'Electrical Inspection Request',
    template_version: '2024.1',
    effective_date: new Date('2024-01-01'),
    form_fields: JSON.stringify({
      sections: [
        {
          name: 'permit_info',
          title: 'Permit Information',
          fields: [
            { name: 'permit_number', type: 'text', required: true, label: 'Permit Number' },
            { name: 'inspection_type', type: 'select', required: true, label: 'Inspection Type', options: ['Rough', 'Service Release', 'Final', 'Re-inspection'] },
            { name: 'requested_date', type: 'date', required: true, label: 'Requested Date' },
            { name: 'requested_time', type: 'select', required: false, label: 'Preferred Time', options: ['Morning', 'Afternoon', 'Any Time'] },
          ]
        },
        {
          name: 'work_ready',
          title: 'Work Ready for Inspection',
          fields: [
            { name: 'rough_complete', type: 'checkbox', required: false, label: 'All wiring installed and secured' },
            { name: 'grounding_complete', type: 'checkbox', required: false, label: 'Grounding and bonding complete' },
            { name: 'panels_labeled', type: 'checkbox', required: false, label: 'Panels installed and circuits labeled' },
            { name: 'access_clear', type: 'checkbox', required: false, label: 'Clear access to all electrical work' },
          ]
        },
        {
          name: 'contact_info',
          title: 'On-Site Contact',
          fields: [
            { name: 'contact_name', type: 'text', required: true, label: 'Contact Name' },
            { name: 'contact_phone', type: 'tel', required: true, label: 'Contact Phone' },
            { name: 'special_instructions', type: 'textarea', required: false, label: 'Special Instructions' },
          ]
        }
      ]
    }),
    required_documents: JSON.stringify([
      'Copy of approved permit',
      'Approved plans on site',
      'Previous inspection corrections (if re-inspection)'
    ]),
    department_name: 'LADBS Inspection Requests',
    department_phone: '(*************',
    department_email: '<EMAIL>',
    submission_url: 'https://www.ladbsservices2.lacity.org/OnlineServices/',
  }
];

async function seedJurisdictions(): Promise<void> {
  logger.info('Seeding jurisdiction templates...');

  for (const template of jurisdictionTemplates) {
    await prisma.jurisdictionTemplate.upsert({
      where: {
        jurisdiction_code_document_type: {
          jurisdiction_code: template.jurisdiction_code,
          document_type: template.document_type,
        },
      },
      update: template,
      create: template,
    });
    logger.info(`Created/Updated template: ${template.jurisdiction_name} - ${template.document_type}`);
  }

  logger.info('Jurisdiction templates seeded successfully!');
}

seedJurisdictions()
  .catch((e) => {
    logger.error({ message: 'Failed to seed jurisdictions', error: e });
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });