# AI Agent System Documentation

The Electrical Contracting Application features an advanced AI agent system designed to assist users with various tasks, from code compliance checking to intelligent troubleshooting and project optimization.

## 🤖 System Overview

### Agent Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      User Interface                          │
├─────────────────────────┬───────────────────────────────────┤
│                         │                                    │
│                    Agent Router                              │
│                         │                                    │
├────────┬────────┬──────┴──────┬────────┬────────┬─────────┤
│        │        │             │        │        │          │
│ Coding │ Debug  │   Project   │Research│ Memory │ Backend  │
│ Agent  │ Agent  │   Manager   │ Agent  │ Agent  │ Database │
│        │        │    Agent    │        │        │  Agent   │
└────────┴────────┴─────────────┴────────┴────────┴─────────┘
                              │
                    ┌─────────┴─────────┐
                    │                   │
              Vector Store        Knowledge Base
              (ChromaDB)         (Neo4j + PG)
```

### Core Components

1. **Agent Router**: Intelligently routes requests to appropriate agents
2. **Base Agent**: Abstract class providing common functionality
3. **Specialized Agents**: Task-specific agents with domain expertise
4. **Memory Store**: Persistent memory across conversations
5. **Message Bus**: Inter-agent communication system
6. **Knowledge Base**: Electrical codes, standards, and best practices

## 📋 Available Agents

### 1. Project Manager Agent

**Purpose**: Oversees project planning, resource allocation, and timeline management.

**Capabilities**:
- Project timeline optimization
- Resource allocation suggestions
- Risk assessment and mitigation
- Progress tracking and reporting
- Dependency management

**Example Usage**:
```typescript
const projectManager = new ProjectManagerAgent();

const analysis = await projectManager.analyzeProject({
  projectId: 'proj_123',
  includeRisks: true,
  optimizeTimeline: true
});

// Returns:
{
  timeline: {
    criticalPath: [...],
    suggestedOptimizations: [...],
    riskFactors: [...]
  },
  resources: {
    allocation: {...},
    conflicts: [...],
    suggestions: [...]
  }
}
```

### 2. Coding Agent

**Purpose**: Assists with code generation, NEC compliance, and electrical calculations.

**Capabilities**:
- Generate code-compliant designs
- Validate electrical calculations
- Suggest optimal solutions
- Create documentation
- Code interpretation

**Example Usage**:
```typescript
const codingAgent = new CodingAgent();

const result = await codingAgent.validateDesign({
  panels: [...],
  circuits: [...],
  loads: [...],
  nec_year: '2023'
});

// Returns compliance issues and suggestions
```

### 3. Debugging Agent

**Purpose**: Troubleshoots electrical issues and system problems.

**Capabilities**:
- Diagnose electrical faults
- Analyze calculation errors
- Suggest troubleshooting steps
- Identify code violations
- Performance optimization

**Example Usage**:
```typescript
const debugAgent = new DebuggingAgent();

const diagnosis = await debugAgent.troubleshoot({
  symptoms: ['circuit breaker tripping', 'lights flickering'],
  systemInfo: {...},
  measurements: {...}
});
```

### 4. Research Agent

**Purpose**: Searches knowledge base and provides relevant information.

**Capabilities**:
- NEC code lookup
- Best practices search
- Material specifications
- Safety guidelines
- Industry standards

**Example Usage**:
```typescript
const researchAgent = new ResearchAgent();

const info = await researchAgent.search({
  query: 'grounding requirements for solar installations',
  context: 'residential',
  necYear: '2023'
});
```

### 5. Backend Database Agent

**Purpose**: Optimizes database queries and data management.

**Capabilities**:
- Query optimization
- Data modeling suggestions
- Performance analysis
- Migration planning
- Backup strategies

### 6. Memory Agent

**Purpose**: Maintains conversation context and user preferences.

**Capabilities**:
- Conversation history
- User preference learning
- Context preservation
- Pattern recognition
- Personalized suggestions

## 🔧 Agent Implementation

### Base Agent Structure

```typescript
// base/base-agent.ts
export abstract class BaseAgent {
  protected name: string;
  protected capabilities: string[];
  protected memoryStore: MemoryStore;
  protected messageBus: MessageBus;
  
  constructor(config: AgentConfig) {
    this.name = config.name;
    this.capabilities = config.capabilities;
    this.memoryStore = new MemoryStore(config.memoryOptions);
    this.messageBus = MessageBus.getInstance();
  }

  abstract async process(input: AgentInput): Promise<AgentOutput>;
  
  protected async getContext(conversationId: string): Promise<Context> {
    return await this.memoryStore.getContext(conversationId);
  }
  
  protected async saveContext(
    conversationId: string, 
    context: Context
  ): Promise<void> {
    await this.memoryStore.saveContext(conversationId, context);
  }
  
  protected async callAgent(
    agentName: string, 
    input: any
  ): Promise<any> {
    return await this.messageBus.send(agentName, input);
  }
  
  protected async log(level: LogLevel, message: string, data?: any): Promise<void> {
    await Logger.log({
      agent: this.name,
      level,
      message,
      data,
      timestamp: new Date()
    });
  }
}
```

### Creating a Custom Agent

```typescript
// agents/safety-compliance-agent.ts
export class SafetyComplianceAgent extends BaseAgent {
  private necDatabase: NECDatabase;
  private oshaGuidelines: OSHAGuidelines;
  
  constructor() {
    super({
      name: 'SafetyComplianceAgent',
      capabilities: [
        'safety_assessment',
        'ppe_requirements',
        'hazard_identification',
        'compliance_checking'
      ]
    });
    
    this.necDatabase = new NECDatabase();
    this.oshaGuidelines = new OSHAGuidelines();
  }
  
  async process(input: AgentInput): Promise<AgentOutput> {
    const { action, data, conversationId } = input;
    
    switch (action) {
      case 'assess_safety':
        return await this.assessSafety(data);
        
      case 'check_ppe':
        return await this.checkPPERequirements(data);
        
      case 'identify_hazards':
        return await this.identifyHazards(data);
        
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }
  
  private async assessSafety(data: SafetyAssessmentInput): Promise<SafetyReport> {
    const context = await this.getContext(data.conversationId);
    
    // Analyze the work environment
    const hazards = await this.identifyHazards(data.environment);
    
    // Check relevant codes
    const codeRequirements = await this.necDatabase.getRelevantCodes({
      workType: data.workType,
      voltage: data.voltage,
      environment: data.environment
    });
    
    // Get OSHA requirements
    const oshaReqs = await this.oshaGuidelines.getRequirements(data.workType);
    
    // Generate comprehensive safety report
    const report: SafetyReport = {
      hazards,
      requiredPPE: this.determinePPE(hazards, data.voltage),
      codeRequirements,
      oshaRequirements: oshaReqs,
      recommendations: this.generateRecommendations(hazards, data),
      riskLevel: this.calculateRiskLevel(hazards, data)
    };
    
    // Save to context for future reference
    await this.saveContext(data.conversationId, {
      ...context,
      lastSafetyAssessment: report
    });
    
    return report;
  }
  
  private determinePPE(hazards: Hazard[], voltage: number): PPERequirement[] {
    const ppe: PPERequirement[] = [];
    
    // Basic PPE always required
    ppe.push(
      { type: 'safety_glasses', category: 'basic', required: true },
      { type: 'hard_hat', category: 'basic', required: true }
    );
    
    // Voltage-specific PPE
    if (voltage > 50) {
      ppe.push(
        { type: 'insulated_gloves', category: 'electrical', class: this.getGloveClass(voltage) },
        { type: 'arc_flash_suit', category: 'electrical', required: voltage > 240 }
      );
    }
    
    // Hazard-specific PPE
    hazards.forEach(hazard => {
      switch (hazard.type) {
        case 'height':
          ppe.push({ type: 'fall_protection', category: 'fall', required: true });
          break;
        case 'chemical':
          ppe.push({ type: 'chemical_gloves', category: 'chemical', required: true });
          break;
        // ... more cases
      }
    });
    
    return ppe;
  }
}
```

## 🔄 Agent Communication

### Message Bus System

```typescript
// infrastructure/message-bus.ts
export class MessageBus {
  private static instance: MessageBus;
  private agents: Map<string, BaseAgent> = new Map();
  private messageQueue: Queue;
  
  static getInstance(): MessageBus {
    if (!MessageBus.instance) {
      MessageBus.instance = new MessageBus();
    }
    return MessageBus.instance;
  }
  
  registerAgent(name: string, agent: BaseAgent): void {
    this.agents.set(name, agent);
  }
  
  async send(
    targetAgent: string, 
    message: AgentMessage
  ): Promise<AgentResponse> {
    const agent = this.agents.get(targetAgent);
    
    if (!agent) {
      throw new Error(`Agent ${targetAgent} not found`);
    }
    
    // Add to queue for processing
    const job = await this.messageQueue.add(targetAgent, {
      message,
      timestamp: Date.now()
    });
    
    return await job.finished();
  }
  
  async broadcast(
    message: BroadcastMessage
  ): Promise<Map<string, AgentResponse>> {
    const responses = new Map<string, AgentResponse>();
    
    const promises = Array.from(this.agents.entries()).map(
      async ([name, agent]) => {
        try {
          const response = await agent.process(message);
          responses.set(name, response);
        } catch (error) {
          responses.set(name, { error: error.message });
        }
      }
    );
    
    await Promise.all(promises);
    return responses;
  }
}
```

### Inter-Agent Collaboration

```typescript
// Example: Complex problem solving with multiple agents
export class AgentOrchestrator {
  private messageBus: MessageBus;
  
  async solveComplexProblem(problem: ComplexProblem): Promise<Solution> {
    // 1. Research agent gathers relevant information
    const research = await this.messageBus.send('ResearchAgent', {
      action: 'gather_info',
      data: { topic: problem.description }
    });
    
    // 2. Coding agent analyzes technical requirements
    const technical = await this.messageBus.send('CodingAgent', {
      action: 'analyze_requirements',
      data: { 
        problem: problem.description,
        context: research.findings
      }
    });
    
    // 3. Project manager creates execution plan
    const plan = await this.messageBus.send('ProjectManagerAgent', {
      action: 'create_plan',
      data: {
        requirements: technical.requirements,
        constraints: problem.constraints
      }
    });
    
    // 4. Safety agent reviews the plan
    const safety = await this.messageBus.send('SafetyComplianceAgent', {
      action: 'review_plan',
      data: { plan }
    });
    
    // 5. Combine all insights
    return {
      research: research.findings,
      technical: technical.analysis,
      plan: plan.timeline,
      safety: safety.recommendations,
      confidence: this.calculateConfidence([research, technical, plan, safety])
    };
  }
}
```

## 🧠 Memory Management

### Memory Store Implementation

```typescript
// infrastructure/memory-store.ts
export class MemoryStore {
  private shortTermMemory: Map<string, Memory[]> = new Map();
  private longTermMemory: ChromaDBClient;
  
  constructor(options: MemoryOptions) {
    this.longTermMemory = new ChromaDBClient({
      url: options.chromaUrl,
      collectionName: 'agent_memories'
    });
  }
  
  async remember(
    conversationId: string, 
    memory: Memory
  ): Promise<void> {
    // Add to short-term memory
    const memories = this.shortTermMemory.get(conversationId) || [];
    memories.push(memory);
    this.shortTermMemory.set(conversationId, memories);
    
    // If important, persist to long-term memory
    if (memory.importance > 0.7) {
      await this.persistMemory(conversationId, memory);
    }
    
    // Cleanup old short-term memories
    if (memories.length > 100) {
      await this.consolidateMemories(conversationId);
    }
  }
  
  async recall(
    query: string, 
    conversationId?: string,
    limit: number = 10
  ): Promise<Memory[]> {
    const shortTerm = conversationId ? 
      this.shortTermMemory.get(conversationId) || [] : [];
    
    // Search long-term memory
    const longTerm = await this.longTermMemory.search({
      query,
      limit,
      filter: conversationId ? { conversationId } : undefined
    });
    
    // Combine and rank by relevance
    return this.rankMemories([...shortTerm, ...longTerm], query)
      .slice(0, limit);
  }
  
  private async consolidateMemories(conversationId: string): Promise<void> {
    const memories = this.shortTermMemory.get(conversationId) || [];
    
    // Summarize related memories
    const summary = await this.summarizeMemories(memories);
    
    // Persist important information
    await this.persistMemory(conversationId, {
      type: 'summary',
      content: summary,
      importance: 0.8,
      timestamp: Date.now()
    });
    
    // Keep only recent memories in short-term
    this.shortTermMemory.set(
      conversationId, 
      memories.slice(-20)
    );
  }
}
```

## 🔐 Security & Privacy

### Agent Security Measures

```typescript
// security/agent-security.ts
export class AgentSecurityManager {
  private permissionStore: PermissionStore;
  private auditLogger: AuditLogger;
  
  async authorizeAgentAction(
    agent: string,
    action: string,
    userId: string,
    data: any
  ): Promise<boolean> {
    // Check agent permissions
    const agentPerms = await this.permissionStore.getAgentPermissions(agent);
    
    if (!agentPerms.includes(action)) {
      await this.auditLogger.log({
        event: 'unauthorized_agent_action',
        agent,
        action,
        userId,
        blocked: true
      });
      return false;
    }
    
    // Check user permissions
    const userPerms = await this.permissionStore.getUserPermissions(userId);
    
    if (!userPerms.canUseAgent(agent)) {
      return false;
    }
    
    // Check data access permissions
    if (data.projectId) {
      const hasAccess = await this.checkProjectAccess(userId, data.projectId);
      if (!hasAccess) return false;
    }
    
    // Log authorized action
    await this.auditLogger.log({
      event: 'agent_action_authorized',
      agent,
      action,
      userId,
      timestamp: Date.now()
    });
    
    return true;
  }
  
  sanitizeAgentOutput(output: any): any {
    // Remove any sensitive information
    const sanitized = { ...output };
    
    // Remove internal IDs
    delete sanitized._id;
    delete sanitized.internalId;
    
    // Mask sensitive data
    if (sanitized.email) {
      sanitized.email = this.maskEmail(sanitized.email);
    }
    
    if (sanitized.apiKeys) {
      sanitized.apiKeys = '[REDACTED]';
    }
    
    return sanitized;
  }
}
```

## 📊 Agent Performance Monitoring

### Metrics Collection

```typescript
// monitoring/agent-metrics.ts
export class AgentMetrics {
  private prometheus: PrometheusClient;
  
  private responseTime = new Histogram({
    name: 'agent_response_time',
    help: 'Agent response time in seconds',
    labelNames: ['agent', 'action'],
    buckets: [0.1, 0.5, 1, 2, 5, 10]
  });
  
  private requestCount = new Counter({
    name: 'agent_requests_total',
    help: 'Total agent requests',
    labelNames: ['agent', 'action', 'status']
  });
  
  private memoryUsage = new Gauge({
    name: 'agent_memory_usage_bytes',
    help: 'Agent memory usage',
    labelNames: ['agent']
  });
  
  async trackRequest(
    agent: string,
    action: string,
    handler: () => Promise<any>
  ): Promise<any> {
    const timer = this.responseTime.startTimer({ agent, action });
    
    try {
      const result = await handler();
      this.requestCount.inc({ agent, action, status: 'success' });
      return result;
    } catch (error) {
      this.requestCount.inc({ agent, action, status: 'error' });
      throw error;
    } finally {
      timer();
    }
  }
  
  trackMemoryUsage(agent: string, bytes: number): void {
    this.memoryUsage.set({ agent }, bytes);
  }
}
```

## 🚀 Deployment & Scaling

### Agent Service Deployment

```yaml
# kubernetes/agent-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-service
  namespace: electrical-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-service
  template:
    metadata:
      labels:
        app: agent-service
    spec:
      containers:
      - name: agent-service
        image: electrical/agent-service:latest
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-credentials
              key: openai-key
        - name: CHROMADB_URL
          value: "http://chromadb:8000"
        - name: REDIS_URL
          value: "redis://redis:6379"
        ports:
        - containerPort: 3001
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: agent-service
  namespace: electrical-app
spec:
  selector:
    app: agent-service
  ports:
  - port: 3001
    targetPort: 3001
```

### Scaling Strategies

```typescript
// scaling/agent-scaler.ts
export class AgentScaler {
  private kubernetes: KubernetesClient;
  private metrics: MetricsClient;
  
  async autoScale(): Promise<void> {
    const metrics = await this.metrics.getAgentMetrics();
    
    for (const [agent, data] of metrics) {
      const { avgResponseTime, queueLength, errorRate } = data;
      
      // Scale up if response time is high
      if (avgResponseTime > 2000 || queueLength > 100) {
        await this.scaleUp(agent);
      }
      
      // Scale down if underutilized
      if (avgResponseTime < 500 && queueLength < 10) {
        await this.scaleDown(agent);
      }
      
      // Alert if error rate is high
      if (errorRate > 0.05) {
        await this.alertOps(agent, 'high_error_rate', errorRate);
      }
    }
  }
  
  private async scaleUp(agent: string): Promise<void> {
    const currentReplicas = await this.kubernetes.getReplicas(agent);
    const newReplicas = Math.min(currentReplicas + 1, 10);
    
    await this.kubernetes.scale(agent, newReplicas);
    await this.logScaling(agent, 'up', currentReplicas, newReplicas);
  }
}
```

## 🧪 Testing Agents

### Agent Testing Framework

```typescript
// tests/agent-test-framework.ts
export class AgentTestFramework {
  private mockMemoryStore: MockMemoryStore;
  private mockMessageBus: MockMessageBus;
  
  async testAgent(
    AgentClass: typeof BaseAgent,
    testCases: TestCase[]
  ): Promise<TestResults> {
    const agent = new AgentClass({
      memoryStore: this.mockMemoryStore,
      messageBus: this.mockMessageBus
    });
    
    const results: TestResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
    
    for (const testCase of testCases) {
      try {
        const result = await agent.process(testCase.input);
        
        if (this.validateOutput(result, testCase.expectedOutput)) {
          results.passed++;
        } else {
          results.failed++;
          results.errors.push({
            test: testCase.name,
            expected: testCase.expectedOutput,
            actual: result
          });
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          test: testCase.name,
          error: error.message
        });
      }
    }
    
    return results;
  }
  
  createTestCase(
    name: string,
    input: AgentInput,
    expectedOutput: Partial<AgentOutput>
  ): TestCase {
    return {
      name,
      input,
      expectedOutput,
      timeout: 5000
    };
  }
}

// Example test
describe('CodingAgent', () => {
  const framework = new AgentTestFramework();
  
  it('should validate wire size calculation', async () => {
    const testCase = framework.createTestCase(
      'wire_size_validation',
      {
        action: 'validate_calculation',
        data: {
          type: 'wire_size',
          inputs: { current: 30, voltage: 240, distance: 100 },
          result: { wireSize: '10 AWG' }
        }
      },
      {
        valid: true,
        confidence: 0.95
      }
    );
    
    const results = await framework.testAgent(CodingAgent, [testCase]);
    expect(results.passed).toBe(1);
  });
});
```

## 📚 Best Practices

### Agent Development Guidelines

1. **Single Responsibility**: Each agent should focus on one domain
2. **Stateless Design**: Agents should not maintain state between requests
3. **Error Handling**: Gracefully handle all error scenarios
4. **Logging**: Comprehensive logging for debugging
5. **Testing**: Unit and integration tests for all agents
6. **Documentation**: Clear documentation of capabilities
7. **Security**: Validate all inputs and sanitize outputs
8. **Performance**: Monitor and optimize response times

### Integration Guidelines

```typescript
// Proper agent integration example
export class ElectricalController {
  private agentRouter: AgentRouter;
  
  async handleCalculationRequest(req: Request, res: Response) {
    try {
      // Validate user permissions
      const hasAccess = await this.authService.checkPermission(
        req.user.id,
        'use_ai_agents'
      );
      
      if (!hasAccess) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }
      
      // Route to appropriate agent
      const result = await this.agentRouter.route({
        type: 'calculation_validation',
        data: req.body,
        userId: req.user.id,
        conversationId: req.session.id
      });
      
      // Sanitize output
      const sanitized = this.securityManager.sanitizeAgentOutput(result);
      
      // Return response
      res.json({
        success: true,
        data: sanitized,
        agent: result.agent,
        confidence: result.confidence
      });
      
    } catch (error) {
      this.logger.error('Agent request failed', error);
      res.status(500).json({ error: 'Agent processing failed' });
    }
  }
}
```

## 🔮 Future Enhancements

### Planned Features

1. **Multi-Modal Agents**: Support for image and document analysis
2. **Predictive Agents**: Predict project outcomes and potential issues
3. **Learning Agents**: Continuously improve from user feedback
4. **Collaborative Agents**: Multiple agents working on complex problems
5. **Custom Agent Builder**: UI for creating custom agents
6. **Agent Marketplace**: Share and discover community agents

### Research Areas

- Federated learning for privacy-preserving improvements
- Quantum-inspired optimization for complex calculations
- Natural language to electrical diagram generation
- Augmented reality integration for field agents

---

For questions about the agent system or to propose new agents, please contact the AI team or create an issue in the repository.