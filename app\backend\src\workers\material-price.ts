import { Job } from 'bullmq';
import { prisma, redis, io } from '../index';
import { emitPriceUpdate } from '../socket';
import axios from 'axios';
import { Decimal } from 'decimal.js';

interface MaterialPriceJobData {
  catalog_numbers: string[];
  suppliers: string[];
  requestedBy: string;
}

export async function materialPriceWorker(job: Job<MaterialPriceJobData>): Promise<void> {
  const { catalog_numbers, suppliers, requestedBy } = job.data;
  
  // Update job progress
  await job.updateProgress(0);
  
  const totalItems = catalog_numbers.length * suppliers.length;
  let processed = 0;
  
  for (const supplier of suppliers) {
    for (const catalogNumber of catalog_numbers) {
      try {
        // Simulate API call to supplier (in real app, would call actual APIs)
        const price = await fetchPriceFromSupplier(supplier, catalogNumber);
        
        if (price) {
          // Save to database
          await prisma.materialPriceHistory.create({
            data: {
              catalog_number: catalogNumber,
              supplier: supplier,
              unit_cost: price,
              effective_date: new Date()
            }
          });
          
          // Update cache
          await redis.setex(
            `price:${catalogNumber}:${supplier}`,
            3600, // 1 hour cache
            JSON.stringify({
              catalog_number: catalogNumber,
              supplier: supplier,
              unit_cost: price,
              effective_date: new Date()
            })
          );
          
          // Emit real-time update
          emitPriceUpdate(io, catalogNumber, price);
        }
        
        processed++;
        await job.updateProgress((processed / totalItems) * 100);
        
        // Rate limiting - wait between requests
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.error(`Failed to fetch price for ${catalogNumber} from ${supplier}:`, error);
      }
    }
  }
  
  // Notify user who requested the update
  if (requestedBy) {
    io.to(`user:${requestedBy}`).emit('price:update:complete', {
      catalog_numbers,
      suppliers,
      processed
    });
  }
}

// Simulated supplier API calls (in real app, would integrate with actual APIs)
async function fetchPriceFromSupplier(supplier: string, catalogNumber: string): Promise<number | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Generate realistic price based on catalog number patterns
  let basePrice = 10;
  
  if (catalogNumber.includes('THHN')) {
    // Wire pricing based on gauge
    const match = catalogNumber.match(/(\d+)/);
    if (match) {
      const gauge = parseInt(match[1]);
      basePrice = new Decimal(500).dividedBy(gauge).toNumber();
    }
  } else if (catalogNumber.includes('PANEL')) {
    // Panel pricing based on amperage
    const match = catalogNumber.match(/(\d+)A/);
    if (match) {
      const amps = parseInt(match[1]);
      basePrice = new Decimal(amps).times(1.5).toNumber();
    }
  } else if (catalogNumber.includes('EMT')) {
    // Conduit pricing
    basePrice = 5 + Math.random() * 10;
  } else if (catalogNumber.includes('REC') || catalogNumber.includes('SW')) {
    // Device pricing
    basePrice = 2 + Math.random() * 20;
  } else if (catalogNumber.includes('FIX')) {
    // Fixture pricing
    basePrice = 25 + Math.random() * 50;
  }
  
  // Add supplier variance (±10%)
  const variance = 0.9 + Math.random() * 0.2;
  const finalPrice = new Decimal(basePrice).times(variance);
  
  // Add market fluctuation (copper prices, etc.)
  const marketFactor = 0.95 + Math.random() * 0.1;
  
  return finalPrice.times(marketFactor).toDecimalPlaces(2).toNumber();
}