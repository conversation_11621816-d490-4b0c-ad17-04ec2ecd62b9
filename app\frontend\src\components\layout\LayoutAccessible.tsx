import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { HeaderAccessible } from './HeaderAccessible';
import { Sidebar } from './Sidebar';
import { announce } from '../../utils/accessibility';

export function LayoutAccessible() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Announce page changes for screen readers
  useEffect(() => {
    const handleRouteChange = () => {
      // Get the page title or a meaningful description
      const pageTitle = document.title || 'Page';
      announce(`Navigated to ${pageTitle}`);
    };

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main content area */}
      <div className="lg:pl-64 flex flex-col min-h-screen">
        {/* Header */}
        <HeaderAccessible onMenuClick={() => setSidebarOpen(!sidebarOpen)} />

        {/* Main content */}
        <main 
          id="main-content"
          className="flex-1 pt-16"
          role="main"
          aria-label="Main content"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Page content outlet */}
            <Outlet />
          </div>
        </main>

        {/* Footer with accessibility information */}
        <footer 
          className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto"
          role="contentinfo"
          aria-label="Site footer"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-500 dark:text-gray-400">
              <p>© 2024 Electrical Project Management System</p>
              <nav aria-label="Footer navigation">
                <ul className="flex space-x-4 mt-2 sm:mt-0">
                  <li>
                    <a 
                      href="#" 
                      className="hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                    >
                      Accessibility
                    </a>
                  </li>
                  <li>
                    <a 
                      href="#" 
                      className="hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                    >
                      Privacy Policy
                    </a>
                  </li>
                  <li>
                    <a 
                      href="#" 
                      className="hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                    >
                      Terms of Service
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </footer>
      </div>

      {/* Live region for announcements */}
      <div
        role="status"
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />
    </div>
  );
}