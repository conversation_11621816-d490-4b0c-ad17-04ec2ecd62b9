import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { redis, prisma, logger } from '../index';
import { createAuditLog } from '../security/audit';
// import { rateLimitMiddleware } from '../security/rate-limiting';
import { RateLimiterRedis } from 'rate-limiter-flexible';

// WebSocket rate limiter
const socketRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'ws_rl',
  points: 100, // Number of events
  duration: 60, // Per minute
  blockDuration: 300, // Block for 5 minutes
});

// Connection rate limiter
const connectionRateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'ws_conn',
  points: 10, // Number of connections
  duration: 300, // Per 5 minutes
  blockDuration: 600, // Block for 10 minutes
});

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
  userRole?: string;
  sessionId?: string;
}

export function setupSecureSocketHandlers(io: Server) {
  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication required'));
      }
      
      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret) as any;
      
      // Check if user exists and is active
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: { id: true, email: true, role: true, deleted_at: true }
      });
      
      if (!user || user.deleted_at) {
        return next(new Error('Invalid user'));
      }
      
      // Rate limit connections per user
      const clientIp = socket.handshake.address;
      try {
        await connectionRateLimiter.consume(`${user.id}:${clientIp}`);
      } catch {
        return next(new Error('Too many connection attempts'));
      }
      
      // Attach user info to socket
      socket.userId = user.id;
      socket.userEmail = user.email;
      socket.userRole = user.role;
      socket.sessionId = socket.id;
      
      // Log connection
      await createAuditLog({
        action: 'WEBSOCKET_CONNECTED',
        userId: user.id,
        resourceType: 'websocket',
        resourceId: socket.id,
        ipAddress: clientIp
      });
      
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });
  
  // Connection handler
  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User ${socket.userEmail} connected via WebSocket`, {
      userId: socket.userId,
      sessionId: socket.sessionId,
      role: socket.userRole
    });
    
    // Join user-specific room
    socket.join(`user:${socket.userId}`);
    
    // Join role-based room
    socket.join(`role:${socket.userRole}`);
    
    // Rate limiting middleware for events
    const rateLimitEvent = async (eventName: string, callback: Function) => {
      try {
        await socketRateLimiter.consume(`${socket.userId}:${eventName}`);
        callback();
      } catch {
        socket.emit('error', {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests'
        });
      }
    };
    
    // Secure event handlers
    socket.on('subscribe', async (data, callback) => {
      await rateLimitEvent('subscribe', async () => {
        const { channel, resourceId } = data;
        
        // Validate subscription permission
        if (!await canSubscribe(socket, channel, resourceId)) {
          return callback({ error: 'Permission denied' });
        }
        
        // Join channel
        socket.join(`${channel}:${resourceId}`);
        
        callback({ success: true });
      });
    });
    
    socket.on('unsubscribe', async (data) => {
      const { channel, resourceId } = data;
      socket.leave(`${channel}:${resourceId}`);
    });
    
    // Project updates
    socket.on('project:update', async (data, callback) => {
      await rateLimitEvent('project:update', async () => {
        if (!hasPermission(socket, 'write:projects')) {
          return callback({ error: 'Permission denied' });
        }
        
        // Validate and sanitize data
        if (!validateProjectData(data)) {
          return callback({ error: 'Invalid data' });
        }
        
        // Broadcast to subscribers
        io.to(`project:${data.projectId}`).emit('project:updated', {
          ...data,
          updatedBy: socket.userId,
          timestamp: new Date()
        });
        
        callback({ success: true });
      });
    });
    
    // Calculation notifications
    socket.on('calculation:complete', async (data) => {
      await rateLimitEvent('calculation:complete', async () => {
        if (!hasPermission(socket, 'write:calculations')) {
          return;
        }
        
        // Notify relevant users
        io.to(`project:${data.projectId}`).emit('calculation:completed', {
          calculationId: data.calculationId,
          type: data.type,
          completedBy: socket.userId,
          timestamp: new Date()
        });
      });
    });
    
    // Real-time collaboration
    socket.on('collaboration:cursor', async (data) => {
      await rateLimitEvent('collaboration:cursor', async () => {
        // Broadcast cursor position to collaborators
        socket.to(`project:${data.projectId}`).emit('collaboration:cursor:update', {
          userId: socket.userId,
          userName: socket.userEmail,
          ...data
        });
      });
    });
    
    // Disconnect handler
    socket.on('disconnect', async () => {
      logger.info(`User ${socket.userEmail} disconnected`, {
        userId: socket.userId,
        sessionId: socket.sessionId
      });
      
      // Log disconnection
      await createAuditLog({
        action: 'WEBSOCKET_DISCONNECTED',
        userId: socket.userId!,
        resourceType: 'websocket',
        resourceId: socket.id
      });
      
      // Notify collaborators
      const rooms = Array.from(socket.rooms);
      rooms.forEach(room => {
        if (room.startsWith('project:')) {
          socket.to(room).emit('collaboration:user:left', {
            userId: socket.userId,
            userName: socket.userEmail
          });
        }
      });
    });
    
    // Error handler
    socket.on('error', (error) => {
      logger.error('Socket error', {
        error: error instanceof Error ? error.message : error,
        userId: socket.userId,
        sessionId: socket.sessionId
      });
      socket.disconnect();
    });
  });
  
  // Broadcast security events to admins
  io.of('/admin').use(async (socket: AuthenticatedSocket, next) => {
    if (socket.userRole !== 'admin') {
      return next(new Error('Admin access required'));
    }
    next();
  }).on('connection', (socket: AuthenticatedSocket) => {
    logger.info('Admin connected to security monitoring', {
      userId: socket.userId,
      userEmail: socket.userEmail
    });
    
    socket.on('security:monitor:start', () => {
      socket.join('security:events');
    });
    
    socket.on('security:monitor:stop', () => {
      socket.leave('security:events');
    });
  });
}

// Permission helpers
async function canSubscribe(
  socket: AuthenticatedSocket,
  channel: string,
  resourceId: string
): Promise<boolean> {
  // Check channel-specific permissions
  switch (channel) {
    case 'project':
      // Check if user has access to project
      const project = await prisma.project.findFirst({
        where: {
          id: resourceId
          // TODO: Add proper user access control when user-project relationship is defined
        }
      });
      return !!project;
    
    case 'calculation':
      // Check if user created or has access to calculation
      const calc = await prisma.calculationLog.findFirst({
        where: {
          id: resourceId,
          performed_by: socket.userId
        }
      });
      return !!calc;
    
    default:
      return false;
  }
}

function hasPermission(socket: AuthenticatedSocket, permission: string): boolean {
  // Role-based permissions
  const rolePermissions: Record<string, string[]> = {
    admin: ['*'],
    foreman: ['read:*', 'write:projects', 'write:calculations', 'write:inspections'],
    electrician: ['read:projects', 'write:calculations', 'write:inspections'],
    estimator: ['read:projects', 'write:estimates']
  };
  
  const userPerms = rolePermissions[socket.userRole || ''] || [];
  
  return userPerms.includes('*') || 
         userPerms.includes(permission) ||
         userPerms.includes(permission.split(':')[0] + ':*');
}

function validateProjectData(data: any): boolean {
  // Basic validation
  if (!data.projectId || typeof data.projectId !== 'string') return false;
  if (!data.updates || typeof data.updates !== 'object') return false;
  
  // Prevent injection attacks
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /\$\{/,
    /\$\(/
  ];
  
  const jsonStr = JSON.stringify(data);
  return !dangerousPatterns.some(pattern => pattern.test(jsonStr));
}

// Emit security event to admin monitoring
export function emitSecurityEvent(io: Server, event: any) {
  io.of('/admin').to('security:events').emit('security:event', {
    ...event,
    timestamp: new Date()
  });
}