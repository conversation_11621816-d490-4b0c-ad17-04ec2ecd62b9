#!/bin/bash

echo "=== Simple Migration Fix Script ==="
echo ""

# Navigate to backend directory
cd /mnt/c/Projects/electrical/app/backend

echo "Step 1: Checking current migration status..."
npx prisma migrate status || true

echo ""
echo "Step 2: Marking the performance indexes migration as applied..."
npx prisma migrate resolve --applied "20240115_performance_indexes" || true

echo ""
echo "Step 3: Fixing the security hardening migration..."
# Backup the original migration
if [ ! -f "prisma/migrations/20240116_security_hardening/migration.sql.backup" ]; then
    cp prisma/migrations/20240116_security_hardening/migration.sql prisma/migrations/20240116_security_hardening/migration.sql.backup
    echo "✓ Backed up original migration"
fi

# Replace with the fixed version
cp prisma/migrations/20240116_security_hardening/migration-fixed.sql prisma/migrations/20240116_security_hardening/migration.sql
echo "✓ Replaced migration with fixed version"

echo ""
echo "Step 4: Deploying all migrations..."
npx prisma migrate deploy

echo ""
echo "Step 5: Final status check..."
npx prisma migrate status

echo ""
echo "✓ Migration fix completed!"