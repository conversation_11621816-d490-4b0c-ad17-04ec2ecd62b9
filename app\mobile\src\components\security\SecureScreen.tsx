import React, { useEffect } from 'react';
import { Platform, NativeModules } from 'react-native';
import { Box } from 'native-base';
import { useSelector } from 'react-redux';
import { RootState } from '@store/index';
import { securityService } from '@services/securityService';

interface SecureScreenProps {
  children: React.ReactNode;
  preventScreenshot?: boolean;
  preventRecording?: boolean;
}

const { FlagSecure } = NativeModules;

const SecureScreen: React.FC<SecureScreenProps> = ({
  children,
  preventScreenshot = true,
  preventRecording = true,
}) => {
  useEffect(() => {
    let shouldPrevent = false;

    const checkSecuritySettings = async () => {
      const config = await securityService.getSecurityConfig();
      shouldPrevent = config.enableScreenshotPrevention && (preventScreenshot || preventRecording);
      
      if (shouldPrevent) {
        enableSecureMode();
      }
    };

    checkSecuritySettings();

    return () => {
      if (shouldPrevent) {
        disableSecureMode();
      }
    };
  }, [preventScreenshot, preventRecording]);

  const enableSecureMode = () => {
    if (Platform.OS === 'android' && FlagSecure) {
      FlagSecure.activate();
    }
    // For iOS, this would be handled in the native code
    // by setting the appropriate flags on the window
  };

  const disableSecureMode = () => {
    if (Platform.OS === 'android' && FlagSecure) {
      FlagSecure.deactivate();
    }
  };

  return <Box flex={1}>{children}</Box>;
};

export default SecureScreen;