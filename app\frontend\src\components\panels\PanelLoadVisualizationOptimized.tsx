import React, { useMemo, memo } from 'react';
import { Panel, Circuit, PanelLoadCalculation } from '@electrical/shared';
import { 
  BoltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface PanelLoadVisualizationProps {
  panel: Panel;
  circuits: Circuit[];
  loadCalculation?: PanelLoadCalculation;
}

// Memoized phase load bar component
const PhaseLoadBar = memo(({ 
  phase, 
  load, 
  maxLoad, 
  color 
}: { 
  phase: string; 
  load: number; 
  maxLoad: number; 
  color: string;
}) => {
  const percentage = (load / maxLoad) * 100;
  
  return (
    <div className="flex items-center">
      <span className="w-12 text-sm font-medium text-gray-600 dark:text-gray-400">
        Phase {phase}
      </span>
      <div className="flex-1 mx-2">
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full ${color}`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
      </div>
      <span className="w-20 text-right text-sm text-gray-700 dark:text-gray-300">
        {(load / 1000).toFixed(1)} kW
      </span>
    </div>
  );
});

PhaseLoadBar.displayName = 'PhaseLoadBar';

// Memoized load type item component
const LoadTypeItem = memo(({ 
  type, 
  load, 
  totalLoad, 
  icon 
}: { 
  type: string; 
  load: number; 
  totalLoad: number; 
  icon: string;
}) => {
  const percentage = (load / totalLoad) * 100;
  
  return (
    <div className="flex items-center">
      <span className="w-6 text-center">{icon}</span>
      <span className="w-24 text-sm text-gray-600 dark:text-gray-400 ml-2">
        {type}
      </span>
      <div className="flex-1 mx-2">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full"
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
      <span className="w-20 text-right text-sm text-gray-700 dark:text-gray-300">
        {(load / 1000).toFixed(1)} kW
      </span>
    </div>
  );
});

LoadTypeItem.displayName = 'LoadTypeItem';

// Helper function moved outside component
const getDefaultPhases = (circuitNumber: number, poles: number): string => {
  const position = ((circuitNumber - 1) % 6) + 1;
  
  if (poles === 1) {
    if (position === 1 || position === 2) return 'A';
    if (position === 3 || position === 4) return 'B';
    return 'C';
  } else if (poles === 2) {
    if (position === 1 || position === 2) return 'AB';
    if (position === 3 || position === 4) return 'BC';
    return 'AC';
  } else {
    return 'ABC';
  }
};

const typeIcons: Record<string, string> = {
  LIGHTING: '💡',
  RECEPTACLE: '🔌',
  MOTOR: '⚙️',
  HVAC: '❄️',
  APPLIANCE: '🔧',
  FEEDER: '⚡',
  EQUIPMENT: '🏭'
};

const phaseColors = {
  A: 'bg-red-500',
  B: 'bg-gray-800',
  C: 'bg-blue-500'
};

export const PanelLoadVisualizationOptimized: React.FC<PanelLoadVisualizationProps> = memo(({
  panel,
  circuits,
  loadCalculation
}) => {
  // Memoized phase loads calculation
  const phaseLoads = useMemo(() => {
    if (loadCalculation) {
      return {
        A: loadCalculation.phase_a_load,
        B: loadCalculation.phase_b_load,
        C: loadCalculation.phase_c_load
      };
    }
    
    const loads = { A: 0, B: 0, C: 0 };
    
    circuits.forEach(circuit => {
      if (circuit.is_spare || circuit.is_space) return;
      
      const load = circuit.calculated_load || 0;
      
      if (panel.phase_config === 'SINGLE_PHASE') {
        loads.A += load;
      } else {
        const phases = circuit.phase_connection || getDefaultPhases(circuit.circuit_number, circuit.poles);
        
        if (phases.includes('A')) loads.A += load / phases.length;
        if (phases.includes('B')) loads.B += load / phases.length;
        if (phases.includes('C')) loads.C += load / phases.length;
      }
    });
    
    return loads;
  }, [circuits, panel.phase_config, loadCalculation]);
  
  // Memoized total load and panel calculations
  const { totalLoad, maxPanelLoad, loadPercentage } = useMemo(() => {
    const total = phaseLoads.A + phaseLoads.B + phaseLoads.C;
    
    const voltageMultiplier = 
      panel.voltage_system === '120/240V_1PH' ? 240 :
      panel.voltage_system === '208V_3PH' ? 208 * 1.732 :
      panel.voltage_system === '240V_3PH' ? 240 * 1.732 :
      panel.voltage_system === '480V_3PH' ? 480 * 1.732 : 277 * 1.732;
    
    const max = panel.ampere_rating * voltageMultiplier;
    const percentage = (total / max) * 100;
    
    return {
      totalLoad: total,
      maxPanelLoad: max,
      loadPercentage: percentage
    };
  }, [phaseLoads, panel.ampere_rating, panel.voltage_system]);
  
  // Memoized phase imbalance calculation
  const imbalancePercent = useMemo(() => {
    if (panel.phase_config === 'SINGLE_PHASE') return 0;
    
    const avgLoad = totalLoad / 3;
    const maxDeviation = Math.max(
      Math.abs(phaseLoads.A - avgLoad),
      Math.abs(phaseLoads.B - avgLoad),
      Math.abs(phaseLoads.C - avgLoad)
    );
    
    return avgLoad > 0 ? (maxDeviation / avgLoad) * 100 : 0;
  }, [phaseLoads, totalLoad, panel.phase_config]);
  
  // Memoized load by type calculation
  const loadByType = useMemo(() => {
    const byType = circuits.reduce((acc, circuit) => {
      if (circuit.is_spare || circuit.is_space) return acc;
      
      const type = circuit.load_type;
      if (!acc[type]) acc[type] = 0;
      acc[type] += circuit.calculated_load || 0;
      return acc;
    }, {} as Record<string, number>);
    
    // Sort by load amount descending
    return Object.entries(byType).sort(([, a], [, b]) => b - a);
  }, [circuits]);
  
  // Memoized circuit counts
  const { activeCircuits, availableSpaces } = useMemo(() => ({
    activeCircuits: circuits.filter(c => !c.is_spare && !c.is_space).length,
    availableSpaces: panel.spaces_total - panel.spaces_used
  }), [circuits, panel.spaces_total, panel.spaces_used]);
  
  // Helper functions for colors
  const getLoadColor = (percentage: number) => {
    if (percentage >= 95) return 'bg-red-500';
    if (percentage >= 80) return 'bg-amber-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-green-500';
  };
  
  const getImbalanceColor = (percentage: number) => {
    if (percentage > 10) return 'text-red-600';
    if (percentage > 5) return 'text-amber-600';
    return 'text-green-600';
  };
  
  const isCompliant = loadPercentage <= 80 && imbalancePercent <= 10;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Load Analysis
      </h3>
      
      {/* Overall Load */}
      <div className="mb-6">
        <div className="flex justify-between items-baseline mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Total Panel Load
          </span>
          <span className="text-lg font-bold text-gray-900 dark:text-white">
            {(totalLoad / 1000).toFixed(1)} kW / {(maxPanelLoad / 1000).toFixed(1)} kW
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-4 relative">
          <div 
            className={`h-4 rounded-full transition-all ${getLoadColor(loadPercentage)}`}
            style={{ width: `${Math.min(loadPercentage, 100)}%` }}
          >
            <span className="absolute right-2 top-0 text-xs text-white font-medium leading-4">
              {loadPercentage.toFixed(1)}%
            </span>
          </div>
        </div>
        {loadPercentage >= 80 && (
          <div className="flex items-center mt-2 text-amber-600">
            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
            <span className="text-sm">
              Panel is {loadPercentage >= 95 ? 'critically' : 'heavily'} loaded
            </span>
          </div>
        )}
      </div>
      
      {/* Phase Balance (for 3-phase) */}
      {panel.phase_config !== 'SINGLE_PHASE' && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Phase Balance
            </span>
            <span className={`text-sm font-medium ${getImbalanceColor(imbalancePercent)}`}>
              {imbalancePercent.toFixed(1)}% imbalance
            </span>
          </div>
          
          <div className="space-y-2">
            {Object.entries(phaseLoads).map(([phase, load]) => (
              <PhaseLoadBar
                key={phase}
                phase={phase}
                load={load}
                maxLoad={maxPanelLoad / 3}
                color={phaseColors[phase as keyof typeof phaseColors]}
              />
            ))}
          </div>
          
          {imbalancePercent > 10 && (
            <div className="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-md">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                High phase imbalance detected. Consider redistributing circuits to balance loads.
              </p>
            </div>
          )}
        </div>
      )}
      
      {/* Load by Type */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Load Distribution by Type
        </h4>
        <div className="space-y-2">
          {loadByType.map(([type, load]) => (
            <LoadTypeItem
              key={type}
              type={type}
              load={load}
              totalLoad={totalLoad}
              icon={typeIcons[type] || '⚡'}
            />
          ))}
        </div>
      </div>
      
      {/* Circuit Summary */}
      <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Active Circuits</p>
          <p className="text-xl font-semibold text-gray-900 dark:text-white">
            {activeCircuits}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Spare/Available</p>
          <p className="text-xl font-semibold text-gray-900 dark:text-white">
            {availableSpaces}
          </p>
        </div>
      </div>
      
      {/* Compliance Status */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            NEC Compliance Status
          </span>
          {isCompliant ? (
            <div className="flex items-center text-green-600">
              <CheckCircleIcon className="h-5 w-5 mr-1" />
              <span className="text-sm">Compliant</span>
            </div>
          ) : (
            <div className="flex items-center text-amber-600">
              <ExclamationTriangleIcon className="h-5 w-5 mr-1" />
              <span className="text-sm">Review Recommended</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

PanelLoadVisualizationOptimized.displayName = 'PanelLoadVisualizationOptimized';