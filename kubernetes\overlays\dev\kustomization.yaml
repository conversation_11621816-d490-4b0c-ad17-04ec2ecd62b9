apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: electrical-dev

bases:
  - ../../base

namePrefix: dev-

commonLabels:
  environment: development

patchesStrategicMerge:
  - deployment-patches.yaml
  - resource-patches.yaml

configMapGenerator:
  - name: backend-config
    behavior: merge
    literals:
      - cors-origin=http://localhost:5173,http://localhost:3000
      - log-level=debug

secretGenerator:
  - name: backend-secrets
    behavior: merge
    literals:
      - database-url=***************************************************/electrical_dev
      - jwt-secret=dev-jwt-secret

replicas:
  - name: backend
    count: 1
  - name: frontend
    count: 1
  - name: agents
    count: 1
  - name: postgres
    count: 1
  - name: redis
    count: 1