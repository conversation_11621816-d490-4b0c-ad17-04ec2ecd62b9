import { useState } from 'react';
import { Tab } from '@headlessui/react';
import { Calculator, Zap, Layers, Cable } from 'lucide-react';
import { LoadCalculator } from '../../components/calculations/LoadCalculator';
import { VoltageDropCalculator } from '../../components/calculations/VoltageDropCalculator';
import { ConduitFillCalculator } from '../../components/calculations/ConduitFillCalculator';
import { WireSizeCalculator } from '../../components/calculations/WireSizeCalculator';

const calculatorTabs = [
  { name: 'Load Calculation', icon: Calculator, component: LoadCalculator },
  { name: 'Voltage Drop', icon: Zap, component: VoltageDropCalculator },
  { name: 'Conduit Fill', icon: Layers, component: ConduitFillCalculator },
  { name: 'Wire Size', icon: Cable, component: WireSizeCalculator },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export function CalculationsPage() {
  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Electrical Calculations</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Perform NEC-compliant electrical calculations for your projects.
        </p>
      </div>

      <Tab.Group>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1">
          {calculatorTabs.map((tab) => (
            <Tab
              key={tab.name}
              className={({ selected }) =>
                classNames(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-800 dark:hover:text-gray-200'
                )
              }
            >
              <div className="flex items-center justify-center">
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </div>
            </Tab>
          ))}
        </Tab.List>
        <Tab.Panels className="mt-6">
          {calculatorTabs.map((tab, idx) => (
            <Tab.Panel
              key={idx}
              className={classNames(
                'rounded-xl',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2'
              )}
            >
              <tab.component />
            </Tab.Panel>
          ))}
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}