import { useState, useEffect } from 'react';
import { SyncEngine } from '../sync/SyncEngine';
import { NetworkMonitor } from '../sync/NetworkMonitor';
import { SyncStatus } from '../sync/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UseSyncStatusResult {
  isOnline: boolean;
  isSyncing: boolean;
  syncProgress: number;
  syncMessage: string;
  lastSyncTime: number | null;
  pendingChanges: number;
  connectionType: string;
  isWifi: boolean;
  syncError: string | null;
  manualSync: () => Promise<void>;
  clearSyncError: () => void;
}

export function useSyncStatus(): UseSyncStatusResult {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isSyncing: false,
    progress: 0,
    message: '',
  });
  const [isOnline, setIsOnline] = useState(true);
  const [connectionType, setConnectionType] = useState('unknown');
  const [isWifi, setIsWifi] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [pendingChanges, setPendingChanges] = useState(0);
  const [syncError, setSyncError] = useState<string | null>(null);

  const syncEngine = SyncEngine.getInstance();
  const networkMonitor = new NetworkMonitor();

  // Load last sync time from storage
  useEffect(() => {
    AsyncStorage.getItem('last_sync_time').then(time => {
      if (time) {
        setLastSyncTime(parseInt(time));
      }
    });
  }, []);

  // Monitor sync status
  useEffect(() => {
    const unsubscribe = syncEngine.addSyncListener((status) => {
      setSyncStatus(status);
      
      if (status.error) {
        setSyncError(status.error);
      } else if (!status.isSyncing && status.lastSyncTime) {
        setLastSyncTime(status.lastSyncTime);
        setSyncError(null);
        AsyncStorage.setItem('last_sync_time', status.lastSyncTime.toString());
      }
    });

    return unsubscribe;
  }, [syncEngine]);

  // Monitor network status
  useEffect(() => {
    const handleConnectionChange = (connected: boolean) => {
      setIsOnline(connected);
    };

    const handleConnectionTypeChange = (type: string) => {
      setConnectionType(type);
      setIsWifi(type === 'wifi');
    };

    networkMonitor.addListener('connectionChange', handleConnectionChange);
    networkMonitor.addListener('connectionTypeChange', handleConnectionTypeChange);
    
    // Set initial values
    setIsOnline(networkMonitor.getIsConnected());
    setConnectionType(networkMonitor.getConnectionType());
    setIsWifi(networkMonitor.getIsWifi());

    return () => {
      networkMonitor.removeListener('connectionChange', handleConnectionChange);
      networkMonitor.removeListener('connectionTypeChange', handleConnectionTypeChange);
    };
  }, [networkMonitor]);

  // Monitor pending changes
  useEffect(() => {
    const checkPendingChanges = async () => {
      try {
        const { SyncQueue } = await import('../sync/SyncQueue');
        const queue = new SyncQueue();
        const stats = await queue.getQueueStats();
        setPendingChanges(stats.pending);
      } catch (error) {
        console.error('Error checking pending changes:', error);
      }
    };

    checkPendingChanges();
    
    // Check periodically
    const interval = setInterval(checkPendingChanges, 10000); // Every 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  const manualSync = async () => {
    if (syncStatus.isSyncing) return;

    try {
      setSyncError(null);
      await syncEngine.sync({ automatic: false });
    } catch (error) {
      setSyncError(error.message);
    }
  };

  const clearSyncError = () => {
    setSyncError(null);
  };

  return {
    isOnline,
    isSyncing: syncStatus.isSyncing,
    syncProgress: syncStatus.progress,
    syncMessage: syncStatus.message,
    lastSyncTime,
    pendingChanges,
    connectionType,
    isWifi,
    syncError,
    manualSync,
    clearSyncError,
  };
}