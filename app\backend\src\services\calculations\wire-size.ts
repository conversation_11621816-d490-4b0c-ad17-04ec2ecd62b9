import { Decimal } from 'decimal.js';
import { 
  COPPER_AMPACITY_75C,
  ALUMINUM_AMPACITY_75C,
  SAFETY_MARGINS,
  calculateVoltageDrop,
  VOLTAGE_DROP_LIMITS
} from '@electrical/shared';
import { CacheService } from '../cache.service';

interface WireSizeInput {
  load_amps: number;
  voltage: number;
  phase: '1PH' | '3PH';
  conductor_type: 'CU' | 'AL';
  ambient_temp_f: number;
  terminals_rated_75c: boolean;
  continuous_load: boolean;
  distance?: number;
  max_voltage_drop_percent?: number;
}

interface WireSizeResult {
  load_amps: number;
  adjusted_amps: number;
  continuous_load_factor: number;
  temperature_derating_factor: number;
  required_ampacity: number;
  selected_wire_size: string;
  wire_ampacity: number;
  voltage_drop_calculated: boolean;
  voltage_drop_percent?: number;
  voltage_drop_passes?: boolean;
  minimum_size_for_vd?: string;
  final_wire_size: string;
  necReferences: string[];
  calculation_steps: string[];
  recommendations: string[];
}

export class WireSizeService {
  async calculate(input: WireSizeInput): Promise<WireSizeResult> {
    // Check cache first
    const cached = await CacheService.getCachedCalculation('wire-size', input);
    if (cached) {
      return cached.output as WireSizeResult;
    }
    
    const steps: string[] = [];
    const necRefs: string[] = [];
    const recommendations: string[] = [];
    
    // Step 1: Apply continuous load factor
    let adjustedAmps = new Decimal(input.load_amps);
    const continuousLoadFactor = input.continuous_load ? 1.25 : 1.0;
    
    if (input.continuous_load) {
      adjustedAmps = adjustedAmps.times(continuousLoadFactor);
      steps.push(`Continuous load adjustment: ${input.load_amps} A × 1.25 = ${adjustedAmps.toNumber()} A`);
      necRefs.push('210.19(A)(1)', '215.2(A)(1)');
    }
    
    // Step 2: Apply temperature derating
    let temperatureDeratingFactor = 1.0;
    
    if (input.ambient_temp_f > 86) { // 30°C
      if (input.ambient_temp_f <= 95) { // 35°C
        temperatureDeratingFactor = 0.94;
      } else if (input.ambient_temp_f <= 104) { // 40°C
        temperatureDeratingFactor = 0.88;
      } else if (input.ambient_temp_f <= 113) { // 45°C
        temperatureDeratingFactor = 0.82;
      } else {
        temperatureDeratingFactor = 0.75;
      }
      
      steps.push(`Temperature derating at ${input.ambient_temp_f}°F: factor = ${temperatureDeratingFactor}`);
      necRefs.push('310.15(B)(1)', 'Table 310.15(B)(1)(1)');
    }
    
    // Step 3: Calculate required ampacity
    const requiredAmpacity = adjustedAmps.dividedBy(temperatureDeratingFactor);
    steps.push(`Required ampacity: ${adjustedAmps.toNumber()} A ÷ ${temperatureDeratingFactor} = ${requiredAmpacity.toNumber()} A`);
    
    // Step 4: Select wire size based on ampacity
    const ampacityTable = input.conductor_type === 'CU' ? 
      COPPER_AMPACITY_75C : ALUMINUM_AMPACITY_75C;
    
    let selectedSize = '';
    let wireAmpacity = 0;
    
    for (const [size, ampacity] of Object.entries(ampacityTable)) {
      if (ampacity >= requiredAmpacity.toNumber()) {
        selectedSize = size;
        wireAmpacity = ampacity;
        break;
      }
    }
    
    if (!selectedSize) {
      throw new Error('No suitable wire size found for the given load');
    }
    
    steps.push(`Selected wire size based on ampacity: ${selectedSize} AWG/kcmil (${wireAmpacity} A)`);
    necRefs.push('310.16', 'Table 310.16');
    
    // Step 5: Check voltage drop if distance provided
    let finalWireSize = selectedSize;
    let voltageDropCalculated = false;
    let voltageDropPercent: number | undefined;
    let voltageDropPasses: boolean | undefined;
    let minimumSizeForVd: string | undefined;
    
    if (input.distance && input.distance > 0) {
      voltageDropCalculated = true;
      const maxVd = input.max_voltage_drop_percent || VOLTAGE_DROP_LIMITS.BRANCH_CIRCUIT;
      
      // Calculate voltage drop with selected size
      const { percentDrop } = calculateVoltageDrop(
        input.load_amps,
        input.distance,
        input.voltage,
        selectedSize,
        input.conductor_type === 'CU',
        input.phase === '3PH',
        0.9 // Assume 0.9 power factor
      );
      
      voltageDropPercent = percentDrop.toNumber();
      voltageDropPasses = percentDrop.lessThanOrEqualTo(maxVd * 100);
      
      steps.push(`Voltage drop check: ${voltageDropPercent.toFixed(2)}% (limit: ${(maxVd * 100).toFixed(0)}%)`);
      necRefs.push('210.19(A)(1) Note 4', '215.2(A)(4) Note 2');
      
      // If voltage drop fails, find larger size
      if (!voltageDropPasses) {
        const wireSizes = Object.keys(ampacityTable);
        const currentIndex = wireSizes.indexOf(selectedSize);
        
        for (let i = currentIndex; i >= 0; i--) {
          const testSize = wireSizes[i];
          const { percentDrop: testVd } = calculateVoltageDrop(
            input.load_amps,
            input.distance,
            input.voltage,
            testSize,
            input.conductor_type === 'CU',
            input.phase === '3PH',
            0.9
          );
          
          if (testVd.lessThanOrEqualTo(maxVd * 100)) {
            minimumSizeForVd = testSize;
            finalWireSize = testSize;
            steps.push(`Upsized to ${finalWireSize} for voltage drop compliance`);
            recommendations.push(`Wire size increased from ${selectedSize} to ${finalWireSize} to meet voltage drop requirements`);
            break;
          }
        }
        
        if (!minimumSizeForVd) {
          recommendations.push('Warning: No standard wire size meets voltage drop requirements');
          recommendations.push('Consider reducing circuit length or using parallel conductors');
        }
      }
    }
    
    // Step 6: Check terminal temperature rating
    if (!input.terminals_rated_75c) {
      recommendations.push('Verify terminal temperature ratings - calculation assumes 75°C terminals');
      necRefs.push('110.14(C)');
    }
    
    // Step 7: Additional recommendations
    if (wireAmpacity > requiredAmpacity.times(1.25).toNumber()) {
      recommendations.push('Selected wire has significant spare capacity for future loads');
    }
    
    if (input.continuous_load && input.load_amps > 100) {
      recommendations.push('Consider parallel conductors for large continuous loads');
      necRefs.push('310.10(G)');
    }
    
    const result: WireSizeResult = {
      load_amps: input.load_amps,
      adjusted_amps: adjustedAmps.toNumber(),
      continuous_load_factor: continuousLoadFactor,
      temperature_derating_factor: temperatureDeratingFactor,
      required_ampacity: requiredAmpacity.toNumber(),
      selected_wire_size: selectedSize,
      wire_ampacity: wireAmpacity,
      voltage_drop_calculated: voltageDropCalculated,
      voltage_drop_percent: voltageDropPercent,
      voltage_drop_passes: voltageDropPasses,
      minimum_size_for_vd: minimumSizeForVd,
      final_wire_size: finalWireSize,
      necReferences: [...new Set(necRefs)],
      calculation_steps: steps,
      recommendations: recommendations.length > 0 ? recommendations : ['Wire size is appropriate for the application']
    };
    
    // Cache the result
    await CacheService.cacheCalculation('wire-size', input, result);
    
    return result;
  }
}