# Electrical Contracting App - Deployment Guide

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Prerequisites](#prerequisites)
4. [Local Development](#local-development)
5. [Docker Configuration](#docker-configuration)
6. [Kubernetes Deployment](#kubernetes-deployment)
7. [CI/CD Pipeline](#cicd-pipeline)
8. [Monitoring & Logging](#monitoring--logging)
9. [Security](#security)
10. [Backup & Recovery](#backup--recovery)
11. [Troubleshooting](#troubleshooting)

## Overview

This guide provides comprehensive instructions for deploying the Electrical Contracting application across different environments using Docker, Kubernetes, and various cloud platforms.

## Architecture

The application consists of the following components:

- **Frontend**: React-based web application
- **Backend API**: Node.js/Express REST API
- **Agents Service**: AI-powered agent system
- **Databases**:
  - PostgreSQL (primary data store)
  - Redis (caching and session store)
  - Neo4j (graph database for relationships)
  - ChromaDB (vector store for AI)
  - InfluxDB (time-series metrics)

## Prerequisites

### Required Tools

```bash
# Docker & Docker Compose
docker --version  # >= 20.10
docker-compose --version  # >= 2.0

# Kubernetes
kubectl version  # >= 1.24
helm version  # >= 3.10

# Node.js & pnpm
node --version  # >= 20.0
pnpm --version  # >= 8.0

# Cloud CLI (choose one)
aws --version  # AWS CLI
gcloud --version  # Google Cloud SDK
az --version  # Azure CLI
```

### Environment Variables

Create a `.env` file for each environment:

```bash
# Database
DATABASE_URL=************************************/database
POSTGRES_USER=electrical
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=electrical_contracting

# Redis
REDIS_URL=redis://:password@host:6379
REDIS_PASSWORD=secure_redis_password

# Authentication
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Neo4j
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=secure_neo4j_password

# ChromaDB
CHROMADB_URL=http://chromadb:8000
CHROMADB_TOKEN=secure_chromadb_token

# InfluxDB
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=secure_influxdb_token
INFLUXDB_ORG=electrical_contracting
INFLUXDB_BUCKET=metrics

# External Services
SENTRY_DSN=https://your-sentry-dsn
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# S3 Backup
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_BACKUP_BUCKET=electrical-backups
```

## Local Development

### Using Docker Compose

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Start specific services
docker-compose -f docker-compose.dev.yml up backend frontend

# Run with build
docker-compose -f docker-compose.dev.yml up --build

# Stop all services
docker-compose -f docker-compose.dev.yml down

# Clean up volumes
docker-compose -f docker-compose.dev.yml down -v
```

### Access Points

- Frontend: http://localhost:5173
- Backend API: http://localhost:3000
- Agents Service: http://localhost:3001
- PostgreSQL: localhost:5432
- Redis: localhost:6379
- Neo4j Browser: http://localhost:7474
- ChromaDB: http://localhost:8000
- InfluxDB: http://localhost:8086
- Adminer: http://localhost:8080
- Mailhog: http://localhost:8025

## Docker Configuration

### Building Images

```bash
# Build all images
docker build -f docker/Dockerfile.backend -t electrical/backend:latest .
docker build -f docker/Dockerfile.frontend -t electrical/frontend:latest .
docker build -f docker/Dockerfile.agents -t electrical/agents:latest .

# Build with specific version
docker build -f docker/Dockerfile.backend -t electrical/backend:v1.0.0 .

# Push to registry
docker push electrical/backend:latest
docker push electrical/frontend:latest
docker push electrical/agents:latest
```

### Production Deployment

```bash
# Deploy with production compose file
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# View logs
docker-compose -f docker-compose.prod.yml logs -f backend
```

## Kubernetes Deployment

### Using Kustomize

```bash
# Deploy to development
kubectl apply -k kubernetes/overlays/dev

# Deploy to staging
kubectl apply -k kubernetes/overlays/staging

# Deploy to production
kubectl apply -k kubernetes/overlays/prod

# Verify deployment
kubectl get all -n electrical-production
```

### Using Helm

```bash
# Add Helm dependencies
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add neo4j https://neo4j.github.io/helm-charts
helm repo update

# Install with default values
helm install electrical-app helm/electrical-app -n electrical-production

# Install with custom values
helm install electrical-app helm/electrical-app \
  -f helm/electrical-app/values-production.yaml \
  -n electrical-production

# Upgrade deployment
helm upgrade electrical-app helm/electrical-app \
  --set image.backend.tag=v1.0.1 \
  -n electrical-production

# Rollback if needed
helm rollback electrical-app 1 -n electrical-production
```

### Manual Deployment

```bash
# Use the deployment script
./scripts/deploy.sh production v1.0.0

# Run smoke tests
./scripts/smoke-tests.sh production
```

## CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline automatically:

1. Runs tests and linting
2. Performs security scanning
3. Builds Docker images
4. Pushes to container registry
5. Deploys to appropriate environment
6. Runs smoke tests

### Manual Trigger

```bash
# Trigger workflow manually
gh workflow run ci-cd.yml -f environment=production -f version=v1.0.0
```

### Environment Promotion

```bash
# Promote from staging to production
git tag -a v1.0.0 -m "Release v1.0.0"
git push origin v1.0.0
```

## Monitoring & Logging

### Prometheus Metrics

```bash
# Port forward Prometheus
kubectl port-forward -n monitoring svc/prometheus 9090:9090

# Access Prometheus
open http://localhost:9090
```

### Grafana Dashboards

```bash
# Port forward Grafana
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Access Grafana (admin/admin)
open http://localhost:3000
```

### Application Logs

```bash
# View backend logs
kubectl logs -f deployment/backend -n electrical-production

# View all pods logs
kubectl logs -f -l app=backend -n electrical-production --all-containers

# Stream logs with stern
stern backend -n electrical-production
```

### Alerts

Alerts are configured for:
- Service downtime
- High CPU/Memory usage
- High error rates
- Slow response times
- Database issues

## Security

### SSL/TLS Certificates

```bash
# Cert-manager automatically provisions Let's Encrypt certificates
kubectl describe certificate electrical-tls -n electrical-production

# Manual certificate renewal
kubectl delete certificate electrical-tls -n electrical-production
```

### Secrets Management

```bash
# Create secrets
kubectl create secret generic backend-secrets \
  --from-env-file=.env.production \
  -n electrical-production

# Update secrets
kubectl create secret generic backend-secrets \
  --from-env-file=.env.production \
  -n electrical-production \
  --dry-run=client -o yaml | kubectl apply -f -
```

### Network Policies

Network policies restrict traffic between pods:
- Frontend can only talk to Backend
- Backend can talk to databases
- Agents can talk to Backend and Redis
- Databases are isolated

## Backup & Recovery

### Automated Backups

```bash
# Run backup manually
kubectl create job manual-backup-$(date +%s) \
  --from=cronjob/backup-job \
  -n electrical-production

# Check backup status
kubectl logs -f job/manual-backup-* -n electrical-production
```

### Restore Process

```bash
# PostgreSQL restore
kubectl exec -it postgres-0 -n electrical-production -- \
  psql -U electrical -d electrical_contracting < backup.sql

# Redis restore
kubectl cp backup.rdb redis-0:/data/dump.rdb -n electrical-production
kubectl exec -it redis-0 -n electrical-production -- redis-cli SHUTDOWN NOSAVE
kubectl delete pod redis-0 -n electrical-production
```

## Troubleshooting

### Common Issues

#### Pods not starting

```bash
# Check pod status
kubectl describe pod <pod-name> -n electrical-production

# Check events
kubectl get events -n electrical-production --sort-by='.lastTimestamp'
```

#### Database connection issues

```bash
# Test database connection
kubectl exec -it backend-<pod-id> -n electrical-production -- \
  nc -zv postgres 5432

# Check database logs
kubectl logs postgres-0 -n electrical-production
```

#### High memory usage

```bash
# Check resource usage
kubectl top pods -n electrical-production

# Adjust resource limits
kubectl edit deployment backend -n electrical-production
```

### Debug Mode

```bash
# Enable debug logging
kubectl set env deployment/backend LOG_LEVEL=debug -n electrical-production

# Access pod shell
kubectl exec -it backend-<pod-id> -n electrical-production -- /bin/sh

# Run diagnostic commands
curl http://localhost:3000/health
```

### Rollback Procedure

```bash
# Rollback deployment
kubectl rollout undo deployment/backend -n electrical-production

# Rollback to specific revision
kubectl rollout undo deployment/backend --to-revision=2 -n electrical-production

# Check rollout history
kubectl rollout history deployment/backend -n electrical-production
```

## Cloud-Specific Configurations

### AWS EKS

```bash
# Create EKS cluster
eksctl create cluster --name electrical-production --region us-east-1

# Configure kubectl
aws eks update-kubeconfig --name electrical-production --region us-east-1

# Install AWS Load Balancer Controller
helm install aws-load-balancer-controller \
  eks/aws-load-balancer-controller \
  -n kube-system
```

### Google GKE

```bash
# Create GKE cluster
gcloud container clusters create electrical-production \
  --zone us-central1-a \
  --num-nodes 3

# Get credentials
gcloud container clusters get-credentials electrical-production \
  --zone us-central1-a
```

### Azure AKS

```bash
# Create AKS cluster
az aks create \
  --resource-group electrical-rg \
  --name electrical-production \
  --node-count 3

# Get credentials
az aks get-credentials \
  --resource-group electrical-rg \
  --name electrical-production
```

## Maintenance

### Regular Tasks

1. **Daily**
   - Monitor alerts and dashboards
   - Check backup completion
   - Review error logs

2. **Weekly**
   - Update dependencies
   - Review security alerts
   - Optimize database queries

3. **Monthly**
   - Disaster recovery drill
   - Performance review
   - Cost optimization
   - Security audit

### Scaling

```bash
# Manual scaling
kubectl scale deployment backend --replicas=5 -n electrical-production

# Update HPA limits
kubectl edit hpa backend-hpa -n electrical-production
```

## Support

For issues or questions:
1. Check the logs first
2. Review this documentation
3. Check GitHub issues
4. Contact the development team

---

Last Updated: January 2024