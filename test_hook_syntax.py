#!/usr/bin/env python3
"""Test the Python syntax of hooks in the settings.json file"""

import ast
import json

# Define the Python commands from the hooks
hooks = [
    # PreToolUse - Memory operation
    """import json, sys, datetime; data = json.load(sys.stdin); print(f'Memory operation: {data.get("tool_name", "unknown")} at {datetime.datetime.utcnow().isoformat()}')""",
    
    # PostToolUse - Backup files
    """import os, json, hashlib, datetime; files = os.environ.get('CLAUDE_FILE_PATHS', '').split(); backup_dir = '.claude/backups'; os.makedirs(backup_dir, exist_ok=True); [os.system(f'cp "{f}" "{backup_dir}/{os.path.basename(f)}.{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.bak"') for f in files if os.path.exists(f)]""",
    
    # PostToolUse - Markdown lint
    """import os, subprocess; files = [f for f in os.environ.get('CLAUDE_FILE_PATHS', '').split() if f.endswith(('.md', '.markdown'))]; [subprocess.run(['markdownlint', f, '--fix'], capture_output=True) for f in files if os.path.exists(f)]""",
    
    # PostToolUse - Sensitive data check
    """import json, sys, os; data = json.load(sys.stdin); cmd = data.get('tool_input', {}).get('command', ''); dangerous_outputs = ['password', 'secret', 'token', 'api_key', 'private_key']; output = data.get('tool_output', {}).get('output', '').lower(); warned = any(term in output for term in dangerous_outputs); print(f'⚠️  Potential sensitive data in output') if warned else None""",
    
    # Stop - Project statistics
    """import json, datetime, os, glob; stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'project': os.path.basename(os.getcwd()), 'files_modified': len(glob.glob('.claude/backups/*.bak')), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown')}; report_file = '.claude/project-stats.json'; existing = []; os.makedirs(os.path.dirname(report_file), exist_ok=True); try: existing = json.load(open(report_file, 'r')) if os.path.exists(report_file) else []; except: pass; existing.append(stats); with open(report_file, 'w') as f: json.dump(existing[-50:], f, indent=2)""",
    
    # Stop - Session statistics
    """import json, datetime, os; session_stats = {'timestamp': datetime.datetime.utcnow().isoformat(), 'session_id': os.environ.get('CLAUDE_SESSION_ID', 'unknown'), 'duration_seconds': os.environ.get('CLAUDE_SESSION_DURATION', '0'), 'tools_used': os.environ.get('CLAUDE_TOOLS_USED', '').split(',') if os.environ.get('CLAUDE_TOOLS_USED') else []}; stats_file = '.claude/session-stats.json'; existing = []; os.makedirs(os.path.dirname(stats_file), exist_ok=True); try: existing = json.load(open(stats_file, 'r')) if os.path.exists(stats_file) else []; except: pass; existing.append(session_stats); with open(stats_file, 'w') as f: json.dump(existing[-100:], f, indent=2)"""
]

def test_python_syntax(code, description):
    """Test if Python code has valid syntax"""
    try:
        compile(code, '<string>', 'exec')
        print(f"✓ {description}: Valid syntax")
        return True
    except SyntaxError as e:
        print(f"✗ {description}: Syntax error")
        print(f"  Error: {e}")
        print(f"  Line: {e.text}")
        return False

# Test each hook
print("Testing Python syntax in hooks:\n")

descriptions = [
    "Memory operation hook",
    "Backup files hook",
    "Markdown lint hook",
    "Sensitive data check hook",
    "Project statistics hook",
    "Session statistics hook"
]

all_valid = True
for code, desc in zip(hooks, descriptions):
    if not test_python_syntax(code, desc):
        all_valid = False
    print()

if all_valid:
    print("✓ All Python hooks have valid syntax!")
else:
    print("✗ Some hooks have syntax errors that need to be fixed.")

# Also validate the JSON file itself
print("\nValidating JSON file structure:")
try:
    with open('/mnt/c/Projects/electrical/.claude/settings.json', 'r') as f:
        json.load(f)
    print("✓ JSON file is valid!")
except Exception as e:
    print(f"✗ JSON file error: {e}")