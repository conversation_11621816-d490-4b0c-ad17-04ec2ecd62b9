# Default values for electrical-app.
replicaCount:
  backend: 3
  frontend: 2
  agents: 2

image:
  backend:
    repository: electrical/backend
    pullPolicy: IfNotPresent
    tag: ""
  frontend:
    repository: electrical/frontend
    pullPolicy: IfNotPresent
    tag: ""
  agents:
    repository: electrical/agents
    pullPolicy: IfNotPresent
    tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1001
  fsGroup: 1001

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1001

service:
  backend:
    type: ClusterIP
    port: 3000
  frontend:
    type: LoadBalancer
    port: 80
    httpsPort: 443
  agents:
    type: ClusterIP
    port: 3001

ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: electrical-app.com
      paths:
        - path: /
          pathType: Prefix
          service: frontend
    - host: api.electrical-app.com
      paths:
        - path: /
          pathType: Prefix
          service: backend
  tls:
    - secretName: electrical-tls
      hosts:
        - electrical-app.com
        - www.electrical-app.com
        - api.electrical-app.com

resources:
  backend:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  frontend:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
  agents:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 512Mi

autoscaling:
  backend:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  frontend:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 80
  agents:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 75

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - backend
        topologyKey: kubernetes.io/hostname

# Application configuration
config:
  backend:
    nodeEnv: production
    corsOrigin: "https://electrical-app.com"
    sessionTimeout: "3600"
    maxUploadSize: "50"
    rateLimitWindow: "900000"
    rateLimitMax: "100"
  frontend:
    apiUrl: "https://api.electrical-app.com"
  agents:
    maxConcurrentAgents: "10"
    agentTimeout: "300000"
    memoryCacheTtl: "3600"

# Secrets configuration
secrets:
  backend:
    databaseUrl: ""
    redisUrl: ""
    jwtSecret: ""
    neo4jUri: ""
    neo4jUser: ""
    neo4jPassword: ""
    influxdbToken: ""
    sentryDsn: ""
  agents:
    redisUrl: ""
    neo4jUri: ""
    neo4jUser: ""
    neo4jPassword: ""
    sentryDsn: ""

# Database configuration
postgresql:
  enabled: true
  auth:
    username: electrical
    password: ""
    database: electrical_contracting
  primary:
    persistence:
      enabled: true
      size: 100Gi
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 2Gi

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: ""
  master:
    persistence:
      enabled: true
      size: 10Gi
  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 10Gi
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 256Mi

# Neo4j configuration
neo4j:
  enabled: true
  auth:
    username: neo4j
    password: ""
  core:
    persistence:
      enabled: true
      size: 50Gi
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 2Gi

# Monitoring
monitoring:
  enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  grafana:
    enabled: true
    dashboards:
      enabled: true

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"
  retention: 30
  s3:
    bucket: ""
    region: ""
    accessKey: ""
    secretKey: ""