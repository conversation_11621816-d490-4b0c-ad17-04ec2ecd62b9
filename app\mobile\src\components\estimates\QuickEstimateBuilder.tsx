import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { QuickEstimate, EstimateItem } from '../../types/electrical';
import { estimateService } from '../../services/estimateService';

interface Props {
  projectName?: string;
  customerId?: string;
  onSave: (estimate: QuickEstimate) => void;
}

export const QuickEstimateBuilder: React.FC<Props> = ({
  projectName = '',
  customerId = '',
  onSave,
}) => {
  const [estimate, setEstimate] = useState<QuickEstimate>({
    id: '',
    projectName,
    customer: customerId,
    items: [],
    laborHours: 0,
    laborRate: 85,
    materialCost: 0,
    laborCost: 0,
    overhead: 20,
    profit: 15,
    totalCost: 0,
    createdDate: new Date(),
    expirationDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    status: 'draft',
  });

  const [newItem, setNewItem] = useState<EstimateItem>({
    description: '',
    quantity: 1,
    unit: 'EA',
    materialCost: 0,
    laborHours: 0,
    totalCost: 0,
  });

  const units = ['EA', 'FT', 'LF', 'SF', 'HR', 'LOT'];

  const addItem = () => {
    if (!newItem.description) {
      Alert.alert('Error', 'Please enter item description');
      return;
    }

    const itemTotal = newItem.materialCost + (newItem.laborHours * estimate.laborRate);
    const item = {
      ...newItem,
      totalCost: itemTotal * newItem.quantity,
    };

    setEstimate(prev => ({
      ...prev,
      items: [...prev.items, item],
    }));

    // Reset new item form
    setNewItem({
      description: '',
      quantity: 1,
      unit: 'EA',
      materialCost: 0,
      laborHours: 0,
      totalCost: 0,
    });

    recalculateTotals();
  };

  const removeItem = (index: number) => {
    setEstimate(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
    recalculateTotals();
  };

  const updateItem = (index: number, field: keyof EstimateItem, value: any) => {
    const updatedItems = [...estimate.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };

    // Recalculate item total
    const item = updatedItems[index];
    item.totalCost = (item.materialCost + (item.laborHours * estimate.laborRate)) * item.quantity;

    setEstimate(prev => ({
      ...prev,
      items: updatedItems,
    }));

    recalculateTotals();
  };

  const recalculateTotals = () => {
    setTimeout(() => {
      setEstimate(prev => {
        const materialCost = prev.items.reduce((sum, item) => 
          sum + (item.materialCost * item.quantity), 0
        );
        const laborHours = prev.items.reduce((sum, item) => 
          sum + (item.laborHours * item.quantity), 0
        );
        const laborCost = laborHours * prev.laborRate;
        const subtotal = materialCost + laborCost;
        const overheadAmount = subtotal * (prev.overhead / 100);
        const profitAmount = (subtotal + overheadAmount) * (prev.profit / 100);
        const totalCost = subtotal + overheadAmount + profitAmount;

        return {
          ...prev,
          materialCost,
          laborHours,
          laborCost,
          totalCost,
        };
      });
    }, 0);
  };

  const handleSave = async () => {
    if (!estimate.projectName || !estimate.customer) {
      Alert.alert('Error', 'Please enter project name and customer');
      return;
    }

    if (estimate.items.length === 0) {
      Alert.alert('Error', 'Please add at least one item');
      return;
    }

    try {
      const savedEstimate = await estimateService.saveEstimate(estimate);
      onSave(savedEstimate);
      Alert.alert('Success', 'Estimate saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save estimate');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Quick Estimate Builder</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Project Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Project Name</Text>
          <TextInput
            style={styles.input}
            value={estimate.projectName}
            onChangeText={(text) => setEstimate({ ...estimate, projectName: text })}
            placeholder="Enter project name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Customer</Text>
          <TextInput
            style={styles.input}
            value={estimate.customer}
            onChangeText={(text) => setEstimate({ ...estimate, customer: text })}
            placeholder="Enter customer name"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Add Line Item</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={styles.input}
            value={newItem.description}
            onChangeText={(text) => setNewItem({ ...newItem, description: text })}
            placeholder="Item description"
          />
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1 }]}>
            <Text style={styles.label}>Quantity</Text>
            <TextInput
              style={styles.input}
              value={newItem.quantity.toString()}
              onChangeText={(text) => setNewItem({ ...newItem, quantity: parseFloat(text) || 0 })}
              keyboardType="numeric"
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
            <Text style={styles.label}>Unit</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={newItem.unit}
                onValueChange={(value) => setNewItem({ ...newItem, unit: value })}
                style={styles.picker}
              >
                {units.map(unit => (
                  <Picker.Item key={unit} label={unit} value={unit} />
                ))}
              </Picker>
            </View>
          </View>
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1 }]}>
            <Text style={styles.label}>Material Cost</Text>
            <TextInput
              style={styles.input}
              value={newItem.materialCost.toString()}
              onChangeText={(text) => setNewItem({ ...newItem, materialCost: parseFloat(text) || 0 })}
              keyboardType="numeric"
              placeholder="0.00"
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
            <Text style={styles.label}>Labor Hours</Text>
            <TextInput
              style={styles.input}
              value={newItem.laborHours.toString()}
              onChangeText={(text) => setNewItem({ ...newItem, laborHours: parseFloat(text) || 0 })}
              keyboardType="numeric"
              placeholder="0.0"
            />
          </View>
        </View>

        <TouchableOpacity style={styles.addButton} onPress={addItem}>
          <Text style={styles.addButtonText}>Add Item</Text>
        </TouchableOpacity>
      </View>

      {estimate.items.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Line Items</Text>
          
          {estimate.items.map((item, index) => (
            <View key={index} style={styles.lineItem}>
              <View style={styles.lineItemHeader}>
                <Text style={styles.lineItemDescription}>{item.description}</Text>
                <TouchableOpacity onPress={() => removeItem(index)}>
                  <Text style={styles.removeText}>Remove</Text>
                </TouchableOpacity>
              </View>
              
              <View style={styles.lineItemDetails}>
                <Text style={styles.lineItemDetail}>
                  {item.quantity} {item.unit} × ${item.materialCost} + {item.laborHours}hr
                </Text>
                <Text style={styles.lineItemTotal}>
                  ${item.totalCost.toFixed(2)}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pricing</Text>
        
        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1 }]}>
            <Text style={styles.label}>Labor Rate ($/hr)</Text>
            <TextInput
              style={styles.input}
              value={estimate.laborRate.toString()}
              onChangeText={(text) => {
                setEstimate({ ...estimate, laborRate: parseFloat(text) || 0 });
                recalculateTotals();
              }}
              keyboardType="numeric"
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
            <Text style={styles.label}>Overhead %</Text>
            <TextInput
              style={styles.input}
              value={estimate.overhead.toString()}
              onChangeText={(text) => {
                setEstimate({ ...estimate, overhead: parseFloat(text) || 0 });
                recalculateTotals();
              }}
              keyboardType="numeric"
            />
          </View>

          <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
            <Text style={styles.label}>Profit %</Text>
            <TextInput
              style={styles.input}
              value={estimate.profit.toString()}
              onChangeText={(text) => {
                setEstimate({ ...estimate, profit: parseFloat(text) || 0 });
                recalculateTotals();
              }}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.summary}>
        <Text style={styles.summaryTitle}>Estimate Summary</Text>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Material Cost:</Text>
          <Text style={styles.summaryValue}>${estimate.materialCost.toFixed(2)}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Labor Cost ({estimate.laborHours.toFixed(1)} hrs):</Text>
          <Text style={styles.summaryValue}>${estimate.laborCost.toFixed(2)}</Text>
        </View>
        
        <View style={styles.summaryDivider} />
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Subtotal:</Text>
          <Text style={styles.summaryValue}>
            ${(estimate.materialCost + estimate.laborCost).toFixed(2)}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Overhead ({estimate.overhead}%):</Text>
          <Text style={styles.summaryValue}>
            ${((estimate.materialCost + estimate.laborCost) * estimate.overhead / 100).toFixed(2)}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Profit ({estimate.profit}%):</Text>
          <Text style={styles.summaryValue}>
            ${((estimate.materialCost + estimate.laborCost) * (1 + estimate.overhead / 100) * estimate.profit / 100).toFixed(2)}
          </Text>
        </View>
        
        <View style={styles.summaryDivider} />
        
        <View style={styles.summaryRow}>
          <Text style={styles.totalLabel}>Total Estimate:</Text>
          <Text style={styles.totalValue}>${estimate.totalCost.toFixed(2)}</Text>
        </View>
      </View>

      <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
        <Text style={styles.saveButtonText}>Save Estimate</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  section: {
    backgroundColor: 'white',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  row: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  addButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  lineItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  lineItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  lineItemDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  removeText: {
    color: '#f44336',
    fontSize: 14,
  },
  lineItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  lineItemDetail: {
    fontSize: 14,
    color: '#666',
  },
  lineItemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  summary: {
    backgroundColor: '#E8F5E9',
    margin: 15,
    padding: 20,
    borderRadius: 10,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  summaryDivider: {
    height: 1,
    backgroundColor: '#4CAF50',
    marginVertical: 10,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  saveButton: {
    backgroundColor: '#2196F3',
    margin: 15,
    padding: 18,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});