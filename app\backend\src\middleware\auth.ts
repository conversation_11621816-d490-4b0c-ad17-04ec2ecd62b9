import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { AppError } from './errorHandler';
import { prisma } from '../index';

export interface JwtPayload {
  userId: string;
  email: string;
  role: string;
  id?: string; // Some routes expect this
  name?: string; // For display purposes
}

// Use the augmented Request type from Express
export type AuthRequest = Request;

export async function authenticate(
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      throw new AppError(401, 'No token provided', true, 'NO_TOKEN');
    }
    
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    
    // Verify user still exists
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });
    
    if (!user || user.deleted_at) {
      throw new AppError(401, 'User not found', true, 'USER_NOT_FOUND');
    }
    
    req.user = decoded;
    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      next(new AppError(401, 'Token expired', true, 'TOKEN_EXPIRED'));
    } else if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError(401, 'Invalid token', true, 'INVALID_TOKEN'));
    } else {
      next(error);
    }
  }
}

export function authorize(...roles: string[]) {
  return (req: Request, _res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AppError(401, 'Not authenticated', true, 'NOT_AUTHENTICATED'));
    }
    
    if (roles.length > 0 && !roles.includes(req.user.role)) {
      return next(new AppError(403, 'Insufficient permissions', true, 'FORBIDDEN'));
    }
    
    next();
  };
}

function extractToken(req: Request): string | null {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
}