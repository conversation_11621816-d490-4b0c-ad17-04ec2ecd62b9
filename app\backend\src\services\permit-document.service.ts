import { PrismaClient, PermitDocument, JurisdictionTemplate, Prisma } from '@prisma/client';
import { PDFDocument, StandardFonts, rgb, PageSizes } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';
import { format } from 'date-fns';
import { getTransactionService, executeStateTransition } from './transaction.service';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

interface PermitFormData {
  // Applicant Information
  applicant_name: string;
  applicant_license?: string;
  applicant_phone: string;
  applicant_email: string;
  
  // Property Information
  property_address: string;
  property_city: string;
  property_state: string;
  property_zip: string;
  property_owner: string;
  property_type: string; // RESIDENTIAL, COMMERCIAL, INDUSTRIAL
  
  // Project Information
  project_type: string; // NEW, ALTERATION, ADDITION, REPAIR
  project_description: string;
  estimated_cost: number;
  square_footage?: number;
  
  // Electrical Details
  service_size: number;
  voltage_system: string;
  main_disconnect_type: string;
  panel_count: number;
  total_circuits: number;
  grounding_system: string;
  
  // Additional Systems
  has_generator: boolean;
  generator_size?: number;
  has_solar: boolean;
  solar_system_size?: number;
  
  // Load Calculation Summary
  total_connected_load: number;
  total_demand_load: number;
  load_calculation_method: string; // STANDARD, OPTIONAL, EXISTING_LOAD
  
  // Code Compliance
  nec_edition: string;
  local_amendments?: string;
  
  // Contractor Information
  contractor_name: string;
  contractor_license: string;
  contractor_phone: string;
  contractor_email: string;
  contractor_insurance?: string;
}

interface LoadCalculationSummary {
  general_lighting_load: number;
  small_appliance_load: number;
  laundry_load: number;
  fixed_appliance_load: number;
  motor_load: number;
  hvac_load: number;
  other_loads: number;
  total_connected_load: number;
  demand_factors_applied: Array<{
    load_type: string;
    factor: number;
    nec_reference: string;
  }>;
  total_demand_load: number;
  service_size_required: number;
  service_size_selected: number;
}

export class PermitDocumentService {
  /**
   * Create a new permit document with transaction support
   */
  async createPermitDocument(data: {
    project_id: string;
    document_type: string;
    jurisdiction: string;
    jurisdiction_code?: string;
    title: string;
    description?: string;
    template_id?: string;
    form_data: any;
    created_by: string;
  }): Promise<PermitDocument> {
    const result = await transactionService.executeTransaction(async (tx) => {
      // Get the latest version number for this document type
      const latestDoc = await tx.permitDocument.findFirst({
        where: {
          project_id: data.project_id,
          document_type: data.document_type,
        },
        orderBy: { document_version: 'desc' },
      });

      const document = await tx.permitDocument.create({
        data: {
          ...data,
          document_version: latestDoc ? latestDoc.document_version + 1 : 1,
          form_data: JSON.stringify(data.form_data),
          status: 'DRAFT',
          created_at: new Date(),
          updated_at: new Date()
        },
      });

      // Create audit log entry
      await createAuditLog({
        action: AUDIT_ACTIONS.PERMIT_SUBMIT,
        userId: data.created_by,
        resourceType: 'PERMIT_DOCUMENT',
        resourceId: document.id,
        details: {
          document_type: data.document_type,
          version: document.document_version,
          jurisdiction: data.jurisdiction
        }
      });

      return document;
    });

    if (!result.success) {
      throw result.error || new Error('Failed to create permit document');
    }

    return result.data!;
  }

  /**
   * Get all permit documents for a project
   */
  async getProjectPermitDocuments(projectId: string): Promise<PermitDocument[]> {
    return await prisma.permitDocument.findMany({
      where: { project_id: projectId },
      orderBy: [
        { document_type: 'asc' },
        { document_version: 'desc' },
      ],
    });
  }

  /**
   * Get latest version of a specific document type
   */
  async getLatestDocument(
    projectId: string,
    documentType: string
  ): Promise<PermitDocument | null> {
    return await prisma.permitDocument.findFirst({
      where: {
        project_id: projectId,
        document_type: documentType,
      },
      orderBy: { document_version: 'desc' },
    });
  }

  /**
   * Update permit document
   */
  async updatePermitDocument(
    id: string,
    data: Partial<{
      status: string;
      form_data: any;
      included_calculations: string[];
      included_panels: string[];
      attachments: string[];
      inspector_notes: string;
      reviewed_by: string;
    }>
  ): Promise<PermitDocument> {
    const updateData: any = { ...data };
    
    if (data.form_data) {
      updateData.form_data = JSON.stringify(data.form_data);
    }
    if (data.included_calculations) {
      updateData.included_calculations = JSON.stringify(data.included_calculations);
    }
    if (data.included_panels) {
      updateData.included_panels = JSON.stringify(data.included_panels);
    }
    if (data.attachments) {
      updateData.attachments = JSON.stringify(data.attachments);
    }

    return await prisma.permitDocument.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Submit permit document with state transition
   */
  async submitPermitDocument(
    id: string,
    data: {
      permit_number?: string;
      submission_date: Date;
      submitted_by: string;
    }
  ): Promise<PermitDocument> {
    const result = await executeStateTransition(
      prisma,
      'PERMIT_DOCUMENT',
      id,
      'DRAFT',
      'SUBMITTED',
      async (tx) => {
        const document = await tx.permitDocument.findUnique({
          where: { id }
        });

        if (!document) {
          throw new Error('Permit document not found');
        }

        if (document.status !== 'DRAFT' && document.status !== 'READY') {
          throw new Error(`Cannot submit document in ${document.status} status`);
        }

        // Ensure document has PDF generated
        if (!document.generated_pdf_path) {
          throw new Error('Document must have PDF generated before submission');
        }

        return await tx.permitDocument.update({
          where: { id },
          data: {
            ...data,
            status: 'SUBMITTED',
            submission_date: data.submission_date,
            updated_at: new Date()
          },
        });
      },
      data.submitted_by
    );

    if (!result.success) {
      throw result.error || new Error('Failed to submit permit document');
    }

    return result.data!;
  }

  /**
   * Generate permit application PDF
   */
  async generatePermitApplicationPDF(
    documentId: string
  ): Promise<{ path: string; pageCount: number; sizeBytes: number }> {
    const document = await prisma.permitDocument.findUnique({
      where: { id: documentId },
      include: { project: { include: { customer: true } } },
    });

    if (!document) {
      throw new Error('Document not found');
    }

    const formData: PermitFormData = JSON.parse(document.form_data);
    
    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const timesRomanFont = await pdfDoc.embedFont(StandardFonts.TimesRoman);
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Add first page
    let page = pdfDoc.addPage(PageSizes.Letter);
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('ELECTRICAL PERMIT APPLICATION', {
      x: 50,
      y: yPosition,
      size: 18,
      font: helveticaBoldFont,
      color: rgb(0, 0, 0),
    });
    yPosition -= 30;

    page.drawText(`Jurisdiction: ${document.jurisdiction}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helveticaFont,
    });
    yPosition -= 20;

    page.drawText(`Application Date: ${format(new Date(), 'MM/dd/yyyy')}`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helveticaFont,
    });
    yPosition -= 30;

    // Section: Applicant Information
    this.drawSection(page, 'APPLICANT INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const applicantInfo = [
      ['Name:', formData.applicant_name],
      ['License #:', formData.applicant_license || 'N/A'],
      ['Phone:', formData.applicant_phone],
      ['Email:', formData.applicant_email],
    ];

    for (const [label, value] of applicantInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 10;

    // Section: Property Information
    this.drawSection(page, 'PROPERTY INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const propertyInfo = [
      ['Address:', formData.property_address],
      ['City/State/ZIP:', `${formData.property_city}, ${formData.property_state} ${formData.property_zip}`],
      ['Owner:', formData.property_owner],
      ['Type:', formData.property_type],
    ];

    for (const [label, value] of propertyInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 10;

    // Section: Project Information
    this.drawSection(page, 'PROJECT INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const projectInfo = [
      ['Project Type:', formData.project_type],
      ['Description:', formData.project_description],
      ['Estimated Cost:', `$${formData.estimated_cost.toLocaleString()}`],
      ['Square Footage:', formData.square_footage ? `${formData.square_footage} sq ft` : 'N/A'],
    ];

    for (const [label, value] of projectInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 10;

    // Section: Electrical System Details
    this.drawSection(page, 'ELECTRICAL SYSTEM DETAILS', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const electricalInfo = [
      ['Service Size:', `${formData.service_size} Amps`],
      ['Voltage System:', formData.voltage_system],
      ['Main Disconnect:', formData.main_disconnect_type],
      ['Number of Panels:', formData.panel_count.toString()],
      ['Total Circuits:', formData.total_circuits.toString()],
      ['Grounding System:', formData.grounding_system],
    ];

    for (const [label, value] of electricalInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }

    // Check if we need a new page
    if (yPosition < 150) {
      page = pdfDoc.addPage(PageSizes.Letter);
      yPosition = height - 50;
    }

    // Section: Load Calculation Summary
    yPosition -= 10;
    this.drawSection(page, 'LOAD CALCULATION SUMMARY', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const loadInfo = [
      ['Connected Load:', `${formData.total_connected_load} VA`],
      ['Demand Load:', `${formData.total_demand_load} VA`],
      ['Calculation Method:', formData.load_calculation_method],
      ['Service Size Required:', `${Math.ceil(formData.total_demand_load / (formData.voltage_system.includes('240') ? 240 : 208))} Amps`],
    ];

    for (const [label, value] of loadInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }

    // Add signature section
    if (yPosition < 200) {
      page = pdfDoc.addPage(PageSizes.Letter);
      yPosition = height - 50;
    }

    yPosition -= 30;
    this.drawSection(page, 'CERTIFICATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const certText = 'I hereby certify that the information provided in this application is true and correct to the best of my knowledge.';
    page.drawText(certText, {
      x: 50,
      y: yPosition,
      size: 10,
      font: helveticaFont,
      maxWidth: width - 100,
    });
    yPosition -= 40;

    // Signature lines
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 250, y: yPosition },
      thickness: 1,
    });
    page.drawText('Applicant Signature', {
      x: 50,
      y: yPosition - 15,
      size: 10,
      font: helveticaFont,
    });

    page.drawLine({
      start: { x: 300, y: yPosition },
      end: { x: 450, y: yPosition },
      thickness: 1,
    });
    page.drawText('Date', {
      x: 300,
      y: yPosition - 15,
      size: 10,
      font: helveticaFont,
    });

    // Save PDF and update document in transaction
    const pdfBytes = await pdfDoc.save();
    const outputDir = path.join(process.cwd(), 'generated-permits');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = `permit-${document.project_id}-${Date.now()}.pdf`;
    const filepath = path.join(outputDir, filename);
    await fs.writeFile(filepath, pdfBytes);

    // Update document with PDF info in a transaction
    const result = await transactionService.executeTransaction(async (tx) => {
      const updatedDoc = await tx.permitDocument.update({
        where: { id: documentId },
        data: {
          generated_pdf_path: filepath,
          generation_date: new Date(),
          file_size_bytes: pdfBytes.length,
          page_count: pdfDoc.getPageCount(),
          status: 'READY',
          updated_at: new Date()
        },
      });

      // Create audit log
      await createAuditLog({
        action: 'PERMIT_GENERATE_PDF',
        userId: document.created_by,
        resourceType: 'PERMIT_DOCUMENT',
        resourceId: documentId,
        details: {
          filename,
          sizeBytes: pdfBytes.length,
          pageCount: pdfDoc.getPageCount()
        }
      });

      return updatedDoc;
    });

    if (!result.success) {
      // Clean up file if transaction failed
      try {
        await fs.unlink(filepath);
      } catch (e) {
        // Ignore cleanup errors
      }
      throw result.error || new Error('Failed to update document with PDF info');
    }

    return {
      path: filepath,
      pageCount: pdfDoc.getPageCount(),
      sizeBytes: pdfBytes.length,
    };
  }

  /**
   * Generate load calculation summary PDF
   */
  async generateLoadCalculationPDF(
    projectId: string,
    panelId: string
  ): Promise<{ path: string; pageCount: number; sizeBytes: number }> {
    // Get project and panel data
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: { customer: true },
    });

    const panel = await prisma.panel.findUnique({
      where: { id: panelId },
      include: {
        circuits: true,
        load_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
      },
    });

    if (!project || !panel) {
      throw new Error('Project or panel not found');
    }

    const loadCalc = panel.load_calculations[0];
    if (!loadCalc) {
      throw new Error('No load calculation found for panel');
    }

    // Create PDF
    const pdfDoc = await PDFDocument.create();
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const page = pdfDoc.addPage(PageSizes.Letter);
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('LOAD CALCULATION SUMMARY', {
      x: 50,
      y: yPosition,
      size: 18,
      font: helveticaBoldFont,
    });
    yPosition -= 30;

    page.drawText(`Per NEC Article 220`, {
      x: 50,
      y: yPosition,
      size: 12,
      font: helveticaFont,
    });
    yPosition -= 30;

    // Project info
    this.drawSection(page, 'PROJECT INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const projectInfo = [
      ['Project:', project.name],
      ['Address:', `${project.address}, ${project.city}, ${project.state} ${project.zip}`],
      ['Panel:', panel.name],
      ['Location:', panel.location],
    ];

    for (const [label, value] of projectInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 20;

    // Panel specifications
    this.drawSection(page, 'PANEL SPECIFICATIONS', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const panelSpecs = [
      ['Voltage System:', panel.voltage_system],
      ['Ampere Rating:', `${panel.ampere_rating} A`],
      ['Bus Rating:', `${panel.bus_rating} A`],
      ['Phase Configuration:', panel.phase_config],
      ['Spaces Total:', panel.spaces_total.toString()],
      ['Spaces Used:', panel.spaces_used.toString()],
    ];

    for (const [label, value] of panelSpecs) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 20;

    // Load summary by type
    this.drawSection(page, 'LOAD SUMMARY BY TYPE', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    // Group circuits by load type
    const loadsByType = panel.circuits.reduce((acc, circuit) => {
      if (!circuit.is_spare && !circuit.is_space) {
        const type = circuit.load_type;
        if (!acc[type]) {
          acc[type] = { count: 0, connected: 0, demand: 0 };
        }
        acc[type].count++;
        acc[type].connected += circuit.connected_load;
        acc[type].demand += circuit.calculated_load;
      }
      return acc;
    }, {} as Record<string, { count: number; connected: number; demand: number }>);

    // Draw load type table
    page.drawText('Load Type', { x: 50, y: yPosition, size: 10, font: helveticaBoldFont });
    page.drawText('Circuits', { x: 200, y: yPosition, size: 10, font: helveticaBoldFont });
    page.drawText('Connected (VA)', { x: 280, y: yPosition, size: 10, font: helveticaBoldFont });
    page.drawText('Demand (VA)', { x: 380, y: yPosition, size: 10, font: helveticaBoldFont });
    yPosition -= 15;

    for (const [type, data] of Object.entries(loadsByType)) {
      page.drawText(type, { x: 50, y: yPosition, size: 10, font: helveticaFont });
      page.drawText(data.count.toString(), { x: 200, y: yPosition, size: 10, font: helveticaFont });
      page.drawText(data.connected.toFixed(0), { x: 280, y: yPosition, size: 10, font: helveticaFont });
      page.drawText(data.demand.toFixed(0), { x: 380, y: yPosition, size: 10, font: helveticaFont });
      yPosition -= 15;
    }
    yPosition -= 10;

    // Totals
    page.drawLine({
      start: { x: 50, y: yPosition },
      end: { x: 450, y: yPosition },
      thickness: 1,
    });
    yPosition -= 15;

    page.drawText('TOTAL:', { x: 50, y: yPosition, size: 10, font: helveticaBoldFont });
    page.drawText(loadCalc.total_connected_load.toFixed(0), { x: 280, y: yPosition, size: 10, font: helveticaBoldFont });
    page.drawText(loadCalc.total_demand_load.toFixed(0), { x: 380, y: yPosition, size: 10, font: helveticaBoldFont });
    yPosition -= 30;

    // Phase loading (for 3-phase panels)
    if (panel.phase_config.includes('THREE_PHASE')) {
      this.drawSection(page, 'PHASE LOADING', 50, yPosition, helveticaBoldFont);
      yPosition -= 20;

      const phaseLoading = [
        ['Phase A:', `${loadCalc.phase_a_load.toFixed(0)} VA`],
        ['Phase B:', `${loadCalc.phase_b_load.toFixed(0)} VA`],
        ['Phase C:', `${loadCalc.phase_c_load.toFixed(0)} VA`],
        ['Phase Imbalance:', `${loadCalc.phase_imbalance_percent.toFixed(1)}%`],
      ];

      for (const [label, value] of phaseLoading) {
        this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
        yPosition -= 18;
      }
      yPosition -= 20;
    }

    // Summary
    this.drawSection(page, 'SUMMARY', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const summary = [
      ['Total Connected Load:', `${loadCalc.total_connected_load.toFixed(0)} VA`],
      ['Total Demand Load:', `${loadCalc.total_demand_load.toFixed(0)} VA`],
      ['Load Percentage:', `${loadCalc.load_percentage.toFixed(1)}%`],
      ['Power Factor:', loadCalc.power_factor.toFixed(2)],
      ['Calculation Date:', format(loadCalc.calculation_date, 'MM/dd/yyyy HH:mm')],
    ];

    for (const [label, value] of summary) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }

    // Save PDF
    const pdfBytes = await pdfDoc.save();
    const outputDir = path.join(process.cwd(), 'generated-permits');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = `load-calc-${panelId}-${Date.now()}.pdf`;
    const filepath = path.join(outputDir, filename);
    await fs.writeFile(filepath, pdfBytes);

    return {
      path: filepath,
      pageCount: pdfDoc.getPageCount(),
      sizeBytes: pdfBytes.length,
    };
  }

  /**
   * Generate panel schedule PDF
   */
  async generatePanelSchedulePDF(
    panelId: string
  ): Promise<{ path: string; pageCount: number; sizeBytes: number }> {
    const panel = await prisma.panel.findUnique({
      where: { id: panelId },
      include: {
        project: { include: { customer: true } },
        circuits: { orderBy: { circuit_number: 'asc' } },
      },
    });

    if (!panel) {
      throw new Error('Panel not found');
    }

    // Create PDF
    const pdfDoc = await PDFDocument.create();
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    const courierFont = await pdfDoc.embedFont(StandardFonts.Courier);

    const page = pdfDoc.addPage(PageSizes.Letter);
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('PANEL SCHEDULE', {
      x: 50,
      y: yPosition,
      size: 18,
      font: helveticaBoldFont,
    });
    yPosition -= 30;

    // Panel info
    const panelInfo = [
      ['Panel Name:', panel.name],
      ['Location:', panel.location],
      ['Voltage:', panel.voltage_system],
      ['Rating:', `${panel.ampere_rating}A / ${panel.bus_rating}A Bus`],
      ['Manufacturer:', panel.manufacturer || 'N/A'],
      ['Model:', panel.model_number || 'N/A'],
    ];

    let xOffset = 50;
    for (let i = 0; i < panelInfo.length; i += 2) {
      this.drawLabelValue(page, panelInfo[i][0], panelInfo[i][1], xOffset, yPosition, helveticaFont);
      if (i + 1 < panelInfo.length) {
        this.drawLabelValue(page, panelInfo[i + 1][0], panelInfo[i + 1][1], xOffset + 250, yPosition, helveticaFont);
      }
      yPosition -= 20;
    }
    yPosition -= 10;

    // Circuit table header
    const tableHeaders = ['CKT', 'LOAD', 'A', 'POLE', 'WIRE', 'DESCRIPTION'];
    const columnWidths = [40, 80, 40, 40, 60, 240];
    xOffset = 50;

    for (let i = 0; i < tableHeaders.length; i++) {
      page.drawText(tableHeaders[i], {
        x: xOffset,
        y: yPosition,
        size: 10,
        font: helveticaBoldFont,
      });
      xOffset += columnWidths[i];
    }
    yPosition -= 15;

    // Draw circuits
    for (const circuit of panel.circuits) {
      if (yPosition < 50) {
        // New page
        page = pdfDoc.addPage(PageSizes.Letter);
        yPosition = height - 50;
      }

      xOffset = 50;
      const circuitData = [
        circuit.circuit_number.toString(),
        circuit.is_spare ? 'SPARE' : circuit.is_space ? 'SPACE' : `${circuit.connected_load.toFixed(0)}VA`,
        circuit.is_spare || circuit.is_space ? '-' : `${circuit.breaker_size}A`,
        circuit.poles.toString(),
        circuit.is_spare || circuit.is_space ? '-' : circuit.wire_size,
        circuit.description,
      ];

      for (let i = 0; i < circuitData.length; i++) {
        page.drawText(circuitData[i], {
          x: xOffset,
          y: yPosition,
          size: 9,
          font: courierFont,
        });
        xOffset += columnWidths[i];
      }
      yPosition -= 12;
    }

    // Save PDF
    const pdfBytes = await pdfDoc.save();
    const outputDir = path.join(process.cwd(), 'generated-permits');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = `panel-schedule-${panelId}-${Date.now()}.pdf`;
    const filepath = path.join(outputDir, filename);
    await fs.writeFile(filepath, pdfBytes);

    return {
      path: filepath,
      pageCount: pdfDoc.getPageCount(),
      sizeBytes: pdfBytes.length,
    };
  }

  /**
   * Generate inspection request form
   */
  async generateInspectionRequestPDF(
    documentId: string
  ): Promise<{ path: string; pageCount: number; sizeBytes: number }> {
    const document = await prisma.permitDocument.findUnique({
      where: { id: documentId },
      include: { project: { include: { customer: true } } },
    });

    if (!document) {
      throw new Error('Document not found');
    }

    const formData = JSON.parse(document.form_data);
    
    // Create PDF
    const pdfDoc = await PDFDocument.create();
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const page = pdfDoc.addPage(PageSizes.Letter);
    const { width, height } = page.getSize();
    let yPosition = height - 50;

    // Header
    page.drawText('ELECTRICAL INSPECTION REQUEST', {
      x: 50,
      y: yPosition,
      size: 18,
      font: helveticaBoldFont,
    });
    yPosition -= 30;

    // Permit info
    this.drawSection(page, 'PERMIT INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const permitInfo = [
      ['Permit Number:', formData.permit_number || 'PENDING'],
      ['Project Address:', `${document.project.address}, ${document.project.city}, ${document.project.state}`],
      ['Inspection Type:', formData.inspection_type],
      ['Requested Date:', formData.requested_date],
    ];

    for (const [label, value] of permitInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }
    yPosition -= 20;

    // Work completed
    this.drawSection(page, 'WORK COMPLETED', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const workItems = formData.work_completed || [];
    for (const item of workItems) {
      page.drawText(`• ${item}`, {
        x: 60,
        y: yPosition,
        size: 10,
        font: helveticaFont,
      });
      yPosition -= 15;
    }
    yPosition -= 20;

    // Contact info
    this.drawSection(page, 'CONTACT INFORMATION', 50, yPosition, helveticaBoldFont);
    yPosition -= 20;

    const contactInfo = [
      ['Contractor:', formData.contractor_name],
      ['License #:', formData.contractor_license],
      ['Phone:', formData.contractor_phone],
      ['On-site Contact:', formData.onsite_contact || formData.contractor_name],
      ['On-site Phone:', formData.onsite_phone || formData.contractor_phone],
    ];

    for (const [label, value] of contactInfo) {
      this.drawLabelValue(page, label, value, 50, yPosition, helveticaFont);
      yPosition -= 18;
    }

    // Save PDF
    const pdfBytes = await pdfDoc.save();
    const outputDir = path.join(process.cwd(), 'generated-permits');
    await fs.mkdir(outputDir, { recursive: true });
    
    const filename = `inspection-request-${document.project_id}-${Date.now()}.pdf`;
    const filepath = path.join(outputDir, filename);
    await fs.writeFile(filepath, pdfBytes);

    // Update document
    await prisma.permitDocument.update({
      where: { id: documentId },
      data: {
        generated_pdf_path: filepath,
        generation_date: new Date(),
        file_size_bytes: pdfBytes.length,
        page_count: pdfDoc.getPageCount(),
      },
    });

    return {
      path: filepath,
      pageCount: pdfDoc.getPageCount(),
      sizeBytes: pdfBytes.length,
    };
  }

  /**
   * Get jurisdiction templates
   */
  async getJurisdictionTemplates(
    state?: string,
    documentType?: string
  ): Promise<JurisdictionTemplate[]> {
    const where: Prisma.JurisdictionTemplateWhereInput = {};
    
    if (state) where.state = state;
    if (documentType) where.document_type = documentType;

    return await prisma.jurisdictionTemplate.findMany({
      where,
      orderBy: [
        { state: 'asc' },
        { jurisdiction_name: 'asc' },
      ],
    });
  }

  /**
   * Helper method to draw section header
   */
  private drawSection(page: any, text: string, x: number, y: number, font: any): void {
    page.drawText(text, {
      x,
      y,
      size: 12,
      font,
      color: rgb(0, 0, 0.8),
    });
  }

  /**
   * Helper method to draw label-value pair
   */
  private drawLabelValue(page: any, label: string, value: string, x: number, y: number, font: any): void {
    page.drawText(label, {
      x,
      y,
      size: 10,
      font,
    });
    page.drawText(value, {
      x: x + 120,
      y,
      size: 10,
      font,
    });
  }

  /**
   * Compile all calculation reports for a project
   */
  async compileCalculationReports(projectId: string): Promise<{
    arcFlashReports: any[];
    shortCircuitReports: any[];
    loadCalculations: any[];
  }> {
    // Get all panels for the project
    const panels = await prisma.panel.findMany({
      where: { project_id: projectId },
      include: {
        arc_flash_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
        short_circuit_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
        load_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
      },
    });

    const arcFlashReports = panels
      .filter(p => p.arc_flash_calculations.length > 0)
      .map(p => ({
        panel_name: p.name,
        calculation: p.arc_flash_calculations[0],
      }));

    const shortCircuitReports = panels
      .filter(p => p.short_circuit_calculations.length > 0)
      .map(p => ({
        panel_name: p.name,
        calculation: p.short_circuit_calculations[0],
      }));

    const loadCalculations = panels
      .filter(p => p.load_calculations.length > 0)
      .map(p => ({
        panel_name: p.name,
        calculation: p.load_calculations[0],
      }));

    return {
      arcFlashReports,
      shortCircuitReports,
      loadCalculations,
    };
  }

  /**
   * Add digital signature to document
   */
  async addDigitalSignature(
    documentId: string,
    signatureData: {
      signed_by: string;
      signature_data: string; // Base64 encoded image
    }
  ): Promise<PermitDocument> {
    return await prisma.permitDocument.update({
      where: { id: documentId },
      data: {
        signed_by: signatureData.signed_by,
        signature_date: new Date(),
        signature_data: signatureData.signature_data,
        status: 'READY',
      },
    });
  }

  /**
   * Track permit approval/rejection
   */
  async updatePermitStatus(
    documentId: string,
    data: {
      status: 'APPROVED' | 'REJECTED';
      permit_number?: string;
      approval_date?: Date;
      rejection_reason?: string;
      expiration_date?: Date;
    }
  ): Promise<PermitDocument> {
    return await prisma.permitDocument.update({
      where: { id: documentId },
      data,
    });
  }
}

export const permitDocumentService = new PermitDocumentService();