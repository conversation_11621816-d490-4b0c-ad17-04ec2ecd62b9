const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const dbPath = path.join(__dirname, 'prisma', 'dev.db');

if (!fs.existsSync(dbPath)) {
    console.error('Database file not found at:', dbPath);
    process.exit(1);
}

console.log('Applying performance indexes manually...\n');

const db = new sqlite3.Database(dbPath);

// Read the migration SQL
const migrationPath = path.join(__dirname, 'prisma', 'migrations', '20240115_performance_indexes', 'migration.sql');
const migrationSQL = fs.readFileSync(migrationPath, 'utf-8');

// Split SQL statements and filter out comments and empty lines
const statements = migrationSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt && !stmt.startsWith('--'));

let successCount = 0;
let errorCount = 0;

// Function to execute each statement
function executeStatement(index) {
    if (index >= statements.length) {
        console.log(`\n--- Summary ---`);
        console.log(`✓ Successfully created: ${successCount} indexes`);
        console.log(`✗ Errors encountered: ${errorCount}`);
        
        if (errorCount === 0) {
            console.log('\nAll indexes created successfully!');
            console.log('\nNow you can mark the migration as applied:');
            console.log('  npx prisma migrate resolve --applied "20240115_performance_indexes"');
        }
        
        db.close();
        return;
    }
    
    const stmt = statements[index] + ';';
    
    // Extract index name for logging
    const indexMatch = stmt.match(/CREATE INDEX IF NOT EXISTS "([^"]+)"/);
    const indexName = indexMatch ? indexMatch[1] : `Statement ${index + 1}`;
    
    db.run(stmt, function(err) {
        if (err) {
            if (err.message.includes('already exists')) {
                console.log(`⚠️  ${indexName} - Already exists (skipping)`);
                successCount++;
            } else {
                console.log(`✗ ${indexName} - Error: ${err.message}`);
                errorCount++;
            }
        } else {
            console.log(`✓ ${indexName} - Created successfully`);
            successCount++;
        }
        
        // Execute next statement
        executeStatement(index + 1);
    });
}

// Start executing statements
console.log('Executing index creation statements...\n');
executeStatement(0);