import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { EnhancedLogger, enhancedLogger } from '../utils/enhanced-logger';

/**
 * Middleware to add correlation ID to requests
 */
export function correlationIdMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Get or generate correlation ID
  const correlationId = (req.headers['x-correlation-id'] as string) || 
                       (req.headers['x-request-id'] as string) || 
                       uuidv4();

  // Generate request ID
  const requestId = uuidv4();

  // Attach to request
  (req as any).correlationId = correlationId;
  (req as any).requestId = requestId;

  // Create logger with context
  const logger = enhancedLogger.fromRequest(req);
  (req as any).logger = logger;

  // Set response headers
  res.setHeader('X-Correlation-ID', correlationId);
  res.setHeader('X-Request-ID', requestId);

  // Log request start
  logger.info('Request started', {
    query: req.query,
    params: req.params,
    headers: sanitizeHeaders(req.headers),
  });

  // Track request timing
  const startTime = Date.now();

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    
    // Log response
    logger.info('Request completed', {
      statusCode: res.statusCode,
      duration,
      responseSize: JSON.stringify(body).length,
    });

    // Log performance if slow
    if (duration > 1000) {
      logger.warn('Slow request detected', {
        duration,
        threshold: 1000,
      });
    }

    return originalJson.call(this, body);
  };

  // Handle response end for non-JSON responses
  const originalEnd = res.end;
  res.end = function(...args: any[]) {
    const duration = Date.now() - startTime;
    
    if (!res.headersSent || !res.getHeader('content-type')?.toString().includes('application/json')) {
      logger.info('Request completed (non-JSON)', {
        statusCode: res.statusCode,
        duration,
      });
    }

    return originalEnd.apply(this, args);
  };

  next();
}

/**
 * Sanitize headers to remove sensitive information
 */
function sanitizeHeaders(headers: any): any {
  const sensitiveHeaders = [
    'authorization',
    'cookie',
    'x-api-key',
    'x-auth-token',
  ];

  const sanitized = { ...headers };
  
  for (const header of sensitiveHeaders) {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]';
    }
  }

  return sanitized;
}