import { authService } from './authService';
import { securityService } from './securityService';
import api from './api';
import { LoginCredentials, RegisterData, AuthResponse, User } from '@types/auth';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { initializePinning } from 'react-native-ssl-pinning';

interface EnhancedLoginCredentials extends LoginCredentials {
  biometricAuth?: boolean;
  pinCode?: string;
  deviceInfo?: DeviceInfoData;
}

interface DeviceInfoData {
  deviceId: string;
  deviceName: string;
  platform: string;
  osVersion: string;
  appVersion: string;
  buildNumber: string;
}

interface PasswordStrength {
  score: number; // 0-4
  feedback: string[];
  isStrong: boolean;
}

class EnhancedAuthService {
  private readonly MIN_PASSWORD_LENGTH = 8;
  private readonly PASSWORD_PATTERNS = {
    uppercase: /[A-Z]/,
    lowercase: /[a-z]/,
    number: /[0-9]/,
    special: /[!@#$%^&*(),.?":{}|<>]/,
  };

  async initialize(): Promise<void> {
    // Initialize SSL pinning for API calls
    if (Platform.OS === 'ios') {
      await initializePinning({
        'api.electrical-contractor.com': {
          includeSubdomains: true,
          publicKeyHashes: [
            // Add your actual SSL certificate hashes here
            'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=',
            'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=',
          ],
        },
      });
    }

    // Initialize security service
    await securityService.initialize();
  }

  async login(credentials: EnhancedLoginCredentials): Promise<AuthResponse> {
    try {
      // Check device security
      const { isSecure, issues } = await securityService.checkDeviceSecurity();
      if (!isSecure) {
        console.warn('Device security issues detected:', issues);
        // You might want to show a warning to the user or block login
      }

      // Verify device binding if enabled
      const config = await securityService.getSecurityConfig();
      if (config.enableDeviceBinding) {
        const isValidDevice = await securityService.verifyDeviceBinding();
        if (!isValidDevice) {
          throw new Error('Device not authorized. Please contact support.');
        }
      }

      // Collect device information
      const deviceInfo = await this.getDeviceInfo();
      
      // Perform login with device info
      const loginData = {
        ...credentials,
        deviceInfo,
      };

      const response = await api.post<AuthResponse>('/auth/login', loginData);
      const { token, refreshToken, user } = response.data;

      // Store tokens securely
      await securityService.storeSecureToken(token, refreshToken);

      // Set up biometrics if requested
      if (credentials.biometricAuth) {
        await this.enableBiometricAuth();
      }

      // Reset activity timer
      securityService.updateLastActiveTime();

      return response.data;
    } catch (error) {
      console.error('Enhanced login error:', error);
      throw error;
    }
  }

  async loginWithBiometrics(): Promise<AuthResponse> {
    try {
      // Authenticate with biometrics
      const authenticated = await securityService.authenticateWithBiometrics(
        'Authenticate to access your account'
      );

      if (!authenticated) {
        throw new Error('Biometric authentication failed');
      }

      // Get stored tokens
      const { token, refreshToken } = await securityService.getSecureToken();
      if (!token) {
        throw new Error('No stored credentials found');
      }

      // Verify token is still valid
      try {
        const user = await this.getCurrentUser(token);
        return { token, refreshToken, user };
      } catch (error) {
        // Token expired, try to refresh
        if (refreshToken) {
          const newTokenData = await this.refreshToken(refreshToken);
          const user = await this.getCurrentUser(newTokenData.token);
          return { ...newTokenData, user };
        }
        throw error;
      }
    } catch (error) {
      console.error('Biometric login error:', error);
      throw error;
    }
  }

  async loginWithPin(pin: string): Promise<AuthResponse> {
    try {
      // Verify PIN
      const isValidPin = await securityService.verifyPinCode(pin);
      if (!isValidPin) {
        throw new Error('Invalid PIN');
      }

      // Get stored tokens
      const { token, refreshToken } = await securityService.getSecureToken();
      if (!token) {
        throw new Error('No stored credentials found');
      }

      // Verify token is still valid
      try {
        const user = await this.getCurrentUser(token);
        return { token, refreshToken, user };
      } catch (error) {
        // Token expired, try to refresh
        if (refreshToken) {
          const newTokenData = await this.refreshToken(refreshToken);
          const user = await this.getCurrentUser(newTokenData.token);
          return { ...newTokenData, user };
        }
        throw error;
      }
    } catch (error) {
      console.error('PIN login error:', error);
      throw error;
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Validate password strength
      const passwordStrength = this.checkPasswordStrength(data.password);
      if (!passwordStrength.isStrong) {
        throw new Error('Password is not strong enough. ' + passwordStrength.feedback.join(' '));
      }

      // Check device security
      const { isSecure, issues } = await securityService.checkDeviceSecurity();
      if (!isSecure) {
        console.warn('Device security issues detected:', issues);
      }

      // Collect device information
      const deviceInfo = await this.getDeviceInfo();

      // Perform registration
      const response = await api.post<AuthResponse>('/auth/register', {
        ...data,
        deviceInfo,
      });

      const { token, refreshToken } = response.data;

      // Store tokens securely
      await securityService.storeSecureToken(token, refreshToken);

      // Initialize device binding
      await securityService.initializeDeviceBinding();

      return response.data;
    } catch (error) {
      console.error('Enhanced registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      // Call logout API
      await authService.logout();

      // Clear all secure data
      await securityService.clearSecureTokens();
      
      // Lock the app
      await securityService.lockApp();
    } catch (error) {
      console.error('Enhanced logout error:', error);
      // Continue with local logout even if API call fails
      await securityService.clearSecureTokens();
      await securityService.lockApp();
    }
  }

  async enableBiometricAuth(): Promise<void> {
    const { available } = await securityService.checkBiometricAvailability();
    if (!available) {
      throw new Error('Biometric authentication is not available on this device');
    }

    // Update security config
    await securityService.setSecurityConfig({ enableBiometrics: true });
  }

  async setupPin(pin: string): Promise<void> {
    if (!/^\d{4,8}$/.test(pin)) {
      throw new Error('PIN must be 4-8 digits');
    }

    await securityService.setPinCode(pin);
    await securityService.setSecurityConfig({ enablePinCode: true });
  }

  async changePin(currentPin: string, newPin: string): Promise<void> {
    // Verify current PIN
    const isValid = await securityService.verifyPinCode(currentPin);
    if (!isValid) {
      throw new Error('Current PIN is incorrect');
    }

    // Set new PIN
    await this.setupPin(newPin);
  }

  checkPasswordStrength(password: string): PasswordStrength {
    const feedback: string[] = [];
    let score = 0;

    if (password.length < this.MIN_PASSWORD_LENGTH) {
      feedback.push(`Password must be at least ${this.MIN_PASSWORD_LENGTH} characters`);
    } else {
      score++;
    }

    if (!this.PASSWORD_PATTERNS.uppercase.test(password)) {
      feedback.push('Include at least one uppercase letter');
    } else {
      score++;
    }

    if (!this.PASSWORD_PATTERNS.lowercase.test(password)) {
      feedback.push('Include at least one lowercase letter');
    } else {
      score++;
    }

    if (!this.PASSWORD_PATTERNS.number.test(password)) {
      feedback.push('Include at least one number');
    } else {
      score++;
    }

    if (!this.PASSWORD_PATTERNS.special.test(password)) {
      feedback.push('Include at least one special character');
    } else {
      score++;
    }

    // Check for common patterns
    if (/(.)\1{2,}/.test(password)) {
      feedback.push('Avoid repeating characters');
      score = Math.max(0, score - 1);
    }

    if (/^(password|12345678|qwerty)/i.test(password)) {
      feedback.push('Avoid common passwords');
      score = 0;
    }

    return {
      score: Math.min(4, score),
      feedback,
      isStrong: score >= 4 && feedback.length === 0,
    };
  }

  private async getDeviceInfo(): Promise<DeviceInfoData> {
    return {
      deviceId: await DeviceInfo.getUniqueId(),
      deviceName: await DeviceInfo.getDeviceName(),
      platform: Platform.OS,
      osVersion: DeviceInfo.getSystemVersion(),
      appVersion: DeviceInfo.getVersion(),
      buildNumber: DeviceInfo.getBuildNumber(),
    };
  }

  private async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken?: string }> {
    const response = await api.post<{ token: string; refreshToken?: string }>('/auth/refresh', {
      refreshToken,
    });

    await securityService.storeSecureToken(response.data.token, response.data.refreshToken);
    return response.data;
  }

  private async getCurrentUser(token: string): Promise<User> {
    const response = await api.get<User>('/auth/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async checkAuthStatus(): Promise<{
    isAuthenticated: boolean;
    isLocked: boolean;
    requiresReauth: boolean;
  }> {
    const { token } = await securityService.getSecureToken();
    const isLocked = await securityService.isAppLocked();
    const config = await securityService.getSecurityConfig();

    return {
      isAuthenticated: !!token,
      isLocked,
      requiresReauth: config.requireReauthOnForeground && isLocked,
    };
  }

  async unlockApp(): Promise<void> {
    await securityService.unlockApp();
  }
}

export const enhancedAuthService = new EnhancedAuthService();
export type { EnhancedLoginCredentials, DeviceInfoData, PasswordStrength };