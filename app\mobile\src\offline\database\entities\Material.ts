import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

@Entity('materials')
export class Material extends BaseEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  category: string;

  @Column({ nullable: true })
  manufacturer: string;

  @Column({ nullable: true })
  modelNumber: string;

  @Column({ nullable: true })
  barcode: string;

  @Column({ type: 'float' })
  quantity: number;

  @Column()
  unit: string;

  @Column({ type: 'float' })
  unitPrice: number;

  @Column({ type: 'float' })
  totalPrice: number;

  @Column({ nullable: true })
  supplier: string;

  @Column({ nullable: true })
  location: string;

  @Column({ type: 'text', default: 'pending' })
  status: 'pending' | 'ordered' | 'received' | 'installed';

  @Column({ nullable: true })
  orderedDate: string;

  @Column({ nullable: true })
  receivedDate: string;

  @Column({ nullable: true })
  installedDate: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string

  @Column()
  projectId: string;

  @ManyToOne(() => Project, project => project.materials)
  project: Project;
}