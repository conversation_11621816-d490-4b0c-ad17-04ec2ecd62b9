// NEC (National Electrical Code) Constants and Tables

export const NEC_TABLES = {
  // NEC Table 310.16 - Ampacities for Copper Conductors at 75°C
  COPPER_AMPACITY_75C: {
    '14': 20,
    '12': 25,
    '10': 35,
    '8': 50,
    '6': 65,
    '4': 85,
    '3': 100,
    '2': 115,
    '1': 130,
    '1/0': 150,
    '2/0': 175,
    '3/0': 200,
    '4/0': 230,
    '250': 255,
    '300': 285,
    '350': 310,
    '400': 335,
    '500': 380,
    '600': 420,
    '700': 460,
    '750': 475,
    '800': 490,
    '900': 520,
    '1000': 545,
  },

  // NEC Table 310.16 - Ampacities for Aluminum Conductors at 75°C
  ALUMINUM_AMPACITY_75C: {
    '12': 20,
    '10': 30,
    '8': 40,
    '6': 50,
    '4': 65,
    '3': 75,
    '2': 90,
    '1': 100,
    '1/0': 120,
    '2/0': 135,
    '3/0': 155,
    '4/0': 180,
    '250': 205,
    '300': 230,
    '350': 250,
    '400': 270,
    '500': 310,
    '600': 340,
    '700': 375,
    '750': 385,
    '800': 395,
    '900': 425,
    '1000': 445,
  },

  // Temperature Correction Factors
  TEMPERATURE_CORRECTION: {
    '60': { '60C': 1.0, '75C': 1.0, '90C': 1.0 },
    '70': { '60C': 0.94, '75C': 1.0, '90C': 1.0 },
    '77': { '60C': 0.88, '75C': 0.94, '90C': 1.0 },
    '86': { '60C': 0.82, '75C': 0.88, '90C': 0.96 },
    '95': { '60C': 0.75, '75C': 0.82, '90C': 0.91 },
    '104': { '60C': 0.67, '75C': 0.75, '90C': 0.87 },
    '113': { '60C': 0.58, '75C': 0.67, '90C': 0.82 },
    '122': { '60C': 0.47, '75C': 0.58, '90C': 0.76 },
    '131': { '60C': 0.33, '75C': 0.47, '90C': 0.71 },
    '140': { '60C': 0.0, '75C': 0.33, '90C': 0.65 },
  },

  // Conduit Fill - Maximum number of conductors
  CONDUIT_FILL_PERCENTAGE: {
    1: 0.53, // 1 conductor
    2: 0.31, // 2 conductors
    3: 0.4, // 3 or more conductors
  },

  // Wire Insulation Types
  INSULATION_TYPES: {
    THHN: { temp: 90, dry: true, wet: false },
    THWN: { temp: 75, dry: true, wet: true },
    XHHW: { temp: 90, dry: true, wet: true },
    USE: { temp: 75, dry: false, wet: true },
  },
};

// Dwelling Unit Load Calculations (NEC 220)
export const DWELLING_LOADS = {
  GENERAL_LIGHTING: 3, // watts per sq ft
  SMALL_APPLIANCE_CIRCUIT: 1500, // watts per circuit
  LAUNDRY_CIRCUIT: 1500, // watts
  DEMAND_FACTORS: {
    FIRST_3000: 1.0,
    NEXT_117000: 0.35,
    OVER_120000: 0.25,
  },
};

// Motor Load Constants
export const MOTOR_CONSTANTS = {
  POWER_FACTORS: {
    SINGLE_PHASE: 0.8,
    THREE_PHASE: 0.85,
  },
  EFFICIENCY: {
    STANDARD: 0.85,
    HIGH_EFFICIENCY: 0.92,
  },
};

// Voltage Drop Limits
export const VOLTAGE_DROP_LIMITS = {
  BRANCH_CIRCUIT: 0.03, // 3%
  FEEDER: 0.03, // 3%
  COMBINED: 0.05, // 5%
};

// Arc Flash PPE Categories
export const PPE_CATEGORIES = [
  {
    category: 1,
    minCalCm2: 4,
    description: 'Arc-rated clothing, minimum 4 cal/cm²',
    requirements: [
      'Arc-rated long-sleeve shirt and pants',
      'Arc-rated face shield or flash suit hood',
      'Arc-rated jacket, parka, rainwear, or hard hat liner (as needed)',
      'Heavy-duty leather gloves',
      'Safety glasses or safety goggles',
      'Hearing protection',
      'Leather footwear',
    ],
  },
  {
    category: 2,
    minCalCm2: 8,
    description: 'Arc-rated clothing, minimum 8 cal/cm²',
    requirements: [
      'Arc-rated long-sleeve shirt and pants',
      'Arc-rated flash suit hood or arc-rated face shield and arc-rated balaclava',
      'Arc-rated jacket, parka, rainwear, or hard hat liner (as needed)',
      'Heavy-duty leather gloves',
      'Safety glasses or safety goggles',
      'Hearing protection',
      'Leather footwear',
    ],
  },
  {
    category: 3,
    minCalCm2: 25,
    description: 'Arc-rated clothing, minimum 25 cal/cm²',
    requirements: [
      'Arc-rated long-sleeve shirt',
      'Arc-rated pants',
      'Arc-rated coveralls',
      'Arc-rated arc flash suit jacket',
      'Arc-rated arc flash suit pants',
      'Arc-rated arc flash suit hood',
      'Arc-rated gloves',
      'Arc-rated jacket, parka, rainwear, or hard hat liner (as needed)',
      'Safety glasses or safety goggles',
      'Hearing protection',
      'Leather footwear',
    ],
  },
  {
    category: 4,
    minCalCm2: 40,
    description: 'Arc-rated clothing, minimum 40 cal/cm²',
    requirements: [
      'Arc-rated long-sleeve shirt',
      'Arc-rated pants',
      'Arc-rated coveralls',
      'Arc-rated arc flash suit jacket',
      'Arc-rated arc flash suit pants',
      'Arc-rated arc flash suit hood',
      'Arc-rated gloves',
      'Arc-rated jacket, parka, rainwear, or hard hat liner (as needed)',
      'Safety glasses or safety goggles',
      'Hearing protection',
      'Leather footwear',
    ],
  },
];

// Common Wire Sizes
export const WIRE_SIZES = [
  '14', '12', '10', '8', '6', '4', '3', '2', '1',
  '1/0', '2/0', '3/0', '4/0',
  '250', '300', '350', '400', '500', '600', '700', '750', '800', '900', '1000',
];

// Common Conduit Sizes
export const CONDUIT_SIZES = {
  EMT: ['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '3-1/2', '4'],
  PVC: ['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '3-1/2', '4', '5', '6'],
  RIGID: ['1/2', '3/4', '1', '1-1/4', '1-1/2', '2', '2-1/2', '3', '3-1/2', '4', '5', '6'],
};

// Common Voltages
export const COMMON_VOLTAGES = {
  RESIDENTIAL: [120, 240, 208],
  COMMERCIAL: [120, 208, 240, 277, 480],
  INDUSTRIAL: [120, 208, 240, 277, 480, 600, 4160],
};

// Inspection Checklist Categories
export const INSPECTION_CATEGORIES = [
  'Service Equipment',
  'Panels and Disconnects',
  'Branch Circuits',
  'Grounding and Bonding',
  'GFCI/AFCI Protection',
  'Lighting and Switches',
  'Receptacles',
  'Motors and Equipment',
  'Special Occupancies',
  'Emergency Systems',
];

// Material Categories
export const MATERIAL_CATEGORIES = [
  'Wire and Cable',
  'Conduit and Fittings',
  'Panels and Breakers',
  'Switches and Receptacles',
  'Lighting Fixtures',
  'Motor Controls',
  'Grounding Materials',
  'Tools and Safety',
  'Fasteners and Hardware',
  'Testing Equipment',
];

// Task Types for Time Tracking
export const TASK_TYPES = [
  { value: 'installation', label: 'Installation' },
  { value: 'troubleshooting', label: 'Troubleshooting' },
  { value: 'inspection', label: 'Inspection' },
  { value: 'planning', label: 'Planning' },
  { value: 'travel', label: 'Travel' },
];

// Emergency Contact Roles
export const EMERGENCY_ROLES = [
  'Electrical Inspector',
  'Utility Company',
  'Fire Department',
  'Project Manager',
  'Safety Officer',
  'General Contractor',
  'Building Owner',
  'Emergency Electrician',
];