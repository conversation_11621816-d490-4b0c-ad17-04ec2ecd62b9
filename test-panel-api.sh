#!/bin/bash

BASE_URL="http://localhost:3001/api"

# Test credentials - we'll use a known test user or register one
echo "Testing Panel Management API..."
echo "================================"

# First, let's try to register a test user
echo -e "\n1. Registering test user..."
REGISTER_RESPONSE=$(curl -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "test123",
    "name": "Test Admin",
    "role": "admin"
  }' \
  -s)

echo "Register response: $REGISTER_RESPONSE"

# Try to login with the test user
echo -e "\n2. Logging in..."
LOGIN_RESPONSE=$(curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "test123"
  }' \
  -s)

echo "Login response: $LOGIN_RESPONSE"

# Extract token using grep and sed
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*' | sed 's/"token":"//')

if [ -z "$TOKEN" ]; then
  echo "Failed to get auth token. Exiting."
  exit 1
fi

echo "Got token: ${TOKEN:0:20}..."

# 3. List all projects
echo -e "\n3. Listing all projects..."
PROJECTS_RESPONSE=$(curl -X GET "$BASE_URL/projects" \
  -H "Authorization: Bearer $TOKEN" \
  -s)

echo "Projects response: $PROJECTS_RESPONSE"

# Extract first project ID
PROJECT_ID=$(echo "$PROJECTS_RESPONSE" | grep -o '"id":"[^"]*' | head -1 | sed 's/"id":"//')

if [ -z "$PROJECT_ID" ]; then
  echo "No projects found. Creating a test project..."
  
  # Create a test project
  CREATE_PROJECT_RESPONSE=$(curl -X POST "$BASE_URL/projects" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "name": "Test Electrical Project",
      "address": "123 Test Street",
      "city": "Test City",
      "state": "TX",
      "zip": "12345",
      "type": "COMMERCIAL",
      "status": "PLANNING",
      "voltage_system": "208Y/120V",
      "service_size": 400,
      "square_footage": 5000
    }' \
    -s)
  
  echo "Create project response: $CREATE_PROJECT_RESPONSE"
  PROJECT_ID=$(echo "$CREATE_PROJECT_RESPONSE" | grep -o '"id":"[^"]*' | sed 's/"id":"//')
fi

echo "Using project ID: $PROJECT_ID"

# 4. Create a new electrical panel
echo -e "\n4. Creating new electrical panel..."
CREATE_PANEL_RESPONSE=$(curl -X POST "$BASE_URL/panels" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "'$PROJECT_ID'",
    "name": "Main Distribution Panel",
    "location": "Electrical Room 1",
    "panel_type": "MAIN",
    "voltage_system": "208Y/120V",
    "ampere_rating": 400,
    "phase_config": "THREE_PHASE",
    "spaces_total": 42
  }' \
  -s)

echo "Create panel response: $CREATE_PANEL_RESPONSE"

# Extract panel ID
PANEL_ID=$(echo "$CREATE_PANEL_RESPONSE" | grep -o '"id":"[^"]*' | sed 's/"id":"//')
echo "Created panel ID: $PANEL_ID"

# 5. List all panels for the project
echo -e "\n5. Listing panels for project..."
LIST_PANELS_RESPONSE=$(curl -X GET "$BASE_URL/panels?project_id=$PROJECT_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -s)

echo "List panels response: $LIST_PANELS_RESPONSE"

# 6. Add circuits to the panel
echo -e "\n6. Adding circuits to panel..."

# Circuit 1: General Lighting
echo "Adding Circuit 1: General Lighting..."
CIRCUIT1_RESPONSE=$(curl -X POST "$BASE_URL/circuits" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "panel_id": "'$PANEL_ID'",
    "circuit_number": 1,
    "description": "General Lighting",
    "ampere_rating": 20,
    "poles": 1,
    "circuit_type": "LIGHTING",
    "voltage": 120
  }' \
  -s)

echo "Circuit 1 response: $CIRCUIT1_RESPONSE"

# Circuit 2: Receptacles
echo -e "\nAdding Circuit 2: Receptacles Room 101..."
CIRCUIT2_RESPONSE=$(curl -X POST "$BASE_URL/circuits" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "panel_id": "'$PANEL_ID'",
    "circuit_number": 2,
    "description": "Receptacles Room 101",
    "ampere_rating": 20,
    "poles": 1,
    "circuit_type": "RECEPTACLE",
    "voltage": 120
  }' \
  -s)

echo "Circuit 2 response: $CIRCUIT2_RESPONSE"

# Circuit 3: HVAC Unit
echo -e "\nAdding Circuit 3: HVAC Unit 1..."
CIRCUIT3_RESPONSE=$(curl -X POST "$BASE_URL/circuits" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "panel_id": "'$PANEL_ID'",
    "circuit_number": 3,
    "description": "HVAC Unit 1",
    "ampere_rating": 30,
    "poles": 2,
    "circuit_type": "MOTOR",
    "voltage": 208
  }' \
  -s)

echo "Circuit 3 response: $CIRCUIT3_RESPONSE"

# 7. Calculate panel load
echo -e "\n7. Calculating panel load..."
LOAD_CALC_RESPONSE=$(curl -X GET "$BASE_URL/panels/$PANEL_ID/load-calculation" \
  -H "Authorization: Bearer $TOKEN" \
  -s)

echo "Load calculation response: $LOAD_CALC_RESPONSE"

# 8. Generate panel schedule
echo -e "\n8. Generating panel schedule..."
SCHEDULE_RESPONSE=$(curl -X GET "$BASE_URL/panels/$PANEL_ID/schedule" \
  -H "Authorization: Bearer $TOKEN" \
  -s)

echo "Panel schedule response: $SCHEDULE_RESPONSE"

echo -e "\n================================"
echo "Panel Management API Test Complete!"