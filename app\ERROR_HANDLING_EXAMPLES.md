# Error Handling Examples

This file provides practical examples of using the error handling system in the Electrical Contracting Application.

## Backend Examples

### 1. Service Layer Error Handling

```typescript
// services/project.service.ts
import { StructuredError, ErrorFactory } from '../utils/structured-error';
import { ErrorCode } from '../utils/error-codes';
import { enhancedLogger } from '../utils/enhanced-logger';
import { dbConnectionManager } from '../database/connection-manager';

export class ProjectService {
  private logger = enhancedLogger.child({ service: 'ProjectService' });

  async createProject(data: CreateProjectDto, userId: string) {
    const logger = this.logger.child({ operation: 'createProject', userId });
    
    try {
      // Validate business rules
      if (data.voltage > 480) {
        throw ErrorFactory.necViolation(
          'Voltage exceeds residential limits',
          'Article 220',
          { voltage: data.voltage, maxAllowed: 480 }
        );
      }

      // Execute with retry
      const project = await dbConnectionManager.executeWithRetry(
        async (prisma) => {
          // Check for duplicates
          const existing = await prisma.project.findFirst({
            where: { name: data.name, userId }
          });

          if (existing) {
            throw ErrorFactory.duplicate('Project', 'name', data.name);
          }

          // Create project
          return await prisma.project.create({
            data: { ...data, userId }
          });
        },
        { operationName: 'createProject', maxRetries: 3 }
      );

      logger.info('Project created successfully', { projectId: project.id });
      return project;

    } catch (error) {
      logger.error('Failed to create project', error as Error);
      throw error;
    }
  }

  async calculateLoad(projectId: string) {
    const logger = this.logger.child({ operation: 'calculateLoad', projectId });
    
    try {
      const project = await this.findById(projectId);
      
      // Perform calculation
      const totalLoad = await this.performLoadCalculation(project);
      
      // Check NEC compliance
      if (totalLoad > project.mainBreakerSize * 0.8) {
        throw new StructuredError(
          ErrorCode.LOAD_EXCEEDS_CAPACITY,
          `Total load ${totalLoad}A exceeds 80% of main breaker capacity`,
          {
            details: [{
              field: 'totalLoad',
              value: totalLoad,
              constraint: `Must not exceed ${project.mainBreakerSize * 0.8}A`
            }],
            context: { projectId, totalLoad, mainBreakerSize: project.mainBreakerSize }
          }
        );
      }

      return { totalLoad, status: 'compliant' };

    } catch (error) {
      // Add context to errors
      if (error instanceof StructuredError) {
        error.context = { ...error.context, projectId };
      }
      throw error;
    }
  }
}
```

### 2. Route Handler Error Handling

```typescript
// routes/projects.ts
import { Router } from 'express';
import { asyncErrorHandler } from '../middleware/enhanced-error-handler';
import { measurePerformance } from '../utils/enhanced-logger';

const router = Router();

router.post('/projects', asyncErrorHandler(async (req, res) => {
  const logger = (req as any).logger;
  
  // Performance monitoring
  const result = await measurePerformance('createProject', async () => {
    return await projectService.createProject(req.body, req.user.id);
  }, logger);

  // Audit log
  logger.audit('PROJECT_CREATED', {
    projectId: result.id,
    name: result.name
  });

  res.status(201).json(result);
}));

router.get('/projects/:id/calculate', asyncErrorHandler(async (req, res) => {
  const { id } = req.params;
  
  try {
    const calculation = await projectService.calculateLoad(id);
    res.json(calculation);
  } catch (error) {
    // Error is automatically handled by asyncErrorHandler
    throw error;
  }
}));
```

### 3. Database Transaction with Error Handling

```typescript
// services/transaction-example.ts
async function transferMaterials(fromProjectId: string, toProjectId: string, materials: Material[]) {
  const logger = enhancedLogger.child({ 
    operation: 'transferMaterials',
    fromProjectId,
    toProjectId 
  });

  return await dbConnectionManager.executeWithRetry(async (prisma) => {
    return await prisma.$transaction(async (tx) => {
      try {
        // Verify both projects exist
        const [fromProject, toProject] = await Promise.all([
          tx.project.findUnique({ where: { id: fromProjectId } }),
          tx.project.findUnique({ where: { id: toProjectId } })
        ]);

        if (!fromProject) {
          throw ErrorFactory.notFound('Source project', fromProjectId);
        }
        if (!toProject) {
          throw ErrorFactory.notFound('Destination project', toProjectId);
        }

        // Transfer materials
        for (const material of materials) {
          // Remove from source
          const removed = await tx.projectMaterial.delete({
            where: {
              projectId_materialId: {
                projectId: fromProjectId,
                materialId: material.id
              }
            }
          });

          // Add to destination
          await tx.projectMaterial.create({
            data: {
              projectId: toProjectId,
              materialId: material.id,
              quantity: removed.quantity
            }
          });

          logger.debug('Transferred material', {
            materialId: material.id,
            quantity: removed.quantity
          });
        }

        logger.info('Materials transferred successfully', {
          count: materials.length
        });

        return { success: true, transferredCount: materials.length };

      } catch (error) {
        logger.error('Transaction failed', error as Error);
        throw error; // Transaction will be rolled back
      }
    });
  }, { 
    operationName: 'materialTransfer',
    maxRetries: 2 // Fewer retries for transactions
  });
}
```

## Frontend Examples

### 1. Component with Error Boundary

```typescript
// components/ProjectDashboard.tsx
import { EnhancedErrorBoundary } from '../components/EnhancedErrorBoundary';
import { usePerformanceMonitor } from '../services/performance-monitor';
import { ErrorHandler, retryRequest } from '../utils/error-handler';

function ProjectDashboard({ projectId }: { projectId: string }) {
  const perf = usePerformanceMonitor('ProjectDashboard');
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchProject = async () => {
    try {
      setLoading(true);
      
      // Measure performance and retry on failure
      const data = await perf.measureAsync('fetchProject', async () => {
        return await retryRequest(
          () => api.get(`/projects/${projectId}`),
          {
            maxAttempts: 3,
            onRetry: (attempt, delay) => {
              console.log(`Retrying fetch (attempt ${attempt}) in ${delay}ms`);
            }
          }
        );
      });

      setProject(data.data);
    } catch (error) {
      const apiError = ErrorHandler.handleApiError(error, {
        context: { projectId },
        showNotification: true,
        onError: (err) => setError(err)
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProject();
  }, [projectId]);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay error={error} onRetry={fetchProject} />;

  return (
    <div>
      {/* Isolate risky components */}
      <EnhancedErrorBoundary 
        isolate 
        fallback={<div>Chart failed to load</div>}
      >
        <ComplexChart data={project.analytics} />
      </EnhancedErrorBoundary>

      <ProjectDetails project={project} />
    </div>
  );
}

// Wrap entire dashboard
export default function ProjectDashboardPage({ projectId }: { projectId: string }) {
  return (
    <EnhancedErrorBoundary
      resetKeys={[projectId]}
      showDetails={process.env.NODE_ENV === 'development'}
    >
      <ProjectDashboard projectId={projectId} />
    </EnhancedErrorBoundary>
  );
}
```

### 2. Form with Validation Error Handling

```typescript
// components/ProjectForm.tsx
import { ErrorHandler } from '../utils/error-handler';
import { useState } from 'react';

function ProjectForm() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      setSubmitting(true);
      setErrors({});

      const response = await api.post('/projects', data);
      
      // Success
      toast.success('Project created successfully!');
      navigate(`/projects/${response.data.id}`);

    } catch (error) {
      const apiError = ErrorHandler.handleApiError(error, {
        showNotification: false, // We'll show field-specific errors
        context: { formData: data }
      });

      // Extract validation errors
      const fieldErrors = ErrorHandler.handleValidationErrors(apiError);
      setErrors(fieldErrors);

      // Show general error if no field-specific errors
      if (Object.keys(fieldErrors).length === 0) {
        toast.error(apiError.message);
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <input 
          name="name" 
          className={errors.name ? 'error' : ''}
        />
        {errors.name && <span className="error-message">{errors.name}</span>}
      </div>

      <div>
        <input 
          name="voltage" 
          type="number"
          className={errors.voltage ? 'error' : ''}
        />
        {errors.voltage && <span className="error-message">{errors.voltage}</span>}
      </div>

      <button type="submit" disabled={submitting}>
        {submitting ? 'Creating...' : 'Create Project'}
      </button>
    </form>
  );
}
```

### 3. Offline-Capable Component

```typescript
// components/OfflineProjectList.tsx
import { ErrorHandler, isNetworkError } from '../utils/error-handler';
import { useOfflineSync } from '../hooks/useOfflineSync';

function OfflineProjectList() {
  const { isOnline, syncPending } = useOfflineSync();
  const [projects, setProjects] = useState([]);
  const [lastSyncError, setLastSyncError] = useState(null);

  const fetchProjects = async () => {
    try {
      const response = await api.get('/projects');
      setProjects(response.data);
      
      // Cache for offline use
      localStorage.setItem('cached_projects', JSON.stringify({
        data: response.data,
        timestamp: Date.now()
      }));

    } catch (error) {
      if (isNetworkError(error)) {
        // Load from cache
        const cached = localStorage.getItem('cached_projects');
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          setProjects(data);
          
          toast.info('Showing cached data (offline mode)');
        } else {
          throw error; // No cache available
        }
      } else {
        throw error;
      }
    }
  };

  const syncData = async () => {
    try {
      // Sync any pending changes
      await syncService.syncPendingChanges();
      
      // Refresh data
      await fetchProjects();
      
      setLastSyncError(null);
      toast.success('Data synchronized successfully');

    } catch (error) {
      const apiError = ErrorHandler.handleApiError(error, {
        showNotification: false,
        context: { operation: 'sync' }
      });
      
      setLastSyncError(apiError);
      
      // Check if it's a network issue
      if (isNetworkError(error)) {
        toast.warning('Sync failed - will retry when connection is restored');
      } else {
        toast.error(`Sync failed: ${apiError.message}`);
      }
    }
  };

  return (
    <div>
      <div className="sync-status">
        {!isOnline && <OfflineIndicator />}
        {syncPending > 0 && (
          <Badge variant="warning">
            {syncPending} changes pending sync
          </Badge>
        )}
        {lastSyncError && (
          <Badge variant="error" onClick={syncData}>
            Sync failed - tap to retry
          </Badge>
        )}
      </div>

      <ProjectList projects={projects} />
    </div>
  );
}
```

### 4. Performance-Monitored Data Table

```typescript
// components/MaterialsTable.tsx
import { usePerformanceMonitor } from '../services/performance-monitor';
import { performanceMonitor } from '../services/performance-monitor';

function MaterialsTable({ projectId }: { projectId: string }) {
  const perf = usePerformanceMonitor('MaterialsTable');
  const [materials, setMaterials] = useState([]);

  useEffect(() => {
    perf.mark('fetchStart');
    
    const fetchMaterials = async () => {
      try {
        const data = await perf.measureAsync('fetchMaterials', async () => {
          return await api.get(`/projects/${projectId}/materials`);
        });

        setMaterials(data.data);
        
        perf.mark('fetchEnd');
        perf.measure('totalFetchTime', 'fetchStart', 'fetchEnd');

        // Check if data set is large
        if (data.data.length > 1000) {
          performanceMonitor.recordMetric('largeMaterialSet', data.data.length, 'items', {
            projectId
          });
        }

      } catch (error) {
        ErrorHandler.handleApiError(error, {
          context: { projectId, component: 'MaterialsTable' }
        });
      }
    };

    fetchMaterials();
  }, [projectId]);

  // Measure render performance
  perf.measureRender(() => {
    return (
      <VirtualizedTable
        data={materials}
        onRenderComplete={(duration) => {
          if (duration > 100) {
            performanceMonitor.recordMetric('slowTableRender', duration, 'ms', {
              rowCount: materials.length
            });
          }
        }}
      />
    );
  });
}
```

## Error Recovery Patterns

### 1. Automatic Retry with Circuit Breaker

```typescript
// services/circuit-breaker.ts
class CircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailTime > 60000) { // 1 minute
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'closed';
  }

  private onFailure() {
    this.failures++;
    this.lastFailTime = Date.now();
    
    if (this.failures >= 5) {
      this.state = 'open';
      errorService.logError(new Error('Circuit breaker opened'), {
        failures: this.failures
      });
    }
  }
}
```

### 2. Graceful Degradation

```typescript
// components/AdvancedCalculator.tsx
function AdvancedCalculator() {
  const [mode, setMode] = useState<'full' | 'basic'>('full');

  return (
    <EnhancedErrorBoundary
      fallback={<BasicCalculator />}
      onError={(error) => {
        // Fall back to basic mode on error
        setMode('basic');
        errorService.logWarning('Falling back to basic calculator', {
          error: error.message
        });
      }}
    >
      {mode === 'full' ? <FullCalculator /> : <BasicCalculator />}
    </EnhancedErrorBoundary>
  );
}
```

## Testing Error Scenarios

### 1. Testing Error Handling

```typescript
// __tests__/project.service.test.ts
describe('ProjectService Error Handling', () => {
  it('should handle database connection errors', async () => {
    // Mock database failure
    jest.spyOn(prisma.project, 'create').mockRejectedValue(
      new Error('Connection refused')
    );

    await expect(projectService.createProject(data, userId))
      .rejects.toThrow(StructuredError);
  });

  it('should enforce NEC compliance', async () => {
    const data = { voltage: 600 }; // Exceeds limit

    await expect(projectService.createProject(data, userId))
      .rejects.toThrow(ErrorCode.NEC_VIOLATION);
  });

  it('should retry transient failures', async () => {
    const spy = jest.spyOn(prisma.project, 'findMany')
      .mockRejectedValueOnce(new Error('Timeout'))
      .mockResolvedValueOnce([]);

    const result = await projectService.findAll();
    
    expect(spy).toHaveBeenCalledTimes(2);
    expect(result).toEqual([]);
  });
});
```

### 2. Testing Frontend Error Handling

```typescript
// __tests__/ProjectForm.test.tsx
describe('ProjectForm Error Handling', () => {
  it('should display validation errors', async () => {
    // Mock API error response
    mockApi.post.mockRejectedValue({
      response: {
        status: 400,
        data: {
          error: {
            code: 'VALIDATION_ERROR',
            details: [
              { field: 'name', message: 'Name is required' },
              { field: 'voltage', message: 'Voltage must be between 120-480' }
            ]
          }
        }
      }
    });

    const { getByText, getByLabelText } = render(<ProjectForm />);
    
    fireEvent.submit(getByText('Create Project'));
    
    await waitFor(() => {
      expect(getByText('Name is required')).toBeInTheDocument();
      expect(getByText('Voltage must be between 120-480')).toBeInTheDocument();
    });
  });

  it('should handle network errors gracefully', async () => {
    mockApi.post.mockRejectedValue({
      code: 'ERR_NETWORK',
      message: 'Network Error'
    });

    const { getByText } = render(<ProjectForm />);
    
    fireEvent.submit(getByText('Create Project'));
    
    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith(
        expect.stringContaining('network')
      );
    });
  });
});
```