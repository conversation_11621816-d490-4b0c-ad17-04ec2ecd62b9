-- Initial database setup for Electrical Contracting App

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'electrician', 'apprentice', 'client');
CREATE TYPE project_status AS ENUM ('draft', 'quoted', 'approved', 'in_progress', 'completed', 'cancelled');
CREATE TYPE inspection_status AS ENUM ('pending', 'scheduled', 'in_progress', 'passed', 'failed', 'requires_followup');
CREATE TYPE calculation_type AS ENUM ('voltage_drop', 'wire_size', 'conduit_fill', 'load_calculation', 'short_circuit', 'arc_flash');

-- Create audit function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    table_name VARCHAR(255) NOT NULL,
    operation VARCHAR(50) NOT NULL,
    user_id UUID,
    changed_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (table_name, operation, user_id, changed_data)
    VALUES (
        TG_TABLE_NAME,
        TG_OP,
        current_setting('app.current_user_id', true)::UUID,
        to_jsonb(NEW)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for audit logs
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_operation ON audit_logs(operation);

-- Create performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gin_audit_changed_data ON audit_logs USING gin(changed_data);

-- Configure shared_preload_libraries for pg_stat_statements
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';

-- Create read-only user for analytics
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'electrical_readonly') THEN
        CREATE USER electrical_readonly WITH PASSWORD 'readonly_password';
    END IF;
END $$;

GRANT CONNECT ON DATABASE electrical_contracting TO electrical_readonly;
GRANT USAGE ON SCHEMA public TO electrical_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO electrical_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO electrical_readonly;

-- Create application user with limited privileges
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'electrical_app') THEN
        CREATE USER electrical_app WITH PASSWORD 'app_password';
    END IF;
END $$;

GRANT CONNECT ON DATABASE electrical_contracting TO electrical_app;
GRANT USAGE ON SCHEMA public TO electrical_app;
GRANT CREATE ON SCHEMA public TO electrical_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO electrical_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO electrical_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO electrical_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO electrical_app;

-- Create backup user
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'electrical_backup') THEN
        CREATE USER electrical_backup WITH PASSWORD 'backup_password';
    END IF;
END $$;

GRANT CONNECT ON DATABASE electrical_contracting TO electrical_backup;
GRANT USAGE ON SCHEMA public TO electrical_backup;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO electrical_backup;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO electrical_backup;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO electrical_backup;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON SEQUENCES TO electrical_backup;

-- Set database parameters for performance
ALTER DATABASE electrical_contracting SET random_page_cost = 1.1;
ALTER DATABASE electrical_contracting SET effective_io_concurrency = 200;
ALTER DATABASE electrical_contracting SET work_mem = '10MB';
ALTER DATABASE electrical_contracting SET maintenance_work_mem = '256MB';
ALTER DATABASE electrical_contracting SET checkpoint_completion_target = 0.9;
ALTER DATABASE electrical_contracting SET wal_buffers = '16MB';
ALTER DATABASE electrical_contracting SET default_statistics_target = 100;
ALTER DATABASE electrical_contracting SET effective_cache_size = '4GB';

-- Vacuum and analyze all tables
VACUUM ANALYZE;

-- Create materialized views for reporting
CREATE MATERIALIZED VIEW IF NOT EXISTS project_summary AS
SELECT 
    p.id,
    p.name,
    p.status,
    COUNT(DISTINCT t.id) as task_count,
    COUNT(DISTINCT i.id) as inspection_count,
    SUM(e.total_amount) as total_estimate,
    p.created_at,
    p.updated_at
FROM projects p
LEFT JOIN tasks t ON t.project_id = p.id
LEFT JOIN inspections i ON i.project_id = p.id
LEFT JOIN estimates e ON e.project_id = p.id
GROUP BY p.id;

CREATE INDEX idx_project_summary_status ON project_summary(status);
CREATE INDEX idx_project_summary_created ON project_summary(created_at);

-- Create function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY project_summary;
END;
$$ LANGUAGE plpgsql;

-- Schedule periodic refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-materialized-views', '0 */6 * * *', 'SELECT refresh_materialized_views();');