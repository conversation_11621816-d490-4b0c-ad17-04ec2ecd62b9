import { AxiosError } from 'axios';

export interface ErrorLog {
  id: string;
  message: string;
  stack?: string;
  context?: Record<string, any>;
  timestamp: string;
  level: 'error' | 'warn' | 'info';
  userId?: string;
  correlationId?: string;
}

class ErrorService {
  private readonly maxLogs = 100;
  private logs: ErrorLog[] = [];

  /**
   * Generate a unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a correlation ID for request tracking
   */
  generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract error message from various error types
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') return error;
    
    if (error instanceof AxiosError) {
      if (error.response?.data?.message) return error.response.data.message;
      if (error.response?.data?.error) return error.response.data.error;
      if (error.message) return error.message;
      return 'Network request failed';
    }
    
    if (error instanceof Error) return error.message;
    if (error?.message) return error.message;
    
    return 'An unknown error occurred';
  }

  /**
   * Extract detailed error information from Axios errors
   */
  private extractAxiosErrorDetails(error: AxiosError): Record<string, any> {
    return {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.config?.headers,
    };
  }

  /**
   * Log an error with context
   */
  logError(
    error: any,
    context?: Record<string, any>,
    level: 'error' | 'warn' | 'info' = 'error'
  ): string {
    const errorId = this.generateErrorId();
    const errorLog: ErrorLog = {
      id: errorId,
      message: this.extractErrorMessage(error),
      stack: error?.stack,
      context: {
        ...context,
        ...(error instanceof AxiosError ? this.extractAxiosErrorDetails(error) : {}),
      },
      timestamp: new Date().toISOString(),
      level,
      userId: this.getCurrentUserId(),
      correlationId: context?.correlationId,
    };

    // Add to in-memory logs
    this.logs.push(errorLog);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${errorId}]`, errorLog);
    }

    // Store in localStorage for debugging
    this.persistToLocalStorage(errorLog);

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService(errorLog);
    }

    return errorId;
  }

  /**
   * Log a warning
   */
  logWarning(message: string, context?: Record<string, any>): string {
    return this.logError(new Error(message), context, 'warn');
  }

  /**
   * Log info for important events
   */
  logInfo(message: string, context?: Record<string, any>): string {
    return this.logError(new Error(message), context, 'info');
  }

  /**
   * Get current user ID from auth store
   */
  private getCurrentUserId(): string | undefined {
    try {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        return parsed.state?.user?.id;
      }
    } catch {
      // Ignore parsing errors
    }
    return undefined;
  }

  /**
   * Persist error to localStorage
   */
  private persistToLocalStorage(errorLog: ErrorLog): void {
    try {
      const key = 'app_error_logs';
      const stored = localStorage.getItem(key);
      const logs = stored ? JSON.parse(stored) : [];
      
      logs.push(errorLog);
      // Keep only last 50 logs in localStorage
      if (logs.length > 50) logs.shift();
      
      localStorage.setItem(key, JSON.stringify(logs));
    } catch {
      // Ignore localStorage errors
    }
  }

  /**
   * Send error to monitoring service (e.g., Sentry, LogRocket)
   */
  private sendToMonitoringService(errorLog: ErrorLog): void {
    // TODO: Implement integration with error monitoring service
    // Example: Sentry.captureException(error, { extra: errorLog.context });
  }

  /**
   * Get recent error logs
   */
  getRecentLogs(count = 10): ErrorLog[] {
    return this.logs.slice(-count);
  }

  /**
   * Clear error logs
   */
  clearLogs(): void {
    this.logs = [];
    try {
      localStorage.removeItem('app_error_logs');
    } catch {
      // Ignore localStorage errors
    }
  }

  /**
   * Export logs for debugging
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Export singleton instance
export const errorService = new ErrorService();

// Export helper function for API error handling
export function handleApiError(error: any, context?: Record<string, any>): {
  message: string;
  errorId: string;
} {
  const errorId = errorService.logError(error, context);
  const message = error instanceof AxiosError && error.response?.data?.message
    ? error.response.data.message
    : 'An error occurred. Please try again.';
  
  return { message, errorId };
}