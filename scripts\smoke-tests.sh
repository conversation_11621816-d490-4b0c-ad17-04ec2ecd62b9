#!/bin/bash

# Smoke Tests for Electrical App
# Usage: ./smoke-tests.sh [environment]

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-staging}
TIMEOUT=30
RETRY_COUNT=3
RETRY_DELAY=5

# URLs based on environment
declare -A URLS
URLS[dev]="http://dev.electrical-app.local"
URLS[staging]="https://staging.electrical-app.com"
URLS[production]="https://electrical-app.com"

declare -A API_URLS
API_URLS[dev]="http://api.dev.electrical-app.local"
API_URLS[staging]="https://api.staging.electrical-app.com"
API_URLS[production]="https://api.electrical-app.com"

BASE_URL=${URLS[$ENVIRONMENT]}
API_URL=${API_URLS[$ENVIRONMENT]}

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Functions
log_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1" >&2
}

# HTTP request with retry
http_request() {
    local url=$1
    local expected_status=${2:-200}
    local method=${3:-GET}
    local data=${4:-}
    
    for i in $(seq 1 $RETRY_COUNT); do
        if [[ -n "$data" ]]; then
            response=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url" || echo "000")
        else
            response=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url" || echo "000")
        fi
        
        if [[ "$response" == "$expected_status" ]]; then
            return 0
        fi
        
        if [[ $i -lt $RETRY_COUNT ]]; then
            sleep $RETRY_DELAY
        fi
    done
    
    return 1
}

# Test frontend availability
test_frontend() {
    log_test "Testing frontend availability..."
    
    if http_request "$BASE_URL" 200; then
        log_pass "Frontend is accessible"
    else
        log_fail "Frontend is not accessible"
        return 1
    fi
}

# Test API health endpoint
test_api_health() {
    log_test "Testing API health endpoint..."
    
    if http_request "$API_URL/health" 200; then
        log_pass "API health check passed"
    else
        log_fail "API health check failed"
        return 1
    fi
}

# Test API authentication
test_api_auth() {
    log_test "Testing API authentication..."
    
    # Test login endpoint exists
    if http_request "$API_URL/api/auth/login" 400 POST '{"email":"<EMAIL>","password":"test"}'; then
        log_pass "Authentication endpoint is responding"
    else
        log_fail "Authentication endpoint is not responding"
        return 1
    fi
}

# Test database connectivity
test_database() {
    log_test "Testing database connectivity..."
    
    if http_request "$API_URL/api/health/db" 200; then
        log_pass "Database connection is healthy"
    else
        log_fail "Database connection failed"
        return 1
    fi
}

# Test Redis connectivity
test_redis() {
    log_test "Testing Redis connectivity..."
    
    if http_request "$API_URL/api/health/redis" 200; then
        log_pass "Redis connection is healthy"
    else
        log_fail "Redis connection failed"
        return 1
    fi
}

# Test WebSocket connectivity
test_websocket() {
    log_test "Testing WebSocket connectivity..."
    
    # Simple check if socket.io endpoint responds
    if http_request "$API_URL/socket.io/" 200; then
        log_pass "WebSocket endpoint is accessible"
    else
        log_fail "WebSocket endpoint is not accessible"
        return 1
    fi
}

# Test critical API endpoints
test_api_endpoints() {
    log_test "Testing critical API endpoints..."
    
    local endpoints=(
        "/api/projects"
        "/api/customers"
        "/api/materials"
        "/api/calculations/voltage-drop"
        "/api/panels"
    )
    
    local failed=0
    for endpoint in "${endpoints[@]}"; do
        if http_request "$API_URL$endpoint" 401; then
            log_pass "Endpoint $endpoint is responding (requires auth)"
        else
            log_fail "Endpoint $endpoint is not responding"
            ((failed++))
        fi
    done
    
    if [[ $failed -gt 0 ]]; then
        return 1
    fi
}

# Test static assets
test_static_assets() {
    log_test "Testing static assets..."
    
    # Test if main JS bundle is accessible
    local js_response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/assets/index.js" || echo "000")
    if [[ "$js_response" =~ ^(200|304)$ ]]; then
        log_pass "Static assets are accessible"
    else
        log_fail "Static assets are not accessible"
        return 1
    fi
}

# Test SSL certificate (for staging/production)
test_ssl_certificate() {
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        log_test "Skipping SSL test for development environment"
        return 0
    fi
    
    log_test "Testing SSL certificate..."
    
    local domain=$(echo "$BASE_URL" | sed 's|https://||')
    if echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates &>/dev/null; then
        log_pass "SSL certificate is valid"
    else
        log_fail "SSL certificate is invalid or expired"
        return 1
    fi
}

# Test response times
test_response_times() {
    log_test "Testing response times..."
    
    local total_time=$(curl -s -o /dev/null -w "%{time_total}" "$BASE_URL" || echo "999")
    local time_ms=$(echo "$total_time * 1000" | bc | cut -d. -f1)
    
    if [[ $time_ms -lt 3000 ]]; then
        log_pass "Frontend response time: ${time_ms}ms"
    else
        log_fail "Frontend response time too high: ${time_ms}ms"
        return 1
    fi
    
    total_time=$(curl -s -o /dev/null -w "%{time_total}" "$API_URL/health" || echo "999")
    time_ms=$(echo "$total_time * 1000" | bc | cut -d. -f1)
    
    if [[ $time_ms -lt 1000 ]]; then
        log_pass "API response time: ${time_ms}ms"
    else
        log_fail "API response time too high: ${time_ms}ms"
        return 1
    fi
}

# Main test suite
main() {
    echo "Running smoke tests for $ENVIRONMENT environment..."
    echo "Base URL: $BASE_URL"
    echo "API URL: $API_URL"
    echo ""
    
    local failed_tests=0
    
    # Run all tests
    test_frontend || ((failed_tests++))
    test_api_health || ((failed_tests++))
    test_api_auth || ((failed_tests++))
    test_database || ((failed_tests++))
    test_redis || ((failed_tests++))
    test_websocket || ((failed_tests++))
    test_api_endpoints || ((failed_tests++))
    test_static_assets || ((failed_tests++))
    test_ssl_certificate || ((failed_tests++))
    test_response_times || ((failed_tests++))
    
    echo ""
    if [[ $failed_tests -eq 0 ]]; then
        echo -e "${GREEN}All smoke tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}$failed_tests smoke tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"