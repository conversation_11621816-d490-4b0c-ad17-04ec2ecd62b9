import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Key, Users, Activity, Lock, TrendingUp, FileText } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Alert } from '../../components/ui/alert';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { api } from '../../services/api';
import { formatDistanceToNow } from 'date-fns';

interface SecurityMetrics {
  recentLogins: number;
  failedLogins: { count: number; unique_users: number };
  activeUsers: number;
  unresolvedEvents: number;
}

interface SecurityEvent {
  id: string;
  event_type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  user_id?: string;
  description: string;
  details?: any;
  resolved: boolean;
  created_at: string;
}

interface RateLimitViolation {
  identifier: string;
  limiter_name: string;
  violations_count: number;
  last_violation_at: string;
}

export function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [violations, setViolations] = useState<RateLimitViolation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/security/dashboard');
      
      setMetrics(response.data.metrics);
      setEvents(response.data.suspiciousEvents);
      setViolations(response.data.rateLimitViolations);
    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const resolveEvent = async (eventId: string) => {
    try {
      await api.patch(`/security/events/${eventId}/resolve`);
      await loadSecurityData();
    } catch (error) {
      console.error('Failed to resolve event:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-500';
      case 'HIGH': return 'bg-orange-500';
      case 'MEDIUM': return 'bg-yellow-500';
      case 'LOW': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'LOGIN_ANOMALY': return Users;
      case 'PERMISSION_ESCALATION': return Shield;
      case 'DATA_EXFILTRATION': return FileText;
      case 'RATE_LIMIT_VIOLATION': return Activity;
      default: return AlertTriangle;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Shield className="h-8 w-8 text-blue-600" />
          Security Dashboard
        </h1>
        <Button onClick={loadSecurityData} variant="outline">
          Refresh
        </Button>
      </div>

      {/* Security Alerts */}
      {events.filter(e => !e.resolved && e.severity === 'CRITICAL').length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <div>
            <h3 className="font-semibold">Critical Security Events</h3>
            <p className="text-sm">
              {events.filter(e => !e.resolved && e.severity === 'CRITICAL').length} critical events require immediate attention
            </p>
          </div>
        </Alert>
      )}

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Recent Logins (24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {metrics?.recentLogins || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">Successful authentications</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Failed Logins (24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {metrics?.failedLogins.count || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              From {metrics?.failedLogins.unique_users || 0} unique users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {metrics?.activeUsers || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">In the last hour</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Unresolved Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {metrics?.unresolvedEvents || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">Require investigation</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="rate-limits">Rate Limits</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Status</CardTitle>
              <CardDescription>
                Overall security health and recent activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Security Health Score */}
                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Shield className="h-8 w-8 text-green-600" />
                    <div>
                      <h3 className="font-semibold">Security Health Score</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Based on recent activity and configurations
                      </p>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-green-600">92%</div>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">API Keys Active</span>
                      <Key className="h-4 w-4 text-gray-400" />
                    </div>
                    <div className="text-xl font-semibold mt-1">12</div>
                  </div>
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">2FA Enabled Users</span>
                      <Lock className="h-4 w-4 text-gray-400" />
                    </div>
                    <div className="text-xl font-semibold mt-1">87%</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Events</CardTitle>
              <CardDescription>
                Recent security events requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {events.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">
                    No security events to display
                  </p>
                ) : (
                  events.map((event) => {
                    const Icon = getEventIcon(event.event_type);
                    return (
                      <div
                        key={event.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${getSeverityColor(event.severity)} text-white`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div>
                            <h4 className="font-medium">{event.description}</h4>
                            <p className="text-sm text-gray-500">
                              {formatDistanceToNow(new Date(event.created_at), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getSeverityColor(event.severity)}>
                            {event.severity}
                          </Badge>
                          {!event.resolved && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => resolveEvent(event.id)}
                            >
                              Resolve
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rate-limits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rate Limit Violations</CardTitle>
              <CardDescription>
                Users or IPs exceeding rate limits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {violations.length === 0 ? (
                  <p className="text-center text-gray-500 py-8">
                    No rate limit violations
                  </p>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="text-left border-b">
                        <th className="pb-2">Identifier</th>
                        <th className="pb-2">Limiter</th>
                        <th className="pb-2">Violations</th>
                        <th className="pb-2">Last Violation</th>
                      </tr>
                    </thead>
                    <tbody>
                      {violations.map((violation, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-2">{violation.identifier}</td>
                          <td className="py-2">
                            <Badge variant="outline">{violation.limiter_name}</Badge>
                          </td>
                          <td className="py-2 text-red-600 font-medium">
                            {violation.violations_count}
                          </td>
                          <td className="py-2 text-sm text-gray-500">
                            {formatDistanceToNow(new Date(violation.last_violation_at), { addSuffix: true })}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Logs</CardTitle>
              <CardDescription>
                Detailed activity logs for security monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <Button variant="outline" onClick={() => window.location.href = '/admin/audit-logs'}>
                  View Full Audit Logs
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}