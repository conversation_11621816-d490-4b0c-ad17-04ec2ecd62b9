import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ScannedItem } from '../../types/barcode';
import { barcodeScannerService } from '../../services/barcodeScannerService';

interface Props {
  onSelectItem?: (item: ScannedItem) => void;
}

export const ScanHistory: React.FC<Props> = ({ onSelectItem }) => {
  const [scanHistory, setScanHistory] = useState<ScannedItem[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<ScannedItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectionMode, setSelectionMode] = useState(false);

  useEffect(() => {
    loadScanHistory();
  }, []);

  useEffect(() => {
    filterHistory();
  }, [searchQuery, scanHistory]);

  const loadScanHistory = async () => {
    const history = await barcodeScannerService.getScanHistory(500);
    setScanHistory(history);
  };

  const filterHistory = () => {
    if (!searchQuery) {
      setFilteredHistory(scanHistory);
      return;
    }

    const filtered = barcodeScannerService.searchScanHistory(searchQuery);
    setFilteredHistory(filtered);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadScanHistory();
    setRefreshing(false);
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const clearHistory = () => {
    Alert.alert(
      'Clear Scan History',
      'Are you sure you want to clear all scan history?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            await barcodeScannerService.clearScanHistory();
            setScanHistory([]);
            setFilteredHistory([]);
          },
        },
      ]
    );
  };

  const exportSelected = () => {
    const selectedData = scanHistory.filter(item => selectedItems.has(item.id));
    // Implement export functionality
    Alert.alert('Export', `Export ${selectedData.length} items to CSV`);
  };

  const groupByDate = (items: ScannedItem[]) => {
    const groups: { [key: string]: ScannedItem[] } = {};
    
    items.forEach(item => {
      const date = new Date(item.timestamp).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(item);
    });

    return Object.entries(groups).map(([date, items]) => ({
      title: date,
      data: items,
    }));
  };

  const renderItem = ({ item }: { item: ScannedItem }) => {
    const isSelected = selectedItems.has(item.id);
    
    return (
      <TouchableOpacity
        style={[styles.historyItem, isSelected && styles.selectedItem]}
        onPress={() => {
          if (selectionMode) {
            toggleItemSelection(item.id);
          } else if (onSelectItem) {
            onSelectItem(item);
          }
        }}
        onLongPress={() => {
          setSelectionMode(true);
          toggleItemSelection(item.id);
        }}
      >
        {selectionMode && (
          <Icon
            name={isSelected ? 'check-box' : 'check-box-outline-blank'}
            size={24}
            color={isSelected ? '#2196F3' : '#666'}
            style={styles.checkbox}
          />
        )}
        
        <View style={styles.itemContent}>
          <View style={styles.itemHeader}>
            <Text style={styles.itemName}>
              {item.material?.name || 'Unknown Material'}
            </Text>
            <Text style={styles.itemTime}>
              {new Date(item.timestamp).toLocaleTimeString()}
            </Text>
          </View>
          
          <View style={styles.itemDetails}>
            <Text style={styles.itemBarcode}>
              <Icon name="qr-code" size={14} /> {item.barcode}
            </Text>
            <Text style={styles.itemQuantity}>Qty: {item.quantity}</Text>
          </View>
          
          {item.material && (
            <View style={styles.itemMeta}>
              <Text style={styles.itemSku}>SKU: {item.material.sku}</Text>
              <Text style={styles.itemPrice}>${item.material.price}</Text>
            </View>
          )}
          
          {item.notes && (
            <Text style={styles.itemNotes}>{item.notes}</Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderSectionHeader = ({ section }: { section: any }) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <Text style={styles.sectionCount}>{section.data.length} scans</Text>
    </View>
  );

  const sections = groupByDate(filteredHistory);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scan History</Text>
        <View style={styles.headerActions}>
          {selectionMode ? (
            <>
              <TouchableOpacity
                onPress={() => {
                  setSelectionMode(false);
                  setSelectedItems(new Set());
                }}
                style={styles.headerButton}
              >
                <Text style={styles.headerButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={exportSelected}
                style={styles.headerButton}
                disabled={selectedItems.size === 0}
              >
                <Icon name="share" size={20} color="#2196F3" />
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity onPress={clearHistory} style={styles.headerButton}>
              <Icon name="delete-sweep" size={20} color="#f44336" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search by barcode, name, or SKU..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="clear" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Stats Bar */}
      <View style={styles.statsBar}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{scanHistory.length}</Text>
          <Text style={styles.statLabel}>Total Scans</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {new Set(scanHistory.map(item => item.material?.id).filter(Boolean)).size}
          </Text>
          <Text style={styles.statLabel}>Unique Items</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {sections.length > 0 ? sections[0].data.length : 0}
          </Text>
          <Text style={styles.statLabel}>Today</Text>
        </View>
      </View>

      {/* History List */}
      <FlatList
        data={sections}
        renderItem={({ item }) => (
          <View>
            {renderSectionHeader({ section: item })}
            {item.data.map(scan => (
              <View key={scan.id}>{renderItem({ item: scan })}</View>
            ))}
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="history" size={64} color="#ccc" />
            <Text style={styles.emptyText}>No scan history</Text>
          </View>
        }
      />

      {/* Selection Actions */}
      {selectionMode && selectedItems.size > 0 && (
        <View style={styles.selectionActions}>
          <Text style={styles.selectionCount}>
            {selectedItems.size} selected
          </Text>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const allIds = filteredHistory.map(item => item.id);
              setSelectedItems(new Set(allIds));
            }}
          >
            <Text style={styles.actionButtonText}>Select All</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 15,
    padding: 5,
  },
  headerButtonText: {
    color: '#2196F3',
    fontSize: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  statsBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'white',
    paddingVertical: 15,
    marginBottom: 10,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  sectionCount: {
    fontSize: 14,
    color: '#666',
  },
  historyItem: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 15,
    marginVertical: 5,
    padding: 15,
    borderRadius: 8,
    elevation: 2,
  },
  selectedItem: {
    backgroundColor: '#E3F2FD',
  },
  checkbox: {
    marginRight: 10,
  },
  itemContent: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  itemTime: {
    fontSize: 12,
    color: '#666',
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  itemBarcode: {
    fontSize: 14,
    color: '#666',
  },
  itemQuantity: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
  },
  itemMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  itemSku: {
    fontSize: 12,
    color: '#999',
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2196F3',
  },
  itemNotes: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 4,
  },
  listContent: {
    paddingBottom: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
  },
  selectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  selectionCount: {
    fontSize: 16,
    color: '#666',
  },
  actionButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#2196F3',
    borderRadius: 6,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});