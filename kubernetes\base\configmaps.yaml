apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: electrical-app
data:
  cors-origin: "https://electrical-app.com,https://www.electrical-app.com"
  chromadb-url: "http://chromadb:8000"
  influxdb-url: "http://influxdb:8086"
  max-upload-size: "50"
  session-timeout: "3600"
  rate-limit-window: "900000"
  rate-limit-max: "100"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: agents-config
  namespace: electrical-app
data:
  chromadb-url: "http://chromadb:8000"
  agent-timeout: "300000"
  max-concurrent-agents: "10"
  memory-cache-ttl: "3600"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: electrical-app
data:
  nginx.conf: |
    user nginx-user;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 50M;

        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/rss+xml application/atom+xml image/svg+xml;

        server {
            listen 80;
            server_name _;
            root /usr/share/nginx/html;
            index index.html;

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location / {
                try_files $uri $uri/ /index.html;
            }

            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: electrical-app
data:
  postgresql.conf: |
    max_connections = 200
    shared_buffers = 2GB
    effective_cache_size = 6GB
    maintenance_work_mem = 512MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 10485kB
    min_wal_size = 1GB
    max_wal_size = 4GB
    max_worker_processes = 4
    max_parallel_workers_per_gather = 2
    max_parallel_workers = 4
    max_parallel_maintenance_workers = 2
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: electrical-app
data:
  redis.conf: |
    bind 0.0.0.0
    protected-mode yes
    port 6379
    tcp-backlog 511
    timeout 0
    tcp-keepalive 300
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    always-show-logo yes
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    replica-serve-stale-data yes
    replica-read-only yes
    repl-diskless-sync no
    repl-diskless-sync-delay 5
    repl-disable-tcp-nodelay no
    replica-priority 100
    requirepass ${REDIS_PASSWORD}
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    aof-use-rdb-preamble yes
    lua-time-limit 5000
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    latency-monitor-threshold 0
    notify-keyspace-events ""
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    list-compress-depth 0
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    hll-sparse-max-bytes 3000
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    activerehashing yes
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    hz 10
    dynamic-hz yes
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init
  namespace: electrical-app
data:
  init.sql: |
    -- Create extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
    
    -- Create read-only user for analytics
    CREATE USER electrical_readonly WITH PASSWORD 'readonly_password';
    GRANT CONNECT ON DATABASE electrical_contracting TO electrical_readonly;
    GRANT USAGE ON SCHEMA public TO electrical_readonly;
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO electrical_readonly;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO electrical_readonly;
    
    -- Create application user with limited privileges
    CREATE USER electrical_app WITH PASSWORD 'app_password';
    GRANT CONNECT ON DATABASE electrical_contracting TO electrical_app;
    GRANT USAGE ON SCHEMA public TO electrical_app;
    GRANT CREATE ON SCHEMA public TO electrical_app;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO electrical_app;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO electrical_app;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO electrical_app;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO electrical_app;