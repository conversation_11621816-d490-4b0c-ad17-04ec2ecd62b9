import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import exec from 'k6/execution';

// Custom metrics for stress testing
const systemOverloadRate = new Rate('system_overload');
const errorsByType = new Counter('errors_by_type');
const recoveryTime = new Trend('recovery_time');
const breakingPointVUs = new Counter('breaking_point_vus');

export const options = {
  scenarios: {
    stress: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 50 },    // Below normal load
        { duration: '5m', target: 100 },   // Normal load
        { duration: '5m', target: 200 },   // Around breaking point
        { duration: '5m', target: 300 },   // Beyond breaking point
        { duration: '5m', target: 400 },   // Extreme stress
        { duration: '10m', target: 400 },  // Stay at extreme
        { duration: '5m', target: 100 },   // Recovery stage
        { duration: '5m', target: 0 },     // Scale down
      ],
    },
    spike_during_stress: {
      executor: 'constant-arrival-rate',
      rate: 100,
      timeUnit: '1s',
      duration: '1m',
      preAllocatedVUs: 50,
      startTime: '15m', // During high stress
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<10000'], // Relaxed for stress test
    http_req_failed: ['rate<0.5'],      // Accept up to 50% failure
    system_overload: ['rate<0.3'],      // System overload threshold
  },
};

const BASE_URL = __ENV.API_BASE_URL || 'http://localhost:3000/api';

// Track when errors start occurring
let errorThresholdReached = false;
let errorStartTime = 0;
let maxVUsBeforeErrors = 0;

export default function() {
  const scenario = exec.scenario.name;
  const currentVUs = exec.instance.vusActive;
  
  // Heavy operations to stress the system
  const requests = [
    // Complex calculation batch
    {
      method: 'POST',
      url: `${BASE_URL}/calculations/batch`,
      body: JSON.stringify({
        calculations: Array(10).fill(null).map(() => ({
          type: 'arc-flash',
          params: {
            systemVoltage: 480,
            faultCurrent: 65000,
            faultClearingTime: 0.5,
            workingDistance: 18,
            enclosureType: 'switchgear',
            groundingType: 'solidly-grounded',
            conductorGap: 32,
          },
        })),
      }),
      tag: 'batch_calculation',
    },
    // Large data query
    {
      method: 'GET',
      url: `${BASE_URL}/panels?limit=1000&includeCircuits=true&includeCalculations=true`,
      tag: 'heavy_query',
    },
    // Multiple concurrent writes
    {
      method: 'POST',
      url: `${BASE_URL}/projects/bulk-create`,
      body: JSON.stringify({
        projects: Array(5).fill(null).map((_, i) => ({
          name: `Stress Test Project ${Date.now()}-${i}`,
          description: 'A'.repeat(1000), // Large description
          panels: Array(10).fill(null).map((_, j) => ({
            name: `Panel ${j}`,
            circuits: Array(42).fill(null).map((_, k) => ({
              circuitNumber: k + 1,
              description: `Circuit ${k + 1}`,
              amperage: 20,
            })),
          })),
        })),
      }),
      tag: 'bulk_write',
    },
    // Memory-intensive operation
    {
      method: 'POST',
      url: `${BASE_URL}/exports/full-database`,
      body: JSON.stringify({
        format: 'json',
        includeAll: true,
        compression: false,
      }),
      tag: 'memory_intensive',
    },
  ];
  
  // Execute random stress operation
  const request = requests[Math.floor(Math.random() * requests.length)];
  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
    tags: {
      type: request.tag,
      scenario: scenario,
      vus: currentVUs,
    },
    timeout: '60s', // Longer timeout for stress operations
  };
  
  const response = request.method === 'POST' 
    ? http.post(request.url, request.body, params)
    : http.get(request.url, params);
  
  // Track breaking point
  if (!errorThresholdReached && currentVUs > maxVUsBeforeErrors) {
    maxVUsBeforeErrors = currentVUs;
  }
  
  const success = check(response, {
    'status is 200-299': (r) => r.status >= 200 && r.status < 300,
    'no timeout': (r) => r.status !== 0,
  });
  
  if (!success) {
    // Categorize errors
    if (response.status === 0) {
      errorsByType.add(1, { type: 'timeout' });
    } else if (response.status >= 500) {
      errorsByType.add(1, { type: 'server_error' });
    } else if (response.status === 429) {
      errorsByType.add(1, { type: 'rate_limit' });
    } else if (response.status >= 400) {
      errorsByType.add(1, { type: 'client_error' });
    }
    
    // Track when errors start
    if (!errorThresholdReached) {
      errorThresholdReached = true;
      errorStartTime = Date.now();
      breakingPointVUs.add(maxVUsBeforeErrors);
      console.log(`Breaking point reached at ${maxVUsBeforeErrors} VUs`);
    }
  }
  
  // Check for system overload indicators
  const overloaded = response.status === 503 || 
                    response.status === 0 || 
                    (response.timings && response.timings.duration > 10000);
  
  systemOverloadRate.add(overloaded);
  
  // During recovery phase, measure recovery time
  if (scenario === 'stress' && currentVUs < 150 && errorThresholdReached) {
    if (success && response.timings.duration < 2000) {
      const recoveryDuration = Date.now() - errorStartTime;
      recoveryTime.add(recoveryDuration);
      console.log(`System recovered after ${recoveryDuration}ms`);
      errorThresholdReached = false;
    }
  }
  
  // Minimal sleep to maximize stress
  sleep(Math.random() * 0.5);
}

// Additional stress test scenarios
export function memoryLeakTest() {
  // Create large objects that might not be garbage collected
  const largePayload = {
    data: 'X'.repeat(1000000), // 1MB string
    nested: Array(100).fill(null).map(() => ({
      id: Math.random().toString(36),
      data: Array(1000).fill(Math.random()),
    })),
  };
  
  const response = http.post(
    `${BASE_URL}/debug/memory-test`,
    JSON.stringify(largePayload),
    {
      headers: { 'Content-Type': 'application/json' },
      timeout: '30s',
    }
  );
  
  check(response, {
    'memory test handled': (r) => r.status !== 0,
  });
}

export function connectionPoolExhaustion() {
  // Open many connections simultaneously
  const batch = Array(50).fill(null).map((_, i) => ({
    method: 'GET',
    url: `${BASE_URL}/panels/keep-alive?id=${i}`,
    params: {
      headers: {
        'Connection': 'keep-alive',
      },
    },
  }));
  
  const responses = http.batch(batch);
  
  const successRate = responses.filter(r => r.status === 200).length / responses.length;
  check(successRate, {
    'connection pool handled load': (rate) => rate > 0.5,
  });
}

export function cascadingFailureTest() {
  // Trigger operations that depend on each other
  const projectRes = http.post(
    `${BASE_URL}/projects`,
    JSON.stringify({
      name: `Cascading Test ${Date.now()}`,
    }),
    { headers: { 'Content-Type': 'application/json' } }
  );
  
  if (projectRes.status === 200 || projectRes.status === 201) {
    const projectId = projectRes.json('id');
    
    // Create multiple dependent resources simultaneously
    const batch = [
      {
        method: 'POST',
        url: `${BASE_URL}/projects/${projectId}/panels`,
        body: JSON.stringify({ name: 'Panel 1' }),
      },
      {
        method: 'POST',
        url: `${BASE_URL}/projects/${projectId}/estimates`,
        body: JSON.stringify({ name: 'Estimate 1' }),
      },
      {
        method: 'POST',
        url: `${BASE_URL}/projects/${projectId}/inspections`,
        body: JSON.stringify({ type: 'rough-in' }),
      },
    ];
    
    const responses = http.batch(batch.map(req => ({
      method: req.method,
      url: req.url,
      body: req.body,
      params: { headers: { 'Content-Type': 'application/json' } },
    })));
    
    const failures = responses.filter(r => r.status >= 500).length;
    errorsByType.add(failures, { type: 'cascading_failure' });
  }
}

export function setup() {
  console.log('Starting stress test - Finding system breaking points');
  
  // Warm up the system
  const warmupRequests = Array(10).fill(null).map(() => 
    http.get(`${BASE_URL.replace('/api', '')}/health`)
  );
  
  return {
    startTime: Date.now(),
    systemInfo: {
      targetUrl: BASE_URL,
      timestamp: new Date().toISOString(),
    },
  };
}

export function teardown(data) {
  console.log('Stress test completed');
  console.log(`Duration: ${(Date.now() - data.startTime) / 1000}s`);
  console.log(`Breaking point: ${maxVUsBeforeErrors} VUs`);
}

export function handleSummary(data) {
  const summary = {
    ...data,
    custom: {
      breakingPoint: maxVUsBeforeErrors,
      errorThreshold: errorThresholdReached,
      timestamp: new Date().toISOString(),
    },
  };
  
  return {
    '../results/k6-stress-test-results.json': JSON.stringify(summary, null, 2),
    'stdout': generateStressTestReport(summary),
  };
}

function generateStressTestReport(data) {
  let report = '\\n=== STRESS TEST REPORT ===\\n\\n';
  
  report += `Breaking Point: ${maxVUsBeforeErrors} Virtual Users\\n`;
  report += `Maximum Load Reached: ${data.metrics.vus_max?.values?.value || 'N/A'} VUs\\n\\n`;
  
  report += 'Error Distribution:\\n';
  const errorTypes = ['timeout', 'server_error', 'rate_limit', 'client_error', 'cascading_failure'];
  errorTypes.forEach(type => {
    const count = data.metrics.errors_by_type?.values?.[type] || 0;
    if (count > 0) {
      report += `  ${type}: ${count}\\n`;
    }
  });
  
  report += `\\nSystem Overload Rate: ${(data.metrics.system_overload?.values?.rate * 100 || 0).toFixed(2)}%\\n`;
  
  if (data.metrics.recovery_time?.values?.avg) {
    report += `Average Recovery Time: ${(data.metrics.recovery_time.values.avg / 1000).toFixed(2)}s\\n`;
  }
  
  report += '\\nResponse Time Under Stress:\\n';
  if (data.metrics.http_req_duration?.values) {
    const vals = data.metrics.http_req_duration.values;
    report += `  Median: ${vals.med?.toFixed(0)}ms\\n`;
    report += `  p95: ${vals['p(95)']?.toFixed(0)}ms\\n`;
    report += `  p99: ${vals['p(99)']?.toFixed(0)}ms\\n`;
    report += `  Max: ${vals.max?.toFixed(0)}ms\\n`;
  }
  
  report += '\\nRecommendations:\\n';
  if (maxVUsBeforeErrors < 100) {
    report += '  - CRITICAL: System breaks under moderate load\\n';
    report += '  - Immediate scaling required\\n';
  } else if (maxVUsBeforeErrors < 200) {
    report += '  - System handles expected load but needs improvement\\n';
    report += '  - Consider horizontal scaling\\n';
  } else {
    report += '  - System shows good stress resistance\\n';
    report += '  - Monitor for optimization opportunities\\n';
  }
  
  return report;
}