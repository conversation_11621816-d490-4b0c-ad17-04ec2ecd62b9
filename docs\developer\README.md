# Developer Documentation

Welcome to the Electrical Contracting Application developer documentation. This guide provides everything you need to contribute to or extend the application.

## 📚 Documentation Structure

### Getting Started
- [Development Setup](./setup.md) - Environment setup and initial configuration
- [Architecture Overview](./architecture.md) - System design and component relationships
- [Code Style Guide](./code-style.md) - Coding standards and best practices
- [Git Workflow](./git-workflow.md) - Branching strategy and commit conventions

### Development Guides
- [Backend Development](./backend/README.md) - API development with Node.js/Express
- [Frontend Development](./frontend/README.md) - React application development
- [Mobile Development](./mobile/README.md) - React Native development
- [Database Guide](./database/README.md) - Working with PostgreSQL and Prisma
- [Testing Guide](./testing.md) - Unit, integration, and E2E testing

### API Documentation
- [REST API Reference](./api/README.md) - Complete API documentation
- [WebSocket Events](./api/websockets.md) - Real-time communication
- [Authentication](./api/authentication.md) - Auth flow and security
- [Error Handling](./api/errors.md) - Error codes and responses

### Advanced Topics
- [Performance Optimization](./performance.md) - Optimization techniques
- [Security Guidelines](./security.md) - Security best practices
- [Deployment](./deployment.md) - Deployment processes
- [Monitoring](./monitoring.md) - Application monitoring
- [AI Agents](./agents/README.md) - Agent system architecture

## 🚀 Quick Start

### Prerequisites
```bash
# Required versions
node --version  # >= 20.0.0
pnpm --version  # >= 8.0.0
docker --version  # >= 20.10.0
```

### Initial Setup
```bash
# Clone repository
git clone https://github.com/your-org/electrical-app.git
cd electrical-app

# Install dependencies
pnpm install

# Setup environment
cp .env.example .env
# Edit .env with your values

# Start development servers
docker-compose -f docker-compose.dev.yml up -d
pnpm dev
```

### Verify Installation
- Frontend: http://localhost:5173
- Backend API: http://localhost:3000
- API Docs: http://localhost:3000/api-docs
- Database Admin: http://localhost:8080

## 🏗️ Project Structure

```
electrical/
├── app/
│   ├── backend/          # Express.js API server
│   ├── frontend/         # React web application
│   ├── mobile/          # React Native mobile app
│   ├── agents/          # AI agent services
│   └── shared/          # Shared utilities and types
├── docs/                # Documentation
├── docker/              # Docker configurations
├── kubernetes/          # K8s manifests
├── scripts/             # Utility scripts
└── tests/              # E2E tests
```

## 🛠️ Development Workflow

### 1. Create Feature Branch
```bash
git checkout -b feature/your-feature-name
```

### 2. Make Changes
- Write code following style guide
- Add/update tests
- Update documentation

### 3. Test Locally
```bash
# Run all tests
pnpm test

# Type checking
pnpm typecheck

# Linting
pnpm lint
```

### 4. Commit Changes
```bash
git add .
git commit -m "feat: add new calculation feature"
```

### 5. Push and Create PR
```bash
git push origin feature/your-feature-name
# Create PR on GitHub
```

## 📋 Development Guidelines

### Code Quality Standards
- **Type Safety**: 100% TypeScript, strict mode enabled
- **Testing**: Minimum 80% code coverage
- **Documentation**: JSDoc for public APIs
- **Linting**: ESLint with our custom config
- **Formatting**: Prettier with standard settings

### Performance Requirements
- **API Response**: < 200ms for queries
- **Frontend FCP**: < 1.5s
- **Mobile Launch**: < 3s
- **Build Size**: < 500KB (gzipped)

### Security Requirements
- Input validation on all endpoints
- SQL injection prevention via Prisma
- XSS protection in React components
- CSRF tokens for state-changing operations
- Rate limiting on all APIs

## 🔧 Common Tasks

### Adding a New API Endpoint
```typescript
// src/routes/your-route.ts
router.post('/endpoint', 
  authenticate,
  validate(YourSchema),
  asyncHandler(async (req, res) => {
    const result = await yourService.process(req.body);
    res.json(result);
  })
);
```

### Adding a New Database Model
```prisma
// prisma/schema.prisma
model YourModel {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

### Adding a React Component
```typescript
// src/components/YourComponent.tsx
interface YourComponentProps {
  data: YourType;
  onAction: (id: string) => void;
}

export const YourComponent: React.FC<YourComponentProps> = ({ 
  data, 
  onAction 
}) => {
  return (
    <div className="your-component">
      {/* Component content */}
    </div>
  );
};
```

## 🧪 Testing Strategy

### Unit Tests
- Components: React Testing Library
- API Routes: Supertest
- Services: Jest mocks
- Utilities: Pure function tests

### Integration Tests
- API workflows
- Database operations
- Authentication flows
- Third-party integrations

### E2E Tests
- Critical user journeys
- Cross-browser testing
- Mobile app flows
- Performance benchmarks

## 📊 Performance Optimization

### Frontend
- Code splitting by route
- Lazy loading components
- Image optimization
- Virtual scrolling for lists
- Memoization of expensive computations

### Backend
- Database query optimization
- Redis caching strategy
- Connection pooling
- Background job processing
- CDN for static assets

### Mobile
- Offline-first architecture
- Image caching
- Batch network requests
- Optimistic updates
- Native module usage

## 🔒 Security Considerations

### Authentication
- JWT with refresh tokens
- Session management
- Two-factor authentication
- Biometric authentication (mobile)

### Authorization
- Role-based access control (RBAC)
- Resource-level permissions
- API key management
- OAuth2 integration

### Data Protection
- Encryption at rest
- TLS for data in transit
- PII data masking
- Audit logging
- GDPR compliance

## 🚀 Deployment

### Environments
- **Development**: Local Docker Compose
- **Staging**: Kubernetes cluster (staging namespace)
- **Production**: Kubernetes cluster (production namespace)

### CI/CD Pipeline
1. Code push triggers GitHub Actions
2. Run tests and linting
3. Build Docker images
4. Push to registry
5. Deploy to appropriate environment
6. Run smoke tests

## 📚 Additional Resources

### Internal Documentation
- [API Design Principles](./api/design-principles.md)
- [Database Migrations](./database/migrations.md)
- [Error Handling Guide](./error-handling.md)
- [Logging Standards](./logging.md)

### External Resources
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [React Documentation](https://react.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Prisma Documentation](https://www.prisma.io/docs)

### Tools & Services
- **IDE**: VS Code with recommended extensions
- **API Testing**: Postman collections available
- **Monitoring**: Sentry for error tracking
- **Analytics**: Mixpanel for user analytics

## 🤝 Contributing

### Before Contributing
1. Read the [Contributing Guidelines](../CONTRIBUTING.md)
2. Check existing issues and PRs
3. Discuss major changes in an issue first

### Contribution Process
1. Fork the repository
2. Create your feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Submit a pull request

### Code Review
- All PRs require 2 approvals
- CI must pass
- Documentation must be updated
- Follow feedback promptly

## 📞 Getting Help

### For Developers
- **Slack**: #dev-electrical channel
- **GitHub Discussions**: Technical questions
- **Stack Overflow**: Tag with 'electrical-app'
- **Office Hours**: Thursdays 2-3 PM EST

### Resources
- **Wiki**: Internal development wiki
- **Videos**: Development tutorials on YouTube
- **Examples**: Sample code in `/examples`
- **Templates**: Boilerplate code in `/templates`

---

Happy coding! If you have questions or suggestions for improving this documentation, please open an issue or submit a PR.