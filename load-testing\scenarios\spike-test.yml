config:
  target: "{{ $processEnvironment.API_BASE_URL }}"
  phases:
    # Normal load
    - duration: 300
      arrivalRate: 10
      name: "Normal load (50 users)"
    
    # Sudden spike
    - duration: 60
      arrivalRate: 100
      name: "Spike (500 users)"
    
    # Return to normal
    - duration: 300
      arrivalRate: 10
      name: "Recovery phase"
    
    # Second spike
    - duration: 60
      arrivalRate: 150
      name: "Larger spike (750 users)"
    
    # Final normal phase
    - duration: 300
      arrivalRate: 10
      name: "Final recovery"
  
  processor: "../scripts/test-data-generator.js"
  
  http:
    timeout: 30
    pool: 100
    maxSockets: 200
  
  plugins:
    metrics-by-endpoint:
      percentilesOutputFileType: "csv"
    ensure:
      thresholds:
        - http.response_time.p95: 2000
        - http.response_time.p99: 5000
        - http.codes.5xx: 2
  
  statsInterval: 5

before:
  flow:
    - log: "Starting spike test - simulating traffic surges"
    - function: "setupSpikeTest"

scenarios:
  # Mix of quick and slow operations to simulate real spikes
  
  - name: "Quick Authentication Spike"
    weight: 30
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 200
      - get:
          url: "/auth/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  - name: "Dashboard Load"
    weight: 25
    flow:
      - function: "authenticateUser"
      - parallel:
          - get:
              url: "/projects/summary"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - get:
              url: "/estimates/recent?limit=10"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - get:
              url: "/inspections/upcoming?days=7"
              headers:
                Authorization: "Bearer {{ authToken }}"
          - get:
              url: "/panels/count"
              headers:
                Authorization: "Bearer {{ authToken }}"
  
  - name: "Search Spike"
    weight: 20
    flow:
      - function: "authenticateUser"
      - get:
          url: "/materials/search?query={{ $randomString(2) }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/projects/search?query={{ $randomString(3) }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  - name: "Calculation Burst"
    weight: 15
    flow:
      - function: "authenticateUser"
      - post:
          url: "/calculations/voltage-drop"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            voltage: "{{ $randomItem([120, 208, 240, 480]) }}"
            current: "{{ $randomNumber(50, 200) }}"
            distance: "{{ $randomNumber(50, 500) }}"
            conductorSize: "{{ $randomItem(['12', '10', '8', '6', '4', '2', '1/0', '2/0']) }}"
            conductorType: "copper"
            conduitType: "steel"
            powerFactor: 0.9
            phases: "{{ $randomItem([1, 3]) }}"
  
  - name: "WebSocket Surge"
    weight: 10
    engine: "ws"
    flow:
      - function: "authenticateUser"
      - connect:
          url: "{{ wsUrl }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - send:
          data:
            type: "subscribe_all"
            channels: ["updates", "notifications", "estimates"]
      - think: 10
      - send:
          data:
            type: "unsubscribe_all"