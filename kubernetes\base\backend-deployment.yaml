apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: electrical-app
  labels:
    app: backend
    tier: api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: backend
        tier: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: backend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: electrical/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: jwt-secret
        - name: CORS_ORIGIN
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: cors-origin
        - name: NEO4J_URI
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: neo4j-uri
        - name: NEO4J_USER
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: neo4j-user
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: neo4j-password
        - name: CHROMADB_URL
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: chromadb-url
        - name: INFLUXDB_URL
          valueFrom:
            configMapKeyRef:
              name: backend-config
              key: influxdb-url
        - name: INFLUXDB_TOKEN
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: influxdb-token
        - name: SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: backend-secrets
              key: sentry-dsn
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
        volumeMounts:
        - name: app-data
          mountPath: /app/data
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: app-data
        persistentVolumeClaim:
          claimName: backend-pvc
      - name: tmp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: backend
  namespace: electrical-app
  labels:
    app: backend
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backend-sa
  namespace: electrical-app
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-pvc
  namespace: electrical-app
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: gp2