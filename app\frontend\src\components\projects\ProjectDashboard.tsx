import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  FileText, Zap, Shield, Calendar, CheckCircle, 
  AlertCircle, Clock, PlusCircle, ArrowRight 
} from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import inspectionService, { InspectionStats } from '../../services/inspectionService';

interface ProjectDashboardProps {
  project: any; // Use proper Project type from your types
}

export const ProjectDashboard: React.FC<ProjectDashboardProps> = ({ project }) => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [inspectionStats, setInspectionStats] = useState<InspectionStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(true);

  useEffect(() => {
    if (projectId) {
      loadInspectionStats();
    }
  }, [projectId]);

  const loadInspectionStats = async () => {
    if (!projectId) return;
    
    try {
      setLoadingStats(true);
      const stats = await inspectionService.getProjectInspectionStats(projectId);
      setInspectionStats(stats);
    } catch (error) {
      console.error('Failed to load inspection stats:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  const quickActions = [
    {
      title: 'Panels',
      icon: Zap,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      path: `/projects/${projectId}/panels`,
      description: 'Manage electrical panels'
    },
    {
      title: 'Arc Flash',
      icon: Shield,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      path: `/projects/${projectId}/arc-flash`,
      description: 'Arc flash calculations'
    },
    {
      title: 'Permits',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      path: `/projects/${projectId}/permits`,
      description: 'Permit documents'
    },
    {
      title: 'Inspections',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      path: `/projects/${projectId}/inspections`,
      description: 'Inspection checklists'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Project Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
            <p className="text-gray-600 mt-1">
              {project.address}, {project.city}, {project.state} {project.zip}
            </p>
            <div className="flex gap-4 mt-3">
              <Badge variant={project.status === 'IN_PROGRESS' ? 'default' : 'secondary'}>
                {project.status.replace(/_/g, ' ')}
              </Badge>
              <Badge variant="secondary">{project.type}</Badge>
              {project.permit_number && (
                <Badge variant="secondary">Permit: {project.permit_number}</Badge>
              )}
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Service Size</p>
            <p className="text-xl font-semibold">{project.service_size}A</p>
            <p className="text-sm text-gray-500 mt-2">{project.voltage_system}</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickActions.map((action) => (
          <Card
            key={action.title}
            className={`p-6 hover:shadow-lg transition-shadow cursor-pointer ${action.bgColor}`}
            onClick={() => navigate(action.path)}
          >
            <div className="flex items-start justify-between">
              <div>
                <action.icon className={`h-8 w-8 ${action.color} mb-3`} />
                <h3 className="font-semibold text-gray-900">{action.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{action.description}</p>
              </div>
              <ArrowRight className="h-5 w-5 text-gray-400" />
            </div>
          </Card>
        ))}
      </div>

      {/* Inspection Summary */}
      {!loadingStats && inspectionStats && (
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Inspection Summary</h2>
            <Button
              size="sm"
              onClick={() => navigate(`/projects/${projectId}/inspections/new`)}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              New Inspection
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {inspectionStats.totalInspections}
              </p>
              <p className="text-sm text-gray-600">Total Inspections</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {inspectionStats.passed}
              </p>
              <p className="text-sm text-gray-600">Passed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {inspectionStats.failed}
              </p>
              <p className="text-sm text-gray-600">Failed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">
                {inspectionStats.pending}
              </p>
              <p className="text-sm text-gray-600">Pending</p>
            </div>
          </div>

          {inspectionStats.correctionsRequired > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                <p className="text-sm">
                  <strong>{inspectionStats.correctionsRequired}</strong> inspection(s) require corrections
                </p>
              </div>
            </div>
          )}

          {Object.keys(inspectionStats.byType).length > 0 && (
            <div>
              <h3 className="font-medium text-gray-700 mb-2">By Type</h3>
              <div className="space-y-2">
                {Object.entries(inspectionStats.byType).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      {type.replace(/_/g, ' ')}
                    </span>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Button
            className="w-full mt-4"
            variant="secondary"
            onClick={() => navigate(`/projects/${projectId}/inspections`)}
          >
            View All Inspections
          </Button>
        </Card>
      )}

      {/* Recent Activity */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center text-sm">
            <Clock className="h-4 w-4 text-gray-400 mr-2" />
            <span className="text-gray-600">
              Project created on {new Date(project.created_at).toLocaleDateString()}
            </span>
          </div>
          {project.inspection_status && (
            <div className="flex items-center text-sm">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-gray-600">
                Inspection status: {project.inspection_status.replace(/_/g, ' ')}
              </span>
            </div>
          )}
          {project.permit_expiry && (
            <div className="flex items-center text-sm">
              <Calendar className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-gray-600">
                Permit expires: {new Date(project.permit_expiry).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};