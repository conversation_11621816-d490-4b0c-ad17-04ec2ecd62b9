import { Router, Request, Response } from 'express';
import { authenticate } from '../middleware/auth';
import { arcFlashServiceInstance } from '../services/arc-flash.service';
import { asyncHandler } from '../utils/async-handler';

const router: Router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @route   POST /api/arc-flash/calculate
 * @desc    Perform arc flash calculation for a panel
 * @access  Private
 */
router.post('/calculate', asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  
  // Validate input
  const validatedInput = arcFlashServiceInstance.validateInput(req.body);
  
  // Perform calculation
  const result = await arcFlashServiceInstance.createCalculation({
    ...validatedInput,
    userId,
  });
  
  res.json({
    success: true,
    data: result,
  });
}));

/**
 * @route   GET /api/arc-flash/panel/:panelId
 * @desc    Get all arc flash calculations for a panel
 * @access  Private
 */
router.get('/panel/:panelId', asyncHandler(async (req: Request, res: Response) => {
  const { panelId } = req.params;
  const userId = (req as any).user.id;
  
  const calculations = await arcFlashServiceInstance.getCalculationsForPanel(panelId, userId);
  
  res.json({
    success: true,
    data: calculations,
  });
}));

/**
 * @route   GET /api/arc-flash/panel/:panelId/latest
 * @desc    Get latest arc flash calculation for a panel
 * @access  Private
 */
router.get('/panel/:panelId/latest', asyncHandler(async (req: Request, res: Response) => {
  const { panelId } = req.params;
  const userId = (req as any).user.id;
  
  const calculation = await arcFlashServiceInstance.getLatestCalculation(panelId, userId);
  
  res.json({
    success: true,
    data: calculation,
  });
}));

/**
 * @route   GET /api/arc-flash/:calculationId/label
 * @desc    Generate arc flash label data
 * @access  Private
 */
router.get('/:calculationId/label', asyncHandler(async (req: Request, res: Response) => {
  const { calculationId } = req.params;
  const userId = (req as any).user.id;
  
  const labelData = await arcFlashServiceInstance.generateLabel(calculationId, userId);
  
  res.json({
    success: true,
    data: labelData,
  });
}));

/**
 * @route   GET /api/arc-flash/project/:projectId/required
 * @desc    Get panels requiring arc flash analysis
 * @access  Private
 */
router.get('/project/:projectId/required', asyncHandler(async (req: Request, res: Response) => {
  const { projectId } = req.params;
  const userId = (req as any).user.id;
  
  const panels = await arcFlashServiceInstance.getPanelsRequiringAnalysis(projectId, userId);
  
  res.json({
    success: true,
    data: panels,
    count: panels.length,
  });
}));

/**
 * @route   GET /api/arc-flash/ppe/:incidentEnergy
 * @desc    Get recommended PPE for incident energy level
 * @access  Private
 */
router.get('/ppe/:incidentEnergy', asyncHandler(async (req: Request, res: Response) => {
  const incidentEnergy = parseFloat(req.params.incidentEnergy);
  
  if (isNaN(incidentEnergy) || incidentEnergy < 0) {
    return res.status(400).json({
      success: false,
      error: 'Invalid incident energy value',
    });
  }
  
  const ppe = arcFlashServiceInstance.getRecommendedPPE(incidentEnergy);
  
  res.json({
    success: true,
    data: ppe,
  });
}));

/**
 * @route   POST /api/arc-flash/validate
 * @desc    Validate arc flash calculation input
 * @access  Private
 */
router.post('/validate', asyncHandler(async (req: Request, res: Response) => {
  try {
    const validatedInput = arcFlashServiceInstance.validateInput(req.body);
    
    res.json({
      success: true,
      data: {
        valid: true,
        input: validatedInput,
      },
    });
  } catch (error: any) {
    res.json({
      success: true,
      data: {
        valid: false,
        error: error.message,
      },
    });
  }
}));

export default router;