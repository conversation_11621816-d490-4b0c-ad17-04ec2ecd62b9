import { ConflictResolution, ConflictData, ConflictStrategy } from './types';

export class ConflictResolver {
  async resolve(conflict: ConflictData): Promise<ConflictResolution> {
    const strategy = conflict.strategy || 'last-write-wins';
    
    switch (strategy) {
      case 'last-write-wins':
        return this.lastWriteWins(conflict);
      
      case 'first-write-wins':
        return this.firstWriteWins(conflict);
      
      case 'manual':
        return this.manualResolution(conflict);
      
      case 'merge':
        return this.autoMerge(conflict);
      
      default:
        return this.lastWriteWins(conflict);
    }
  }

  private lastWriteWins(conflict: ConflictData): ConflictResolution {
    const localTime = new Date(conflict.localData.updatedAt).getTime();
    const remoteTime = new Date(conflict.remoteData.updatedAt).getTime();
    
    if (localTime > remoteTime) {
      return {
        action: 'use-local',
        data: conflict.localData,
      };
    } else {
      return {
        action: 'use-remote',
        data: conflict.remoteData,
      };
    }
  }

  private firstWriteWins(conflict: ConflictData): ConflictResolution {
    const localTime = new Date(conflict.localData.updatedAt).getTime();
    const remoteTime = new Date(conflict.remoteData.updatedAt).getTime();
    
    if (localTime < remoteTime) {
      return {
        action: 'use-local',
        data: conflict.localData,
      };
    } else {
      return {
        action: 'use-remote',
        data: conflict.remoteData,
      };
    }
  }

  private manualResolution(conflict: ConflictData): ConflictResolution {
    // Store conflict for manual resolution
    return {
      action: 'defer',
      conflictId: `${conflict.entityType}-${conflict.entityId}-${Date.now()}`,
    };
  }

  private autoMerge(conflict: ConflictData): ConflictResolution {
    const merged = this.deepMerge(conflict.localData, conflict.remoteData, conflict.entityType);
    
    return {
      action: 'retry',
      data: merged,
    };
  }

  private deepMerge(local: any, remote: any, entityType: string): any {
    const merged = { ...local };
    
    // Define merge rules for different entity types
    const mergeRules = this.getMergeRules(entityType);
    
    for (const field in remote) {
      if (remote[field] !== local[field]) {
        if (mergeRules[field]) {
          merged[field] = mergeRules[field](local[field], remote[field]);
        } else if (typeof remote[field] === 'object' && !Array.isArray(remote[field])) {
          merged[field] = this.deepMerge(local[field] || {}, remote[field], `${entityType}.${field}`);
        } else {
          // Use remote value for non-object fields
          merged[field] = remote[field];
        }
      }
    }
    
    // Increment version
    merged.version = Math.max(local.version || 0, remote.version || 0) + 1;
    merged.updatedAt = new Date().toISOString();
    
    return merged;
  }

  private getMergeRules(entityType: string): Record<string, (local: any, remote: any) => any> {
    const rules: Record<string, Record<string, (local: any, remote: any) => any>> = {
      Project: {
        estimatedBudget: (local, remote) => Math.max(local, remote),
        actualCost: (local, remote) => local + remote, // Sum costs from different sources
        notes: (local, remote) => `${local}\n\n--- Remote Update ---\n${remote}`,
      },
      Panel: {
        circuits: (local, remote) => {
          // Merge circuit arrays by circuit number
          const circuitMap = new Map();
          [...local, ...remote].forEach(circuit => {
            if (!circuitMap.has(circuit.number) || 
                circuit.updatedAt > circuitMap.get(circuit.number).updatedAt) {
              circuitMap.set(circuit.number, circuit);
            }
          });
          return Array.from(circuitMap.values());
        },
      },
      Material: {
        quantity: (local, remote) => Math.max(local, remote),
        totalPrice: (local, remote) => Math.max(local, remote),
      },
    };
    
    return rules[entityType] || {};
  }

  async resolveConflictWithUser(
    conflictId: string,
    resolution: 'local' | 'remote' | 'merge',
    mergeData?: any
  ): Promise<void> {
    // This would be called from the UI when user manually resolves a conflict
    // Store the resolution for the sync engine to process
  }
}