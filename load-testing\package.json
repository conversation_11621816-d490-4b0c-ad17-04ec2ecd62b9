{"name": "@electrical/load-testing", "version": "1.0.0", "description": "Load testing suite for electrical contracting application", "scripts": {"test:load": "artillery run scenarios/full-load-test.yml", "test:stress": "artillery run scenarios/stress-test.yml", "test:spike": "artillery run scenarios/spike-test.yml", "test:endurance": "artillery run scenarios/endurance-test.yml", "test:k6": "k6 run scenarios/k6-load-test.js", "test:k6:stress": "k6 run scenarios/k6-stress-test.js", "report": "artillery report results/latest.json", "monitor": "node scripts/monitor-resources.js", "analyze": "node scripts/analyze-results.js", "dashboard": "node scripts/dashboard.js", "test:all": "bash run-tests.sh load", "test:quick": "artillery quick --count 10 --num 5 $API_BASE_URL"}, "dependencies": {"artillery": "^2.0.0", "artillery-plugin-metrics-by-endpoint": "^1.0.0", "artillery-plugin-ensure": "^1.0.0", "@faker-js/faker": "^8.3.1", "k6": "^0.48.0", "axios": "^1.6.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "systeminformation": "^5.21.20", "chalk": "^5.3.0", "table": "^6.8.1", "json2csv": "^6.0.0", "blessed": "^0.1.81", "blessed-contrib": "^4.11.0"}, "devDependencies": {"@types/node": "^20.11.5"}}