import { getDatabaseConnection } from '../database/connection';
import { SyncQueue as SyncQueueEntity } from '../database/entities';
import { SyncQueueItem, SyncOptions } from './types';
import { In, LessThan, Not } from 'typeorm';

export class SyncQueue {
  async addToQueue(item: SyncQueueItem): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    // Check if item already exists in queue
    const existing = await repository.findOne({
      where: {
        entityType: item.entityType,
        entityId: item.entityId,
        status: In(['pending', 'processing']),
      },
    });

    if (existing) {
      // Update priority if new item has higher priority
      if (item.priority !== undefined && item.priority < existing.priority) {
        await repository.update(existing.id, { priority: item.priority });
      }
      return;
    }

    // Get entity data
    const entityDb = db.getRepository(item.entityType);
    const entity = await entityDb.findOne({ where: { id: item.entityId } });
    
    if (!entity) {
      throw new Error(`Entity ${item.entityType}:${item.entityId} not found`);
    }

    await repository.save({
      entityType: item.entityType,
      entityId: item.entityId,
      action: item.action,
      data: JSON.stringify(entity),
      priority: item.priority || 1,
      status: 'pending',
      metadata: JSON.stringify(item.metadata || {}),
    });
  }

  async getPendingItems(options: SyncOptions = {}): Promise<SyncQueueEntity[]> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    const query = repository.createQueryBuilder('queue')
      .where('queue.status = :status', { status: 'pending' })
      .orderBy('queue.priority', 'ASC')
      .addOrderBy('queue.createdAt', 'ASC');

    if (options.limit) {
      query.limit(options.limit);
    }

    if (options.filter?.entityType) {
      query.andWhere('queue.entityType = :entityType', { 
        entityType: options.filter.entityType 
      });
    }

    if (options.filter?.entityId) {
      query.andWhere('queue.entityId = :entityId', { 
        entityId: options.filter.entityId 
      });
    }

    return query.getMany();
  }

  async markAsProcessing(id: string): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    await repository.update(id, {
      status: 'processing',
      processedAt: new Date().toISOString(),
    });
  }

  async markAsCompleted(id: string): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    await repository.update(id, {
      status: 'completed',
      processedAt: new Date().toISOString(),
    });
  }

  async markAsFailed(id: string, error: string): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    await repository.update(id, {
      status: 'failed',
      error,
      processedAt: new Date().toISOString(),
    });
  }

  async incrementRetryCount(id: string): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    const item = await repository.findOne({ where: { id } });
    if (item) {
      await repository.update(id, {
        retryCount: item.retryCount + 1,
        status: 'pending',
      });
    }
  }

  async updateData(id: string, data: any): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    await repository.update(id, {
      data: JSON.stringify(data),
    });
  }

  async cleanupOldItems(daysToKeep: number = 7): Promise<number> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const result = await repository.delete({
      status: 'completed',
      processedAt: LessThan(cutoffDate.toISOString()),
    });
    
    return result.affected || 0;
  }

  async getFailedItems(): Promise<SyncQueueEntity[]> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    return repository.find({
      where: { status: 'failed' },
      order: { createdAt: 'DESC' },
    });
  }

  async retryFailedItems(): Promise<void> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    await repository.update(
      { status: 'failed' },
      { 
        status: 'pending',
        error: null,
      }
    );
  }

  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    const db = await getDatabaseConnection();
    const repository = db.getRepository(SyncQueueEntity);
    
    const stats = await repository
      .createQueryBuilder('queue')
      .select('queue.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('queue.status')
      .getRawMany();
    
    const result = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      total: 0,
    };
    
    stats.forEach(stat => {
      result[stat.status] = parseInt(stat.count);
      result.total += parseInt(stat.count);
    });
    
    return result;
  }
}