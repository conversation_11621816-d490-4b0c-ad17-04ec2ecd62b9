import { PrismaClient, ShortCircuitCalculation, Prisma } from '@prisma/client';
import { ShortCircuitCalculationService, ShortCircuitInputSchema, ShortCircuitInput } from './calculations/short-circuit';
import { z } from 'zod';
import { getTransactionService } from './transaction.service';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';

const prisma = new PrismaClient();
const transactionService = getTransactionService(prisma);

export interface ShortCircuitCalculationWithPanel extends ShortCircuitCalculation {
  panel: {
    id: string;
    name: string;
    location: string;
    voltage_system: string;
    ampere_rating: number;
    bus_rating: number;
    main_breaker_size: number | null;
  };
}

export class ShortCircuitService {
  /**
   * Create a new short circuit calculation with transaction support
   */
  static async createCalculation(
    panelId: string,
    input: ShortCircuitInput,
    userId: string
  ): Promise<ShortCircuitCalculationWithPanel> {
    // Validate input
    const validatedInput = ShortCircuitInputSchema.parse(input);
    
    const transactionResult = await transactionService.executeTransaction(async (tx) => {
      // Get panel information
      const panel = await tx.panel.findUnique({
        where: { id: panelId },
        include: {
          project: true,
          circuits: true,
        },
      });
      
      if (!panel) {
        throw new Error('Panel not found');
      }
      
      // Perform calculation
      const result = ShortCircuitCalculationService.calculate(validatedInput);
      
      // Verify equipment ratings
      const equipmentVerification = ShortCircuitCalculationService.verifyEquipmentRatings(
        result.asymmetricalFault3ph,
        {
          busBracingRating: panel.bus_rating,
          mainBreakerAic: panel.main_breaker_size ? 22 : undefined, // Default AIC for standard breakers
          branchBreakerAic: 10, // Default AIC for branch breakers
        }
      );
      
      // Calculate motor contribution if there are motor loads
      let totalMotorHp = 0;
      if (validatedInput.includeMotorContribution) {
        const motorCircuits = panel.circuits.filter(c => c.load_type === 'MOTOR' || c.load_type === 'HVAC');
        for (const circuit of motorCircuits) {
          // Estimate HP from load (746W per HP)
          totalMotorHp += circuit.connected_load / 746;
        }
      }
      
      // Create database record
      const calculation = await tx.shortCircuitCalculation.create({
        data: {
          panel_id: panelId,
          calculation_method: validatedInput.calculationMethod,
          
          // Source/Utility Data
          utility_voltage: validatedInput.utilityVoltage,
          utility_fault_current: validatedInput.utilityFaultCurrent,
          utility_x_r_ratio: validatedInput.utilityXRRatio,
          
          // Transformer Data
          transformer_kva: validatedInput.transformerKva || null,
          transformer_impedance: validatedInput.transformerImpedance || null,
          transformer_x_r_ratio: validatedInput.transformerXRRatio || null,
          transformer_primary_v: validatedInput.transformerPrimaryV || null,
          transformer_secondary_v: validatedInput.transformerSecondaryV || null,
          transformer_type: validatedInput.transformerType || null,
          
          // Conductor Data
          conductor_length: validatedInput.conductorLength,
          conductor_size: validatedInput.conductorSize,
          conductor_material: validatedInput.conductorMaterial,
          conductor_type: validatedInput.conductorType,
          conductors_per_phase: validatedInput.conductorsPerPhase,
          conduit_type: validatedInput.conduitType,
          
          // Motor Contribution
          motor_contribution: validatedInput.includeMotorContribution,
          motor_hp_total: totalMotorHp || null,
          motor_contribution_multiplier: validatedInput.motorContributionMultiplier,
          
          // Calculated Impedances
          source_impedance_r: result.sourceImpedance.r,
          source_impedance_x: result.sourceImpedance.x,
          transformer_impedance_r: result.transformerImpedance?.r || null,
          transformer_impedance_x: result.transformerImpedance?.x || null,
          conductor_impedance_r: result.conductorImpedance.r,
          conductor_impedance_x: result.conductorImpedance.x,
          total_impedance_r: result.totalImpedance.r,
          total_impedance_x: result.totalImpedance.x,
          total_impedance_z: result.totalImpedance.z,
          total_x_r_ratio: result.totalImpedance.xrRatio,
          
          // Fault Current Results
          symmetrical_fault_3ph: result.symmetricalFault3ph,
          symmetrical_fault_lg: result.symmetricalFaultLG,
          symmetrical_fault_ll: result.symmetricalFaultLL,
          asymmetrical_fault_3ph: result.asymmetricalFault3ph,
          peak_fault_current: result.peakFaultCurrent,
          
          // Equipment Ratings Verification
          bus_bracing_adequate: equipmentVerification.busBracingAdequate,
          bus_bracing_rating: panel.bus_rating,
          main_breaker_adequate: equipmentVerification.mainBreakerAdequate,
          main_breaker_aic: panel.main_breaker_size ? 22 : null,
          branch_breaker_adequate: equipmentVerification.branchBreakerAdequate,
          branch_breaker_aic: 10,
          
          // Notes and metadata
          calculation_notes: result.calculationSteps.join('\n'),
          assumptions: result.warnings.join('\n'),
          calculated_by: userId,
          calculation_date: new Date(),
          valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
          created_at: new Date(),
          updated_at: new Date()
        },
        include: {
          panel: {
            select: {
              id: true,
              name: true,
              location: true,
              voltage_system: true,
              ampere_rating: true,
              bus_rating: true,
              main_breaker_size: true,
            },
          },
        },
      });
      
      // Update panel with short circuit data
      await tx.panel.update({
        where: { id: panelId },
        data: {
          short_circuit_rating: result.asymmetricalFault3ph,
          short_circuit_last_calculated: new Date(),
          updated_at: new Date()
        }
      });
      
      // Create calculation log
      await tx.calculationLog.create({
        data: {
          calculation_type: 'SHORT_CIRCUIT',
          input_data: JSON.stringify(validatedInput),
          output_data: JSON.stringify(result),
          nec_references: JSON.stringify([
            'NEC 110.9 - Interrupting Rating',
            'NEC 110.10 - Circuit Impedance',
            'IEEE 141 - Industrial Power System Analysis'
          ]),
          performed_by: userId,
          project_id: panel.project_id,
        },
      });
      
      // Create audit log
      await createAuditLog({
        action: AUDIT_ACTIONS.CALCULATION_PERFORM,
        userId: userId,
        resourceType: 'PANEL',
        resourceId: panelId,
        details: {
          calculation_id: calculation.id,
          calculation_type: 'SHORT_CIRCUIT',
          fault_current: result.asymmetricalFault3ph,
          equipment_adequate: equipmentVerification.allEquipmentAdequate
        }
      });
      
      return calculation;
    });
    
    if (!transactionResult.success) {
      throw transactionResult.error || new Error('Failed to create short circuit calculation');
    }
    
    return transactionResult.data!;
  }
  
  /**
   * Get all calculations for a panel
   */
  static async getCalculationsByPanel(panelId: string): Promise<ShortCircuitCalculation[]> {
    return prisma.shortCircuitCalculation.findMany({
      where: { panel_id: panelId },
      orderBy: { calculation_date: 'desc' },
    });
  }
  
  /**
   * Get the latest calculation for a panel
   */
  static async getLatestCalculation(panelId: string): Promise<ShortCircuitCalculation | null> {
    return prisma.shortCircuitCalculation.findFirst({
      where: { panel_id: panelId },
      orderBy: { calculation_date: 'desc' },
    });
  }
  
  /**
   * Get a specific calculation by ID
   */
  static async getCalculationById(id: string): Promise<ShortCircuitCalculationWithPanel | null> {
    return prisma.shortCircuitCalculation.findUnique({
      where: { id },
      include: {
        panel: {
          select: {
            id: true,
            name: true,
            location: true,
            voltage_system: true,
            ampere_rating: true,
            bus_rating: true,
            main_breaker_size: true,
          },
        },
      },
    });
  }
  
  /**
   * Get all panels in a project that need short circuit analysis
   */
  static async getPanelsRequiringAnalysis(projectId: string): Promise<any[]> {
    const panels = await prisma.panel.findMany({
      where: {
        project_id: projectId,
      },
      include: {
        short_circuit_calculations: {
          orderBy: { calculation_date: 'desc' },
          take: 1,
        },
      },
    });
    
    return panels.map(panel => ({
      id: panel.id,
      name: panel.name,
      location: panel.location,
      voltage_system: panel.voltage_system,
      ampere_rating: panel.ampere_rating,
      hasCalculation: panel.short_circuit_calculations.length > 0,
      lastCalculationDate: panel.short_circuit_calculations[0]?.calculation_date || null,
      calculationExpired: panel.short_circuit_calculations[0]
        ? new Date(panel.short_circuit_calculations[0].valid_until) < new Date()
        : true,
    }));
  }
  
  /**
   * Generate equipment recommendations based on fault current with transaction support
   */
  static async generateEquipmentRecommendations(
    calculationId: string,
    userId: string
  ): Promise<{
    mainBreaker: any[];
    branchBreakers: any[];
    seriesRatings: any[];
  }> {
    const transactionResult = await transactionService.executeTransaction(async (tx) => {
      const calculation = await tx.shortCircuitCalculation.findUnique({
        where: { id: calculationId },
        include: {
          panel: {
            select: {
              id: true,
              name: true,
              location: true,
              voltage_system: true,
              ampere_rating: true,
              bus_rating: true,
              main_breaker_size: true,
            },
          },
        },
      });
      
      if (!calculation) {
        throw new Error('Calculation not found');
      }
      
      const requiredAic = ShortCircuitCalculationService.getRequiredAicRating(
        calculation.asymmetrical_fault_3ph
      );
      
      // Get breakers with adequate AIC rating
      const mainBreakers = await tx.breakerType.findMany({
        where: {
          interrupt_rating: { gte: requiredAic },
          voltage_rating: { gte: calculation.utility_voltage },
          ampere_rating: { gte: calculation.panel.main_breaker_size || 0 },
        },
        orderBy: [
          { interrupt_rating: 'asc' },
          { list_price: 'asc' },
        ],
        take: 5,
      });
      
      const branchBreakers = await tx.breakerType.findMany({
        where: {
          interrupt_rating: { gte: requiredAic },
          voltage_rating: { gte: calculation.utility_voltage },
          poles: { lte: 2 },
        },
        orderBy: [
          { interrupt_rating: 'asc' },
          { list_price: 'asc' },
        ],
        take: 10,
      });
      
      // Series ratings would require a separate table with tested combinations
      const seriesRatings: any[] = [];
      if (requiredAic > 22) {
        seriesRatings.push({
          upstream: 'Current Limiting Fuse',
          downstream: 'Standard 10kA Breaker',
          combinationRating: requiredAic,
          note: 'Verify specific manufacturer series rating charts',
        });
      }
      
      // Create equipment recommendation record
      await tx.equipmentRecommendation.create({
        data: {
          calculation_id: calculationId,
          calculation_type: 'SHORT_CIRCUIT',
          required_aic_rating: requiredAic,
          recommended_main_breakers: JSON.stringify(mainBreakers.map(b => b.id)),
          recommended_branch_breakers: JSON.stringify(branchBreakers.map(b => b.id)),
          series_ratings: JSON.stringify(seriesRatings),
          generated_by: userId,
          generated_at: new Date()
        }
      });
      
      // Create audit log
      await createAuditLog({
        action: 'SHORT_CIRCUIT_RECOMMENDATIONS',
        userId: userId,
        resourceType: 'SHORT_CIRCUIT_CALCULATION',
        resourceId: calculationId,
        details: {
          required_aic: requiredAic,
          main_breaker_count: mainBreakers.length,
          branch_breaker_count: branchBreakers.length
        }
      });
      
      return {
        mainBreaker: mainBreakers,
        branchBreakers: branchBreakers,
        seriesRatings: seriesRatings,
      };
    });
    
    if (!transactionResult.success) {
      throw transactionResult.error || new Error('Failed to generate equipment recommendations');
    }
    
    return transactionResult.data!;
  }
  
  /**
   * Update an existing calculation
   */
  static async updateCalculation(
    id: string,
    reviewedBy: string
  ): Promise<ShortCircuitCalculation> {
    return prisma.shortCircuitCalculation.update({
      where: { id },
      data: {
        reviewed_by: reviewedBy,
        updated_at: new Date(),
      },
    });
  }
  
  /**
   * Delete a calculation
   */
  static async deleteCalculation(id: string): Promise<void> {
    await prisma.shortCircuitCalculation.delete({
      where: { id },
    });
  }
  
  /**
   * Generate report data for a calculation
   */
  static async generateReportData(calculationId: string): Promise<any> {
    const calculation = await this.getCalculationById(calculationId);
    if (!calculation) {
      throw new Error('Calculation not found');
    }
    
    const project = await prisma.project.findFirst({
      where: {
        panels: {
          some: { id: calculation.panel_id },
        },
      },
      include: {
        customer: true,
      },
    });
    
    return {
      project: project,
      panel: calculation.panel,
      calculation: {
        ...calculation,
        necReferences: [
          'NEC 110.9 - Interrupting Rating',
          'NEC 110.10 - Circuit Impedance and Short-Circuit Current Ratings',
          'NEC 240.86 - Series Ratings',
          'IEEE 141 - Industrial Power System Analysis',
        ],
        requiredAicRating: ShortCircuitCalculationService.getRequiredAicRating(
          calculation.asymmetrical_fault_3ph
        ),
      },
      generatedDate: new Date(),
    };
  }
}