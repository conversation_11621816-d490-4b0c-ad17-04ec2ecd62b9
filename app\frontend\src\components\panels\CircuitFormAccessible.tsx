import React, { useState, useEffect, useRef } from 'react';
import { z } from 'zod';
import { CircuitSchema, Circuit } from '@electrical/shared';
import { panelService } from '../../services/panelService';
import { useAuthStore } from '../../stores/auth';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { FocusTrap, announce, generateId } from '../../utils/accessibility';

// Real wire sizes per NEC 2023 Table 310.15(B)(16)
const WIRE_SIZES = [
  { value: '14_AWG', label: '14 AWG', ampacity60: 15, ampacity75: 20, ampacity90: 25 },
  { value: '12_AWG', label: '12 AWG', ampacity60: 20, ampacity75: 25, ampacity90: 30 },
  { value: '10_AWG', label: '10 AWG', ampacity60: 30, ampacity75: 35, ampacity90: 40 },
  { value: '8_AWG', label: '8 AWG', ampacity60: 40, ampacity75: 50, ampacity90: 55 },
  { value: '6_AWG', label: '6 AWG', ampacity60: 55, ampacity75: 65, ampacity90: 75 },
  { value: '4_AWG', label: '4 AWG', ampacity60: 70, ampacity75: 85, ampacity90: 95 },
  { value: '3_AWG', label: '3 AWG', ampacity60: 85, ampacity75: 100, ampacity90: 115 },
  { value: '2_AWG', label: '2 AWG', ampacity60: 95, ampacity75: 115, ampacity90: 130 },
  { value: '1_AWG', label: '1 AWG', ampacity60: 110, ampacity75: 130, ampacity90: 145 },
  { value: '1/0_AWG', label: '1/0 AWG', ampacity60: 125, ampacity75: 150, ampacity90: 170 },
  { value: '2/0_AWG', label: '2/0 AWG', ampacity60: 145, ampacity75: 175, ampacity90: 195 },
  { value: '3/0_AWG', label: '3/0 AWG', ampacity60: 165, ampacity75: 200, ampacity90: 225 },
  { value: '4/0_AWG', label: '4/0 AWG', ampacity60: 195, ampacity75: 230, ampacity90: 260 },
  { value: '250_kcmil', label: '250 kcmil', ampacity60: 215, ampacity75: 255, ampacity90: 290 },
  { value: '300_kcmil', label: '300 kcmil', ampacity60: 240, ampacity75: 285, ampacity90: 320 },
  { value: '350_kcmil', label: '350 kcmil', ampacity60: 260, ampacity75: 310, ampacity90: 350 },
  { value: '400_kcmil', label: '400 kcmil', ampacity60: 280, ampacity75: 335, ampacity90: 380 },
  { value: '500_kcmil', label: '500 kcmil', ampacity60: 320, ampacity75: 380, ampacity90: 430 },
  { value: '600_kcmil', label: '600 kcmil', ampacity60: 350, ampacity75: 420, ampacity90: 475 },
  { value: '750_kcmil', label: '750 kcmil', ampacity60: 400, ampacity75: 475, ampacity90: 535 },
];

const WIRE_TYPES = [
  { value: 'THHN', label: 'THHN - 90°C Dry', tempRating: 90 },
  { value: 'THWN-2', label: 'THWN-2 - 90°C Wet/Dry', tempRating: 90 },
  { value: 'XHHW-2', label: 'XHHW-2 - 90°C Wet/Dry', tempRating: 90 },
  { value: 'NM', label: 'NM (Romex) - 60°C', tempRating: 60 },
  { value: 'MC', label: 'MC Cable - 90°C', tempRating: 90 },
  { value: 'USE-2', label: 'USE-2 - 90°C Underground', tempRating: 90 },
  { value: 'RHH', label: 'RHH - 90°C Dry', tempRating: 90 },
  { value: 'RHW-2', label: 'RHW-2 - 90°C Wet/Dry', tempRating: 90 },
];

const CONDUIT_SIZES = [
  { value: '1/2', label: '1/2"', area: 0.122 },
  { value: '3/4', label: '3/4"', area: 0.213 },
  { value: '1', label: '1"', area: 0.346 },
  { value: '1-1/4', label: '1-1/4"', area: 0.598 },
  { value: '1-1/2', label: '1-1/2"', area: 0.814 },
  { value: '2', label: '2"', area: 1.316 },
  { value: '2-1/2', label: '2-1/2"', area: 2.067 },
  { value: '3', label: '3"', area: 3.356 },
  { value: '3-1/2', label: '3-1/2"', area: 4.618 },
  { value: '4', label: '4"', area: 5.901 },
];

interface CircuitFormProps {
  panelId: string;
  circuit?: Circuit | null;
  defaultSlot?: number;
  voltage: number;
  phaseConfig: string;
  onClose: () => void;
  onSave: () => void;
}

export const CircuitFormAccessible: React.FC<CircuitFormProps> = ({
  panelId,
  circuit,
  defaultSlot,
  voltage,
  phaseConfig,
  onClose,
  onSave,
}) => {
  const { token } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const modalRef = useRef<HTMLDivElement>(null);
  const focusTrapRef = useRef<FocusTrap | null>(null);
  const errorAlertRef = useRef<HTMLDivElement>(null);
  
  // Generate unique IDs for form field associations
  const fieldIds = {
    circuitNumber: generateId('circuit-number'),
    description: generateId('description'),
    breakerSize: generateId('breaker-size'),
    poles: generateId('poles'),
    breakerType: generateId('breaker-type'),
    phaseConnection: generateId('phase-connection'),
    voltage: generateId('voltage'),
    wireSize: generateId('wire-size'),
    wireType: generateId('wire-type'),
    wireCount: generateId('wire-count'),
    conduitType: generateId('conduit-type'),
    conduitSize: generateId('conduit-size'),
    loadType: generateId('load-type'),
    connectedLoad: generateId('connected-load'),
    demandFactor: generateId('demand-factor'),
    continuousLoad: generateId('continuous-load'),
    roomArea: generateId('room-area'),
    controlType: generateId('control-type'),
    isSpare: generateId('is-spare'),
    isSpace: generateId('is-space'),
    notes: generateId('notes'),
  };
  
  const [formData, setFormData] = useState({
    panel_id: panelId,
    circuit_number: defaultSlot || circuit?.circuit_number || 1,
    description: circuit?.description || '',
    breaker_size: circuit?.breaker_size || 20,
    breaker_type: circuit?.breaker_type || 'STANDARD' as const,
    poles: circuit?.poles || 1,
    phase_connection: circuit?.phase_connection || '',
    wire_size: circuit?.wire_size || '12_AWG',
    wire_type: circuit?.wire_type || 'THHN' as const,
    wire_count: circuit?.wire_count || 2,
    conduit_type: circuit?.conduit_type || 'EMT' as const,
    conduit_size: circuit?.conduit_size || '1/2',
    voltage: circuit?.voltage || voltage,
    load_type: circuit?.load_type || 'RECEPTACLE' as const,
    continuous_load: circuit?.continuous_load || false,
    connected_load: circuit?.connected_load || 0,
    demand_factor: circuit?.demand_factor || 1,
    room_area: circuit?.room_area || '',
    control_type: circuit?.control_type || undefined,
    is_spare: circuit?.is_spare || false,
    is_space: circuit?.is_space || false,
    notes: circuit?.notes || '',
  });

  useEffect(() => {
    if (modalRef.current) {
      focusTrapRef.current = new FocusTrap(modalRef.current);
      focusTrapRef.current.activate();
      
      announce(circuit ? 'Edit circuit form opened' : 'Add new circuit form opened');
    }

    return () => {
      if (focusTrapRef.current) {
        focusTrapRef.current.deactivate();
      }
    };
  }, [circuit]);

  useEffect(() => {
    // Auto-calculate wire size based on breaker size and continuous load
    const minAmpacity = formData.continuous_load 
      ? formData.breaker_size * 1.25 
      : formData.breaker_size;
    
    const wireType = WIRE_TYPES.find(wt => wt.value === formData.wire_type);
    const tempRating = wireType?.tempRating || 75;
    
    const recommendedWire = WIRE_SIZES.find(ws => {
      const ampacity = tempRating === 60 ? ws.ampacity60 :
                      tempRating === 75 ? ws.ampacity75 :
                      ws.ampacity90;
      return ampacity >= minAmpacity;
    });
    
    if (recommendedWire && recommendedWire.value !== formData.wire_size) {
      setFormData(prev => ({ ...prev, wire_size: recommendedWire.value }));
      announce(`Wire size automatically updated to ${recommendedWire.label}`);
    }
  }, [formData.breaker_size, formData.continuous_load, formData.wire_type]);

  useEffect(() => {
    // Calculate load with continuous factor
    const baseFactor = formData.continuous_load ? 1.25 : 1.0;
    const calculatedLoad = formData.connected_load * formData.demand_factor * baseFactor;
    
    setFormData(prev => ({ 
      ...prev, 
      calculated_load: calculatedLoad 
    }));
  }, [formData.connected_load, formData.demand_factor, formData.continuous_load]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!token) return;

    try {
      setLoading(true);
      setErrors({});
      announce('Saving circuit...');

      // Validate with Zod schema
      const validatedData = CircuitSchema.parse(formData);

      if (circuit?.id) {
        await panelService.updateCircuit(circuit.id, validatedData, token);
        announce('Circuit updated successfully');
      } else {
        await panelService.createCircuit(panelId, validatedData, token);
        announce('Circuit added successfully');
      }

      onSave();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(fieldErrors);
        announce('Please correct the form errors', 'assertive');
        
        // Focus on first error field
        setTimeout(() => {
          errorAlertRef.current?.focus();
        }, 100);
      } else {
        console.error('Error saving circuit:', error);
        setErrors({ general: 'Failed to save circuit' });
        announce('Error saving circuit', 'assertive');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked :
              type === 'number' ? (value === '' ? 0 : Number(value)) : 
              value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const getPhaseOptions = () => {
    if (phaseConfig === 'SINGLE_PHASE') {
      return [];
    } else if (phaseConfig === 'THREE_PHASE_3W') {
      if (formData.poles === 1) return ['A', 'B', 'C'];
      if (formData.poles === 2) return ['AB', 'BC', 'AC'];
      if (formData.poles === 3) return ['ABC'];
    } else if (phaseConfig === 'THREE_PHASE_4W') {
      if (formData.poles === 1) return ['A', 'B', 'C'];
      if (formData.poles === 2) return ['AB', 'BC', 'AC'];
      if (formData.poles === 3) return ['ABC'];
    }
    return [];
  };

  const getDemandFactor = (loadType: string) => {
    // NEC 2023 demand factors
    switch (loadType) {
      case 'LIGHTING': return 1.0; // 100% for first 3kVA, then reduced
      case 'RECEPTACLE': return 1.0; // 100% for first 10kVA, then reduced
      case 'MOTOR': return 1.25; // 125% of largest motor
      case 'HVAC': return 1.0;
      case 'APPLIANCE': return 0.75; // 75% for 4+ appliances
      case 'FEEDER': return 1.0;
      case 'EQUIPMENT': return 1.0;
      default: return 1.0;
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="circuit-form-title"
      onKeyDown={handleKeyDown}
    >
      <div 
        ref={modalRef}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 id="circuit-form-title" className="text-lg font-medium text-gray-900 dark:text-white">
            {circuit ? 'Edit Circuit' : 'Add Circuit'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
            aria-label="Close dialog"
          >
            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {errors.general && (
            <div 
              ref={errorAlertRef}
              className="rounded-md bg-red-50 p-4"
              role="alert"
              tabIndex={-1}
            >
              <p className="text-sm text-red-800">{errors.general}</p>
            </div>
          )}

          {/* Circuit Configuration */}
          <fieldset>
            <legend className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Circuit Configuration
            </legend>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor={fieldIds.circuitNumber} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Circuit Number <span aria-label="required">*</span>
                </label>
                <input
                  id={fieldIds.circuitNumber}
                  type="number"
                  name="circuit_number"
                  value={formData.circuit_number}
                  onChange={handleChange}
                  min="1"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  required
                  aria-invalid={!!errors.circuit_number}
                  aria-describedby={errors.circuit_number ? `${fieldIds.circuitNumber}-error` : undefined}
                />
                {errors.circuit_number && (
                  <p id={`${fieldIds.circuitNumber}-error`} className="mt-1 text-sm text-red-600" role="alert">
                    {errors.circuit_number}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor={fieldIds.breakerSize} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Breaker Size (A) <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.breakerSize}
                  name="breaker_size"
                  value={formData.breaker_size}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  required
                  aria-invalid={!!errors.breaker_size}
                  aria-describedby={errors.breaker_size ? `${fieldIds.breakerSize}-error` : undefined}
                >
                  {[15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100, 125, 150, 175, 200, 225, 250, 300, 350, 400].map(size => (
                    <option key={size} value={size}>{size}A</option>
                  ))}
                </select>
                {errors.breaker_size && (
                  <p id={`${fieldIds.breakerSize}-error`} className="mt-1 text-sm text-red-600" role="alert">
                    {errors.breaker_size}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor={fieldIds.poles} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Poles <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.poles}
                  name="poles"
                  value={formData.poles}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  aria-describedby={`${fieldIds.poles}-desc`}
                >
                  <option value={1}>1 Pole</option>
                  <option value={2}>2 Pole</option>
                  <option value={3}>3 Pole</option>
                </select>
                <p id={`${fieldIds.poles}-desc`} className="mt-1 text-xs text-gray-500">
                  Number of phases the breaker spans
                </p>
              </div>

              <div>
                <label htmlFor={fieldIds.breakerType} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Breaker Type <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.breakerType}
                  name="breaker_type"
                  value={formData.breaker_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  aria-describedby={`${fieldIds.breakerType}-desc`}
                >
                  <option value="STANDARD">Standard</option>
                  <option value="GFCI">GFCI</option>
                  <option value="AFCI">AFCI</option>
                  <option value="GFCI_AFCI">GFCI/AFCI Dual Function</option>
                  <option value="SPACE_ONLY">Space Only (No Breaker)</option>
                </select>
                <p id={`${fieldIds.breakerType}-desc`} className="mt-1 text-xs text-gray-500">
                  Protection type provided by the breaker
                </p>
              </div>

              {getPhaseOptions().length > 0 && (
                <div>
                  <label htmlFor={fieldIds.phaseConnection} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phase Connection
                  </label>
                  <select
                    id={fieldIds.phaseConnection}
                    name="phase_connection"
                    value={formData.phase_connection}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  >
                    <option value="">Auto-assign</option>
                    {getPhaseOptions().map(phase => (
                      <option key={phase} value={phase}>{phase}</option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label htmlFor={fieldIds.voltage} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Voltage <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.voltage}
                  name="voltage"
                  value={formData.voltage}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value={120}>120V</option>
                  <option value={208}>208V</option>
                  <option value={240}>240V</option>
                  <option value={277}>277V</option>
                  <option value={480}>480V</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor={fieldIds.description} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description <span aria-label="required">*</span>
              </label>
              <input
                id={fieldIds.description}
                type="text"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                placeholder="Kitchen Outlets, HVAC Unit, Lighting Circuit, etc."
                required
                aria-invalid={!!errors.description}
                aria-describedby={errors.description ? `${fieldIds.description}-error` : undefined}
              />
              {errors.description && (
                <p id={`${fieldIds.description}-error`} className="mt-1 text-sm text-red-600" role="alert">
                  {errors.description}
                </p>
              )}
            </div>
          </fieldset>

          {/* Wire and Conduit */}
          <fieldset>
            <legend className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Wire and Conduit
            </legend>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor={fieldIds.wireSize} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Wire Size <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.wireSize}
                  name="wire_size"
                  value={formData.wire_size}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  aria-describedby={`${fieldIds.wireSize}-desc`}
                >
                  {WIRE_SIZES.map(ws => (
                    <option key={ws.value} value={ws.value}>{ws.label}</option>
                  ))}
                </select>
                <p id={`${fieldIds.wireSize}-desc`} className="mt-1 text-xs text-gray-500">
                  Auto-calculated based on breaker size and load type
                </p>
              </div>

              <div>
                <label htmlFor={fieldIds.wireType} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Wire Type <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.wireType}
                  name="wire_type"
                  value={formData.wire_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {WIRE_TYPES.map(wt => (
                    <option key={wt.value} value={wt.value}>{wt.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor={fieldIds.wireCount} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Wire Count <span aria-label="required">*</span>
                </label>
                <input
                  id={fieldIds.wireCount}
                  type="number"
                  name="wire_count"
                  value={formData.wire_count}
                  onChange={handleChange}
                  min="2"
                  max="10"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  required
                  aria-describedby={`${fieldIds.wireCount}-desc`}
                />
                <p id={`${fieldIds.wireCount}-desc`} className="mt-1 text-xs text-gray-500">
                  Including ground wire
                </p>
              </div>

              <div>
                <label htmlFor={fieldIds.conduitType} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Conduit Type
                </label>
                <select
                  id={fieldIds.conduitType}
                  name="conduit_type"
                  value={formData.conduit_type}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="EMT">EMT</option>
                  <option value="PVC">PVC</option>
                  <option value="RIGID">Rigid</option>
                  <option value="MC_CABLE">MC Cable</option>
                  <option value="NM_CABLE">NM Cable (Romex)</option>
                  <option value="FLEX">Flexible</option>
                </select>
              </div>

              <div>
                <label htmlFor={fieldIds.conduitSize} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Conduit Size
                </label>
                <select
                  id={fieldIds.conduitSize}
                  name="conduit_size"
                  value={formData.conduit_size}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  {CONDUIT_SIZES.map(cs => (
                    <option key={cs.value} value={cs.value}>{cs.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </fieldset>

          {/* Load Information */}
          <fieldset>
            <legend className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Load Information
            </legend>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor={fieldIds.loadType} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Load Type <span aria-label="required">*</span>
                </label>
                <select
                  id={fieldIds.loadType}
                  name="load_type"
                  value={formData.load_type}
                  onChange={(e) => {
                    handleChange(e);
                    const newDemandFactor = getDemandFactor(e.target.value);
                    setFormData(prev => ({ ...prev, demand_factor: newDemandFactor }));
                    announce(`Demand factor updated to ${(newDemandFactor * 100).toFixed(0)}%`);
                  }}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="LIGHTING">Lighting</option>
                  <option value="RECEPTACLE">Receptacle</option>
                  <option value="MOTOR">Motor</option>
                  <option value="HVAC">HVAC</option>
                  <option value="APPLIANCE">Appliance</option>
                  <option value="FEEDER">Feeder</option>
                  <option value="EQUIPMENT">Equipment</option>
                </select>
              </div>

              <div>
                <label htmlFor={fieldIds.connectedLoad} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Connected Load (W) <span aria-label="required">*</span>
                </label>
                <input
                  id={fieldIds.connectedLoad}
                  type="number"
                  name="connected_load"
                  value={formData.connected_load}
                  onChange={handleChange}
                  min="0"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  required
                  aria-describedby={`${fieldIds.connectedLoad}-desc`}
                />
                <p id={`${fieldIds.connectedLoad}-desc`} className="mt-1 text-xs text-gray-500">
                  Total connected watts for this circuit
                </p>
              </div>

              <div>
                <label htmlFor={fieldIds.demandFactor} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Demand Factor
                </label>
                <input
                  id={fieldIds.demandFactor}
                  type="number"
                  name="demand_factor"
                  value={formData.demand_factor}
                  onChange={handleChange}
                  min="0"
                  max="1.25"
                  step="0.05"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  aria-describedby={`${fieldIds.demandFactor}-desc`}
                />
                <p id={`${fieldIds.demandFactor}-desc`} className="mt-1 text-xs text-gray-500">
                  NEC demand factor (auto-calculated)
                </p>
              </div>

              <div className="sm:col-span-3">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    id={fieldIds.continuousLoad}
                    type="checkbox"
                    name="continuous_load"
                    checked={formData.continuous_load}
                    onChange={handleChange}
                    className="sr-only peer"
                    aria-describedby={`${fieldIds.continuousLoad}-desc`}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    Continuous Load (3+ hours)
                  </span>
                </label>
                <p id={`${fieldIds.continuousLoad}-desc`} className="mt-1 text-xs text-gray-500 ml-14">
                  125% factor will be applied per NEC 210.19(A)(1)
                </p>
              </div>
            </div>
          </fieldset>

          {/* Additional Information */}
          <fieldset>
            <legend className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Additional Information
            </legend>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor={fieldIds.roomArea} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Room/Area
                </label>
                <input
                  id={fieldIds.roomArea}
                  type="text"
                  name="room_area"
                  value={formData.room_area}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  placeholder="Kitchen, Master Bedroom, Garage, etc."
                />
              </div>

              <div>
                <label htmlFor={fieldIds.controlType} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Control Type
                </label>
                <select
                  id={fieldIds.controlType}
                  name="control_type"
                  value={formData.control_type || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                >
                  <option value="">None</option>
                  <option value="SWITCH">Switch</option>
                  <option value="TIMER">Timer</option>
                  <option value="PHOTOCELL">Photocell</option>
                  <option value="OCCUPANCY">Occupancy Sensor</option>
                  <option value="DIMMER">Dimmer</option>
                  <option value="CONTACTOR">Contactor</option>
                </select>
              </div>
            </div>

            <div className="mt-4 flex space-x-4">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  id={fieldIds.isSpare}
                  type="checkbox"
                  name="is_spare"
                  checked={formData.is_spare}
                  onChange={handleChange}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                  Spare Breaker
                </span>
              </label>

              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  id={fieldIds.isSpace}
                  type="checkbox"
                  name="is_space"
                  checked={formData.is_space}
                  onChange={handleChange}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                  Empty Space
                </span>
              </label>
            </div>

            <div className="mt-4">
              <label htmlFor={fieldIds.notes} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Notes
              </label>
              <textarea
                id={fieldIds.notes}
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                placeholder="Additional notes or special requirements"
              />
            </div>
          </fieldset>

          {/* Calculated Values Display */}
          {formData.connected_load > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4" role="region" aria-labelledby="calculated-values-heading">
              <h4 id="calculated-values-heading" className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
                Calculated Values
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm" role="list">
                <div role="listitem">
                  <span className="text-blue-700 dark:text-blue-400">Calculated Load:</span>
                  <span className="ml-2 font-medium text-blue-900 dark:text-blue-200">
                    <span aria-label={`${(formData.connected_load * formData.demand_factor * (formData.continuous_load ? 1.25 : 1)).toFixed(0)} watts`}>
                      {(formData.connected_load * formData.demand_factor * (formData.continuous_load ? 1.25 : 1)).toFixed(0)}W
                    </span>
                  </span>
                </div>
                <div role="listitem">
                  <span className="text-blue-700 dark:text-blue-400">Current Draw:</span>
                  <span className="ml-2 font-medium text-blue-900 dark:text-blue-200">
                    <span aria-label={`${(formData.connected_load / formData.voltage).toFixed(1)} amps`}>
                      {(formData.connected_load / formData.voltage).toFixed(1)}A
                    </span>
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              aria-busy={loading}
            >
              {loading ? 'Saving...' : circuit ? 'Update Circuit' : 'Add Circuit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};