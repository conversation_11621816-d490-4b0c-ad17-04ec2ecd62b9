import { Router } from 'express';
import { InspectionService } from '../services/inspection.service';
import { authenticate } from '../middleware/auth';
import { asyncHandler } from '../utils/async-handler';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';

const router: Router = Router();
const inspectionService = new InspectionService();

// Configure multer for photo uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'inspections');
    await fs.mkdir(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|heic|heif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get available inspection types
router.get('/types', authenticate, asyncHandler(async (req, res) => {
  const types = inspectionService.getAvailableInspectionTypes();
  res.json({ types });
}));

// Create new inspection checklist
router.post('/', authenticate, asyncHandler(async (req, res) => {
  const { projectId, inspectionType, inspectionSubtype, scheduledDate, permitDocumentId } = req.body;

  if (!projectId || !inspectionType) {
    return res.status(400).json({ error: 'Project ID and inspection type are required' });
  }

  const checklist = await inspectionService.createInspectionChecklist({
    projectId,
    inspectionType,
    inspectionSubtype,
    scheduledDate: scheduledDate ? new Date(scheduledDate) : undefined,
    permitDocumentId,
    createdBy: req.user!.id
  });

  res.status(201).json({ checklist });
}));

// Get inspection checklist by ID
router.get('/:id', authenticate, asyncHandler(async (req, res) => {
  const checklist = await inspectionService.getInspectionChecklist(req.params.id);
  
  if (!checklist) {
    return res.status(404).json({ error: 'Inspection checklist not found' });
  }

  res.json({ checklist });
}));

// Get inspection by QR code (no auth required for mobile access)
router.get('/qr/:qrCodeId', asyncHandler(async (req, res) => {
  const checklist = await inspectionService.getInspectionByQRCode(req.params.qrCodeId);
  
  if (!checklist) {
    return res.status(404).json({ error: 'Inspection not found' });
  }

  res.json({ checklist });
}));

// List project inspections
router.get('/project/:projectId', authenticate, asyncHandler(async (req, res) => {
  const { status, inspectionType, dateFrom, dateTo } = req.query;

  const inspections = await inspectionService.listProjectInspections(
    req.params.projectId,
    {
      status: status as string,
      inspectionType: inspectionType as string,
      dateFrom: dateFrom ? new Date(dateFrom as string) : undefined,
      dateTo: dateTo ? new Date(dateTo as string) : undefined
    }
  );

  res.json({ inspections });
}));

// Update inspection status
router.patch('/:id/status', authenticate, asyncHandler(async (req, res) => {
  const {
    status,
    inspectorName,
    inspectorId,
    inspectorCompany,
    inspectorPhone,
    inspectorEmail,
    inspectionDate,
    overallResult,
    reinspectionRequired,
    correctionsRequired
  } = req.body;

  const checklist = await inspectionService.updateInspectionStatus(req.params.id, {
    status,
    inspectorName,
    inspectorId,
    inspectorCompany,
    inspectorPhone,
    inspectorEmail,
    inspectionDate: inspectionDate ? new Date(inspectionDate) : undefined,
    overallResult,
    reinspectionRequired,
    correctionsRequired
  });

  res.json({ checklist });
}));

// Update checklist item
router.patch('/item/:itemId', authenticate, asyncHandler(async (req, res) => {
  const {
    status,
    inspectorNotes,
    failureReason,
    correctionRequired,
    correctionDescription,
    correctionDeadline,
    measurementValue,
    photosAttached
  } = req.body;

  const item = await inspectionService.updateChecklistItem(req.params.itemId, {
    status,
    inspectorNotes,
    failureReason,
    correctionRequired,
    correctionDescription,
    correctionDeadline: correctionDeadline ? new Date(correctionDeadline) : undefined,
    measurementValue,
    photosAttached
  });

  res.json({ item });
}));

// Complete correction
router.post('/item/:itemId/complete-correction', authenticate, asyncHandler(async (req, res) => {
  const item = await inspectionService.completeCorrection(
    req.params.itemId,
    req.user!.id
  );

  res.json({ item });
}));

// Upload inspection photo
router.post('/:id/photos', authenticate, upload.single('photo'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No photo uploaded' });
  }

  const {
    itemId,
    caption,
    photoType,
    locationTag,
    latitude,
    longitude
  } = req.body;

  const photo = await inspectionService.addInspectionPhoto({
    checklistId: req.params.id,
    itemId,
    filePath: `/uploads/inspections/${req.file.filename}`,
    fileName: req.file.originalname,
    fileSize: req.file.size,
    mimeType: req.file.mimetype,
    caption,
    photoType: photoType || 'DETAIL',
    locationTag,
    uploadedBy: req.user!.id,
    latitude: latitude ? parseFloat(latitude) : undefined,
    longitude: longitude ? parseFloat(longitude) : undefined
  });

  res.status(201).json({ photo });
}));

// Sign off inspection
router.post('/:id/sign-off', authenticate, asyncHandler(async (req, res) => {
  const {
    contractorPresent,
    contractorName,
    contractorSignature,
    inspectorSignature
  } = req.body;

  const checklist = await inspectionService.signOffInspection(req.params.id, {
    contractorPresent,
    contractorName,
    contractorSignature,
    inspectorSignature
  });

  res.json({ checklist });
}));

// Generate inspection report
router.post('/:id/generate-report', authenticate, asyncHandler(async (req, res) => {
  const result = await inspectionService.generateInspectionReport(req.params.id);
  res.json(result);
}));

// Get project inspection statistics
router.get('/project/:projectId/stats', authenticate, asyncHandler(async (req, res) => {
  const stats = await inspectionService.getProjectInspectionStats(req.params.projectId);
  res.json({ stats });
}));

export default router;