import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import <PERSON><PERSON> from 'dexie';
import { toast } from 'react-hot-toast';

// Create IndexedDB database
class ElectricalDB extends Dexie {
  pendingRequests!: Dexie.Table<PendingRequest, number>;
  cachedData!: Dexie.Table<CachedData, string>;
  
  constructor() {
    super('ElectricalContractorDB');
    
    this.version(1).stores({
      pendingRequests: '++id, endpoint, method, timestamp',
      cachedData: 'key, timestamp',
    });
  }
}

interface PendingRequest {
  id?: number;
  endpoint: string;
  method: string;
  data?: unknown;
  timestamp: number;
}

interface CachedData {
  key: string;
  data: unknown;
  timestamp: number;
}

const db = new ElectricalDB();

export const useOfflineSync = (): {
  initializeOfflineSync: () => () => void;
  saveForOfflineSync: (endpoint: string, method: string, data?: unknown) => Promise<void>;
  cacheData: (key: string, data: unknown) => Promise<void>;
  getCachedData: (key: string) => Promise<unknown | null>;
  syncPendingRequests: () => Promise<void>;
  isOnline: boolean;
} => {
  const queryClient = useQueryClient();
  
  // Initialize offline sync
  const initializeOfflineSync = useCallback((): (() => void) => {
    // Listen for online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Check initial state
    if (!navigator.onLine) {
      toast.error('You are offline. Changes will be synced when connection is restored.');
    }
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Handle coming online
  const handleOnline = async (): Promise<void> => {
    toast.success('Connection restored. Syncing data...');
    await syncPendingRequests();
  };
  
  // Handle going offline
  const handleOffline = (): void => {
    toast.error('Connection lost. Working in offline mode.');
  };
  
  // Sync pending requests
  const syncPendingRequests = async (): Promise<void> => {
    try {
      const pendingRequests = await db.pendingRequests.toArray();
      
      for (const request of pendingRequests) {
        try {
          // Attempt to sync request
          const response = await fetch(request.endpoint, {
            method: request.method,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
            },
            body: request.data ? JSON.stringify(request.data) : undefined,
          });
          
          if (response.ok) {
            // Remove successful request
            await db.pendingRequests.delete(request.id!);
          }
        } catch (error) {
          console.error('Failed to sync request:', error);
        }
      }
      
      // Invalidate queries to refresh data
      await queryClient.invalidateQueries();
      
      if (pendingRequests.length > 0) {
        toast.success(`Synced ${pendingRequests.length} pending changes`);
      }
    } catch (error) {
      console.error('Sync failed:', error);
      toast.error('Failed to sync some changes');
    }
  };
  
  // Save request for offline sync
  const saveForOfflineSync = async (endpoint: string, method: string, data?: unknown): Promise<void> => {
    await db.pendingRequests.add({
      endpoint,
      method,
      data,
      timestamp: Date.now(),
    });
  };
  
  // Cache data for offline access
  const cacheData = async (key: string, data: unknown): Promise<void> => {
    await db.cachedData.put({
      key,
      data,
      timestamp: Date.now(),
    });
  };
  
  // Get cached data
  const getCachedData = async (key: string): Promise<unknown | null> => {
    const cached = await db.cachedData.get(key);
    if (cached) {
      // Check if cache is still valid (24 hours)
      const isValid = Date.now() - cached.timestamp < 24 * 60 * 60 * 1000;
      return isValid ? cached.data : null;
    }
    return null;
  };
  
  // Clear old cache
  const clearOldCache = useCallback(async (): Promise<void> => {
    const oldTimestamp = Date.now() - 7 * 24 * 60 * 60 * 1000; // 7 days
    await db.cachedData.where('timestamp').below(oldTimestamp).delete();
    await db.pendingRequests.where('timestamp').below(oldTimestamp).delete();
  }, []);
  
  useEffect(() => {
    // Clear old cache periodically
    const interval = setInterval(clearOldCache, 60 * 60 * 1000); // Every hour
    return () => clearInterval(interval);
  }, [clearOldCache]);
  
  return {
    initializeOfflineSync,
    saveForOfflineSync,
    cacheData,
    getCachedData,
    syncPendingRequests,
    isOnline: navigator.onLine,
  };
};