# Redis Sentinel Configuration

port 26379
bind 0.0.0.0
protected-mode yes

# Sentinel monitoring
sentinel monitor electrical-redis redis 6379 2
sentinel auth-pass electrical-redis ${REDIS_PASSWORD}
sentinel down-after-milliseconds electrical-redis 30000
sentinel parallel-syncs electrical-redis 1
sentinel failover-timeout electrical-redis 180000

# Notification scripts
sentinel notification-script electrical-redis /scripts/notify.sh
sentinel client-reconfig-script electrical-redis /scripts/reconfig.sh

# Logging
logfile ""
loglevel notice

# Working directory
dir /tmp

# Deny dangerous commands
sentinel deny-scripts-reconfig yes