import React, { forwardRef } from 'react';
import { generateId } from '../../utils/accessibility';

interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  hint?: string;
  required?: boolean;
}

export const FormFieldAccessible = forwardRef<HTMLInputElement, FormFieldProps>(
  ({ label, error, hint, required, className = '', ...props }, ref) => {
    const fieldId = props.id || generateId('field');
    const errorId = error ? `${fieldId}-error` : undefined;
    const hintId = hint ? `${fieldId}-hint` : undefined;
    
    const ariaDescribedBy = [errorId, hintId].filter(Boolean).join(' ') || undefined;

    return (
      <div className="mb-4">
        <label 
          htmlFor={fieldId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && (
            <span className="text-red-500 ml-1" aria-label="required">*</span>
          )}
        </label>
        
        {hint && (
          <p id={hintId} className="text-sm text-gray-500 mb-1">
            {hint}
          </p>
        )}
        
        <input
          ref={ref}
          id={fieldId}
          className={`
            block w-full rounded-md border-gray-300 shadow-sm 
            focus:border-blue-500 focus:ring-blue-500 
            dark:bg-gray-700 dark:border-gray-600 dark:text-white
            disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500
            ${error ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500' : ''}
            ${className}
          `}
          aria-invalid={!!error}
          aria-describedby={ariaDescribedBy}
          aria-required={required}
          {...props}
        />
        
        {error && (
          <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormFieldAccessible.displayName = 'FormFieldAccessible';

interface SelectFieldProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  error?: string;
  hint?: string;
  required?: boolean;
  options: Array<{ value: string; label: string }>;
}

export const SelectFieldAccessible = forwardRef<HTMLSelectElement, SelectFieldProps>(
  ({ label, error, hint, required, options, className = '', ...props }, ref) => {
    const fieldId = props.id || generateId('select');
    const errorId = error ? `${fieldId}-error` : undefined;
    const hintId = hint ? `${fieldId}-hint` : undefined;
    
    const ariaDescribedBy = [errorId, hintId].filter(Boolean).join(' ') || undefined;

    return (
      <div className="mb-4">
        <label 
          htmlFor={fieldId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && (
            <span className="text-red-500 ml-1" aria-label="required">*</span>
          )}
        </label>
        
        {hint && (
          <p id={hintId} className="text-sm text-gray-500 mb-1">
            {hint}
          </p>
        )}
        
        <select
          ref={ref}
          id={fieldId}
          className={`
            block w-full rounded-md border-gray-300 shadow-sm 
            focus:border-blue-500 focus:ring-blue-500 
            dark:bg-gray-700 dark:border-gray-600 dark:text-white
            disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500
            ${error ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500' : ''}
            ${className}
          `}
          aria-invalid={!!error}
          aria-describedby={ariaDescribedBy}
          aria-required={required}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {error && (
          <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);

SelectFieldAccessible.displayName = 'SelectFieldAccessible';

interface TextareaFieldProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  hint?: string;
  required?: boolean;
}

export const TextareaFieldAccessible = forwardRef<HTMLTextAreaElement, TextareaFieldProps>(
  ({ label, error, hint, required, className = '', ...props }, ref) => {
    const fieldId = props.id || generateId('textarea');
    const errorId = error ? `${fieldId}-error` : undefined;
    const hintId = hint ? `${fieldId}-hint` : undefined;
    
    const ariaDescribedBy = [errorId, hintId].filter(Boolean).join(' ') || undefined;

    return (
      <div className="mb-4">
        <label 
          htmlFor={fieldId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && (
            <span className="text-red-500 ml-1" aria-label="required">*</span>
          )}
        </label>
        
        {hint && (
          <p id={hintId} className="text-sm text-gray-500 mb-1">
            {hint}
          </p>
        )}
        
        <textarea
          ref={ref}
          id={fieldId}
          className={`
            block w-full rounded-md border-gray-300 shadow-sm 
            focus:border-blue-500 focus:ring-blue-500 
            dark:bg-gray-700 dark:border-gray-600 dark:text-white
            disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-500
            ${error ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500' : ''}
            ${className}
          `}
          aria-invalid={!!error}
          aria-describedby={ariaDescribedBy}
          aria-required={required}
          {...props}
        />
        
        {error && (
          <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);

TextareaFieldAccessible.displayName = 'TextareaFieldAccessible';

interface CheckboxFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  hint?: string;
}

export const CheckboxFieldAccessible = forwardRef<HTMLInputElement, CheckboxFieldProps>(
  ({ label, error, hint, className = '', ...props }, ref) => {
    const fieldId = props.id || generateId('checkbox');
    const errorId = error ? `${fieldId}-error` : undefined;
    const hintId = hint ? `${fieldId}-hint` : undefined;
    
    const ariaDescribedBy = [errorId, hintId].filter(Boolean).join(' ') || undefined;

    return (
      <div className="mb-4">
        <div className="flex items-start">
          <input
            ref={ref}
            id={fieldId}
            type="checkbox"
            className={`
              h-4 w-4 text-blue-600 rounded border-gray-300 
              focus:ring-blue-500 dark:border-gray-600 
              dark:bg-gray-700 dark:focus:ring-blue-600
              disabled:cursor-not-allowed disabled:opacity-50
              ${error ? 'border-red-300' : ''}
              ${className}
            `}
            aria-invalid={!!error}
            aria-describedby={ariaDescribedBy}
            {...props}
          />
          <div className="ml-3">
            <label 
              htmlFor={fieldId}
              className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
            >
              {label}
            </label>
            {hint && (
              <p id={hintId} className="text-sm text-gray-500 mt-1">
                {hint}
              </p>
            )}
          </div>
        </div>
        
        {error && (
          <p id={errorId} className="mt-1 text-sm text-red-600 ml-7" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);

CheckboxFieldAccessible.displayName = 'CheckboxFieldAccessible';

interface RadioGroupProps {
  label: string;
  name: string;
  options: Array<{ value: string; label: string; hint?: string }>;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  required?: boolean;
}

export const RadioGroupAccessible: React.FC<RadioGroupProps> = ({
  label,
  name,
  options,
  value,
  onChange,
  error,
  required
}) => {
  const groupId = generateId('radio-group');
  const errorId = error ? `${groupId}-error` : undefined;

  return (
    <fieldset 
      className="mb-4"
      aria-describedby={errorId}
      aria-required={required}
    >
      <legend className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">*</span>
        )}
      </legend>
      
      <div className="space-y-2" role="radiogroup">
        {options.map((option) => {
          const optionId = generateId(`radio-${option.value}`);
          const hintId = option.hint ? `${optionId}-hint` : undefined;
          
          return (
            <div key={option.value} className="flex items-start">
              <input
                id={optionId}
                name={name}
                type="radio"
                value={option.value}
                checked={value === option.value}
                onChange={(e) => onChange?.(e.target.value)}
                className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                aria-describedby={hintId}
              />
              <div className="ml-3">
                <label 
                  htmlFor={optionId}
                  className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
                >
                  {option.label}
                </label>
                {option.hint && (
                  <p id={hintId} className="text-sm text-gray-500 mt-1">
                    {option.hint}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {error && (
        <p id={errorId} className="mt-2 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </fieldset>
  );
};