import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { authenticate, authorize } from '../middleware/auth';
import { AgentService } from '../services/agent-service';

const router: Router = Router();
const agentService = AgentService.getInstance();

// AI-assisted estimate creation
const aiEstimateSchema = z.object({
  projectDescription: z.string().min(10),
  customerId: z.string(),
  buildingType: z.string().optional(),
  squareFootage: z.number().optional(),
});

router.post('/estimate', authenticate, async (req: Request, res: Response) => {
  try {
    const data = aiEstimateSchema.parse(req.body);
    
    const result = await agentService.createEstimateWithAI(
      data.projectDescription,
      data.customerId
    );
    
    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'AI estimate creation failed',
      },
    });
  }
});

// NEC compliance check
const complianceCheckSchema = z.object({
  parameters: z.record(z.unknown()),
  checkType: z.enum(['load', 'voltage-drop', 'conduit-fill', 'grounding', 'general']).optional(),
});

router.post('/compliance-check', authenticate, async (req: Request, res: Response) => {
  try {
    const data = complianceCheckSchema.parse(req.body);
    
    const result = await agentService.checkNECCompliance(data.parameters);
    
    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Compliance check failed',
      },
    });
  }
});

// Material pricing lookup
const materialPricingSchema = z.object({
  materials: z.array(z.object({
    name: z.string(),
    quantity: z.number(),
    specifications: z.string().optional(),
  })),
  location: z.string().optional(),
});

router.post('/material-pricing', authenticate, async (req: Request, res: Response) => {
  try {
    const data = materialPricingSchema.parse(req.body);
    
    const result = await agentService.getMaterialPricing(data.materials);
    
    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Material pricing lookup failed',
      },
    });
  }
});

// Error diagnosis
const errorDiagnosisSchema = z.object({
  error: z.string(),
  context: z.record(z.any()),
  stackTrace: z.string().optional(),
});

router.post('/diagnose-error', authenticate, async (req: Request, res: Response) => {
  try {
    const data = errorDiagnosisSchema.parse(req.body);
    
    const result = await agentService.diagnoseError(data.error, data.context);
    
    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Error diagnosis failed',
      },
    });
  }
});

// Optimized calculation
const optimizedCalculationSchema = z.object({
  calculationType: z.enum(['load', 'voltage-drop', 'conduit-fill', 'wire-size']),
  parameters: z.record(z.any()),
});

router.post('/optimize-calculation', authenticate, async (req: Request, res: Response) => {
  try {
    const data = optimizedCalculationSchema.parse(req.body);
    
    const result = await agentService.optimizeCalculation(
      data.calculationType,
      data.parameters
    );
    
    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Calculation optimization failed',
      },
    });
  }
});

// Get agent system status (admin only)
router.get('/status', authenticate, authorize('admin'), async (_req: Request, res: Response) => {
  try {
    const status = agentService.getStatus();
    
    res.json({
      success: true,
      data: status,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message || 'Failed to get agent status',
      },
    });
  }
});

export default router;