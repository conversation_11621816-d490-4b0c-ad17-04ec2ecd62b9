import { lazy, Suspense, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './stores/auth';
import { Layout } from './components/layout/Layout';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { useOfflineSync } from './hooks/useOfflineSync';

// Loading component for suspense fallback
const PageLoader = (): JSX.Element => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
      <p className="mt-4 text-gray-600 dark:text-gray-400">Loading...</p>
    </div>
  </div>
);

// Lazy load all pages for code splitting
const LoginPage = lazy(() => import('./pages/auth/LoginPage').then(m => ({ default: m.LoginPage })));
const RegisterPage = lazy(() => import('./pages/auth/RegisterPage').then(m => ({ default: m.RegisterPage })));
const DashboardPage = lazy(() => import('./pages/DashboardPage').then(m => ({ default: m.DashboardPage })));
const CustomersPage = lazy(() => import('./pages/customers/CustomersPage').then(m => ({ default: m.CustomersPage })));
const ProjectsPage = lazy(() => import('./pages/projects/ProjectsPage').then(m => ({ default: m.ProjectsPage })));
const EstimatesPage = lazy(() => import('./pages/estimates/EstimatesPage').then(m => ({ default: m.EstimatesPage })));
const CalculationsPage = lazy(() => import('./pages/calculations/CalculationsPage').then(m => ({ default: m.CalculationsPage })));
const MaterialsPage = lazy(() => import('./pages/materials/MaterialsPage').then(m => ({ default: m.MaterialsPage })));
const PanelsPage = lazy(() => import('./pages/PanelsPage').then(m => ({ default: m.PanelsPage })));
const PanelFormPage = lazy(() => import('./pages/PanelFormPage').then(m => ({ default: m.PanelFormPage })));
const PanelSchedulePage = lazy(() => import('./pages/PanelSchedulePage').then(m => ({ default: m.PanelSchedulePage })));
const ArcFlashPage = lazy(() => import('./pages/arc-flash/ArcFlashPage').then(m => ({ default: m.ArcFlashPage })));
const ShortCircuitPage = lazy(() => import('./pages/ShortCircuitPage').then(m => ({ default: m.ShortCircuitPage })));
const PermitDocumentsPage = lazy(() => import('./pages/permits/PermitDocumentsPage'));
const InspectionsPage = lazy(() => import('./pages/inspections/InspectionsPage').then(m => ({ default: m.InspectionsPage })));
const InspectionFormPage = lazy(() => import('./pages/inspections/InspectionFormPage').then(m => ({ default: m.InspectionFormPage })));
const InspectionChecklistPage = lazy(() => import('./pages/inspections/InspectionChecklistPage').then(m => ({ default: m.InspectionChecklistPage })));
const InspectionMobilePage = lazy(() => import('./pages/inspections/InspectionMobilePage').then(m => ({ default: m.InspectionMobilePage })));

// Lazy load AI Assistant only when needed
const AIAssistant = lazy(() => import('./components/ai/AIAssistant').then(m => ({ default: m.AIAssistant })));

// Preload critical routes
const preloadCriticalRoutes = (): void => {
  // Preload dashboard and customers as they're likely to be accessed
  import('./pages/DashboardPage');
  import('./pages/customers/CustomersPage');
  import('./pages/projects/ProjectsPage');
};

function AppOptimized(): JSX.Element {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const { initializeOfflineSync } = useOfflineSync();

  useEffect(() => {
    checkAuth();
    initializeOfflineSync();
    
    // Preload critical routes after authentication
    if (isAuthenticated) {
      preloadCriticalRoutes();
    }
  }, [checkAuth, initializeOfflineSync, isAuthenticated]);

  return (
    <>
      <Suspense fallback={<PageLoader />}>
        <Routes>
          {/* Public routes */}
          <Route 
            path="/login" 
            element={!isAuthenticated ? <LoginPage /> : <Navigate to="/" />} 
          />
          <Route 
            path="/register" 
            element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/" />} 
          />
          
          {/* Mobile inspection route (no auth required for QR code access) */}
          <Route path="/inspection/mobile/:qrCodeId" element={<InspectionMobilePage />} />
          
          {/* Protected routes */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              <Route path="/" element={<DashboardPage />} />
              <Route path="/customers" element={<CustomersPage />} />
              <Route path="/customers/:id" element={<CustomersPage />} />
              <Route path="/projects" element={<ProjectsPage />} />
              <Route path="/projects/:id" element={<ProjectsPage />} />
              <Route path="/projects/:projectId/panels" element={<PanelsPage />} />
              <Route path="/projects/:projectId/panels/new" element={<PanelFormPage />} />
              <Route path="/projects/:projectId/panels/:panelId" element={<PanelSchedulePage />} />
              <Route path="/projects/:projectId/panels/:panelId/edit" element={<PanelFormPage />} />
              <Route path="/projects/:projectId/panels/:panelId/schedule" element={<PanelSchedulePage />} />
              <Route path="/projects/:projectId/arc-flash" element={<ArcFlashPage />} />
              <Route path="/projects/:projectId/short-circuit" element={<ShortCircuitPage />} />
              <Route path="/projects/:projectId/permits" element={<PermitDocumentsPage />} />
              <Route path="/projects/:projectId/inspections" element={<InspectionsPage />} />
              <Route path="/projects/:projectId/inspections/new" element={<InspectionFormPage />} />
              <Route path="/projects/:projectId/inspections/:inspectionId" element={<InspectionChecklistPage />} />
              <Route path="/estimates" element={<EstimatesPage />} />
              <Route path="/estimates/:id" element={<EstimatesPage />} />
              <Route path="/calculations" element={<CalculationsPage />} />
              <Route path="/materials" element={<MaterialsPage />} />
            </Route>
          </Route>
          
          {/* Catch all */}
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </Suspense>
      
      {/* AI Assistant - shown when authenticated */}
      {isAuthenticated && (
        <Suspense fallback={null}>
          <AIAssistant />
        </Suspense>
      )}
    </>
  );
}

export default AppOptimized;