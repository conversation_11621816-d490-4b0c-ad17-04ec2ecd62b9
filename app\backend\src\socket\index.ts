import { Server, Socket } from 'socket.io';
import { logger } from '../index';
import { redis } from '../index';
import { config } from '../config';
import jwt from 'jsonwebtoken';
import { JwtPayload } from '../middleware/auth';

interface SocketWithAuth extends Socket {
  userId?: string;
  userRole?: string;
}

export function setupSocketHandlers(io: Server): void {
  // Authentication middleware
  io.use(async (socket: SocketWithAuth, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('No token provided'));
      }
      
      const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
      socket.userId = decoded.userId;
      socket.userRole = decoded.role;
      
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });
  
  io.on('connection', (socket: SocketWithAuth) => {
    logger.info(`User ${socket.userId} connected via WebSocket`);
    
    // Join user-specific room
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
    }
    
    // Join project rooms
    socket.on('join:project', async (projectId: string) => {
      // TODO: Verify user has access to project
      socket.join(`project:${projectId}`);
      logger.info(`User ${socket.userId} joined project room: ${projectId}`);
    });
    
    // Leave project room
    socket.on('leave:project', (projectId: string) => {
      socket.leave(`project:${projectId}`);
      logger.info(`User ${socket.userId} left project room: ${projectId}`);
    });
    
    // Real-time calculation collaboration
    socket.on('calculation:update', async (data: {
      projectId: string;
      calculationType: string;
      values: Record<string, unknown>;
    }) => {
      // Broadcast to other users in the project room
      socket.to(`project:${data.projectId}`).emit('calculation:updated', {
        userId: socket.userId,
        ...data
      });
      
      // Store in Redis for temporary collaboration state
      const key = `calc:${data.projectId}:${data.calculationType}`;
      await redis.setex(key, 300, JSON.stringify(data.values)); // 5 minute TTL
    });
    
    // Material price updates
    socket.on('price:subscribe', async (catalogNumbers: string[]) => {
      // Join price update rooms
      catalogNumbers.forEach(catalogNumber => {
        socket.join(`price:${catalogNumber}`);
      });
    });
    
    // Agent system messages
    socket.on('agent:message', async (message: {
      sender: string;
      recipient: string;
      content: string;
    }) => {
      // Publish to Redis for agent system
      await redis.publish(config.agents.redisChannel, JSON.stringify({
        ...message,
        userId: socket.userId,
        timestamp: new Date().toISOString()
      }));
    });
    
    // Estimate collaboration
    socket.on('estimate:lock', async (estimateId: string) => {
      const lockKey = `lock:estimate:${estimateId}`;
      const locked = await redis.set(lockKey, socket.userId || '', 'NX', 'EX', 300); // 5 minute lock
      
      if (locked) {
        socket.emit('estimate:locked', { estimateId, userId: socket.userId });
        socket.broadcast.emit('estimate:locked', { estimateId, userId: socket.userId });
      } else {
        const lockedBy = await redis.get(lockKey);
        socket.emit('estimate:lock:failed', { estimateId, lockedBy });
      }
    });
    
    socket.on('estimate:unlock', async (estimateId: string) => {
      const lockKey = `lock:estimate:${estimateId}`;
      const lockedBy = await redis.get(lockKey);
      
      if (lockedBy === socket.userId) {
        await redis.del(lockKey);
        io.emit('estimate:unlocked', { estimateId });
      }
    });
    
    // Handle disconnect
    socket.on('disconnect', async () => {
      logger.info(`User ${socket.userId} disconnected`);
      
      // Clean up any locks held by this user
      const locks = await redis.keys(`lock:*`);
      for (const lock of locks) {
        const lockedBy = await redis.get(lock);
        if (lockedBy === socket.userId) {
          await redis.del(lock);
          const estimateId = lock.split(':')[2];
          io.emit('estimate:unlocked', { estimateId });
        }
      }
    });
  });
}

// Utility functions for emitting events from other parts of the application
export function emitToUser(io: Server, userId: string, event: string, data: unknown): void {
  io.to(`user:${userId}`).emit(event, data);
}

export function emitToProject(io: Server, projectId: string, event: string, data: unknown): void {
  io.to(`project:${projectId}`).emit(event, data);
}

export function emitPriceUpdate(io: Server, catalogNumber: string, newPrice: number): void {
  io.to(`price:${catalogNumber}`).emit('price:updated', {
    catalogNumber,
    price: newPrice,
    timestamp: new Date().toISOString()
  });
}