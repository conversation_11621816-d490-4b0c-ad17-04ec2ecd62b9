import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

@Entity('calculations')
export class Calculation extends BaseEntity {
  @Column()
  name: string;

  @Column()
  type: string; // 'voltage-drop', 'wire-size', 'conduit-fill', 'load', etc.

  @Column({ type: 'text' })
  inputs: string; // JSON string of calculation inputs

  @Column({ type: 'text' })
  outputs: string; // JSON string of calculation results

  @Column({ type: 'text', nullable: true })
  formula: string;

  @Column({ nullable: true })
  codeReference: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string

  @Column({ nullable: true })
  createdBy: string;

  @Column()
  projectId: string;

  @ManyToOne(() => Project, project => project.calculations)
  project: Project;
}