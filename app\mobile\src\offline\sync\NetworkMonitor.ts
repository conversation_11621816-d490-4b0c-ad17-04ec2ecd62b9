import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { EventEmitter } from 'events';

export class NetworkMonitor extends EventEmitter {
  private isConnected: boolean = true;
  private connectionType: string = 'unknown';
  private isWifi: boolean = false;
  private unsubscribe: (() => void) | null = null;

  constructor() {
    super();
    this.initialize();
  }

  private async initialize() {
    // Get initial state
    const state = await NetInfo.fetch();
    this.updateConnectionState(state);

    // Subscribe to updates
    this.unsubscribe = NetInfo.addEventListener(state => {
      this.updateConnectionState(state);
    });
  }

  private updateConnectionState(state: NetInfoState) {
    const wasConnected = this.isConnected;
    
    this.isConnected = state.isConnected ?? false;
    this.connectionType = state.type;
    this.isWifi = state.type === 'wifi';

    // Emit events
    if (wasConnected !== this.isConnected) {
      this.emit('connectionChange', this.isConnected);
      
      if (this.isConnected) {
        this.emit('connected');
      } else {
        this.emit('disconnected');
      }
    }

    if (state.type !== this.connectionType) {
      this.emit('connectionTypeChange', state.type);
    }
  }

  getIsConnected(): boolean {
    return this.isConnected;
  }

  getConnectionType(): string {
    return this.connectionType;
  }

  getIsWifi(): boolean {
    return this.isWifi;
  }

  async waitForConnection(timeout: number = 30000): Promise<boolean> {
    if (this.isConnected) {
      return true;
    }

    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        this.removeListener('connected', onConnected);
        resolve(false);
      }, timeout);

      const onConnected = () => {
        clearTimeout(timer);
        resolve(true);
      };

      this.once('connected', onConnected);
    });
  }

  addListener(event: 'connectionChange', listener: (isConnected: boolean) => void): this;
  addListener(event: 'connected' | 'disconnected', listener: () => void): this;
  addListener(event: 'connectionTypeChange', listener: (type: string) => void): this;
  addListener(event: string, listener: (...args: any[]) => void): this {
    return super.addListener(event, listener);
  }

  destroy() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.removeAllListeners();
  }
}