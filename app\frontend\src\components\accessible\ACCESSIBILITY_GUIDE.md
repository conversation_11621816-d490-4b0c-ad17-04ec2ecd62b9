# Accessibility Guide for Electrical Frontend Components

This guide documents the accessibility features implemented in the React frontend components and provides usage examples.

## Overview

All accessible components follow WCAG 2.1 AA standards and include:

- ✅ ARIA labels and roles for all interactive elements
- ✅ Full keyboard navigation support
- ✅ Focus management and trapping for modals
- ✅ Screen reader announcements for dynamic content
- ✅ Minimum touch targets of 44px
- ✅ Color alternatives for phase identification
- ✅ Skip navigation links
- ✅ Form field label associations
- ✅ Error message announcements
- ✅ Live regions for real-time updates

## Component Usage

### PanelScheduleAccessible

The accessible panel schedule includes keyboard navigation for drag-and-drop functionality:

```tsx
import { PanelScheduleAccessible } from '@/components/accessible';

// Keyboard controls:
// - Tab/Shift+Tab: Navigate between slots
// - Arrow keys: Move focused circuit up/down
// - Enter/Space: Edit circuit or add new
// - Escape: Cancel operations
```

Key features:
- Phase identification using both color AND text labels
- Keyboard-only circuit rearrangement
- Screen reader announcements for all operations
- Proper focus management during edits

### CircuitFormAccessible

Fully accessible form with proper field associations:

```tsx
import { CircuitFormAccessible } from '@/components/accessible';

// All form fields include:
// - Associated labels with IDs
// - Error messages linked via aria-describedby
// - Required field indicators
// - Helpful hints for complex fields
// - Focus trap while form is open
```

### Modal Components

All modals include focus trapping and keyboard navigation:

```tsx
import { QRCodeModalAccessible } from '@/components/accessible';

// Modal features:
// - Focus trapped within modal
// - Escape key to close
// - Focus returned to trigger element on close
// - Screen reader announcements
```

### Form Fields

Use the accessible form field components for consistent accessibility:

```tsx
import { 
  FormFieldAccessible,
  SelectFieldAccessible,
  CheckboxFieldAccessible,
  RadioGroupAccessible 
} from '@/components/accessible';

// Text input with error handling
<FormFieldAccessible
  label="Circuit Description"
  required
  error={errors.description}
  hint="Describe the circuit purpose"
  value={description}
  onChange={(e) => setDescription(e.target.value)}
/>

// Select with proper associations
<SelectFieldAccessible
  label="Breaker Type"
  required
  options={[
    { value: 'STANDARD', label: 'Standard' },
    { value: 'GFCI', label: 'GFCI Protected' },
    { value: 'AFCI', label: 'AFCI Protected' }
  ]}
  value={breakerType}
  onChange={(e) => setBreakerType(e.target.value)}
/>

// Radio group with fieldset
<RadioGroupAccessible
  label="Load Type"
  name="loadType"
  required
  options={[
    { value: 'LIGHTING', label: 'Lighting', hint: '100% demand factor' },
    { value: 'MOTOR', label: 'Motor', hint: '125% demand factor' }
  ]}
  value={loadType}
  onChange={setLoadType}
/>
```

### Buttons

Accessible buttons with proper touch targets:

```tsx
import { ButtonAccessible, IconButtonAccessible } from '@/components/accessible';

// Standard button
<ButtonAccessible
  variant="primary"
  size="md"
  loading={isLoading}
  loadingText="Calculating..."
  onClick={handleCalculate}
>
  Calculate Load
</ButtonAccessible>

// Icon button with label
<IconButtonAccessible
  icon={<Settings />}
  label="Open settings"
  variant="ghost"
  onClick={openSettings}
/>
```

### Layout Components

The accessible layout includes proper landmarks and skip links:

```tsx
import { LayoutAccessible, HeaderAccessible } from '@/components/accessible';

// Layout includes:
// - Skip to main content link
// - Proper landmark regions (header, main, footer)
// - Live region for announcements
// - Responsive navigation with keyboard support
```

## Utility Functions

### Screen Reader Announcements

```tsx
import { announce } from '@/components/accessible';

// Polite announcement (waits for screen reader to finish)
announce('Circuit added successfully');

// Assertive announcement (interrupts screen reader)
announce('Error: Invalid circuit configuration', 'assertive');
```

### Focus Management

```tsx
import { FocusTrap } from '@/components/accessible';

// Create focus trap for modal
const focusTrap = new FocusTrap(modalElement);
focusTrap.activate();

// Clean up on close
focusTrap.deactivate();
```

### Keyboard Navigation Helper

```tsx
import { handleListKeyboardNavigation } from '@/components/accessible';

const handleKeyDown = (e: KeyboardEvent) => {
  handleListKeyboardNavigation(
    e,
    currentIndex,
    itemCount,
    (newIndex) => setSelectedIndex(newIndex),
    'vertical' // or 'horizontal'
  );
};
```

## Testing Accessibility

### Keyboard Navigation Testing

1. Tab through all interactive elements
2. Ensure focus indicators are visible
3. Test all keyboard shortcuts
4. Verify no keyboard traps (except modals)

### Screen Reader Testing

1. Use NVDA (Windows) or VoiceOver (Mac)
2. Navigate by headings and landmarks
3. Verify all form fields are announced
4. Check dynamic content announcements

### Color Contrast Testing

1. Use browser DevTools or accessibility checker
2. Verify 4.5:1 ratio for normal text
3. Verify 3:1 ratio for large text
4. Check phase indicators have text alternatives

### Touch Target Testing

1. Verify all buttons are at least 44x44px
2. Check spacing between interactive elements
3. Test on actual touch devices

## Best Practices

1. **Always provide text alternatives**: Don't rely solely on color or icons
2. **Use semantic HTML**: Proper heading hierarchy and landmark regions
3. **Test with real assistive technology**: Automated testing isn't enough
4. **Consider all input methods**: Keyboard, touch, mouse, and voice
5. **Provide clear focus indicators**: Never remove outline without replacement
6. **Announce dynamic changes**: Use live regions for updates
7. **Group related controls**: Use fieldsets and proper labeling
8. **Provide skip links**: Allow users to bypass repetitive content

## Migration Guide

To migrate existing components to accessible versions:

1. Replace imports:
```tsx
// Before
import { PanelSchedule } from '@/components/panels/PanelSchedule';

// After
import { PanelScheduleAccessible } from '@/components/accessible';
```

2. Update form fields to use accessible components
3. Add proper ARIA labels to custom components
4. Test thoroughly with keyboard and screen reader

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [WebAIM Screen Reader Testing](https://webaim.org/articles/screenreader_testing/)
- [Inclusive Components](https://inclusive-components.design/)