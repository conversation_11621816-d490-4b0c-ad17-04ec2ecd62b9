export declare const GENERAL_LIGHTING_LOADS: {
    readonly DWELLING: 3;
    readonly OFFICE: 3.5;
    readonly STORE: 3;
    readonly WAREHOUSE: 0.25;
    readonly GARAGE: 0.5;
    readonly HOSPITAL: 2;
    readonly HOTEL: 2;
    readonly SCHOOL: 3;
    readonly RESTAURANT: 2;
};
export declare const STANDARD_SERVICE_SIZES: readonly [100, 125, 150, 200, 225, 320, 400, 600, 800, 1000, 1200, 1600, 2000, 2500, 3000];
export declare const VOLTAGE_SYSTEMS: {
    readonly '120/240V_1PH': {
        readonly voltage: 240;
        readonly phases: 1;
        readonly lineToNeutral: 120;
    };
    readonly '208V_3PH': {
        readonly voltage: 208;
        readonly phases: 3;
        readonly lineToNeutral: 120;
    };
    readonly '240V_3PH': {
        readonly voltage: 240;
        readonly phases: 3;
        readonly lineToNeutral: null;
    };
    readonly '480V_3PH': {
        readonly voltage: 480;
        readonly phases: 3;
        readonly lineToNeutral: 277;
    };
    readonly '277/480V_3PH': {
        readonly voltage: 480;
        readonly phases: 3;
        readonly lineToNeutral: 277;
    };
};
export declare const COPPER_AMPACITY_75C: {
    readonly '14': 20;
    readonly '12': 25;
    readonly '10': 35;
    readonly '8': 50;
    readonly '6': 65;
    readonly '4': 85;
    readonly '3': 100;
    readonly '2': 115;
    readonly '1': 130;
    readonly '1/0': 150;
    readonly '2/0': 175;
    readonly '3/0': 200;
    readonly '4/0': 230;
    readonly '250': 255;
    readonly '300': 285;
    readonly '350': 310;
    readonly '400': 335;
    readonly '500': 380;
    readonly '600': 420;
    readonly '700': 460;
    readonly '750': 475;
    readonly '800': 490;
    readonly '900': 520;
    readonly '1000': 545;
};
export declare const ALUMINUM_AMPACITY_75C: {
    readonly '12': 20;
    readonly '10': 30;
    readonly '8': 40;
    readonly '6': 50;
    readonly '4': 65;
    readonly '3': 75;
    readonly '2': 90;
    readonly '1': 100;
    readonly '1/0': 120;
    readonly '2/0': 135;
    readonly '3/0': 155;
    readonly '4/0': 180;
    readonly '250': 205;
    readonly '300': 230;
    readonly '350': 250;
    readonly '400': 270;
    readonly '500': 310;
    readonly '600': 340;
    readonly '700': 375;
    readonly '750': 385;
    readonly '800': 395;
    readonly '900': 425;
    readonly '1000': 445;
};
export declare const CONDUIT_FILL_PERCENT: {
    readonly 1: 0.53;
    readonly 2: 0.31;
    readonly OVER_2: 0.4;
};
export declare const EMT_CONDUIT_ID: {
    readonly '1/2': 0.622;
    readonly '3/4': 0.824;
    readonly '1': 1.049;
    readonly '1-1/4': 1.38;
    readonly '1-1/2': 1.61;
    readonly '2': 2.067;
    readonly '2-1/2': 2.731;
    readonly '3': 3.356;
    readonly '3-1/2': 3.834;
    readonly '4': 4.334;
};
export declare const THHN_WIRE_AREA: {
    readonly '14': 0.0097;
    readonly '12': 0.0133;
    readonly '10': 0.0211;
    readonly '8': 0.0366;
    readonly '6': 0.0507;
    readonly '4': 0.0824;
    readonly '3': 0.0973;
    readonly '2': 0.1158;
    readonly '1': 0.1562;
    readonly '1/0': 0.1855;
    readonly '2/0': 0.2223;
    readonly '3/0': 0.2679;
    readonly '4/0': 0.3237;
    readonly '250': 0.397;
    readonly '300': 0.4608;
    readonly '350': 0.5242;
    readonly '400': 0.5863;
    readonly '500': 0.7073;
    readonly '600': 0.8676;
    readonly '700': 0.9887;
    readonly '750': 1.0496;
    readonly '800': 1.1085;
    readonly '900': 1.2311;
    readonly '1000': 1.3478;
};
export declare const DWELLING_DEMAND_FACTORS: readonly [{
    readonly min: 0;
    readonly max: 3000;
    readonly factor: 1;
}, {
    readonly min: 3001;
    readonly max: 120000;
    readonly factor: 0.35;
}, {
    readonly min: 120001;
    readonly max: number;
    readonly factor: 0.25;
}];
export declare const VOLTAGE_DROP_LIMITS: {
    readonly BRANCH_CIRCUIT: 0.03;
    readonly FEEDER: 0.03;
    readonly TOTAL: 0.05;
};
export declare const TYPICAL_POWER_FACTORS: {
    readonly INCANDESCENT: 1;
    readonly FLUORESCENT: 0.85;
    readonly LED: 0.9;
    readonly MOTOR_LOADED: 0.85;
    readonly MOTOR_UNLOADED: 0.5;
    readonly GENERAL: 0.9;
};
export declare const TEMP_CORRECTION_FACTORS: {
    readonly '60': 1.29;
    readonly '65': 1.22;
    readonly '70': 1.15;
    readonly '75': 1.08;
    readonly '80': 1;
    readonly '85': 0.91;
    readonly '90': 0.82;
    readonly '95': 0.71;
    readonly '100': 0.58;
};
export declare const LABOR_PRODUCTIVITY: {
    readonly RECEPTACLE: 4;
    readonly SWITCH: 4;
    readonly LIGHT_FIXTURE: 2;
    readonly PANEL_CIRCUIT: 2;
    readonly CONDUIT_EMT: 40;
    readonly WIRE_PULL: 100;
    readonly JUNCTION_BOX: 3;
};
export declare const MATERIAL_WASTE_FACTORS: {
    readonly WIRE: 0.1;
    readonly CONDUIT: 0.05;
    readonly FITTINGS: 0.03;
    readonly DEVICES: 0.02;
    readonly FIXTURES: 0.01;
};
export declare const SAFETY_MARGINS: {
    readonly AMPACITY: 0.8;
    readonly BREAKER_SIZE: 1.25;
    readonly TRANSFORMER: 1.25;
    readonly GENERATOR: 1.1;
};
export declare function calculateVoltageDrop(current: number, distance: number, voltage: number, wireSize: string, isCoppper?: boolean, is3Phase?: boolean, powerFactor?: number): {
    voltageDrop: number;
    percentDrop: number;
};
//# sourceMappingURL=electrical.d.ts.map