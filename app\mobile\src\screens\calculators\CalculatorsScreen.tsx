import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';

interface Props {
  navigation: NavigationProp<any>;
}

export const CalculatorsScreen: React.FC<Props> = ({ navigation }) => {
  const calculators = [
    {
      id: 'load',
      title: 'Load Calculator',
      subtitle: 'NEC 220 compliant calculations',
      icon: '⚡',
      color: '#2196F3',
      screen: 'LoadCalculator',
    },
    {
      id: 'voltage-drop',
      title: 'Voltage Drop',
      subtitle: 'Real-time voltage drop analysis',
      icon: '📉',
      color: '#FF9800',
      screen: 'VoltageDropCalculator',
    },
    {
      id: 'wire-size',
      title: 'Wire Size',
      subtitle: 'With temperature derating',
      icon: '🔌',
      color: '#4CAF50',
      screen: 'WireSizeCalculator',
    },
    {
      id: 'conduit-fill',
      title: 'Conduit Fill',
      subtitle: 'Visual fill gauge',
      icon: '📏',
      color: '#9C27B0',
      screen: 'ConduitFillCalculator',
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Electrical Calculators</Text>
        <Text style={styles.subtitle}>NEC compliant • Works offline</Text>
      </View>

      <View style={styles.grid}>
        {calculators.map((calc) => (
          <TouchableOpacity
            key={calc.id}
            style={[styles.card, { borderLeftColor: calc.color }]}
            onPress={() => navigation.navigate(calc.screen)}
            activeOpacity={0.8}
          >
            <View style={[styles.iconContainer, { backgroundColor: calc.color + '20' }]}>
              <Text style={styles.icon}>{calc.icon}</Text>
            </View>
            <View style={styles.cardContent}>
              <Text style={styles.cardTitle}>{calc.title}</Text>
              <Text style={styles.cardSubtitle}>{calc.subtitle}</Text>
            </View>
            <Text style={styles.arrow}>→</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.recentSection}>
        <Text style={styles.sectionTitle}>Recent Calculations</Text>
        <Text style={styles.emptyText}>Your recent calculations will appear here</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  grid: {
    padding: 15,
  },
  card: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    marginBottom: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  icon: {
    fontSize: 28,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 24,
    color: '#999',
  },
  recentSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
});