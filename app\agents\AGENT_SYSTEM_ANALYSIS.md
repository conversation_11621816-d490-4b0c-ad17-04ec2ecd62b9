# AI Agent System Analysis

## Overview
The AI agents system is a multi-agent architecture designed to assist with various aspects of the electrical contracting application. It consists of specialized agents that communicate via a message bus and share memory through a persistent store.

## Agent Types
1. **Project Manager Agent** - Orchestrates tasks and coordinates other agents
2. **Backend Database Agent** - Handles data operations and calculations  
3. **Research Agent** - Researches NEC codes and material prices
4. **Debugging Agent** - Diagnoses errors and ensures compliance
5. **Coding Agent** - Generates and analyzes code
6. **Frontend Agent** - Manages UI state, components, and frontend optimizations
7. **UI Designer Agent** - Designs UI components following electrical design system
8. **Prompt Engineering Agent** - Optimizes prompts for electrical domain AI interactions

## Dependencies
The agent system has several external dependencies that are NOT currently installed:

1. **ChromaDB** (`chromadb` package) - Vector database for semantic search
2. **Neo4j** (`neo4j-driver` package) - Graph database for relationships
3. **Prisma Client** - Already available but requires database connection
4. **Redis** (`ioredis` package) - For message queue (optional)

## Current Status
- The agent routes are **DISABLED** in the backend (`/api/agents` is commented out)
- The agent service exists but uses placeholder implementations
- The compiled JavaScript version exists in `/dist` but fails to initialize due to missing dependencies

## Running the Agents

### Option 1: Use Compiled Version (Recommended when dependencies are installed)
```bash
cd app/agents
npm run build  # If not already built
npm start      # Runs node dist/index.js
```

### Option 2: Fix TypeScript Execution
```bash
cd app/agents
# Install tsx globally if needed
npm install -g tsx
npm run dev    # Uses tsx watch src/index.ts
```

### Option 3: Use Node with --loader (Experimental)
```bash
cd app/agents
node --loader tsx src/index.js
```

## Integration with Main Application

The agents are integrated into the backend through:
- `/app/backend/src/services/agent-service.ts` - Service layer
- `/app/backend/src/routes/agents.ts` - API endpoints

Available endpoints (when enabled):
- `POST /api/agents/estimate` - AI-assisted estimate creation
- `POST /api/agents/compliance-check` - NEC compliance verification
- `POST /api/agents/material-pricing` - Material price lookup
- `POST /api/agents/diagnose-error` - Error diagnosis
- `POST /api/agents/optimize-calculation` - Calculation optimization
- `GET /api/agents/status` - System status (admin only)

## Essential for Basic Functionality?

**NO** - The agent system is NOT essential for basic application functionality.

The main application provides:
- Full electrical calculations (load, voltage drop, conduit fill, etc.)
- Project and estimate management
- Material database
- User authentication and authorization
- Complete UI functionality

The agents add AI-powered enhancements:
- Automated estimate generation from descriptions
- NEC code compliance suggestions
- Real-time material pricing lookup
- Intelligent error diagnosis
- Optimized calculation recommendations

## To Enable Agents Later

1. Install missing dependencies:
   ```bash
   cd app/agents
   npm install chromadb neo4j-driver
   ```

2. Set up external services:
   - ChromaDB instance for vector storage
   - Neo4j instance for graph database
   - Redis instance (optional) for message queue

3. Enable agent routes in backend:
   ```typescript
   // In app/backend/src/index.ts, uncomment:
   app.use('/api/agents', agentsRouter);
   ```

4. Update agent service implementation:
   ```typescript
   // In app/backend/src/services/agent-service.ts, uncomment:
   import { getAgentSystem, AgentSystem } from '@electrical/agents';
   ```

5. Start the agent system:
   ```bash
   cd app/agents
   npm start
   ```

## Recommendation

For now, continue using the application without the AI agents. They can be added later when:
- External dependencies are available
- There's a specific need for AI-powered features
- Infrastructure for vector/graph databases is set up

The core electrical contracting functionality is fully operational without the agent system.