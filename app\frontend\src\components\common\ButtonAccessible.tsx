import React, { forwardRef } from 'react';
import { Loader2 } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  loadingText?: string;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export const ButtonAccessible = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      loading = false,
      loadingText = 'Loading...',
      fullWidth = false,
      icon,
      iconPosition = 'left',
      className = '',
      disabled,
      type = 'button',
      ...props
    },
    ref
  ) => {
    const baseStyles = `
      inline-flex items-center justify-center font-medium rounded-md
      transition-colors duration-200 
      focus:outline-none focus:ring-2 focus:ring-offset-2 
      disabled:cursor-not-allowed disabled:opacity-50
      min-h-[44px] min-w-[44px]
    `;

    const variantStyles = {
      primary: `
        bg-blue-600 text-white hover:bg-blue-700 
        focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600
      `,
      secondary: `
        bg-white text-gray-700 border border-gray-300 
        hover:bg-gray-50 focus:ring-blue-500 
        dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 
        dark:hover:bg-gray-700
      `,
      danger: `
        bg-red-600 text-white hover:bg-red-700 
        focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600
      `,
      ghost: `
        bg-transparent text-gray-700 hover:bg-gray-100 
        focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800
      `
    };

    const sizeStyles = {
      sm: 'px-3 py-2 text-sm min-h-[36px]',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    const widthStyle = fullWidth ? 'w-full' : '';

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        type={type}
        disabled={isDisabled}
        className={`
          ${baseStyles}
          ${variantStyles[variant]}
          ${sizeStyles[size]}
          ${widthStyle}
          ${className}
        `}
        aria-busy={loading}
        aria-disabled={isDisabled}
        {...props}
      >
        {loading && (
          <Loader2 
            className="animate-spin h-4 w-4 mr-2" 
            aria-hidden="true"
          />
        )}
        
        {icon && !loading && iconPosition === 'left' && (
          <span className="mr-2" aria-hidden="true">
            {icon}
          </span>
        )}
        
        <span>
          {loading ? loadingText : children}
        </span>
        
        {icon && !loading && iconPosition === 'right' && (
          <span className="ml-2" aria-hidden="true">
            {icon}
          </span>
        )}
      </button>
    );
  }
);

ButtonAccessible.displayName = 'ButtonAccessible';

// Icon button with proper touch target
interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: React.ReactNode;
  label: string;
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
}

export const IconButtonAccessible = forwardRef<HTMLButtonElement, IconButtonProps>(
  (
    {
      icon,
      label,
      variant = 'ghost',
      size = 'md',
      loading = false,
      className = '',
      disabled,
      ...props
    },
    ref
  ) => {
    const baseStyles = `
      inline-flex items-center justify-center rounded-md
      transition-colors duration-200 
      focus:outline-none focus:ring-2 focus:ring-offset-2 
      disabled:cursor-not-allowed disabled:opacity-50
    `;

    const variantStyles = {
      primary: `
        bg-blue-600 text-white hover:bg-blue-700 
        focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600
      `,
      secondary: `
        bg-white text-gray-700 border border-gray-300 
        hover:bg-gray-50 focus:ring-blue-500 
        dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 
        dark:hover:bg-gray-700
      `,
      danger: `
        bg-red-600 text-white hover:bg-red-700 
        focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600
      `,
      ghost: `
        bg-transparent text-gray-700 hover:bg-gray-100 
        focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800
      `
    };

    const sizeStyles = {
      sm: 'h-9 w-9 min-h-[36px] min-w-[36px]',
      md: 'h-11 w-11 min-h-[44px] min-w-[44px]',
      lg: 'h-12 w-12 min-h-[48px] min-w-[48px]'
    };

    const iconSizes = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        type="button"
        disabled={isDisabled}
        className={`
          ${baseStyles}
          ${variantStyles[variant]}
          ${sizeStyles[size]}
          ${className}
        `}
        aria-label={label}
        aria-busy={loading}
        aria-disabled={isDisabled}
        {...props}
      >
        {loading ? (
          <Loader2 
            className={`animate-spin ${iconSizes[size]}`} 
            aria-hidden="true"
          />
        ) : (
          <span className={iconSizes[size]} aria-hidden="true">
            {icon}
          </span>
        )}
      </button>
    );
  }
);

IconButtonAccessible.displayName = 'IconButtonAccessible';

// Button group for related actions
interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  label?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({ 
  children, 
  className = '',
  label 
}) => {
  return (
    <div 
      className={`inline-flex rounded-md shadow-sm ${className}`}
      role="group"
      aria-label={label}
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          const isFirst = index === 0;
          const isLast = index === React.Children.count(children) - 1;
          
          return React.cloneElement(child, {
            className: `
              ${child.props.className || ''}
              ${!isFirst ? '-ml-px' : ''}
              ${isFirst ? 'rounded-r-none' : ''}
              ${isLast ? 'rounded-l-none' : ''}
              ${!isFirst && !isLast ? 'rounded-none' : ''}
              focus:z-10
            `
          });
        }
        return child;
      })}
    </div>
  );
};