import { PrismaClient } from '@prisma/client';
import winston from 'winston';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

export async function initializeDatabase(): Promise<void> {
  try {
    // Check if database exists
    const dbPath = process.env.DATABASE_URL?.replace('file:', '') || './database/electrical.db';
    const dbDir = path.dirname(dbPath);
    
    // Create database directory if it doesn't exist
    await fs.mkdir(dbDir, { recursive: true });
    
    // Test database connection
    await prisma.$connect();
    logger.info('Database connected successfully');
    
    // Enable WAL mode for better performance
    if (process.env.DATABASE_WAL_MODE === 'true') {
      await prisma.$queryRawUnsafe('PRAGMA journal_mode = WAL');
      logger.info('SQLite WAL mode enabled');
    }

    // Set pragmas for better performance
    await prisma.$queryRawUnsafe('PRAGMA synchronous = NORMAL');
    await prisma.$queryRawUnsafe('PRAGMA cache_size = -64000'); // 64MB cache
    await prisma.$queryRawUnsafe('PRAGMA temp_store = MEMORY');
    await prisma.$queryRawUnsafe('PRAGMA mmap_size = 30000000000'); // 30GB mmap
    
    logger.info('Database initialized with performance optimizations');
  } catch (error) {
    logger.error('Failed to initialize database:', error);
    throw error;
  }
}