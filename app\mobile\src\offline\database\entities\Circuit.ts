import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from './BaseEntity';
import { Panel } from './Panel';

@Entity('circuits')
export class Circuit extends BaseEntity {
  @Column({ type: 'integer' })
  number: number;

  @Column()
  name: string;

  @Column({ type: 'integer' })
  amperage: number;

  @Column({ type: 'text', default: 'single' })
  type: 'single' | 'double' | 'triple';

  @Column({ type: 'text', default: 'active' })
  status: 'active' | 'inactive' | 'maintenance';

  @Column({ nullable: true })
  wireSize: string;

  @Column({ nullable: true })
  wireType: string;

  @Column({ nullable: true })
  destination: string;

  @Column({ type: 'float', nullable: true })
  loadPercentage: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  metadata: string; // JSON string

  @Column()
  panelId: string;

  @ManyToOne(() => Panel, panel => panel.circuits)
  panel: Panel;
}