# Quick Start Guide - Electrical Contracting Application

## 🚀 Fastest Way to Start

Run this command and follow the prompts:
```bash
START_HERE.bat
```

This will:
1. Ask if you want to automatically open two terminal windows
2. Start the backend server (port 3001)
3. Start the frontend server (port 3000)

### Alternative: Manual Start
If you prefer to start services manually:

**Terminal 1:**
```bash
start-backend-only.bat
```

**Terminal 2:**
```bash
start-frontend-only.bat
```

## 📋 What's Fixed

✅ **PostCSS Configuration** - Changed from ES modules to CommonJS
✅ **Package Scripts** - Added `dev:main` to run only essential services
✅ **Startup Script** - Created `start-main-app.bat` for easy startup

## 🛠️ Manual Start (if needed)

If the batch file doesn't work, run these commands in separate terminals:

### Terminal 1 - Backend:
```bash
cd app/backend
npx prisma generate
npx prisma migrate deploy
npm run dev
```

### Terminal 2 - Frontend:
```bash
cd app/frontend
npm run dev
```

## 🌐 Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001

## ⚠️ Important Notes

1. **Node.js Version**: You have v18.20.5 but v20+ is recommended. The app should still work with warnings.

2. **AI Agents**: The agents have initialization issues but are NOT required for:
   - Electrical calculations
   - Project management
   - Customer management
   - Panel schedules
   - Material tracking
   - All core features

3. **First Time Setup**:
   - Visit http://localhost:3000
   - Click "Register" to create an account
   - First user automatically becomes admin

## 🔧 Troubleshooting

### If you see "Cannot find module" errors:
```bash
cd app
pnpm install
```

### If database errors occur:
```bash
cd app/backend
npx prisma migrate reset
```

### If frontend won't start:
1. Make sure `postcss.config.js` uses `module.exports` not `export default`
2. Check that port 3000 is not in use

### If backend won't start:
1. Check that port 3001 is not in use
2. Ensure `.env` file exists with proper configuration

## 💡 Features Available Without AI Agents

✅ **Electrical Calculations**
- Load calculations
- Voltage drop
- Wire sizing
- Conduit fill
- Arc flash
- Short circuit

✅ **Project Management**
- Create and manage projects
- Track progress
- Generate estimates
- Handle invoicing

✅ **Panel Management**
- Create panel schedules
- Track circuits
- Load balancing
- Visual load display

✅ **Material Management**
- Material takeoffs
- Price tracking
- Inventory management

✅ **Customer Management**
- Customer database
- Contact information
- Project history

✅ **Safety & Compliance**
- NEC code compliance checks
- Safety checklists
- Inspection forms

## 🎯 Next Steps

1. Start the application with `start-main-app.bat`
2. Create your admin account
3. Explore the features
4. Add your first project

The application is fully functional without the AI agents!