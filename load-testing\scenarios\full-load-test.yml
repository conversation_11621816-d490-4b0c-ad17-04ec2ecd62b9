config:
  target: "{{ $processEnvironment.API_BASE_URL }}"
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 2
      name: "Warm-up"
    
    # Ramp-up phase
    - duration: 300
      arrivalRate: 2
      rampTo: 20
      name: "Ramp-up to 100 users"
    
    # Sustained load phase
    - duration: 1800
      arrivalRate: 20
      name: "Sustained load (100 concurrent users)"
    
    # Cool-down phase
    - duration: 120
      arrivalRate: 20
      rampTo: 2
      name: "Cool-down"
  
  processor: "../scripts/test-data-generator.js"
  
  payload:
    - path: "../data/test-users.csv"
      fields:
        - "email"
        - "password"
      order: "random"
  
  variables:
    baseUrl: "{{ $processEnvironment.API_BASE_URL }}"
    wsUrl: "{{ $processEnvironment.WS_URL }}"
  
  http:
    timeout: 30
    pool: 50
    maxSockets: 100
  
  ws:
    rejectUnauthorized: false
  
  plugins:
    metrics-by-endpoint:
      percentilesOutputFileType: "csv"
    ensure:
      thresholds:
        - http.response_time.p95: 1000
        - http.response_time.p99: 2000
        - http.codes.2xx: 95
        - http.request_rate: 100
  
  # Performance tracking
  statsInterval: 10

before:
  flow:
    - log: "Starting full load test with 100 concurrent users target"
    - function: "setupTestEnvironment"

after:
  flow:
    - log: "Load test completed"
    - function: "generateReport"

scenarios:
  # User Registration (5% of traffic)
  - name: "New User Registration"
    weight: 5
    flow:
      - function: "generateNewUser"
      - post:
          url: "/auth/register"
          json:
            email: "{{ newEmail }}"
            password: "{{ newPassword }}"
            firstName: "{{ firstName }}"
            lastName: "{{ lastName }}"
            company: "{{ company }}"
            licenseNumber: "{{ licenseNumber }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 201
      - think: 2
  
  # User Login (15% of traffic)
  - name: "User Login Flow"
    weight: 15
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"
              as: "userId"
          expect:
            - statusCode: 200
            - hasProperty: "token"
      - think: 2
      - get:
          url: "/auth/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
  
  # Electrical Calculations (25% of traffic)
  - name: "Heavy Calculation Workflow"
    weight: 25
    flow:
      - function: "authenticateUser"
      - loop:
          - post:
              url: "/calculations/voltage-drop"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateVoltageDropData"
              json: "{{ voltageDropData }}"
              expect:
                - statusCode: 200
                - hasProperty: "voltageDrop"
                - hasProperty: "percentDrop"
          - think: 1
          - post:
              url: "/calculations/short-circuit"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateShortCircuitData"
              json: "{{ shortCircuitData }}"
              expect:
                - statusCode: 200
                - hasProperty: "faultCurrent"
          - think: 1
          - post:
              url: "/calculations/arc-flash"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateArcFlashData"
              json: "{{ arcFlashData }}"
              expect:
                - statusCode: 200
                - hasProperty: "incidentEnergy"
                - hasProperty: "arcFlashBoundary"
          count: 3
  
  # Panel Schedule Management (20% of traffic)
  - name: "Panel Schedule Operations"
    weight: 20
    flow:
      - function: "authenticateUser"
      - get:
          url: "/panels?limit=10&includeCircuits=true"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$[0].id"
              as: "panelId"
            - json: "$[0].projectId"
              as: "projectId"
          expect:
            - statusCode: 200
      - think: 1
      - get:
          url: "/panels/{{ panelId }}/schedule"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
            - hasProperty: "circuits"
      - think: 2
      - post:
          url: "/panels/{{ panelId }}/circuits"
          headers:
            Authorization: "Bearer {{ authToken }}"
          beforeRequest: "generateCircuitData"
          json: "{{ circuitData }}"
          capture:
            - json: "$.id"
              as: "circuitId"
      - think: 1
      - put:
          url: "/panels/{{ panelId }}/circuits/{{ circuitId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            amperage: "{{ $randomNumber(20, 60) }}"
            description: "Updated Circuit - Load Test"
      - think: 1
      - get:
          url: "/panels/{{ panelId }}/load-summary"
          headers:
            Authorization: "Bearer {{ authToken }}"
  
  # Material Database Search (15% of traffic)
  - name: "Material Search and Pricing"
    weight: 15
    flow:
      - function: "authenticateUser"
      - loop:
          - get:
              url: "/materials/search"
              headers:
                Authorization: "Bearer {{ authToken }}"
              qs:
                query: "{{ $randomString(3) }}"
                category: "{{ $randomItem(['electrical', 'conduit', 'wire', 'devices']) }}"
                limit: 20
              expect:
                - statusCode: 200
          - think: 1
          - post:
              url: "/materials/pricing/calculate"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateMaterialList"
              json:
                items: "{{ materialList }}"
                location: "{{ $randomItem(['CA', 'TX', 'NY', 'FL']) }}"
                taxRate: "{{ $randomNumber(5, 10) }}"
              expect:
                - statusCode: 200
                - hasProperty: "subtotal"
                - hasProperty: "tax"
                - hasProperty: "total"
          count: 2
  
  # Real-time Collaboration (10% of traffic)
  - name: "WebSocket Collaboration"
    weight: 10
    engine: "ws"
    flow:
      - function: "authenticateUser"
      - connect:
          url: "{{ wsUrl }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 2
      - send:
          data:
            type: "join_room"
            room: "project_{{ projectId }}"
      - think: 3
      - loop:
          - send:
              data:
                type: "estimate_update"
                estimateId: "{{ $randomString(10) }}"
                changes:
                  lineItem: "{{ $randomNumber(1, 50) }}"
                  quantity: "{{ $randomNumber(1, 100) }}"
                  price: "{{ $randomNumber(10, 1000) }}"
          - think: 5
          count: 6
      - think: 2
      - send:
          data:
            type: "leave_room"
            room: "project_{{ projectId }}"
  
  # File Export Operations (5% of traffic)
  - name: "Report Generation"
    weight: 5
    flow:
      - function: "authenticateUser"
      - post:
          url: "/exports/panel-schedule"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            panelId: "{{ panelId }}"
            format: "pdf"
            includeLoadCalculations: true
            includeCircuitDetails: true
          capture:
            - json: "$.jobId"
              as: "exportJobId"
          expect:
            - statusCode: 202
      - think: 2
      - loop:
          - get:
              url: "/exports/status/{{ exportJobId }}"
              headers:
                Authorization: "Bearer {{ authToken }}"
              capture:
                - json: "$.status"
                  as: "jobStatus"
              expect:
                - statusCode: 200
          - think: 2
          count: 10
          whileTrue: "jobStatus !== 'completed'"
      - get:
          url: "/exports/download/{{ exportJobId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
  
  # Complex Estimate Operations (5% of traffic)
  - name: "Estimate Workflow"
    weight: 5
    flow:
      - function: "authenticateUser"
      - post:
          url: "/estimates"
          headers:
            Authorization: "Bearer {{ authToken }}"
          beforeRequest: "generateEstimateData"
          json: "{{ estimateData }}"
          capture:
            - json: "$.id"
              as: "estimateId"
      - think: 2
      - loop:
          - post:
              url: "/estimates/{{ estimateId }}/items"
              headers:
                Authorization: "Bearer {{ authToken }}"
              beforeRequest: "generateLineItem"
              json: "{{ lineItem }}"
          - think: 1
          count: 10
      - get:
          url: "/estimates/{{ estimateId }}/calculate"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
            - hasProperty: "subtotal"
            - hasProperty: "tax"
            - hasProperty: "total"