import { DataSource } from 'typeorm';
import SQLite from 'react-native-sqlite-storage';
import {
  Project,
  Panel,
  Circuit,
  Material,
  Calculation,
  Photo,
  SyncQueue,
} from './entities';

// Enable promise-based SQLite
SQLite.enablePromise(true);

let dataSource: DataSource | null = null;

export const getDatabaseConnection = async (): Promise<DataSource> => {
  if (dataSource?.isInitialized) {
    return dataSource;
  }

  dataSource = new DataSource({
    type: 'react-native',
    database: 'electrical_contractor.db',
    location: 'default',
    driver: SQLite,
    entities: [
      Project,
      Panel,
      Circuit,
      Material,
      Calculation,
      Photo,
      SyncQueue,
    ],
    synchronize: true, // In production, use migrations instead
    logging: __DEV__ ? ['error', 'warn', 'migration'] : ['error'],
  });

  await dataSource.initialize();
  return dataSource;
};

export const closeDatabaseConnection = async () => {
  if (dataSource?.isInitialized) {
    await dataSource.destroy();
    dataSource = null;
  }
};

export const clearDatabase = async () => {
  const ds = await getDatabaseConnection();
  await ds.dropDatabase();
  await ds.synchronize();
};