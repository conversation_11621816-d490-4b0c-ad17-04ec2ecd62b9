apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  namespace: electrical-app
type: Opaque
stringData:
  database-url: "******************************************************/electrical_contracting"
  redis-url: "redis://:redis_password@redis:6379"
  jwt-secret: "your-jwt-secret-here"
  neo4j-uri: "bolt://neo4j:7687"
  neo4j-user: "neo4j"
  neo4j-password: "your-neo4j-password"
  influxdb-token: "your-influxdb-token"
  sentry-dsn: "your-sentry-dsn"
---
apiVersion: v1
kind: Secret
metadata:
  name: agents-secrets
  namespace: electrical-app
type: Opaque
stringData:
  redis-url: "redis://:redis_password@redis:6379"
  neo4j-uri: "bolt://neo4j:7687"
  neo4j-user: "neo4j"
  neo4j-password: "your-neo4j-password"
  sentry-dsn: "your-sentry-dsn"
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: electrical-app
type: Opaque
stringData:
  username: "postgres"
  password: "your-postgres-password"
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secrets
  namespace: electrical-app
type: Opaque
stringData:
  password: "redis_password"