{"monitoring": {"intervals": {"metrics": 5000, "health": 10000, "resources": 5000}, "thresholds": {"cpu": {"warning": 70, "critical": 85}, "memory": {"warning": 75, "critical": 90}, "disk": {"warning": 80, "critical": 95}, "responseTime": {"warning": 1000, "critical": 3000}, "errorRate": {"warning": 2, "critical": 5}, "database": {"connectionPool": {"warning": 80, "critical": 95}, "queryTime": {"warning": 500, "critical": 2000}}}, "alerts": {"enabled": true, "channels": ["console", "file"], "cooldown": 300000}}, "endpoints": {"health": "/health", "metrics": "/api/admin/metrics", "database": "/api/admin/database/metrics", "redis": "/api/admin/redis/info"}, "testProfiles": {"load": {"targetUsers": 100, "rampUpTime": 300, "sustainedDuration": 1800, "expectedRPS": 100, "acceptableErrorRate": 1}, "stress": {"targetUsers": 1000, "rampUpTime": 600, "sustainedDuration": 600, "expectedRPS": 500, "acceptableErrorRate": 5}, "spike": {"normalUsers": 50, "spikeUsers": 500, "spikeDuration": 60, "recoveryTime": 300, "acceptableErrorRate": 2}, "endurance": {"targetUsers": 50, "duration": 14400, "expectedRPS": 50, "acceptableErrorRate": 0.5, "memoryLeakThreshold": 10}}, "reporting": {"formats": ["json", "csv", "html", "markdown"], "includeRawData": false, "aggregationIntervals": [10, 60, 300], "percentiles": [50, 90, 95, 99, 99.9], "charts": {"responseTime": true, "throughput": true, "errorRate": true, "concurrency": true, "resources": true}}}