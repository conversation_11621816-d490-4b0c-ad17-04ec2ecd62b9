# Development Setup Guide

This guide walks you through setting up your development environment for the Electrical Contracting Application.

## 🔧 Prerequisites

### Required Software

#### 1. Node.js and pnpm
```bash
# Install Node.js 20+ (using nvm recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Install pnpm
npm install -g pnpm@8
```

#### 2. Docker and Docker Compose
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose

# macOS (using Homebrew)
brew install docker docker-compose

# Windows
# Download Docker Desktop from https://www.docker.com/products/docker-desktop
```

#### 3. Development Tools
```bash
# Git
sudo apt install git  # Ubuntu/Debian
brew install git     # macOS

# VS Code (recommended IDE)
# Download from https://code.visualstudio.com/

# PostgreSQL client (optional)
sudo apt install postgresql-client  # Ubuntu/Debian
brew install postgresql            # macOS
```

### System Requirements
- **RAM**: Minimum 8GB (16GB recommended)
- **Storage**: 10GB free space
- **OS**: Windows 10+, macOS 10.15+, Ubuntu 20.04+

## 🚀 Initial Setup

### 1. Clone the Repository
```bash
# Using SSH (recommended)
<NAME_EMAIL>:your-org/electrical-app.git

# Using HTTPS
git clone https://github.com/your-org/electrical-app.git

cd electrical-app
```

### 2. Install Dependencies
```bash
# Install all dependencies using pnpm workspaces
pnpm install

# This installs dependencies for:
# - Backend API
# - Frontend Web App
# - Mobile App
# - Agent Services
# - Shared Libraries
```

### 3. Environment Configuration

#### Create Environment Files
```bash
# Copy example environment files
cp .env.example .env
cp app/backend/.env.example app/backend/.env
cp app/frontend/.env.example app/frontend/.env
cp app/mobile/.env.example app/mobile/.env
cp app/agents/.env.example app/agents/.env
```

#### Configure Environment Variables
Edit the `.env` file in the root directory:

```bash
# Database Configuration
DATABASE_URL="postgresql://electrical:password@localhost:5432/electrical_dev"
POSTGRES_USER=electrical
POSTGRES_PASSWORD=development_password_change_me
POSTGRES_DB=electrical_dev

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=development_redis_password

# Authentication
JWT_SECRET=your_development_jwt_secret_minimum_32_chars
JWT_REFRESH_SECRET=your_development_refresh_secret_minimum_32_chars
SESSION_SECRET=your_development_session_secret

# API Keys (for development)
OPENAI_API_KEY=your_openai_api_key_if_testing_ai
STRIPE_SECRET_KEY=your_stripe_test_key
SENDGRID_API_KEY=your_sendgrid_api_key

# Application URLs
BACKEND_URL=http://localhost:3000
FRONTEND_URL=http://localhost:5173
MOBILE_API_URL=http://localhost:3000

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB

# Development Settings
NODE_ENV=development
LOG_LEVEL=debug
```

### 4. Database Setup

#### Start Database Services
```bash
# Start PostgreSQL and Redis using Docker
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Verify services are running
docker-compose -f docker-compose.dev.yml ps
```

#### Run Database Migrations
```bash
# Navigate to backend directory
cd app/backend

# Generate Prisma client
pnpm db:generate

# Run migrations
pnpm db:push

# Seed the database with sample data
pnpm db:seed

# Return to root directory
cd ../..
```

## 🏃 Running the Application

### Start All Services
```bash
# Using the dev script (recommended)
pnpm dev

# This starts:
# - Backend API (port 3000)
# - Frontend Web (port 5173)
# - Agent Service (port 3001)
```

### Start Services Individually
```bash
# Terminal 1: Backend API
cd app/backend
pnpm dev

# Terminal 2: Frontend Web
cd app/frontend
pnpm dev

# Terminal 3: Agent Service
cd app/agents
pnpm dev

# Terminal 4: Mobile (optional)
cd app/mobile
pnpm start
```

### Verify Everything Works
1. **Backend API**: http://localhost:3000/health
2. **API Documentation**: http://localhost:3000/api-docs
3. **Frontend**: http://localhost:5173
4. **Database Admin**: http://localhost:8080 (Adminer)

## 🛠️ VS Code Setup

### Recommended Extensions
Create `.vscode/extensions.json`:
```json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "prisma.prisma",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "formulahendry.auto-close-tag",
    "christian-kohler.path-intellisense",
    "mikestead.dotenv",
    "eamodio.gitlens",
    "usernamehw.errorlens",
    "yoavbls.pretty-ts-errors"
  ]
}
```

### Workspace Settings
Create `.vscode/settings.json`:
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "eslint.workingDirectories": [
    "./app/backend",
    "./app/frontend",
    "./app/mobile",
    "./app/agents"
  ],
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  }
}
```

## 📱 Mobile Development Setup

### iOS Setup (macOS only)
```bash
# Install Xcode from App Store
# Install CocoaPods
sudo gem install cocoapods

# Navigate to mobile app
cd app/mobile

# Install iOS dependencies
cd ios && pod install
cd ..

# Run iOS simulator
pnpm ios
```

### Android Setup
```bash
# Install Android Studio
# Download from https://developer.android.com/studio

# Set up environment variables
export ANDROID_HOME=$HOME/Library/Android/sdk  # macOS
export ANDROID_HOME=$HOME/Android/Sdk          # Linux

# Add to PATH
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin

# Run Android emulator
pnpm android
```

## 🧪 Testing Setup

### Unit Testing
```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

### E2E Testing
```bash
# Install Playwright
pnpm playwright install

# Run E2E tests
pnpm test:e2e

# Run E2E tests in UI mode
pnpm test:e2e:ui
```

## 🐛 Debugging

### Backend Debugging
Add to `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Backend",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/app/backend/src/index.ts",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "dev"],
      "cwd": "${workspaceFolder}/app/backend",
      "console": "integratedTerminal"
    }
  ]
}
```

### Frontend Debugging
1. Install React Developer Tools browser extension
2. Use Chrome DevTools for debugging
3. Add `debugger` statements in code
4. Use VS Code's Chrome debugger extension

## 🔧 Common Issues

### Port Already in Use
```bash
# Find process using port
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# Kill process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose -f docker-compose.dev.yml ps

# View PostgreSQL logs
docker-compose -f docker-compose.dev.yml logs postgres

# Restart PostgreSQL
docker-compose -f docker-compose.dev.yml restart postgres
```

### Node Module Issues
```bash
# Clear all node_modules and reinstall
pnpm clean
pnpm install

# Clear pnpm cache
pnpm store prune
```

## 📦 Additional Services

### Start All Development Services
```bash
# This includes additional services for development
docker-compose -f docker-compose.dev.yml up -d

# Services started:
# - PostgreSQL (5432)
# - Redis (6379)
# - Adminer (8080)
# - MailHog (8025) - Email testing
# - MinIO (9000) - S3-compatible storage
```

### Access Development Tools
- **Database Admin**: http://localhost:8080
  - System: PostgreSQL
  - Server: postgres
  - Username: electrical
  - Password: (from .env)
- **Email Testing**: http://localhost:8025
- **Object Storage**: http://localhost:9000

## 🎯 Next Steps

1. **Run the test suite** to ensure everything is working
2. **Explore the codebase** starting with `app/backend/src/index.ts`
3. **Try making a small change** to familiarize yourself
4. **Read the architecture guide** to understand the system
5. **Join the developer Slack** for questions and discussions

## 🆘 Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](../user/troubleshooting.md)
2. Search existing [GitHub Issues](https://github.com/your-org/electrical-app/issues)
3. Ask in the #dev-help Slack channel
4. Create a new issue with:
   - Your OS and version
   - Node.js and pnpm versions
   - Error messages and logs
   - Steps to reproduce

Welcome to the team! We're excited to have you contributing to the Electrical Contracting Application.