# Troubleshooting Guide

This guide helps you resolve common issues with the Electrical Contracting Application. If you can't find a solution here, please contact our support team.

## 🔍 Quick Diagnostics

Before troubleshooting specific issues, try these general steps:

1. **Clear Browser Cache** (Web)
   - Chrome: Ctrl+Shift+Delete (Cmd+Shift+Delete on Mac)
   - Firefox: Ctrl+Shift+Delete (Cmd+Shift+Delete on Mac)
   - Safari: Develop → Empty Caches
   - Edge: Ctrl+Shift+Delete

2. **Check System Status**
   - Visit status.electricalapp.com
   - Check for scheduled maintenance
   - Verify service availability

3. **Test Internet Connection**
   - Visit fast.com to check speed
   - Minimum 5 Mbps recommended
   - Check firewall settings

## 🔐 Login Issues

### Cannot Log In

#### Symptoms
- "Invalid credentials" error
- Login button not responding
- Page refreshes without logging in

#### Solutions
1. **Verify Credentials**
   - Check caps lock
   - Use "Show Password" option
   - Try password reset

2. **Clear Browser Data**
   ```
   Settings → Privacy → Clear Browsing Data
   - Cookies and site data
   - Cached images and files
   ```

3. **Disable Extensions**
   - Ad blockers may interfere
   - Try incognito/private mode
   - Disable VPN temporarily

4. **Check Account Status**
   - Verify email confirmation
   - Check if account is active
   - Contact admin if suspended

### Two-Factor Authentication Issues

#### Can't Receive Code
1. Check phone number in profile
2. Verify SMS not blocked
3. Try voice call option
4. Use backup codes
5. Contact support for reset

#### Invalid Code
1. Check code hasn't expired (5 minutes)
2. Ensure correct code entered
3. Sync device time
4. Try backup authentication method

## 💻 Performance Issues

### Slow Loading

#### Web Application
1. **Check Internet Speed**
   - Run speed test
   - Try different network
   - Reduce concurrent users

2. **Browser Optimization**
   - Update to latest version
   - Disable unnecessary extensions
   - Close other tabs
   - Try different browser

3. **Clear Application Cache**
   - Settings → Advanced → Clear Cache
   - Restart browser
   - Log in again

#### Mobile Application
1. **Device Storage**
   - Ensure 500MB free space
   - Clear app cache
   - Archive old projects

2. **Background Apps**
   - Close unnecessary apps
   - Restart device
   - Check for OS updates

### Freezing or Crashing

#### Immediate Steps
1. Save any work (if possible)
2. Take screenshot of error
3. Note what you were doing
4. Refresh or restart app

#### Long-term Solutions
1. **Update Everything**
   - Browser/App to latest version
   - Operating system updates
   - Clear all caches

2. **Reduce Data Load**
   - Archive completed projects
   - Delete old calculations
   - Compress photos
   - Limit active projects

## 📊 Calculation Issues

### Incorrect Results

#### Common Causes
1. **Wrong Input Units**
   - Verify feet vs meters
   - Check AWG vs mm²
   - Confirm voltage level

2. **Missing Parameters**
   - Temperature corrections
   - Derating factors
   - Installation method

3. **Outdated Code Version**
   - Check NEC year in settings
   - Verify local amendments
   - Update if needed

#### Verification Steps
1. Double-check all inputs
2. Compare with manual calculation
3. Try different calculator
4. Check calculation history
5. Report persistent issues

### Calculator Not Loading

1. **Enable JavaScript**
   - Required for calculations
   - Check browser settings
   - Disable script blockers

2. **Clear Calculator Cache**
   - Settings → Calculations → Clear Cache
   - Refresh page
   - Re-enter values

## 🔄 Sync Problems

### Data Not Syncing

#### Mobile to Cloud
1. **Check Sync Status**
   - Look for sync icon
   - Tap to see queue
   - Check error messages

2. **Manual Sync**
   - Pull down to refresh
   - Force sync in settings
   - Check specific items

3. **Connection Issues**
   - Verify internet connection
   - Check WiFi/cellular data
   - Disable airplane mode
   - Try different network

#### Between Devices
1. Ensure logged into same account
2. Check sync settings on all devices
3. Wait for sync to complete (progress bar)
4. Verify time zones match
5. Check for conflicts

### Sync Conflicts

#### Resolution Steps
1. **Review Both Versions**
   - Compare timestamps
   - Check which has latest data
   - Consider merging manually

2. **Choose Resolution**
   - Keep local version
   - Keep server version
   - Merge changes (if available)

3. **Prevent Future Conflicts**
   - Sync before starting work
   - Avoid editing on multiple devices
   - Use check-out system for documents

## 📸 Photo Issues

### Cannot Take Photos

1. **Check Permissions**
   - Settings → Apps → Permissions → Camera
   - Grant camera access
   - Restart app

2. **Storage Space**
   - Ensure adequate free space
   - Clear photo cache
   - Delete unnecessary photos

3. **Camera Problems**
   - Test camera in default app
   - Clean lens
   - Restart device
   - Check for damage

### Photos Not Uploading

1. **Check File Size**
   - Maximum 10MB per photo
   - Enable compression in settings
   - Reduce quality if needed

2. **Network Issues**
   - Wait for better connection
   - Try WiFi instead of cellular
   - Check upload queue

3. **Format Problems**
   - Supported: JPG, PNG
   - Convert HEIC if needed
   - Avoid special characters in names

## 🔍 Search Problems

### No Search Results

1. **Check Spelling**
   - Use autocomplete suggestions
   - Try partial words
   - Remove special characters

2. **Broaden Search**
   - Use fewer keywords
   - Remove filters
   - Search all projects

3. **Index Issues**
   - Rebuild search index (Settings)
   - Wait for indexing to complete
   - Try again later

### Wrong Results

1. Refine search terms
2. Use advanced filters
3. Check search scope
4. Use exact match (quotes)
5. Try different keywords

## 📱 Mobile-Specific Issues

### App Won't Open

1. **Force Close and Restart**
   - iOS: Swipe up and remove
   - Android: Settings → Apps → Force Stop

2. **Restart Device**
   - Power off completely
   - Wait 30 seconds
   - Power on and try again

3. **Reinstall App**
   - Delete app
   - Restart device
   - Download fresh copy
   - Sign in again

### GPS Not Working

1. **Check Permissions**
   - Location services enabled
   - App has location permission
   - Not in airplane mode

2. **Improve Accuracy**
   - Go outside or near window
   - Wait for GPS lock
   - Enable WiFi (helps with location)

3. **Reset Location Services**
   - Toggle location off/on
   - Reset network settings
   - Calibrate compass

## 🖨️ Printing Issues

### Cannot Print Reports

1. **Check PDF Generation**
   - Preview works correctly
   - Download PDF first
   - Open in PDF reader

2. **Browser Print Issues**
   - Try different browser
   - Disable pop-up blocker
   - Check print preview

3. **Format Problems**
   - Select correct paper size
   - Adjust margins
   - Try landscape orientation
   - Scale to fit

## ⚡ Quick Fixes

### Universal Solutions
1. **The Classic**: Turn it off and on again
2. **Update Everything**: App, OS, browser
3. **Clear Everything**: Cache, cookies, data
4. **Try Different**: Browser, device, network
5. **Check Basics**: Internet, credentials, permissions

### When All Else Fails
1. Document the issue:
   - Screenshots
   - Error messages
   - Steps to reproduce
   - Device/browser info

2. Contact Support:
   - In-app support chat
   - <EMAIL>
   - 1-800-ELECTRICAL
   - Include documentation

## 🛠️ Preventive Maintenance

### Daily
- Sync data regularly
- Clear notifications
- Check for updates

### Weekly
- Clear cache
- Archive completed items
- Review error logs
- Backup important data

### Monthly
- Update app/browser
- Clean up storage
- Review permissions
- Test offline mode

## 📞 Support Escalation

### Level 1: Self-Service
- This troubleshooting guide
- In-app help articles
- Video tutorials
- Community forum

### Level 2: Standard Support
- Email: <EMAIL>
- Response time: 24 hours
- Include error details
- Provide account info

### Level 3: Priority Support
- Phone: 1-800-ELECTRICAL
- Live chat (business hours)
- Screen sharing available
- Immediate assistance

### Level 4: Emergency Support
- For system-wide issues
- Data loss situations
- Security concerns
- Call emergency line

## 📝 Reporting Bugs

### What to Include
1. **Description**: What happened?
2. **Expected**: What should happen?
3. **Steps**: How to reproduce?
4. **Environment**: Device, OS, browser
5. **Screenshots**: Visual evidence
6. **Frequency**: How often?
7. **Impact**: How severe?

### Where to Report
- In-app feedback button
- <EMAIL>
- GitHub issues (for developers)
- Community forum

Remember: The more details you provide, the faster we can fix the issue!