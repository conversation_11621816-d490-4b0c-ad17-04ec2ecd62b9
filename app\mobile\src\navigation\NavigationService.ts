import { createNavigationContainerRef, StackActions } from '@react-navigation/native';
import { RootStackParamList } from '@types/navigation';

export const navigationRef = createNavigationContainerRef<RootStackParamList>();

export const NavigationService = {
  navigate: <RouteName extends keyof RootStackParamList>(
    name: RouteName,
    params?: RootStackParamList[RouteName]
  ) => {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name as any, params as any);
    }
  },

  push: <RouteName extends keyof RootStackParamList>(
    name: RouteName,
    params?: RootStackParamList[RouteName]
  ) => {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.push(name as string, params));
    }
  },

  goBack: () => {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.goBack();
    }
  },

  replace: <RouteName extends keyof RootStackParamList>(
    name: RouteName,
    params?: RootStackParamList[RouteName]
  ) => {
    if (navigationRef.isReady()) {
      navigationRef.dispatch(StackActions.replace(name as string, params));
    }
  },

  reset: (routes: Array<{ name: keyof RootStackParamList; params?: any }>, index = 0) => {
    if (navigationRef.isReady()) {
      navigationRef.reset({
        index,
        routes,
      });
    }
  },

  getCurrentRoute: () => {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute();
    }
    return null;
  },
};