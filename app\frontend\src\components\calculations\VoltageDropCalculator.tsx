import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Zap, AlertCircle, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { COPPER_AMPACITY_75C, ALUMINUM_AMPACITY_75C } from '@electrical/shared';

const voltageDropSchema = z.object({
  project_id: z.string().optional(),
  conductor_material: z.enum(['COPPER', 'ALUMINUM']),
  wire_size: z.enum(Object.keys(COPPER_AMPACITY_75C) as [keyof typeof COPPER_AMPACITY_75C]),
  voltage: z.number().positive(),
  phase: z.enum(['SINGLE', 'THREE']),
  power_factor: z.number().min(0.7).max(1).default(0.9),
  length_feet: z.number().positive(),
  load_amps: z.number().positive(),
  conduit_type: z.enum(['PVC', 'EMT', 'RIGID', 'FLEXIBLE']).default('EMT'),
  ambient_temp_f: z.number().default(86),
  conductors_in_conduit: z.number().int().positive().default(3),
});

type VoltageDropFormData = z.infer<typeof voltageDropSchema>;

export function VoltageDropCalculator() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<VoltageDropFormData>({
    resolver: zodResolver(voltageDropSchema),
    defaultValues: {
      conductor_material: 'COPPER',
      wire_size: '12',
      voltage: 120,
      phase: 'SINGLE',
      power_factor: 0.9,
      conduit_type: 'EMT',
      ambient_temp_f: 86,
      conductors_in_conduit: 3,
    },
  });

  const conductorMaterial = watch('conductor_material');
  const phase = watch('phase');

  const onSubmit = async (data: VoltageDropFormData) => {
    setIsCalculating(true);
    try {
      const response = await api.post('/calculations/voltage-drop', data);
      setResult(response.data);
      toast.success('Voltage drop calculation completed');
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Calculation failed');
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Zap className="mr-2 h-5 w-5" />
          Voltage Drop Calculation (NEC Chapter 9)
        </h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conductor Material
              </label>
              <select
                {...register('conductor_material')}
                className="mt-1 input"
              >
                <option value="COPPER">Copper</option>
                <option value="ALUMINUM">Aluminum</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Wire Size (AWG/kcmil)
              </label>
              <select
                {...register('wire_size')}
                className="mt-1 input"
              >
                {Object.keys(conductorMaterial === 'COPPER' ? COPPER_AMPACITY_75C : ALUMINUM_AMPACITY_75C).map((size) => (
                  <option key={size} value={size}>
                    {size} AWG{parseInt(size) > 0 ? '' : '/kcmil'}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Voltage (V)
              </label>
              <select
                {...register('voltage', { valueAsNumber: true })}
                className="mt-1 input"
              >
                <option value={120}>120V</option>
                <option value={208}>208V</option>
                <option value={240}>240V</option>
                <option value={277}>277V</option>
                <option value={480}>480V</option>
                <option value={600}>600V</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Phase
              </label>
              <select
                {...register('phase')}
                className="mt-1 input"
              >
                <option value="SINGLE">Single Phase</option>
                <option value="THREE">Three Phase</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Circuit Length (feet)
              </label>
              <input
                type="number"
                {...register('length_feet', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="100"
              />
              {errors.length_feet && (
                <p className="mt-1 text-sm text-red-600">{errors.length_feet.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Load Current (Amps)
              </label>
              <input
                type="number"
                {...register('load_amps', { valueAsNumber: true })}
                className="mt-1 input"
                placeholder="20"
                step="0.1"
              />
              {errors.load_amps && (
                <p className="mt-1 text-sm text-red-600">{errors.load_amps.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Power Factor
              </label>
              <input
                type="number"
                {...register('power_factor', { valueAsNumber: true })}
                className="mt-1 input"
                step="0.01"
                min="0.7"
                max="1"
              />
              <p className="mt-1 text-xs text-gray-500">Typically 0.85-0.95 for most loads</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conduit Type
              </label>
              <select
                {...register('conduit_type')}
                className="mt-1 input"
              >
                <option value="PVC">PVC</option>
                <option value="EMT">EMT</option>
                <option value="RIGID">Rigid Metal</option>
                <option value="FLEXIBLE">Flexible Metal</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Ambient Temperature (°F)
              </label>
              <input
                type="number"
                {...register('ambient_temp_f', { valueAsNumber: true })}
                className="mt-1 input"
              />
              <p className="mt-1 text-xs text-gray-500">Default 86°F (30°C)</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Conductors in Conduit
              </label>
              <input
                type="number"
                {...register('conductors_in_conduit', { valueAsNumber: true })}
                className="mt-1 input"
                min="1"
              />
              <p className="mt-1 text-xs text-gray-500">For derating calculation</p>
            </div>
          </div>

          <button
            type="submit"
            disabled={isCalculating}
            className="btn-primary w-full sm:w-auto"
          >
            {isCalculating ? (
              <>
                <div className="spinner mr-2" />
                Calculating...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-4 w-4" />
                Calculate Voltage Drop
              </>
            )}
          </button>
        </form>

        {result && (
          <div className="mt-6 space-y-4">
            <div className={`${result.voltageDropPercent > 3 ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800' : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'} border rounded-md p-4`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className={`h-5 w-5 ${result.voltageDropPercent > 3 ? 'text-yellow-400' : 'text-green-400'}`} />
                </div>
                <div className="ml-3">
                  <h3 className={`text-sm font-medium ${result.voltageDropPercent > 3 ? 'text-yellow-800 dark:text-yellow-200' : 'text-green-800 dark:text-green-200'}`}>
                    Voltage Drop Analysis
                  </h3>
                  <div className={`mt-2 text-sm ${result.voltageDropPercent > 3 ? 'text-yellow-700 dark:text-yellow-300' : 'text-green-700 dark:text-green-300'}`}>
                    <dl className="space-y-1">
                      <div className="flex justify-between">
                        <dt>Voltage Drop:</dt>
                        <dd className="font-medium">{result.voltageDrop.toFixed(2)} V</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Voltage Drop Percentage:</dt>
                        <dd className="font-bold text-lg">{result.voltageDropPercent.toFixed(2)}%</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Voltage at Load:</dt>
                        <dd className="font-medium">{result.voltageAtLoad.toFixed(2)} V</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Power Loss:</dt>
                        <dd className="font-medium">{result.powerLossWatts.toFixed(1)} W</dd>
                      </div>
                    </dl>
                  </div>
                  {result.voltageDropPercent > 3 && (
                    <p className="mt-2 text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      ⚠️ Voltage drop exceeds 3% recommendation
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Circuit Parameters
                </h4>
              </div>
              <div className="p-4">
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt>Wire Resistance:</dt>
                    <dd>{result.wireResistance.toFixed(4)} Ω/1000ft</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Wire Reactance:</dt>
                    <dd>{result.wireReactance.toFixed(4)} Ω/1000ft</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Total Circuit Resistance:</dt>
                    <dd>{result.totalResistance.toFixed(4)} Ω</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Total Circuit Reactance:</dt>
                    <dd>{result.totalReactance.toFixed(4)} Ω</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Circuit Impedance:</dt>
                    <dd>{result.totalImpedance.toFixed(4)} Ω</dd>
                  </div>
                  {result.temperatureFactor !== 1 && (
                    <div className="flex justify-between text-primary-600 dark:text-primary-400">
                      <dt>Temperature Derating Factor:</dt>
                      <dd>{(result.temperatureFactor * 100).toFixed(0)}%</dd>
                    </div>
                  )}
                  {result.bundlingFactor !== 1 && (
                    <div className="flex justify-between text-primary-600 dark:text-primary-400">
                      <dt>Bundling Derating Factor:</dt>
                      <dd>{(result.bundlingFactor * 100).toFixed(0)}%</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>

            {result.recommendations && result.recommendations.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Recommendations
                </h4>
                <ul className="list-disc list-inside text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  {result.recommendations.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-md p-4">
              <div className="flex">
                <FileText className="h-5 w-5 text-gray-400 flex-shrink-0" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    NEC References
                  </h4>
                  <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                    {result.necReferences.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}