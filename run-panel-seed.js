// Simple Node.js script to run the panel seed
const { exec } = require('child_process');
const path = require('path');

console.log('Running panel seed script...');

const backendPath = path.join(__dirname, 'app', 'backend');
const seedScript = 'tsx src/database/seed-panels.ts';

exec(`cd ${backendPath} && ${seedScript}`, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`Stderr: ${stderr}`);
    return;
  }
  console.log(`Output: ${stdout}`);
});