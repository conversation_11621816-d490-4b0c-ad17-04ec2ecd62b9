import React, { useState, useEffect } from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { BackgroundSync } from '@offline/sync/BackgroundSync';
import { SyncEngine } from '@offline/sync/SyncEngine';
import { StorageManager } from '@offline/storage/StorageManager';
import { StorageManagementCard } from '@components/offline/StorageManagementCard';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Slider from '@react-native-community/slider';

export const SyncSettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState({
    enabled: true,
    syncInterval: 15,
    wifiOnly: false,
    batteryOptimized: true,
    autoCleanup: true,
    cleanupThreshold: 90,
    compressPhotos: true,
  });

  const backgroundSync = BackgroundSync.getInstance();
  const syncEngine = SyncEngine.getInstance();
  const storageManager = StorageManager.getInstance();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const [syncSettings, storageSettings] = await Promise.all([
        AsyncStorage.getItem('sync_settings'),
        AsyncStorage.getItem('storage_settings'),
      ]);

      if (syncSettings) {
        const parsed = JSON.parse(syncSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      }

      if (storageSettings) {
        const parsed = JSON.parse(storageSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const updateSetting = async (key: string, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    // Update background sync settings
    if (['enabled', 'syncInterval', 'wifiOnly', 'batteryOptimized'].includes(key)) {
      await backgroundSync.updateSyncSettings({
        [key]: value,
      });
    }

    // Update storage settings
    if (['autoCleanup', 'cleanupThreshold', 'compressPhotos'].includes(key)) {
      await storageManager.updateStorageSettings({
        [key]: value,
      });
    }
  };

  const handleManualSync = async () => {
    Alert.alert(
      'Manual Sync',
      'Start a manual sync now?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sync',
          onPress: async () => {
            try {
              await syncEngine.sync({ automatic: false });
              Alert.alert('Success', 'Sync completed successfully');
            } catch (error) {
              Alert.alert('Error', 'Sync failed. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleClearCache = async () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              const freed = await storageManager.cleanupCache(true);
              Alert.alert('Success', `Cleared ${freed} bytes of cache`);
            } catch (error) {
              Alert.alert('Error', 'Failed to clear cache');
            }
          },
        },
      ]
    );
  };

  const handleResetSync = async () => {
    Alert.alert(
      'Reset Sync Data',
      'This will clear all sync history and require a full re-sync. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement sync reset
              Alert.alert('Success', 'Sync data has been reset');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset sync data');
            }
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Sync Settings</Text>
        
        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Background Sync</Text>
            <Text style={styles.settingDescription}>
              Automatically sync data in the background
            </Text>
          </View>
          <Switch
            value={settings.enabled}
            onValueChange={(value) => updateSetting('enabled', value)}
          />
        </View>

        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Sync Interval</Text>
            <Text style={styles.settingDescription}>
              {settings.syncInterval} minutes
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={5}
            maximumValue={60}
            step={5}
            value={settings.syncInterval}
            onSlidingComplete={(value) => updateSetting('syncInterval', value)}
            minimumTrackTintColor="#2196F3"
            maximumTrackTintColor="#E0E0E0"
          />
        </View>

        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>WiFi Only</Text>
            <Text style={styles.settingDescription}>
              Only sync when connected to WiFi
            </Text>
          </View>
          <Switch
            value={settings.wifiOnly}
            onValueChange={(value) => updateSetting('wifiOnly', value)}
          />
        </View>

        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Battery Optimization</Text>
            <Text style={styles.settingDescription}>
              Reduce sync frequency when battery is low
            </Text>
          </View>
          <Switch
            value={settings.batteryOptimized}
            onValueChange={(value) => updateSetting('batteryOptimized', value)}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Storage Settings</Text>
        
        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Auto Cleanup</Text>
            <Text style={styles.settingDescription}>
              Automatically clean up old data when storage is low
            </Text>
          </View>
          <Switch
            value={settings.autoCleanup}
            onValueChange={(value) => updateSetting('autoCleanup', value)}
          />
        </View>

        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Cleanup Threshold</Text>
            <Text style={styles.settingDescription}>
              Clean when storage reaches {settings.cleanupThreshold}%
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={70}
            maximumValue={95}
            step={5}
            value={settings.cleanupThreshold}
            onSlidingComplete={(value) => updateSetting('cleanupThreshold', value)}
            minimumTrackTintColor="#FF9800"
            maximumTrackTintColor="#E0E0E0"
          />
        </View>

        <View style={styles.setting}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingLabel}>Compress Photos</Text>
            <Text style={styles.settingDescription}>
              Compress photos to save storage space
            </Text>
          </View>
          <Switch
            value={settings.compressPhotos}
            onValueChange={(value) => updateSetting('compressPhotos', value)}
          />
        </View>
      </View>

      <StorageManagementCard />

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleManualSync}>
          <Icon name="sync" size={20} color="#2196F3" />
          <Text style={styles.actionText}>Sync Now</Text>
          <Icon name="chevron-right" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleClearCache}>
          <Icon name="delete-sweep" size={20} color="#FF9800" />
          <Text style={styles.actionText}>Clear Cache</Text>
          <Icon name="chevron-right" size={20} color="#999" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleResetSync}>
          <Icon name="restore" size={20} color="#F44336" />
          <Text style={[styles.actionText, { color: '#F44336' }]}>
            Reset Sync Data
          </Text>
          <Icon name="chevron-right" size={20} color="#999" />
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Offline sync ensures your data is available even without an internet connection.
          Changes are automatically synced when you're back online.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  section: {
    backgroundColor: 'white',
    marginTop: 16,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  setting: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  slider: {
    width: 150,
    height: 40,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  footer: {
    padding: 16,
    marginBottom: 32,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});