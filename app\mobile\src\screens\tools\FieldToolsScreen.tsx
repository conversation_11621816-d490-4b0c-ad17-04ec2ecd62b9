import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';

interface Props {
  navigation: NavigationProp<any>;
}

export const FieldToolsScreen: React.FC<Props> = ({ navigation }) => {
  const tools = [
    {
      category: 'Inspection & Documentation',
      items: [
        {
          id: 'inspection',
          title: 'Inspection Checklist',
          subtitle: 'With photo capture',
          icon: '📋',
          color: '#2196F3',
          screen: 'InspectionChecklist',
        },
        {
          id: 'photos',
          title: 'Job Site Photos',
          subtitle: 'Document work progress',
          icon: '📸',
          color: '#9C27B0',
          screen: 'PhotoDocumentation',
        },
      ],
    },
    {
      category: 'Material & Estimating',
      items: [
        {
          id: 'takeoff',
          title: 'Material Takeoff',
          subtitle: 'Scan and track materials',
          icon: '📦',
          color: '#FF9800',
          screen: 'MaterialTakeoff',
        },
        {
          id: 'estimate',
          title: 'Quick Estimate',
          subtitle: 'Build estimates on-site',
          icon: '💰',
          color: '#4CAF50',
          screen: 'QuickEstimate',
        },
      ],
    },
    {
      category: 'Time & Labor',
      items: [
        {
          id: 'time',
          title: 'Time Tracking',
          subtitle: 'Track labor hours',
          icon: '⏱️',
          color: '#00BCD4',
          screen: 'TimeTracking',
        },
      ],
    },
  ];

  const renderToolCard = (tool: any) => (
    <TouchableOpacity
      key={tool.id}
      style={styles.toolCard}
      onPress={() => navigation.navigate(tool.screen)}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, { backgroundColor: tool.color + '20' }]}>
        <Text style={styles.icon}>{tool.icon}</Text>
      </View>
      <View style={styles.toolInfo}>
        <Text style={styles.toolTitle}>{tool.title}</Text>
        <Text style={styles.toolSubtitle}>{tool.subtitle}</Text>
      </View>
      <Text style={styles.arrow}>→</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Field Tools</Text>
        <Text style={styles.subtitle}>Essential tools for on-site work</Text>
      </View>

      {tools.map((category) => (
        <View key={category.category} style={styles.categorySection}>
          <Text style={styles.categoryTitle}>{category.category}</Text>
          <View style={styles.toolsGrid}>
            {category.items.map(renderToolCard)}
          </View>
        </View>
      ))}

      <View style={styles.offlineSection}>
        <View style={styles.offlineIndicator}>
          <View style={styles.offlineDot} />
          <Text style={styles.offlineText}>All tools work offline</Text>
        </View>
        <Text style={styles.offlineDescription}>
          Data syncs automatically when connection is restored
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  categorySection: {
    marginTop: 20,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  toolsGrid: {
    paddingHorizontal: 15,
  },
  toolCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    marginHorizontal: 5,
    marginBottom: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  icon: {
    fontSize: 24,
  },
  toolInfo: {
    flex: 1,
  },
  toolTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  toolSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 20,
    color: '#999',
  },
  offlineSection: {
    backgroundColor: '#E8F5E9',
    margin: 20,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  offlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: 8,
  },
  offlineText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  offlineDescription: {
    fontSize: 14,
    color: '#388E3C',
    textAlign: 'center',
  },
});