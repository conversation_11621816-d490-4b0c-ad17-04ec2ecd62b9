#!/usr/bin/env node

// Test script to check if agents can run without external dependencies

console.log('Testing Agent System...\n');

// Check if basic modules are available
try {
  const { AgentSystem } = require('./dist/index.js');
  console.log('✓ Agent system module loaded successfully');
  
  // Try to create agent system with minimal configuration
  const system = new AgentSystem({
    enabledAgents: ['project-manager'], // Only enable the project manager which has fewer dependencies
    logLevel: 'info'
  });
  
  console.log('✓ Agent system instance created');
  console.log('\nAgent system status:');
  console.log(JSON.stringify(system.getStatus(), null, 2));
  
} catch (error) {
  console.error('✗ Failed to load agent system:', error.message);
  process.exit(1);
}

console.log('\n---\nAgent system can be loaded but has the following dependencies:');
console.log('1. ChromaDB - for vector storage (not installed)');
console.log('2. Neo4j - for graph database (not installed)');
console.log('3. Prisma/Database - backend database agent tries to connect');
console.log('4. Redis - for message queue (optional)');
console.log('\nThe agents are NOT essential for basic app functionality.');
console.log('The main app (backend + frontend) can run without them.');