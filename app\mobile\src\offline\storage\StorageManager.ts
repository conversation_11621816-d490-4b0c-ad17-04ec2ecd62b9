import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getDatabaseConnection } from '../database/connection';
import { Photo } from '../database/entities';
import { StorageStats } from '../sync/types';
import LZString from 'lz-string';

export class StorageManager {
  private static instance: StorageManager;
  private readonly CACHE_DIR = `${RNFS.DocumentDirectoryPath}/cache`;
  private readonly PHOTOS_DIR = `${RNFS.DocumentDirectoryPath}/photos`;
  private readonly TEMP_DIR = `${RNFS.CachesDirectoryPath}/temp`;
  private readonly MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private readonly MAX_PHOTO_SIZE = 500 * 1024 * 1024; // 500MB

  private constructor() {
    this.ensureDirectories();
  }

  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  private async ensureDirectories() {
    const dirs = [this.CACHE_DIR, this.PHOTOS_DIR, this.TEMP_DIR];
    
    for (const dir of dirs) {
      const exists = await RNFS.exists(dir);
      if (!exists) {
        await RNFS.mkdir(dir);
      }
    }
  }

  async getStorageStats(): Promise<StorageStats> {
    const [totalSpace, freeSpace] = await Promise.all([
      RNFS.getFSInfo().then(info => info.totalSpace),
      RNFS.getFSInfo().then(info => info.freeSpace),
    ]);

    const [cacheSize, databaseSize, photosSize] = await Promise.all([
      this.getDirectorySize(this.CACHE_DIR),
      this.getDatabaseSize(),
      this.getDirectorySize(this.PHOTOS_DIR),
    ]);

    return {
      totalSpace,
      freeSpace,
      usedSpace: totalSpace - freeSpace,
      cacheSize,
      databaseSize,
      photosSize,
    };
  }

  private async getDirectorySize(path: string): Promise<number> {
    try {
      const exists = await RNFS.exists(path);
      if (!exists) return 0;

      const files = await RNFS.readDir(path);
      let totalSize = 0;

      for (const file of files) {
        if (file.isFile()) {
          totalSize += parseInt(file.size);
        } else if (file.isDirectory()) {
          totalSize += await this.getDirectorySize(file.path);
        }
      }

      return totalSize;
    } catch (error) {
      console.error('Error calculating directory size:', error);
      return 0;
    }
  }

  private async getDatabaseSize(): Promise<number> {
    try {
      const dbPath = `${RNFS.DocumentDirectoryPath}/electrical_contractor.db`;
      const exists = await RNFS.exists(dbPath);
      
      if (exists) {
        const stat = await RNFS.stat(dbPath);
        return parseInt(stat.size);
      }
      
      return 0;
    } catch (error) {
      console.error('Error getting database size:', error);
      return 0;
    }
  }

  async cleanupCache(aggressive: boolean = false): Promise<number> {
    let freedSpace = 0;

    // Clean temporary files
    freedSpace += await this.cleanDirectory(this.TEMP_DIR);

    // Clean old cache files
    const cacheFiles = await RNFS.readDir(this.CACHE_DIR);
    const now = Date.now();
    const maxAge = aggressive ? 1 : 7; // days

    for (const file of cacheFiles) {
      if (file.isFile()) {
        const fileAge = now - new Date(file.mtime).getTime();
        if (fileAge > maxAge * 24 * 60 * 60 * 1000) {
          await RNFS.unlink(file.path);
          freedSpace += parseInt(file.size);
        }
      }
    }

    // Clean orphaned photos
    freedSpace += await this.cleanOrphanedPhotos();

    return freedSpace;
  }

  private async cleanDirectory(path: string): Promise<number> {
    let freedSpace = 0;
    
    try {
      const files = await RNFS.readDir(path);
      
      for (const file of files) {
        if (file.isFile()) {
          await RNFS.unlink(file.path);
          freedSpace += parseInt(file.size);
        }
      }
    } catch (error) {
      console.error('Error cleaning directory:', error);
    }

    return freedSpace;
  }

  private async cleanOrphanedPhotos(): Promise<number> {
    let freedSpace = 0;
    
    try {
      const db = await getDatabaseConnection();
      const photoRepo = db.getRepository(Photo);
      
      // Get all photo records
      const photos = await photoRepo.find();
      const validPaths = new Set(photos.map(p => p.localPath));
      
      // Check physical files
      const photoFiles = await RNFS.readDir(this.PHOTOS_DIR);
      
      for (const file of photoFiles) {
        if (file.isFile() && !validPaths.has(file.path)) {
          await RNFS.unlink(file.path);
          freedSpace += parseInt(file.size);
        }
      }
    } catch (error) {
      console.error('Error cleaning orphaned photos:', error);
    }

    return freedSpace;
  }

  async compressAndStorePhoto(sourcePath: string, projectId: string): Promise<{
    path: string;
    thumbnailPath: string;
    size: number;
    compressed: boolean;
  }> {
    const filename = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
    const destPath = `${this.PHOTOS_DIR}/${filename}`;
    const thumbnailPath = `${this.PHOTOS_DIR}/thumb_${filename}`;

    // Copy original
    await RNFS.copyFile(sourcePath, destPath);
    
    const stats = await RNFS.stat(destPath);
    const originalSize = parseInt(stats.size);
    
    let compressed = false;
    let finalSize = originalSize;

    // Compress if larger than 1MB
    if (originalSize > 1024 * 1024) {
      try {
        // TODO: Implement actual image compression
        // For now, we'll just use the original
        compressed = true;
      } catch (error) {
        console.error('Error compressing photo:', error);
      }
    }

    // Create thumbnail
    // TODO: Implement thumbnail generation

    return {
      path: destPath,
      thumbnailPath,
      size: finalSize,
      compressed,
    };
  }

  async deletePhoto(path: string): Promise<void> {
    try {
      const exists = await RNFS.exists(path);
      if (exists) {
        await RNFS.unlink(path);
      }

      // Also try to delete thumbnail
      const thumbnailPath = path.replace(/([^/]+)$/, 'thumb_$1');
      const thumbExists = await RNFS.exists(thumbnailPath);
      if (thumbExists) {
        await RNFS.unlink(thumbnailPath);
      }
    } catch (error) {
      console.error('Error deleting photo:', error);
    }
  }

  async exportData(projectId?: string): Promise<string> {
    const db = await getDatabaseConnection();
    const exportData: any = {};

    if (projectId) {
      // Export specific project
      const projectRepo = db.getRepository('Project');
      const project = await projectRepo.findOne({
        where: { id: projectId },
        relations: ['panels', 'materials', 'calculations', 'photos'],
      });
      
      exportData.projects = [project];
    } else {
      // Export all data
      const entities = ['Project', 'Panel', 'Circuit', 'Material', 'Calculation', 'Photo'];
      
      for (const entity of entities) {
        const repo = db.getRepository(entity);
        exportData[entity.toLowerCase() + 's'] = await repo.find();
      }
    }

    // Compress the data
    const jsonString = JSON.stringify(exportData);
    const compressed = LZString.compressToBase64(jsonString);
    
    // Save to file
    const filename = `export_${Date.now()}.ecdata`;
    const filePath = `${this.TEMP_DIR}/${filename}`;
    
    await RNFS.writeFile(filePath, compressed, 'utf8');
    
    return filePath;
  }

  async importData(filePath: string): Promise<{
    success: boolean;
    imported: Record<string, number>;
    errors: string[];
  }> {
    const result = {
      success: true,
      imported: {} as Record<string, number>,
      errors: [] as string[],
    };

    try {
      // Read and decompress data
      const compressed = await RNFS.readFile(filePath, 'utf8');
      const jsonString = LZString.decompressFromBase64(compressed);
      const data = JSON.parse(jsonString);

      const db = await getDatabaseConnection();

      // Import in correct order to maintain relationships
      const importOrder = [
        'projects',
        'panels',
        'circuits',
        'materials',
        'calculations',
        'photos',
      ];

      for (const entityKey of importOrder) {
        if (data[entityKey]) {
          const entityName = entityKey.slice(0, -1); // Remove 's'
          const repo = db.getRepository(entityName.charAt(0).toUpperCase() + entityName.slice(1));
          
          try {
            for (const item of data[entityKey]) {
              // Check if already exists
              const existing = await repo.findOne({ where: { remoteId: item.remoteId } });
              
              if (!existing) {
                await repo.save({
                  ...item,
                  id: undefined, // Let TypeORM generate new ID
                  syncStatus: 'pending',
                });
                
                result.imported[entityKey] = (result.imported[entityKey] || 0) + 1;
              }
            }
          } catch (error) {
            result.errors.push(`Error importing ${entityKey}: ${error.message}`);
            result.success = false;
          }
        }
      }
    } catch (error) {
      result.errors.push(`Error reading import file: ${error.message}`);
      result.success = false;
    }

    return result;
  }

  async getStorageSettings(): Promise<{
    autoCleanup: boolean;
    cleanupThreshold: number; // percentage
    maxCacheAge: number; // days
    compressPhotos: boolean;
    maxPhotoSize: number; // bytes
  }> {
    try {
      const settings = await AsyncStorage.getItem('storage_settings');
      return settings ? JSON.parse(settings) : this.getDefaultStorageSettings();
    } catch {
      return this.getDefaultStorageSettings();
    }
  }

  private getDefaultStorageSettings() {
    return {
      autoCleanup: true,
      cleanupThreshold: 90, // Cleanup when storage is 90% full
      maxCacheAge: 7, // days
      compressPhotos: true,
      maxPhotoSize: 2 * 1024 * 1024, // 2MB
    };
  }

  async updateStorageSettings(settings: Partial<{
    autoCleanup: boolean;
    cleanupThreshold: number;
    maxCacheAge: number;
    compressPhotos: boolean;
    maxPhotoSize: number;
  }>) {
    const current = await this.getStorageSettings();
    const updated = { ...current, ...settings };
    await AsyncStorage.setItem('storage_settings', JSON.stringify(updated));
  }

  async checkStorageHealth(): Promise<{
    healthy: boolean;
    warnings: string[];
    recommendations: string[];
  }> {
    const stats = await this.getStorageStats();
    const settings = await this.getStorageSettings();
    
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    const usagePercentage = (stats.usedSpace / stats.totalSpace) * 100;
    
    if (usagePercentage > settings.cleanupThreshold) {
      warnings.push(`Storage usage is at ${usagePercentage.toFixed(1)}%`);
      recommendations.push('Run cleanup to free up space');
    }
    
    if (stats.cacheSize > this.MAX_CACHE_SIZE) {
      warnings.push('Cache size exceeds recommended limit');
      recommendations.push('Clear old cache files');
    }
    
    if (stats.photosSize > this.MAX_PHOTO_SIZE) {
      warnings.push('Photo storage is getting large');
      recommendations.push('Consider backing up and removing old photos');
    }
    
    if (stats.freeSpace < 100 * 1024 * 1024) { // Less than 100MB free
      warnings.push('Low storage space available');
      recommendations.push('Free up device storage immediately');
    }
    
    return {
      healthy: warnings.length === 0,
      warnings,
      recommendations,
    };
  }
}