@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }
  
  /* Base styles */
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    font-feature-settings: 'cv11', 'ss01', 'ss03';
  }
  
  /* Focus styles */
  *:focus {
    outline: none;
  }
  
  *:focus-visible {
    @apply ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600 focus-visible:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-danger text-white hover:bg-red-600 focus-visible:ring-danger;
  }
  
  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 dark:placeholder-gray-500;
  }
  
  .input-error {
    @apply border-danger focus:ring-danger focus:border-danger;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }
  
  .table-row {
    @apply bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors;
  }
  
  /* Loading spinner */
  .spinner {
    @apply inline-block w-6 h-6 border-[3px] border-gray-300 border-t-primary-600 rounded-full animate-spin;
  }
}

@layer utilities {
  /* Animation delays */
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  /* Text selection */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-after: always;
    }
  }
}