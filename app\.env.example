# Application
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database
DATABASE_URL=file:./database/electrical.db
DATABASE_WAL_MODE=true

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Neo4j (for Memory Agent)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=electrical123

# ChromaDB (for Memory Agent)
CHROMADB_URL=http://localhost:8000

# InfluxDB (for metrics)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=electrical_token_123
INFLUXDB_ORG=electrical_contracting
INFLUXDB_BUCKET=metrics

# External APIs
GEMINI_API_KEY=your-gemini-api-key
CLAUDE_API_KEY=your-claude-api-key

# Material Pricing APIs (examples)
GRAYBAR_API_KEY=
WESCO_API_KEY=
PLATT_API_KEY=

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Agent System
AGENT_REDIS_CHANNEL=electrical:agents
AGENT_MEMORY_TTL=86400
AGENT_MESSAGE_RETENTION=604800

# Security
ENCRYPTION_KEY=your-32-character-encryption-key
CORS_ORIGINS=http://localhost:3000,http://localhost:3001