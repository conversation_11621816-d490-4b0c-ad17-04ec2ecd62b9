// Centralized error codes and categories
export enum ErrorCategory {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  DATABASE = 'DATABASE',
  NETWORK = 'NETWORK',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  CALCULATION = 'CALCULATION',
  FILE_SYSTEM = 'FILE_SYSTEM',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  SYSTEM = 'SYSTEM',
}

export enum ErrorCode {
  // Validation errors (4000-4099)
  INVALID_INPUT = 'ERR_4000',
  MISSING_REQUIRED_FIELD = 'ERR_4001',
  INVALID_FORMAT = 'ERR_4002',
  VALUE_OUT_OF_RANGE = 'ERR_4003',
  INVALID_EMAIL = 'ERR_4004',
  INVALID_PHONE = 'ERR_4005',
  INVALID_DATE = 'ERR_4006',

  // Authentication errors (4100-4199)
  INVALID_CREDENTIALS = 'ERR_4100',
  TOKEN_EXPIRED = 'ERR_4101',
  TOKEN_INVALID = 'ERR_4102',
  SESSION_EXPIRED = 'ERR_4103',
  ACCOUNT_LOCKED = 'ERR_4104',
  ACCOUNT_DISABLED = 'ERR_4105',
  MFA_REQUIRED = 'ERR_4106',
  MFA_INVALID = 'ERR_4107',

  // Authorization errors (4200-4299)
  UNAUTHORIZED = 'ERR_4200',
  FORBIDDEN = 'ERR_4201',
  INSUFFICIENT_PERMISSIONS = 'ERR_4202',
  RESOURCE_ACCESS_DENIED = 'ERR_4203',

  // Database errors (4300-4399)
  RECORD_NOT_FOUND = 'ERR_4300',
  DUPLICATE_RECORD = 'ERR_4301',
  CONSTRAINT_VIOLATION = 'ERR_4302',
  DATABASE_CONNECTION_FAILED = 'ERR_4303',
  TRANSACTION_FAILED = 'ERR_4304',
  QUERY_TIMEOUT = 'ERR_4305',
  DATA_INTEGRITY_ERROR = 'ERR_4306',

  // Business logic errors (4400-4499)
  INVALID_OPERATION = 'ERR_4400',
  BUSINESS_RULE_VIOLATION = 'ERR_4401',
  INSUFFICIENT_FUNDS = 'ERR_4402',
  QUOTA_EXCEEDED = 'ERR_4403',
  WORKFLOW_ERROR = 'ERR_4404',
  STATE_TRANSITION_ERROR = 'ERR_4405',

  // Calculation errors (4500-4599)
  CALCULATION_ERROR = 'ERR_4500',
  INVALID_CALCULATION_INPUT = 'ERR_4501',
  CALCULATION_OVERFLOW = 'ERR_4502',
  NEC_VIOLATION = 'ERR_4503',
  LOAD_EXCEEDS_CAPACITY = 'ERR_4504',
  VOLTAGE_DROP_EXCEEDED = 'ERR_4505',
  ARC_FLASH_HAZARD = 'ERR_4506',

  // External service errors (4600-4699)
  EXTERNAL_SERVICE_ERROR = 'ERR_4600',
  API_RATE_LIMIT = 'ERR_4601',
  THIRD_PARTY_ERROR = 'ERR_4602',
  PAYMENT_FAILED = 'ERR_4603',
  EMAIL_SEND_FAILED = 'ERR_4604',
  SMS_SEND_FAILED = 'ERR_4605',

  // System errors (5000-5099)
  INTERNAL_ERROR = 'ERR_5000',
  SERVICE_UNAVAILABLE = 'ERR_5001',
  CONFIGURATION_ERROR = 'ERR_5002',
  DEPENDENCY_ERROR = 'ERR_5003',
  MEMORY_ERROR = 'ERR_5004',
  FILE_SYSTEM_ERROR = 'ERR_5005',
  NETWORK_ERROR = 'ERR_5006',
}

// Error metadata for user-friendly messages
export const ErrorMetadata: Record<ErrorCode, { message: string; httpStatus: number; category: ErrorCategory }> = {
  // Validation errors
  [ErrorCode.INVALID_INPUT]: { message: 'Invalid input provided', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.MISSING_REQUIRED_FIELD]: { message: 'Required field is missing', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.INVALID_FORMAT]: { message: 'Invalid format', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.VALUE_OUT_OF_RANGE]: { message: 'Value is out of acceptable range', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.INVALID_EMAIL]: { message: 'Invalid email address', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.INVALID_PHONE]: { message: 'Invalid phone number', httpStatus: 400, category: ErrorCategory.VALIDATION },
  [ErrorCode.INVALID_DATE]: { message: 'Invalid date', httpStatus: 400, category: ErrorCategory.VALIDATION },

  // Authentication errors
  [ErrorCode.INVALID_CREDENTIALS]: { message: 'Invalid credentials', httpStatus: 401, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.TOKEN_EXPIRED]: { message: 'Token has expired', httpStatus: 401, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.TOKEN_INVALID]: { message: 'Invalid token', httpStatus: 401, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.SESSION_EXPIRED]: { message: 'Session has expired', httpStatus: 401, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.ACCOUNT_LOCKED]: { message: 'Account is locked', httpStatus: 403, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.ACCOUNT_DISABLED]: { message: 'Account is disabled', httpStatus: 403, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.MFA_REQUIRED]: { message: 'Multi-factor authentication required', httpStatus: 403, category: ErrorCategory.AUTHENTICATION },
  [ErrorCode.MFA_INVALID]: { message: 'Invalid multi-factor authentication code', httpStatus: 401, category: ErrorCategory.AUTHENTICATION },

  // Authorization errors
  [ErrorCode.UNAUTHORIZED]: { message: 'Unauthorized access', httpStatus: 401, category: ErrorCategory.AUTHORIZATION },
  [ErrorCode.FORBIDDEN]: { message: 'Access forbidden', httpStatus: 403, category: ErrorCategory.AUTHORIZATION },
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: { message: 'Insufficient permissions', httpStatus: 403, category: ErrorCategory.AUTHORIZATION },
  [ErrorCode.RESOURCE_ACCESS_DENIED]: { message: 'Access to resource denied', httpStatus: 403, category: ErrorCategory.AUTHORIZATION },

  // Database errors
  [ErrorCode.RECORD_NOT_FOUND]: { message: 'Record not found', httpStatus: 404, category: ErrorCategory.DATABASE },
  [ErrorCode.DUPLICATE_RECORD]: { message: 'Duplicate record', httpStatus: 409, category: ErrorCategory.DATABASE },
  [ErrorCode.CONSTRAINT_VIOLATION]: { message: 'Database constraint violation', httpStatus: 400, category: ErrorCategory.DATABASE },
  [ErrorCode.DATABASE_CONNECTION_FAILED]: { message: 'Database connection failed', httpStatus: 503, category: ErrorCategory.DATABASE },
  [ErrorCode.TRANSACTION_FAILED]: { message: 'Database transaction failed', httpStatus: 500, category: ErrorCategory.DATABASE },
  [ErrorCode.QUERY_TIMEOUT]: { message: 'Database query timeout', httpStatus: 504, category: ErrorCategory.DATABASE },
  [ErrorCode.DATA_INTEGRITY_ERROR]: { message: 'Data integrity error', httpStatus: 500, category: ErrorCategory.DATABASE },

  // Business logic errors
  [ErrorCode.INVALID_OPERATION]: { message: 'Invalid operation', httpStatus: 400, category: ErrorCategory.BUSINESS_LOGIC },
  [ErrorCode.BUSINESS_RULE_VIOLATION]: { message: 'Business rule violation', httpStatus: 422, category: ErrorCategory.BUSINESS_LOGIC },
  [ErrorCode.INSUFFICIENT_FUNDS]: { message: 'Insufficient funds', httpStatus: 402, category: ErrorCategory.BUSINESS_LOGIC },
  [ErrorCode.QUOTA_EXCEEDED]: { message: 'Quota exceeded', httpStatus: 429, category: ErrorCategory.BUSINESS_LOGIC },
  [ErrorCode.WORKFLOW_ERROR]: { message: 'Workflow error', httpStatus: 422, category: ErrorCategory.BUSINESS_LOGIC },
  [ErrorCode.STATE_TRANSITION_ERROR]: { message: 'Invalid state transition', httpStatus: 422, category: ErrorCategory.BUSINESS_LOGIC },

  // Calculation errors
  [ErrorCode.CALCULATION_ERROR]: { message: 'Calculation error', httpStatus: 422, category: ErrorCategory.CALCULATION },
  [ErrorCode.INVALID_CALCULATION_INPUT]: { message: 'Invalid calculation input', httpStatus: 400, category: ErrorCategory.CALCULATION },
  [ErrorCode.CALCULATION_OVERFLOW]: { message: 'Calculation overflow', httpStatus: 422, category: ErrorCategory.CALCULATION },
  [ErrorCode.NEC_VIOLATION]: { message: 'NEC code violation', httpStatus: 422, category: ErrorCategory.CALCULATION },
  [ErrorCode.LOAD_EXCEEDS_CAPACITY]: { message: 'Load exceeds capacity', httpStatus: 422, category: ErrorCategory.CALCULATION },
  [ErrorCode.VOLTAGE_DROP_EXCEEDED]: { message: 'Voltage drop exceeds limits', httpStatus: 422, category: ErrorCategory.CALCULATION },
  [ErrorCode.ARC_FLASH_HAZARD]: { message: 'Arc flash hazard detected', httpStatus: 422, category: ErrorCategory.CALCULATION },

  // External service errors
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: { message: 'External service error', httpStatus: 502, category: ErrorCategory.EXTERNAL_SERVICE },
  [ErrorCode.API_RATE_LIMIT]: { message: 'API rate limit exceeded', httpStatus: 429, category: ErrorCategory.EXTERNAL_SERVICE },
  [ErrorCode.THIRD_PARTY_ERROR]: { message: 'Third party service error', httpStatus: 502, category: ErrorCategory.EXTERNAL_SERVICE },
  [ErrorCode.PAYMENT_FAILED]: { message: 'Payment processing failed', httpStatus: 402, category: ErrorCategory.EXTERNAL_SERVICE },
  [ErrorCode.EMAIL_SEND_FAILED]: { message: 'Failed to send email', httpStatus: 502, category: ErrorCategory.EXTERNAL_SERVICE },
  [ErrorCode.SMS_SEND_FAILED]: { message: 'Failed to send SMS', httpStatus: 502, category: ErrorCategory.EXTERNAL_SERVICE },

  // System errors
  [ErrorCode.INTERNAL_ERROR]: { message: 'Internal server error', httpStatus: 500, category: ErrorCategory.SYSTEM },
  [ErrorCode.SERVICE_UNAVAILABLE]: { message: 'Service unavailable', httpStatus: 503, category: ErrorCategory.SYSTEM },
  [ErrorCode.CONFIGURATION_ERROR]: { message: 'Configuration error', httpStatus: 500, category: ErrorCategory.SYSTEM },
  [ErrorCode.DEPENDENCY_ERROR]: { message: 'Dependency error', httpStatus: 500, category: ErrorCategory.SYSTEM },
  [ErrorCode.MEMORY_ERROR]: { message: 'Memory error', httpStatus: 500, category: ErrorCategory.SYSTEM },
  [ErrorCode.FILE_SYSTEM_ERROR]: { message: 'File system error', httpStatus: 500, category: ErrorCategory.SYSTEM },
  [ErrorCode.NETWORK_ERROR]: { message: 'Network error', httpStatus: 503, category: ErrorCategory.SYSTEM },
};

// Helper function to get error details
export function getErrorDetails(code: ErrorCode): { message: string; httpStatus: number; category: ErrorCategory } {
  return ErrorMetadata[code] || {
    message: 'Unknown error',
    httpStatus: 500,
    category: ErrorCategory.SYSTEM,
  };
}