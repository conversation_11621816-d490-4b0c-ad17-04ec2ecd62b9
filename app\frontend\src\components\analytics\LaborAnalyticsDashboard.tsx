import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  AreaChart,
  Area,
  ComposedChart,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import type { LaborAnalytics } from '../../services/analyticsService';
import { format } from 'date-fns';
import { Users, Clock, TrendingUp, Award, AlertTriangle, DollarSign } from 'lucide-react';

interface LaborAnalyticsDashboardProps {
  dateRange: { start: Date; end: Date };
}

const LaborAnalyticsDashboard: React.FC<LaborAnalyticsDashboardProps> = ({ dateRange }) => {
  const [selectedTrade, setSelectedTrade] = useState<string>('all');

  // Fetch labor analytics
  const { data: laborData } = useQuery({
    queryKey: ['labor-analytics', dateRange],
    queryFn: () => analyticsService.getLaborAnalytics(dateRange),
  });

  // Fetch productivity metrics
  const { data: productivityData } = useQuery({
    queryKey: ['productivity-metrics', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { week: 'W1', productivity: 85, target: 90 },
        { week: 'W2', productivity: 88, target: 90 },
        { week: 'W3', productivity: 92, target: 90 },
        { week: 'W4', productivity: 87, target: 90 },
        { week: 'W5', productivity: 91, target: 90 },
        { week: 'W6', productivity: 94, target: 90 },
      ];
    },
  });

  // Fetch overtime trends
  const { data: overtimeData } = useQuery({
    queryKey: ['overtime-trends', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { month: 'Jan', regular: 3200, overtime: 280 },
        { month: 'Feb', regular: 3100, overtime: 320 },
        { month: 'Mar', regular: 3400, overtime: 250 },
        { month: 'Apr', regular: 3300, overtime: 380 },
        { month: 'May', regular: 3500, overtime: 300 },
        { month: 'Jun', regular: 3600, overtime: 340 },
      ];
    },
  });

  // Fetch skill utilization
  const { data: skillData } = useQuery({
    queryKey: ['skill-utilization', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { skill: 'Panel Installation', utilization: 92, certified: 15 },
        { skill: 'Conduit Bending', utilization: 88, certified: 18 },
        { skill: 'Motor Control', utilization: 75, certified: 12 },
        { skill: 'PLC Programming', utilization: 65, certified: 8 },
        { skill: 'Arc Flash Safety', utilization: 95, certified: 22 },
        { skill: 'Solar Installation', utilization: 70, certified: 10 },
      ];
    },
  });

  // Fetch certification tracking
  const { data: certificationData } = useQuery({
    queryKey: ['certification-tracking', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { certification: 'Journeyman License', total: 18, expiringSoon: 3 },
        { certification: 'Master Electrician', total: 8, expiringSoon: 1 },
        { certification: 'OSHA 30', total: 22, expiringSoon: 5 },
        { certification: 'Arc Flash NFPA 70E', total: 20, expiringSoon: 4 },
        { certification: 'First Aid/CPR', total: 25, expiringSoon: 8 },
      ];
    },
  });

  // Fetch labor cost breakdown
  const { data: costBreakdown } = useQuery({
    queryKey: ['labor-cost-breakdown', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { category: 'Regular Wages', value: 185000 },
        { category: 'Overtime', value: 28000 },
        { category: 'Benefits', value: 42000 },
        { category: 'Payroll Taxes', value: 18000 },
        { category: 'Workers Comp', value: 12000 },
        { category: 'Training', value: 8000 },
      ];
    },
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const MetricCard = ({ title, value, subtitle, icon: Icon, trend }: any) => (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        </div>
      </div>
      {trend && (
        <div className="mt-2 flex items-center">
          <TrendingUp className={`h-4 w-4 ${trend > 0 ? 'text-green-500' : 'text-red-500'} mr-1`} />
          <span className={`text-sm ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend > 0 ? '+' : ''}{trend}%
          </span>
        </div>
      )}
    </Card>
  );

  // Calculate summary metrics
  const totalHours = laborData?.reduce((sum, emp) => sum + emp.hoursWorked, 0) || 0;
  const totalOvertimeHours = laborData?.reduce((sum, emp) => sum + emp.overtimeHours, 0) || 0;
  const avgProductivity = laborData?.reduce((sum, emp) => sum + emp.productivity, 0) / (laborData?.length || 1) || 0;
  const totalLaborCost = laborData?.reduce((sum, emp) => sum + emp.totalCost, 0) || 0;

  return (
    <div className="space-y-6">
      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Hours Worked"
          value={totalHours.toLocaleString()}
          subtitle="This period"
          icon={Clock}
          trend={5.2}
        />
        <MetricCard
          title="Overtime Hours"
          value={totalOvertimeHours.toLocaleString()}
          subtitle={`${((totalOvertimeHours / totalHours) * 100).toFixed(1)}% of total`}
          icon={AlertTriangle}
          trend={-3.1}
        />
        <MetricCard
          title="Average Productivity"
          value={`${avgProductivity.toFixed(1)}%`}
          subtitle="vs 90% target"
          icon={TrendingUp}
          trend={2.5}
        />
        <MetricCard
          title="Total Labor Cost"
          value={formatCurrency(totalLaborCost)}
          subtitle="Including benefits"
          icon={DollarSign}
          trend={4.8}
        />
      </div>

      {/* Employee Productivity Ranking */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Employee Productivity Metrics
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hours Worked
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Regular/OT
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Productivity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost/Hour
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Cost
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {laborData?.slice(0, 10).map((employee) => (
                <tr key={employee.employeeId}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {employee.employeeName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {employee.hoursWorked.toFixed(1)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {employee.regularHours.toFixed(1)} / {employee.overtimeHours.toFixed(1)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${employee.productivity}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {employee.productivity}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatCurrency(employee.costPerHour)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {formatCurrency(employee.totalCost)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Productivity Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Weekly Productivity Trend
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={productivityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="week" />
              <YAxis domain={[70, 100]} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="productivity"
                stroke="#3B82F6"
                strokeWidth={2}
                name="Actual"
              />
              <Line
                type="monotone"
                dataKey="target"
                stroke="#10B981"
                strokeDasharray="5 5"
                name="Target"
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>

        {/* Overtime Analysis */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Regular vs Overtime Hours
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={overtimeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => `${value} hours`} />
              <Legend />
              <Area
                type="monotone"
                dataKey="regular"
                stackId="1"
                stroke="#3B82F6"
                fill="#3B82F6"
                name="Regular Hours"
              />
              <Area
                type="monotone"
                dataKey="overtime"
                stackId="1"
                stroke="#F59E0B"
                fill="#F59E0B"
                name="Overtime Hours"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Skill Utilization */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Skill Utilization Rates
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="80%" data={skillData}>
              <RadialBar
                minAngle={15}
                background
                clockWise
                dataKey="utilization"
                fill="#8884d8"
              />
              <Legend
                iconSize={10}
                layout="vertical"
                verticalAlign="middle"
                align="right"
                formatter={(value, entry) => `${entry.payload.skill}: ${value}%`}
              />
              <Tooltip />
            </RadialBarChart>
          </ResponsiveContainer>
        </Card>

        {/* Labor Cost Breakdown */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Labor Cost Breakdown
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={costBreakdown}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {costBreakdown?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(value as number)} />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Certification Tracking */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Certification Status
        </h3>
        <div className="space-y-4">
          {certificationData?.map((cert) => (
            <div key={cert.certification} className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {cert.certification}
                </p>
                <div className="mt-1 flex items-center">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-4">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${((cert.total - cert.expiringSoon) / cert.total) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {cert.total - cert.expiringSoon} / {cert.total} current
                  </span>
                </div>
              </div>
              {cert.expiringSoon > 0 && (
                <div className="ml-4 flex items-center text-yellow-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  <span className="text-sm">{cert.expiringSoon} expiring soon</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default LaborAnalyticsDashboard;