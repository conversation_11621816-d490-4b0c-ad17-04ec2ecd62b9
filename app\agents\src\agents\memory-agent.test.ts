import { MemoryAgent, KnowledgeCategory } from './memory-agent';
import { MemoryStore } from '../infrastructure/memory-store';
import { MessageBus } from '../infrastructure/message-bus';

describe('MemoryAgent', () => {
  let memoryAgent: MemoryAgent;
  let memoryStore: MemoryStore;

  beforeEach(() => {
    // Clear singleton instance for tests
    (MessageBus as any).instance = null;
    
    memoryStore = new MemoryStore('./test-data/memory');
    memoryAgent = new MemoryAgent({
      id: 'memory-agent-test',
      name: 'Test Memory Agent',
      description: 'Memory agent for testing',
      memoryStore,
    });
  });

  afterEach(async () => {
    await memoryAgent.shutdown();
  });

  describe('Knowledge Storage and Retrieval', () => {
    it('should store electrical knowledge with categorization', async () => {
      const knowledge = {
        content: {
          rule: 'NEC 210.52(A)',
          description: 'Receptacle outlets shall be installed so that no point measured horizontally along the floor line of any wall space is more than 6 feet from a receptacle outlet.',
          violations: ['Missing outlets in living areas', 'Outlets spaced more than 12 feet apart'],
        },
        category: KnowledgeCategory.NEC_VIOLATIONS,
        tags: ['NEC', '210.52', 'receptacles', 'spacing'],
        importance: 0.9,
        relatedConcepts: ['outlet spacing', 'wall receptacles', 'residential wiring'],
      };

      const result = await memoryAgent['processTask']('store-knowledge', knowledge);
      
      expect(result.success).toBe(true);
      expect(result.memoryId).toBeDefined();
    });

    it('should retrieve knowledge using semantic search', async () => {
      // First store some knowledge
      const knowledgeItems = [
        {
          content: { topic: 'Wire sizing for 30A circuit', ampacity: 30, wireSize: '10 AWG' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['wire-sizing', 'ampacity', '30A'],
          importance: 0.8,
        },
        {
          content: { topic: 'Wire sizing for 20A circuit', ampacity: 20, wireSize: '12 AWG' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['wire-sizing', 'ampacity', '20A'],
          importance: 0.8,
        },
        {
          content: { topic: 'Conduit fill calculation', rule: 'NEC Chapter 9', maxFill: '40%' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['conduit-fill', 'NEC'],
          importance: 0.7,
        },
      ];

      for (const item of knowledgeItems) {
        await memoryAgent['processTask']('store-knowledge', item);
      }

      // Search for wire sizing information
      const results = await memoryAgent['processTask']('retrieve-knowledge', {
        query: 'wire size for 30 amp circuit',
        category: KnowledgeCategory.CALCULATION_PATTERNS,
        limit: 5,
      });

      expect(results.length).toBeGreaterThan(0);
      expect(results[0].content.wireSize).toBe('10 AWG');
    });
  });

  describe('Pattern Recognition', () => {
    it('should identify recurring calculation patterns', async () => {
      // Store similar calculations multiple times
      const calculations = [
        { voltage: 240, current: 30, power: 7200, formula: 'P = V × I' },
        { voltage: 240, current: 20, power: 4800, formula: 'P = V × I' },
        { voltage: 240, current: 50, power: 12000, formula: 'P = V × I' },
        { voltage: 120, current: 15, power: 1800, formula: 'P = V × I' },
      ];

      for (const calc of calculations) {
        await memoryAgent['processTask']('store-knowledge', {
          content: calc,
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['power-calculation', 'ohms-law'],
          importance: 0.6,
        });
      }

      const patterns = await memoryAgent['processTask']('find-patterns', {
        category: KnowledgeCategory.CALCULATION_PATTERNS,
        minConfidence: 0.3,
        includeRelated: true,
      });

      expect(patterns.length).toBeGreaterThan(0);
      expect(patterns[0].category).toBe(KnowledgeCategory.CALCULATION_PATTERNS);
      expect(patterns[0].occurrences).toBeGreaterThanOrEqual(3);
    });
  });

  describe('Error Learning', () => {
    it('should learn from electrical calculation errors', async () => {
      const error = {
        errorType: 'wire-sizing-underrated',
        context: {
          circuit: '30A',
          selectedWire: '14 AWG',
          correctWire: '10 AWG',
          reason: 'Wire gauge too small for ampacity',
        },
        correctSolution: {
          rule: 'NEC 310.16',
          wireSize: '10 AWG',
          ampacity: 30,
        },
        category: KnowledgeCategory.COMMON_MISTAKES,
      };

      const result = await memoryAgent['processTask']('learn-from-errors', error);
      
      expect(result.learned).toBe(true);
      
      // Store more similar errors to trigger pattern detection
      for (let i = 0; i < 3; i++) {
        await memoryAgent['processTask']('learn-from-errors', {
          ...error,
          context: { ...error.context, circuit: `${25 + i}A` },
        });
      }

      // Check if pattern was detected
      const patterns = await memoryAgent['processTask']('find-patterns', {
        category: KnowledgeCategory.COMMON_MISTAKES,
        minConfidence: 0.1,
      });

      expect(patterns.some(p => p.pattern.includes('wire-sizing-underrated'))).toBe(true);
    });
  });

  describe('Memory Consolidation', () => {
    it('should consolidate important short-term memories to long-term', async () => {
      // Store some short-term memories with high importance
      const importantMemories = [
        {
          content: { rule: 'Critical safety violation', severity: 'high' },
          category: KnowledgeCategory.NEC_VIOLATIONS,
          tags: ['safety', 'critical'],
          importance: 0.95,
        },
        {
          content: { pattern: 'Frequently used calculation', usage: 'daily' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['common', 'daily'],
          importance: 0.85,
        },
      ];

      for (const memory of importantMemories) {
        await memoryAgent['processTask']('store-knowledge', memory);
      }

      // Trigger consolidation
      const result = await memoryAgent['processTask']('consolidate-memory', {
        threshold: 0.8,
        minAccessCount: 1,
        timeWindow: 1000 * 60 * 60 * 24 * 7, // 7 days
      });

      expect(result.consolidated).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Knowledge Graph Building', () => {
    it('should build knowledge graph for electrical concepts', async () => {
      // Store related electrical concepts
      const concepts = [
        {
          content: { concept: 'Circuit Breaker', type: 'protection', rating: '20A' },
          category: KnowledgeCategory.COMPONENT_SPECS,
          tags: ['protection', 'breaker'],
          importance: 0.8,
          relatedConcepts: ['overcurrent protection', 'wire sizing'],
        },
        {
          content: { concept: 'Wire Sizing', type: 'calculation', basis: 'ampacity' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['sizing', 'ampacity'],
          importance: 0.9,
          relatedConcepts: ['circuit breaker', 'voltage drop'],
        },
        {
          content: { concept: 'Voltage Drop', type: 'calculation', limit: '3%' },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['voltage', 'calculation'],
          importance: 0.8,
          relatedConcepts: ['wire sizing', 'circuit length'],
        },
      ];

      for (const concept of concepts) {
        await memoryAgent['processTask']('store-knowledge', concept);
      }

      const result = await memoryAgent['processTask']('build-knowledge-graph', {
        category: KnowledgeCategory.CALCULATION_PATTERNS,
      });

      expect(result.nodes).toBeGreaterThan(0);
      expect(result.connections).toBeGreaterThan(0);
    });
  });

  describe('Insights Generation', () => {
    it('should provide insights on electrical topics', async () => {
      // Store diverse knowledge about wire sizing
      const wireSizingKnowledge = [
        {
          content: {
            topic: 'Wire sizing basics',
            rule: 'Size wire based on ampacity from NEC 310.16',
            factors: ['ambient temperature', 'number of conductors', 'insulation type'],
          },
          category: KnowledgeCategory.BEST_PRACTICES,
          tags: ['wire-sizing', 'ampacity', 'NEC'],
          importance: 0.9,
        },
        {
          content: {
            topic: 'Common wire sizing mistakes',
            errors: ['ignoring derating factors', 'using wrong temperature column', 'not considering voltage drop'],
          },
          category: KnowledgeCategory.COMMON_MISTAKES,
          tags: ['wire-sizing', 'errors'],
          importance: 0.85,
        },
        {
          content: {
            topic: 'Wire sizing calculation pattern',
            steps: ['determine load current', 'apply derating factors', 'check voltage drop', 'select wire size'],
          },
          category: KnowledgeCategory.CALCULATION_PATTERNS,
          tags: ['wire-sizing', 'calculation'],
          importance: 0.88,
        },
      ];

      for (const knowledge of wireSizingKnowledge) {
        await memoryAgent['processTask']('store-knowledge', knowledge);
      }

      const insights = await memoryAgent['processTask']('get-insights', {
        topic: 'wire sizing',
        category: KnowledgeCategory.BEST_PRACTICES,
      });

      expect(insights.insights.length).toBeGreaterThan(0);
      expect(insights.relatedConcepts.length).toBeGreaterThan(0);
      expect(insights.confidence).toBeGreaterThan(0.5);
    });
  });

  describe('Memory Optimization', () => {
    it('should optimize memory by removing redundant entries', async () => {
      // Store redundant memories
      const redundantData = {
        content: { rule: 'Same NEC rule', description: 'Identical content' },
        category: KnowledgeCategory.NEC_VIOLATIONS,
        tags: ['redundant'],
        importance: 0.3,
      };

      // Store the same content multiple times
      for (let i = 0; i < 5; i++) {
        await memoryAgent['processTask']('store-knowledge', redundantData);
      }

      const result = await memoryAgent['processTask']('optimize-memory', {
        minImportance: 0.4,
        preserveCategories: [KnowledgeCategory.BEST_PRACTICES],
      });

      expect(result.removed).toBeGreaterThan(0);
    });
  });

  describe('Electrical Domain Specific Features', () => {
    it('should track NEC code violations patterns', async () => {
      const violations = [
        {
          content: {
            code: '210.52(A)',
            violation: 'Insufficient receptacle outlets',
            location: 'Living room',
            severity: 'moderate',
          },
          category: KnowledgeCategory.NEC_VIOLATIONS,
          tags: ['NEC', 'receptacles', 'residential'],
          importance: 0.8,
        },
        {
          content: {
            code: '210.52(A)',
            violation: 'Insufficient receptacle outlets',
            location: 'Bedroom',
            severity: 'moderate',
          },
          category: KnowledgeCategory.NEC_VIOLATIONS,
          tags: ['NEC', 'receptacles', 'residential'],
          importance: 0.8,
        },
      ];

      for (const violation of violations) {
        await memoryAgent['processTask']('store-knowledge', violation);
      }

      const insights = await memoryAgent['processTask']('get-insights', {
        topic: 'receptacle spacing violations',
        category: KnowledgeCategory.NEC_VIOLATIONS,
      });

      expect(insights.insights.some(i => i.includes('NEC Violation'))).toBe(true);
    });

    it('should track material pricing trends', async () => {
      const pricingData = [
        {
          content: {
            material: '12 AWG THHN',
            price: 0.45,
            unit: 'per foot',
            date: new Date('2024-01-01'),
            supplier: 'Supplier A',
          },
          category: KnowledgeCategory.MATERIAL_PRICING,
          tags: ['wire', '12AWG', 'pricing'],
          importance: 0.7,
        },
        {
          content: {
            material: '12 AWG THHN',
            price: 0.48,
            unit: 'per foot',
            date: new Date('2024-02-01'),
            supplier: 'Supplier A',
          },
          category: KnowledgeCategory.MATERIAL_PRICING,
          tags: ['wire', '12AWG', 'pricing'],
          importance: 0.7,
        },
      ];

      for (const pricing of pricingData) {
        await memoryAgent['processTask']('store-knowledge', pricing);
      }

      const patterns = await memoryAgent['processTask']('find-patterns', {
        category: KnowledgeCategory.MATERIAL_PRICING,
        minConfidence: 0.1,
      });

      expect(patterns.length).toBeGreaterThanOrEqual(0);
    });

    it('should store regional code variations', async () => {
      const regionalCodes = [
        {
          content: {
            region: 'California',
            code: 'Title 24',
            requirement: 'Additional energy efficiency requirements',
            necAddendum: true,
          },
          category: KnowledgeCategory.REGIONAL_CODES,
          tags: ['California', 'Title24', 'energy'],
          importance: 0.85,
        },
        {
          content: {
            region: 'Chicago',
            code: 'Chicago Electrical Code',
            requirement: 'Conduit required for all wiring',
            necAddendum: true,
          },
          category: KnowledgeCategory.REGIONAL_CODES,
          tags: ['Chicago', 'conduit', 'local-code'],
          importance: 0.85,
        },
      ];

      for (const code of regionalCodes) {
        await memoryAgent['processTask']('store-knowledge', code);
      }

      const results = await memoryAgent['processTask']('retrieve-knowledge', {
        query: 'Chicago electrical requirements',
        category: KnowledgeCategory.REGIONAL_CODES,
        limit: 5,
      });

      expect(results.some(r => r.content.region === 'Chicago')).toBe(true);
    });
  });
});