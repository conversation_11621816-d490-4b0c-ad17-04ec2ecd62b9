import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Input,
  Button,
  Text,
  Heading,
  Icon,
  Pressable,
  FormControl,
  WarningOutlineIcon,
  Center,
  KeyboardAvoidingView,
  ScrollView,
  Progress,
  Checkbox,
} from 'native-base';
import { Platform } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useDispatch, useSelector } from 'react-redux';
import { AuthStackScreenProps } from '@types/navigation';
import { AppDispatch, RootState } from '@store/index';
import { showToast } from '@store/slices/uiSlice';
import { enhancedAuthService, PasswordStrength } from '@services/enhancedAuthService';

type Props = AuthStackScreenProps<'Register'>;

const EnhancedRegisterScreen: React.FC<Props> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.auth);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    company: '',
    licenseNumber: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    feedback: [],
    isStrong: false,
  });
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  useEffect(() => {
    if (formData.password) {
      const strength = enhancedAuthService.checkPasswordStrength(formData.password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ score: 0, feedback: [], isStrong: false });
    }
  }, [formData.password]);

  const validate = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name || formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (!passwordStrength.isStrong) {
      newErrors.password = passwordStrength.feedback[0] || 'Password is not strong enough';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (!formData.company || formData.company.trim().length < 2) {
      newErrors.company = 'Company name is required';
    }
    
    if (!formData.licenseNumber || formData.licenseNumber.trim().length < 4) {
      newErrors.licenseNumber = 'Valid license number is required';
    }
    
    if (!agreedToTerms) {
      newErrors.terms = 'You must agree to the terms and conditions';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validate()) return;

    try {
      await enhancedAuthService.initialize();
      const response = await enhancedAuthService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        company: formData.company,
        licenseNumber: formData.licenseNumber,
      });
      
      dispatch(showToast({ 
        message: 'Registration successful! Setting up security...', 
        type: 'success' 
      }));
      
      // Navigate to PIN setup
      navigation.navigate('PinSetup' as never);
    } catch (error: any) {
      dispatch(showToast({ 
        message: error.message || 'Registration failed. Please try again.', 
        type: 'error' 
      }));
    }
  };

  const getPasswordStrengthColor = () => {
    switch (passwordStrength.score) {
      case 0:
      case 1:
        return 'error.500';
      case 2:
        return 'warning.500';
      case 3:
        return 'info.500';
      case 4:
        return 'success.500';
      default:
        return 'gray.300';
    }
  };

  const getPasswordStrengthLabel = () => {
    switch (passwordStrength.score) {
      case 0:
      case 1:
        return 'Weak';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Strong';
      default:
        return '';
    }
  };

  return (
    <KeyboardAvoidingView
      flex={1}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <Center flex={1} px={4} bg="white">
          <VStack space={4} w="100%" maxW="350" alignItems="center" py={8}>
            <VStack space={2} alignItems="center" mb={4}>
              <Icon as={MaterialIcons} name="electric-bolt" size={12} color="primary.500" />
              <Heading size="lg" color="primary.600">
                Create Account
              </Heading>
              <Text fontSize="md" color="gray.600" textAlign="center">
                Join the professional electrical contractor network
              </Text>
            </VStack>

            <VStack space={3} w="100%">
              {/* Personal Information */}
              <FormControl isInvalid={'name' in errors}>
                <FormControl.Label>Full Name</FormControl.Label>
                <Input
                  placeholder="John Doe"
                  value={formData.name}
                  onChangeText={value => setFormData({ ...formData, name: value })}
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="person" size={5} ml={2} color="muted.400" />
                  }
                />
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.name}
                </FormControl.ErrorMessage>
              </FormControl>

              <FormControl isInvalid={'email' in errors}>
                <FormControl.Label>Email</FormControl.Label>
                <Input
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChangeText={value => setFormData({ ...formData, email: value })}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="email" size={5} ml={2} color="muted.400" />
                  }
                />
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.email}
                </FormControl.ErrorMessage>
              </FormControl>

              {/* Password with strength indicator */}
              <FormControl isInvalid={'password' in errors}>
                <FormControl.Label>Password</FormControl.Label>
                <Input
                  placeholder="Create strong password"
                  value={formData.password}
                  onChangeText={value => setFormData({ ...formData, password: value })}
                  type={showPassword ? 'text' : 'password'}
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="lock" size={5} ml={2} color="muted.400" />
                  }
                  InputRightElement={
                    <Pressable onPress={() => setShowPassword(!showPassword)}>
                      <Icon
                        as={MaterialIcons}
                        name={showPassword ? 'visibility' : 'visibility-off'}
                        size={5}
                        mr={2}
                        color="muted.400"
                      />
                    </Pressable>
                  }
                />
                {formData.password && (
                  <VStack space={1} mt={2}>
                    <HStack justifyContent="space-between" alignItems="center">
                      <Text fontSize="xs" color="gray.600">
                        Password Strength:
                      </Text>
                      <Text fontSize="xs" color={getPasswordStrengthColor()} fontWeight="medium">
                        {getPasswordStrengthLabel()}
                      </Text>
                    </HStack>
                    <Progress
                      value={passwordStrength.score * 25}
                      colorScheme={
                        passwordStrength.score <= 1 ? 'error' :
                        passwordStrength.score === 2 ? 'warning' :
                        passwordStrength.score === 3 ? 'info' : 'success'
                      }
                      size="xs"
                    />
                    {passwordStrength.feedback.length > 0 && (
                      <VStack space={1} mt={1}>
                        {passwordStrength.feedback.map((feedback, index) => (
                          <Text key={index} fontSize="xs" color="gray.600">
                            • {feedback}
                          </Text>
                        ))}
                      </VStack>
                    )}
                  </VStack>
                )}
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.password}
                </FormControl.ErrorMessage>
              </FormControl>

              <FormControl isInvalid={'confirmPassword' in errors}>
                <FormControl.Label>Confirm Password</FormControl.Label>
                <Input
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChangeText={value => setFormData({ ...formData, confirmPassword: value })}
                  type={showConfirmPassword ? 'text' : 'password'}
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="lock-outline" size={5} ml={2} color="muted.400" />
                  }
                  InputRightElement={
                    <Pressable onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                      <Icon
                        as={MaterialIcons}
                        name={showConfirmPassword ? 'visibility' : 'visibility-off'}
                        size={5}
                        mr={2}
                        color="muted.400"
                      />
                    </Pressable>
                  }
                />
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.confirmPassword}
                </FormControl.ErrorMessage>
              </FormControl>

              {/* Professional Information */}
              <Box borderTopWidth={1} borderColor="gray.200" pt={3} mt={2}>
                <Text fontSize="sm" fontWeight="medium" color="gray.700" mb={3}>
                  Professional Information
                </Text>
                
                <FormControl isInvalid={'company' in errors} mb={3}>
                  <FormControl.Label>Company Name</FormControl.Label>
                  <Input
                    placeholder="ABC Electrical Services"
                    value={formData.company}
                    onChangeText={value => setFormData({ ...formData, company: value })}
                    InputLeftElement={
                      <Icon as={MaterialIcons} name="business" size={5} ml={2} color="muted.400" />
                    }
                  />
                  <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                    {errors.company}
                  </FormControl.ErrorMessage>
                </FormControl>

                <FormControl isInvalid={'licenseNumber' in errors}>
                  <FormControl.Label>License Number</FormControl.Label>
                  <Input
                    placeholder="EC-123456"
                    value={formData.licenseNumber}
                    onChangeText={value => setFormData({ ...formData, licenseNumber: value })}
                    InputLeftElement={
                      <Icon as={MaterialIcons} name="badge" size={5} ml={2} color="muted.400" />
                    }
                  />
                  <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                    {errors.licenseNumber}
                  </FormControl.ErrorMessage>
                </FormControl>
              </Box>

              {/* Terms and Conditions */}
              <FormControl isInvalid={'terms' in errors} mt={2}>
                <Checkbox
                  value="terms"
                  isChecked={agreedToTerms}
                  onChange={setAgreedToTerms}
                  colorScheme="primary"
                >
                  <Text fontSize="sm" color="gray.600">
                    I agree to the{' '}
                    <Text color="primary.500" onPress={() => navigation.navigate('Terms' as never)}>
                      Terms and Conditions
                    </Text>
                    {' '}and{' '}
                    <Text color="primary.500" onPress={() => navigation.navigate('Privacy' as never)}>
                      Privacy Policy
                    </Text>
                  </Text>
                </Checkbox>
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.terms}
                </FormControl.ErrorMessage>
              </FormControl>

              <Button
                mt={4}
                onPress={handleRegister}
                isLoading={isLoading}
                isLoadingText="Creating account..."
                size="lg"
              >
                Create Account
              </Button>
            </VStack>

            <HStack space={2} mt={4}>
              <Text color="gray.600">Already have an account?</Text>
              <Pressable onPress={() => navigation.navigate('Login')}>
                <Text color="primary.500" fontWeight="medium">
                  Sign In
                </Text>
              </Pressable>
            </HStack>
          </VStack>
        </Center>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EnhancedRegisterScreen;