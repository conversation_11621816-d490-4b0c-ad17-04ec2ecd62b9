import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
import { StructuredError, ErrorFactory } from '../utils/structured-error';
import { ErrorCode } from '../utils/error-codes';
import { EnhancedLogger, enhancedLogger } from '../utils/enhanced-logger';

interface ErrorResponse {
  error: {
    id: string;
    code: string;
    message: string;
    details?: Array<{
      field?: string;
      message: string;
    }>;
    timestamp?: string;
    correlationId?: string;
    requestId?: string;
  };
}

export function enhancedErrorHandler(
  err: Error | StructuredError,
  req: Request,
  res: Response,
  _next: NextFunction
): void {
  // Get logger from request or create new one
  const logger: EnhancedLogger = (req as any).logger || enhancedLogger.fromRequest(req);
  
  // Get correlation and request IDs
  const correlationId = (req as any).correlationId || req.headers['x-correlation-id'] as string;
  const requestId = (req as any).requestId;

  let structuredError: StructuredError;

  // Convert various error types to StructuredError
  if (err instanceof StructuredError) {
    structuredError = err;
  } else if (err instanceof ZodError) {
    // Handle Zod validation errors
    const details = err.errors.map(e => ({
      field: e.path.join('.'),
      value: e.code,
      constraint: e.message,
      suggestion: `Please provide a valid ${e.path.join('.')}`,
    }));

    structuredError = new StructuredError(
      ErrorCode.INVALID_INPUT,
      'Validation failed',
      {
        details,
        context: { correlationId, requestId },
        userMessage: 'Please check your input and try again.',
      }
    );
  } else if (err instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle Prisma errors
    structuredError = handlePrismaError(err, { correlationId, requestId });
  } else if (err instanceof Prisma.PrismaClientValidationError) {
    structuredError = new StructuredError(
      ErrorCode.INVALID_INPUT,
      'Invalid data provided',
      {
        context: { correlationId, requestId },
        userMessage: 'The provided data is invalid. Please check your input.',
      }
    );
  } else if (err instanceof Prisma.PrismaClientInitializationError) {
    structuredError = new StructuredError(
      ErrorCode.DATABASE_CONNECTION_FAILED,
      'Database initialization failed',
      {
        cause: err,
        context: { correlationId, requestId },
        isOperational: false,
        userMessage: 'Unable to connect to the database. Please try again later.',
      }
    );
  } else {
    // Handle generic errors
    structuredError = ErrorFactory.internal(err, 'request processing', {
      correlationId,
      requestId,
    });
  }

  // Log the error
  if (structuredError.shouldAlert()) {
    logger.fatal('Critical error occurred', { error: structuredError.toJSON() });
    // TODO: Send alert to monitoring service
  } else if (structuredError.isOperational) {
    logger.warn('Operational error occurred', { error: structuredError.toJSON() });
  } else {
    logger.error('Unexpected error occurred', structuredError);
  }

  // Prepare response
  const errorResponse: ErrorResponse = {
    error: {
      id: structuredError.id,
      code: structuredError.code,
      message: structuredError.userMessage,
      ...(correlationId && { correlationId }),
      ...(requestId && { requestId }),
    },
  };

  // Add details if available
  if (structuredError.details && structuredError.details.length > 0) {
    errorResponse.error.details = structuredError.details.map(d => ({
      field: d.field,
      message: d.suggestion || d.constraint || 'Invalid value',
    }));
  }

  // Add timestamp in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.timestamp = structuredError.timestamp.toISOString();
  }

  // Set response headers
  res.setHeader('X-Error-ID', structuredError.id);
  if (correlationId) {
    res.setHeader('X-Correlation-ID', correlationId);
  }
  if (requestId) {
    res.setHeader('X-Request-ID', requestId);
  }

  // Set retry-after header for rate limit errors
  if (structuredError.code === ErrorCode.API_RATE_LIMIT && structuredError.details?.[0]?.value) {
    res.setHeader('Retry-After', structuredError.details[0].value.toString());
  }

  // Send response
  res.status(structuredError.statusCode).json(errorResponse);
}

/**
 * Handle Prisma-specific errors
 */
function handlePrismaError(
  err: Prisma.PrismaClientKnownRequestError,
  context: { correlationId?: string; requestId?: string }
): StructuredError {
  switch (err.code) {
    case 'P2002': {
      // Unique constraint violation
      const target = err.meta?.target as string[] | undefined;
      const field = target?.[0] || 'field';
      return new StructuredError(
        ErrorCode.DUPLICATE_RECORD,
        `Duplicate value for ${field}`,
        {
          details: [{ field, constraint: 'unique' }],
          context,
          userMessage: `A record with this ${field} already exists.`,
        }
      );
    }

    case 'P2003': {
      // Foreign key constraint violation
      const field = err.meta?.field_name as string | undefined;
      return new StructuredError(
        ErrorCode.CONSTRAINT_VIOLATION,
        `Invalid reference for ${field || 'foreign key'}`,
        {
          details: field ? [{ field, constraint: 'foreign_key' }] : undefined,
          context,
          userMessage: 'Referenced record does not exist.',
        }
      );
    }

    case 'P2025': {
      // Record not found
      return new StructuredError(
        ErrorCode.RECORD_NOT_FOUND,
        'Record not found',
        {
          context,
          userMessage: 'The requested record was not found.',
        }
      );
    }

    case 'P2014': {
      // Relation violation
      return new StructuredError(
        ErrorCode.CONSTRAINT_VIOLATION,
        'Relation constraint violation',
        {
          context,
          userMessage: 'This operation would violate a data relationship.',
        }
      );
    }

    case 'P2024': {
      // Timed out fetching a new connection from the pool
      return new StructuredError(
        ErrorCode.DATABASE_CONNECTION_FAILED,
        'Database connection timeout',
        {
          cause: err,
          context,
          isOperational: false,
          userMessage: 'Database is currently unavailable. Please try again.',
        }
      );
    }

    default: {
      // Generic database error
      return new StructuredError(
        ErrorCode.DATABASE_CONNECTION_FAILED,
        `Database error: ${err.code}`,
        {
          cause: err,
          context,
          isOperational: false,
          userMessage: 'A database error occurred. Please try again.',
        }
      );
    }
  }
}

/**
 * Not found handler
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new StructuredError(
    ErrorCode.RECORD_NOT_FOUND,
    `Route not found: ${req.method} ${req.path}`,
    {
      context: {
        method: req.method,
        path: req.path,
        correlationId: (req as any).correlationId,
        requestId: (req as any).requestId,
      },
      userMessage: 'The requested endpoint does not exist.',
    }
  );
  next(error);
}

/**
 * Async error wrapper
 */
export function asyncErrorHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}