import { ProjectManagerAgent } from './agents/project-manager-agent';
import { BackendDatabaseAgent } from './agents/backend-database-agent';
import { ResearchAgent } from './agents/research-agent';
import { DebuggingAgent } from './agents/debugging-agent';
import { CodingAgent } from './agents/coding-agent';
import { FrontendAgent } from './agents/frontend-agent';
import { UIDesignerAgent } from './agents/ui-designer-agent';
import { PromptEngineeringAgent } from './agents/prompt-engineering-agent';
import { MessageBus } from './infrastructure/message-bus';
import { MemoryStore } from './infrastructure/memory-store';

// Export all components
export { MessageBus } from './infrastructure/message-bus';
export { MemoryStore, MemoryType } from './infrastructure/memory-store';
export { BaseAgent } from './base/base-agent';
export * from './agents/project-manager-agent';
export * from './agents/backend-database-agent';
export * from './agents/research-agent';
export * from './agents/debugging-agent';
export * from './agents/coding-agent';
export * from './agents/frontend-agent';
export * from './agents/ui-designer-agent';
export * from './agents/prompt-engineering-agent';

// Agent system configuration
export interface AgentSystemConfig {
  enabledAgents?: string[];
  memoryPath?: string;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

// Agent system class
export class AgentSystem {
  private agents: Map<string, any> = new Map();
  private messageBus: MessageBus;
  private memoryStore: MemoryStore;
  private config: AgentSystemConfig;

  constructor(config: AgentSystemConfig = {}) {
    this.config = {
      enabledAgents: ['project-manager', 'backend', 'research', 'debugging', 'coding', 'frontend', 'ui-designer', 'prompt-engineering'],
      memoryPath: './data/memory/system',
      logLevel: 'info',
      ...config,
    };

    this.messageBus = MessageBus.getInstance();
    this.memoryStore = new MemoryStore(this.config.memoryPath);
  }

  // Initialize the agent system
  async initialize(): Promise<void> {
    console.log('Initializing AI Agent System...');

    // Create shared memory store
    const sharedMemory = new MemoryStore('./data/memory/shared');

    // Initialize agents based on configuration
    if (this.config.enabledAgents?.includes('project-manager')) {
      const projectManager = new ProjectManagerAgent({
        id: 'pm-001',
        name: 'Project Manager',
        type: 'project-manager',
        description: 'Orchestrates tasks and coordinates other agents',
        memoryStore: sharedMemory,
      });
      this.agents.set('project-manager', projectManager);
    }

    if (this.config.enabledAgents?.includes('backend')) {
      const backend = new BackendDatabaseAgent({
        id: 'db-001',
        name: 'Backend Database Agent',
        type: 'backend-database',
        description: 'Handles data operations and calculations',
        memoryStore: sharedMemory,
      });
      this.agents.set('backend', backend);
    }

    if (this.config.enabledAgents?.includes('research')) {
      const research = new ResearchAgent({
        id: 'res-001',
        name: 'Research Agent',
        type: 'research',
        description: 'Researches NEC codes and material prices',
        memoryStore: sharedMemory,
      });
      this.agents.set('research', research);
    }

    if (this.config.enabledAgents?.includes('debugging')) {
      const debugging = new DebuggingAgent({
        id: 'debug-001',
        name: 'Debugging Agent',
        type: 'debugging',
        description: 'Diagnoses errors and ensures compliance',
        memoryStore: sharedMemory,
      });
      this.agents.set('debugging', debugging);
    }

    if (this.config.enabledAgents?.includes('coding')) {
      const coding = new CodingAgent({
        id: 'code-001',
        name: 'Coding Agent',
        type: 'coding',
        description: 'Generates and analyzes code',
        memoryStore: sharedMemory,
      });
      this.agents.set('coding', coding);
    }

    if (this.config.enabledAgents?.includes('frontend')) {
      const frontend = new FrontendAgent({
        id: 'fe-001',
        name: 'Frontend Agent',
        type: 'frontend',
        description: 'Manages UI state, components, and frontend optimizations',
        memoryStore: sharedMemory,
      });
      this.agents.set('frontend', frontend);
    }

    if (this.config.enabledAgents?.includes('ui-designer')) {
      const uiDesigner = new UIDesignerAgent({
        id: 'ui-001',
        name: 'UI Designer Agent',
        type: 'ui-designer',
        description: 'Designs UI components following electrical design system',
        memoryStore: sharedMemory,
      });
      this.agents.set('ui-designer', uiDesigner);
    }

    if (this.config.enabledAgents?.includes('prompt-engineering')) {
      const promptEngineering = new PromptEngineeringAgent({
        id: 'pe-001',
        name: 'Prompt Engineering Agent',
        type: 'prompt-engineering',
        description: 'Optimizes prompts for electrical domain AI interactions',
        memoryStore: sharedMemory,
      });
      this.agents.set('prompt-engineering', promptEngineering);
    }

    // Log system status
    console.log(`Agent System initialized with ${this.agents.size} agents`);
    this.agents.forEach((agent, key) => {
      console.log(`  - ${key}: ${agent.getInfo().name} (${agent.getInfo().state})`);
    });
  }

  // Get an agent by type
  getAgent(type: string): any {
    return this.agents.get(type);
  }

  // Get all agents
  getAllAgents(): Map<string, any> {
    return new Map(this.agents);
  }

  // Get system status
  getStatus(): any {
    const status = {
      agents: {} as Record<string, any>,
      messageBus: {
        registeredAgents: this.messageBus.getAgents().size,
      },
      memory: this.memoryStore.getStats(),
    };

    this.agents.forEach((agent, key) => {
      status.agents[key] = agent.getInfo();
    });

    return status;
  }

  // Request a workflow execution
  async executeWorkflow(workflowName: string, context: any): Promise<any> {
    const projectManager = this.agents.get('project-manager');
    if (!projectManager) {
      throw new Error('Project Manager agent not available');
    }

    return projectManager.processTask('orchestrate-workflow', {
      workflow: workflowName,
      context,
      priority: 'MEDIUM',
    });
  }

  // Shutdown the system
  async shutdown(): Promise<void> {
    console.log('Shutting down Agent System...');

    // Shutdown all agents
    const shutdownPromises = Array.from(this.agents.values()).map(agent => 
      agent.shutdown()
    );

    await Promise.all(shutdownPromises);

    console.log('Agent System shutdown complete');
  }
}

// Create and export default instance
let agentSystem: AgentSystem | null = null;

export function getAgentSystem(config?: AgentSystemConfig): AgentSystem {
  if (!agentSystem) {
    agentSystem = new AgentSystem(config);
  }
  return agentSystem;
}

// Auto-initialize if running as main module
if (require.main === module) {
  const system = getAgentSystem();
  
  system.initialize().then(() => {
    console.log('Agent System ready');
    
    // Example: Execute a workflow
    // system.executeWorkflow('create-estimate', {
    //   projectDescription: 'Install 200A panel upgrade for residential home',
    //   buildingType: 'DWELLING',
    //   squareFootage: 2500,
    // }).then(result => {
    //   console.log('Workflow completed:', result);
    // });
  }).catch(error => {
    console.error('Failed to initialize agent system:', error);
    process.exit(1);
  });

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    await system.shutdown();
    process.exit(0);
  });
}