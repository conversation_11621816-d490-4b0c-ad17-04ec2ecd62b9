import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import Decimal from 'decimal.js';
import { 
  COPPER_AMPACITY_75C, 
  GENERAL_LIGHTING_LOADS,
  COPPER_PROPERTIES,
  CONDUIT_AREA,
  CONDUCTOR_AREA
} from '@electrical/shared';

// Database operation schemas
const querySchema = z.object({
  entity: z.enum(['customer', 'project', 'estimate', 'material', 'calculation']),
  operation: z.enum(['find', 'findMany', 'create', 'update', 'delete']),
  where: z.record(z.any()).optional(),
  data: z.record(z.any()).optional(),
  include: z.record(z.any()).optional(),
  orderBy: z.record(z.any()).optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
});

const calculationSchema = z.object({
  type: z.enum(['load', 'voltage-drop', 'conduit-fill', 'wire-size']),
  parameters: z.record(z.any()),
});

const optimizationSchema = z.object({
  query: z.string(),
  explain: z.boolean().default(false),
});

export class BackendDatabaseAgent extends BaseAgent {
  private prisma: PrismaClient;
  private queryCache: Map<string, { result: any; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'execute-query',
        description: 'Execute database queries with Prisma',
        inputSchema: querySchema,
      },
      {
        name: 'calculate-electrical-loads',
        description: 'Perform NEC-compliant electrical load calculations',
        inputSchema: calculationSchema,
      },
      {
        name: 'optimize-query',
        description: 'Optimize database queries and suggest indexes',
        inputSchema: optimizationSchema,
      },
      {
        name: 'generate-estimate',
        description: 'Generate complete estimate with calculations',
      },
      {
        name: 'backup-database',
        description: 'Create database backup',
      },
      {
        name: 'analyze-schema',
        description: 'Analyze database schema and suggest improvements',
      },
    ];

    super({
      ...config,
      capabilities,
    });

    this.prisma = new PrismaClient({
      log: ['error', 'warn'],
    });
  }

  protected async onInitialize(): Promise<void> {
    // Test database connection
    try {
      await this.prisma.$connect();
      await this.log('Database connection established', { level: 'info' });
      
      // Load common queries into memory
      await this.preloadCommonQueries();
      
      // Set up periodic cache cleanup
      setInterval(() => this.cleanupCache(), this.cacheTimeout);
    } catch (error) {
      await this.log('Failed to connect to database', { 
        level: 'error', 
        error: (error as Error).message 
      });
      throw error;
    }
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'execute-query':
        return this.executeQuery(data);
      case 'calculate-electrical-loads':
        return this.calculateElectricalLoads(data);
      case 'optimize-query':
        return this.optimizeQuery(data);
      case 'generate-estimate':
        return this.generateEstimate(data);
      case 'backup-database':
        return this.backupDatabase();
      case 'analyze-schema':
        return this.analyzeSchema();
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Execute database query with caching
  private async executeQuery(data: z.infer<typeof querySchema>): Promise<any> {
    const { entity, operation, where, data: queryData, include, orderBy, take, skip } = data;
    
    // Generate cache key
    const cacheKey = JSON.stringify({ entity, operation, where, orderBy, take, skip });
    
    // Check cache for read operations
    if (operation === 'find' || operation === 'findMany') {
      const cached = this.queryCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        await this.log('Returning cached query result', { 
          level: 'debug', 
          entity, 
          operation 
        });
        return cached.result;
      }
    }
    
    try {
      let result: any;
      const model = (this.prisma as any)[entity];
      
      switch (operation) {
        case 'find':
          result = await model.findFirst({ where, include });
          break;
        case 'findMany':
          result = await model.findMany({ where, include, orderBy, take, skip });
          break;
        case 'create':
          result = await model.create({ data: queryData, include });
          this.invalidateCache(entity);
          break;
        case 'update':
          result = await model.update({ where, data: queryData, include });
          this.invalidateCache(entity);
          break;
        case 'delete':
          result = await model.delete({ where });
          this.invalidateCache(entity);
          break;
      }
      
      // Cache read results
      if (operation === 'find' || operation === 'findMany') {
        this.queryCache.set(cacheKey, { result, timestamp: Date.now() });
      }
      
      // Store query pattern in memory for optimization
      await this.storeKnowledge(
        { entity, operation, hasWhere: !!where, hasInclude: !!include },
        ['query', 'pattern', entity, operation],
        0.3
      );
      
      return result;
    } catch (error) {
      await this.log('Database query failed', {
        level: 'error',
        entity,
        operation,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  // Perform electrical load calculations
  private async calculateElectricalLoads(data: z.infer<typeof calculationSchema>): Promise<any> {
    const { type, parameters } = data;
    
    switch (type) {
      case 'load':
        return this.calculateLoadRequirements(parameters);
      case 'voltage-drop':
        return this.calculateVoltageDrop(parameters);
      case 'conduit-fill':
        return this.calculateConduitFill(parameters);
      case 'wire-size':
        return this.calculateWireSize(parameters);
      default:
        throw new Error(`Unknown calculation type: ${type}`);
    }
  }

  // NEC Article 220 Load Calculation
  private async calculateLoadRequirements(params: any): Promise<any> {
    const {
      building_type,
      square_footage,
      small_appliance_circuits = 2,
      laundry_circuit = true,
      heating_va = 0,
      cooling_va = 0,
      largest_motor_va = 0,
      other_loads_va = 0,
    } = params;

    // Step 1: General Lighting Load (NEC 220.12)
    const lightingVaPerSqFt = GENERAL_LIGHTING_LOADS[building_type as keyof typeof GENERAL_LIGHTING_LOADS];
    const generalLightingVa = new Decimal(square_footage).times(lightingVaPerSqFt);

    // Step 2: Small Appliance and Laundry Circuits (NEC 220.52)
    const smallApplianceVa = new Decimal(small_appliance_circuits).times(1500);
    const laundryVa = laundry_circuit ? new Decimal(1500) : new Decimal(0);

    // Step 3: Apply demand factors (NEC 220.42)
    let totalGeneralVa = generalLightingVa.plus(smallApplianceVa).plus(laundryVa);
    let demandVa = new Decimal(0);
    let demandFactorApplied = false;

    if (building_type === 'DWELLING') {
      // Dwelling unit demand factors
      if (totalGeneralVa.lte(3000)) {
        demandVa = totalGeneralVa;
      } else {
        demandVa = new Decimal(3000).plus(
          totalGeneralVa.minus(3000).times(0.35)
        );
        demandFactorApplied = true;
      }
    } else {
      // Non-dwelling demand factors vary by building type
      demandVa = totalGeneralVa.times(0.5); // Simplified for example
      demandFactorApplied = true;
    }

    // Step 4: Fixed Appliances (NEC 220.53)
    // Heating and cooling loads (larger of the two)
    const heatingCoolingVa = Decimal.max(heating_va, cooling_va);

    // Step 5: Motor loads (NEC 220.50)
    const motorVa = new Decimal(largest_motor_va);
    const motorAdditional = motorVa.times(0.25); // 25% of largest motor

    // Step 6: Calculate totals
    const totalComputedVa = demandVa
      .plus(heatingCoolingVa)
      .plus(motorVa)
      .plus(motorAdditional)
      .plus(other_loads_va);

    // Step 7: Service/Feeder sizing
    const requiredAmperage240v = totalComputedVa.div(240);
    const requiredAmperage208v = totalComputedVa.div(208);

    // Standard service sizes
    const serviceSizes = [100, 125, 150, 200, 225, 300, 400, 600, 800, 1000, 1200];
    const recommendedService240v = serviceSizes.find(size => size >= requiredAmperage240v.toNumber()) || 1200;
    const recommendedService208v = serviceSizes.find(size => size >= requiredAmperage208v.toNumber()) || 1200;

    const result = {
      general_lighting_va: generalLightingVa.toNumber(),
      small_appliance_va: smallApplianceVa.toNumber(),
      laundry_va: laundryVa.toNumber(),
      demand_factor_applied: demandFactorApplied,
      demand_factor: demandFactorApplied ? demandVa.div(totalGeneralVa).toNumber() : 1,
      heating_cooling_va: heatingCoolingVa.toNumber(),
      largest_motor_va: motorVa.toNumber(),
      motor_additional_va: motorAdditional.toNumber(),
      total_computed_va: totalComputedVa.toNumber(),
      total_demand_va: totalComputedVa.toNumber(),
      required_amperage_240v: requiredAmperage240v.toNumber(),
      required_amperage_208v: requiredAmperage208v.toNumber(),
      recommended_service_240v: recommendedService240v,
      recommended_service_208v: recommendedService208v,
      necReferences: ['220.12', '220.42', '220.50', '220.52', '220.53'],
    };

    // Log calculation for auditing
    await this.prisma.calculationLog.create({
      data: {
        type: 'LOAD_CALCULATION',
        input: params,
        output: result,
        nec_references: result.necReferences,
      },
    });

    return result;
  }

  // Voltage drop calculation
  private async calculateVoltageDrop(params: any): Promise<any> {
    const {
      conductor_material,
      wire_size,
      voltage,
      phase,
      length_feet,
      load_amps,
      power_factor = 0.9,
      ambient_temp_f = 86,
    } = params;

    // Get conductor properties
    const properties = COPPER_PROPERTIES[wire_size as keyof typeof COPPER_PROPERTIES];
    if (!properties) {
      throw new Error(`Invalid wire size: ${wire_size}`);
    }

    // Calculate resistance and reactance
    const resistance = new Decimal(properties.resistance_ohms_per_1000ft);
    const reactance = new Decimal(properties.reactance_ohms_per_1000ft);

    // Adjust for temperature if needed
    let tempFactor = 1;
    if (ambient_temp_f > 86) {
      tempFactor = 1 + (0.00393 * (ambient_temp_f - 86));
    }

    // Calculate total impedance
    const circuitLength = new Decimal(length_feet).div(1000);
    const multiplier = phase === 'THREE' ? Math.sqrt(3) : 2;
    
    const totalResistance = resistance.times(circuitLength).times(multiplier).times(tempFactor);
    const totalReactance = reactance.times(circuitLength).times(multiplier);

    // Calculate voltage drop
    const current = new Decimal(load_amps);
    const cosPhi = new Decimal(power_factor);
    const sinPhi = new Decimal(Math.sqrt(1 - power_factor ** 2));

    const voltageDrop = current.times(
      totalResistance.times(cosPhi).plus(totalReactance.times(sinPhi))
    );

    const voltageDropPercent = voltageDrop.div(voltage).times(100);
    const voltageAtLoad = new Decimal(voltage).minus(voltageDrop);

    // Power loss calculation
    const powerLossWatts = current.pow(2).times(totalResistance);

    // Generate recommendations
    const recommendations: string[] = [];
    if (voltageDropPercent.gt(3)) {
      recommendations.push('Voltage drop exceeds 3% recommendation for branch circuits');
      recommendations.push('Consider using larger wire size or shorter circuit length');
    }
    if (voltageDropPercent.gt(5)) {
      recommendations.push('Voltage drop exceeds 5% limit for combined feeder and branch circuits');
    }

    const result = {
      voltageDrop: voltageDrop.toNumber(),
      voltageDropPercent: voltageDropPercent.toNumber(),
      voltageAtLoad: voltageAtLoad.toNumber(),
      powerLossWatts: powerLossWatts.toNumber(),
      wireResistance: resistance.toNumber(),
      wireReactance: reactance.toNumber(),
      totalResistance: totalResistance.toNumber(),
      totalReactance: totalReactance.toNumber(),
      totalImpedance: totalResistance.pow(2).plus(totalReactance.pow(2)).sqrt().toNumber(),
      temperatureFactor: tempFactor,
      bundlingFactor: 1, // Simplified
      recommendations,
      necReferences: ['215.2(A)(1)', '210.19(A)(1)', '310.15'],
    };

    return result;
  }

  // Conduit fill calculation
  private async calculateConduitFill(params: any): Promise<any> {
    const { conduit_type, conduit_size, conductors } = params;

    // Get conduit area
    const conduitArea = CONDUIT_AREA[conduit_type]?.[conduit_size];
    if (!conduitArea) {
      throw new Error(`Invalid conduit type/size: ${conduit_type} ${conduit_size}`);
    }

    // Calculate total conductor area
    let totalConductorArea = new Decimal(0);
    const conductorDetails = [];

    for (const conductor of conductors) {
      const area = CONDUCTOR_AREA[conductor.type]?.[conductor.size];
      if (!area) {
        throw new Error(`Invalid conductor: ${conductor.type} ${conductor.size}`);
      }

      const conductorArea = new Decimal(area).times(conductor.count);
      totalConductorArea = totalConductorArea.plus(conductorArea);

      conductorDetails.push({
        ...conductor,
        area: area,
        totalArea: conductorArea.toNumber(),
      });
    }

    // Determine maximum fill percentage based on conductor count
    const totalConductors = conductors.reduce((sum: number, c: any) => sum + c.count, 0);
    let maxFillPercentage = 40; // Default for more than 2 conductors
    if (totalConductors === 1) maxFillPercentage = 53;
    else if (totalConductors === 2) maxFillPercentage = 31;

    // Calculate fill percentage
    const fillPercentage = totalConductorArea.div(conduitArea).times(100);
    const availableArea = new Decimal(conduitArea).times(maxFillPercentage / 100);
    const remainingArea = availableArea.minus(totalConductorArea);

    // Generate recommendations
    const recommendations: string[] = [];
    if (fillPercentage.gt(maxFillPercentage)) {
      recommendations.push(`Conduit fill exceeds ${maxFillPercentage}% limit`);
      recommendations.push('Use larger conduit size or multiple conduits');
    }

    const result = {
      conduitArea: conduitArea,
      totalConductorArea: totalConductorArea.toNumber(),
      fillPercentage: fillPercentage.toNumber(),
      maxFillPercentage,
      availableArea: availableArea.toNumber(),
      remainingArea: remainingArea.toNumber(),
      conductors: conductorDetails,
      recommendations,
      necReferences: ['Chapter 9 Table 1', 'Chapter 9 Table 4', 'Chapter 9 Table 5'],
    };

    return result;
  }

  // Wire size calculation
  private async calculateWireSize(params: any): Promise<any> {
    const {
      load_amps,
      voltage,
      phase,
      conductor_material = 'COPPER',
      length_feet,
      voltage_drop_limit = 3,
      continuous_load = false,
      motor_load = false,
      ambient_temp_f = 86,
      conductors_in_conduit = 3,
    } = params;

    // Apply continuous load factor
    let adjustedLoad = new Decimal(load_amps);
    const factors = [];
    
    if (continuous_load) {
      adjustedLoad = adjustedLoad.times(1.25);
      factors.push('125% continuous load factor applied (NEC 215.2)');
    }
    
    if (motor_load) {
      adjustedLoad = adjustedLoad.times(1.25);
      factors.push('125% motor load factor applied (NEC 430.22)');
    }

    // Temperature derating
    let tempFactor = 1;
    if (ambient_temp_f > 86) {
      if (ambient_temp_f <= 95) tempFactor = 0.96;
      else if (ambient_temp_f <= 104) tempFactor = 0.91;
      else if (ambient_temp_f <= 113) tempFactor = 0.87;
      else if (ambient_temp_f <= 122) tempFactor = 0.82;
      else tempFactor = 0.76;
      factors.push(`Temperature derating: ${(tempFactor * 100).toFixed(0)}%`);
    }

    // Bundling derating
    let bundlingFactor = 1;
    if (conductors_in_conduit > 3) {
      if (conductors_in_conduit <= 6) bundlingFactor = 0.8;
      else if (conductors_in_conduit <= 9) bundlingFactor = 0.7;
      else if (conductors_in_conduit <= 20) bundlingFactor = 0.5;
      else bundlingFactor = 0.45;
      factors.push(`Bundling derating: ${(bundlingFactor * 100).toFixed(0)}%`);
    }

    // Find minimum wire size for ampacity
    const requiredAmpacity = adjustedLoad.div(tempFactor).div(bundlingFactor);
    const ampacityTable = conductor_material === 'COPPER' ? COPPER_AMPACITY_75C : COPPER_AMPACITY_75C;
    
    let selectedSize = '1000';
    let selectedAmpacity = 0;
    
    for (const [size, ampacity] of Object.entries(ampacityTable)) {
      if (ampacity >= requiredAmpacity.toNumber()) {
        selectedSize = size;
        selectedAmpacity = ampacity;
        break;
      }
    }

    // Check voltage drop
    const vdParams = {
      conductor_material,
      wire_size: selectedSize,
      voltage,
      phase,
      length_feet,
      load_amps: load_amps,
      power_factor: 0.9,
    };
    
    const vdResult = await this.calculateVoltageDrop(vdParams);
    
    // If voltage drop exceeds limit, increase wire size
    if (vdResult.voltageDropPercent > voltage_drop_limit) {
      factors.push('Wire size increased to meet voltage drop requirements');
      // Find next larger size
      const sizes = Object.keys(ampacityTable);
      const currentIndex = sizes.indexOf(selectedSize);
      if (currentIndex > 0) {
        selectedSize = sizes[currentIndex - 1];
        selectedAmpacity = ampacityTable[selectedSize as keyof typeof ampacityTable];
      }
    }

    // Determine ground wire size
    const groundSize = this.determineGroundWireSize(adjustedLoad.toNumber());

    const result = {
      baseLoadAmps: load_amps,
      adjustedLoadAmps: adjustedLoad.toNumber(),
      requiredAmpacity: requiredAmpacity.toNumber(),
      phaseWireSize: selectedSize,
      neutralWireSize: selectedSize,
      groundWireSize: groundSize,
      selectedAmpacity,
      temperatureFactor: tempFactor,
      bundlingFactor,
      deratedAmpacity: selectedAmpacity * tempFactor * bundlingFactor,
      voltageDropPercent: vdResult.voltageDropPercent,
      voltageDropLimit,
      voltageAtLoad: vdResult.voltageAtLoad,
      factors,
      necReferences: ['310.16', '310.15(B)(2)', '310.15(B)(3)', '250.122'],
    };

    return result;
  }

  // Determine ground wire size based on overcurrent protection
  private determineGroundWireSize(amperage: number): string {
    if (amperage <= 15) return '14';
    if (amperage <= 20) return '12';
    if (amperage <= 30) return '10';
    if (amperage <= 40) return '10';
    if (amperage <= 60) return '10';
    if (amperage <= 100) return '8';
    if (amperage <= 200) return '6';
    if (amperage <= 300) return '4';
    if (amperage <= 400) return '3';
    return '2';
  }

  // Generate complete estimate
  private async generateEstimate(data: any): Promise<any> {
    const { 
      customer_id, 
      project_id, 
      calculations, 
      materials, 
      labor_hours, 
      markup_percent = 35,
      tax_rate = 8.25 
    } = data;

    try {
      // Calculate material costs
      let materialSubtotal = new Decimal(0);
      const materialItems = [];

      for (const material of materials) {
        const cost = new Decimal(material.quantity).times(material.unit_price);
        materialSubtotal = materialSubtotal.plus(cost);
        
        materialItems.push({
          material_id: material.id,
          description: material.description,
          quantity: material.quantity,
          unit_price: material.unit_price,
          total_price: cost.toNumber(),
        });
      }

      // Calculate labor costs
      const laborRate = 85; // Default hourly rate
      const laborSubtotal = new Decimal(labor_hours).times(laborRate);

      // Apply markup
      const subtotal = materialSubtotal.plus(laborSubtotal);
      const markupAmount = subtotal.times(markup_percent / 100);
      const subtotalWithMarkup = subtotal.plus(markupAmount);

      // Calculate tax
      const taxAmount = subtotalWithMarkup.times(tax_rate / 100);
      const total = subtotalWithMarkup.plus(taxAmount);

      // Create estimate in database
      const estimate = await this.prisma.estimate.create({
        data: {
          customer_id,
          project_id,
          estimate_number: `EST-${Date.now()}`,
          status: 'DRAFT',
          subtotal: subtotal.toNumber(),
          tax_rate: tax_rate,
          tax_amount: taxAmount.toNumber(),
          total: total.toNumber(),
          valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          material_items: {
            create: materialItems,
          },
          labor_items: {
            create: [{
              description: 'Electrical Installation Labor',
              hours: labor_hours,
              rate: laborRate,
              total_price: laborSubtotal.toNumber(),
            }],
          },
        },
        include: {
          material_items: true,
          labor_items: true,
          customer: true,
          project: true,
        },
      });

      return estimate;
    } catch (error) {
      await this.log('Failed to generate estimate', { 
        level: 'error', 
        error: (error as Error).message 
      });
      throw error;
    }
  }

  // Optimize database query
  private async optimizeQuery(data: z.infer<typeof optimizationSchema>): Promise<any> {
    const { query, explain } = data;
    
    // Analyze query patterns from memory
    const queryPatterns = await this.retrieveKnowledge(['query', 'pattern'], 100);
    
    // Find similar queries
    const similarQueries = queryPatterns.filter(item => {
      const content = item.content;
      return content.entity && content.operation;
    });
    
    // Generate optimization suggestions
    const suggestions = [];
    
    // Analyze for missing indexes
    const frequentFilters: Record<string, number> = {};
    similarQueries.forEach(item => {
      if (item.content.hasWhere) {
        const key = `${item.content.entity}_where`;
        frequentFilters[key] = (frequentFilters[key] || 0) + 1;
      }
    });
    
    // Suggest indexes for frequently filtered entities
    Object.entries(frequentFilters).forEach(([key, count]) => {
      if (count > 10) {
        const entity = key.split('_')[0];
        suggestions.push(`Consider adding index on frequently queried ${entity} fields`);
      }
    });
    
    // Check for N+1 queries
    const includePatterns = similarQueries.filter(q => q.content.hasInclude);
    if (includePatterns.length > 20) {
      suggestions.push('Consider using include to avoid N+1 queries');
    }
    
    return {
      query,
      suggestions,
      queryPatterns: {
        totalQueries: similarQueries.length,
        byEntity: this.groupByEntity(similarQueries),
        frequentFilters,
      },
    };
  }

  // Backup database
  private async backupDatabase(): Promise<any> {
    try {
      // Export all data
      const backup = {
        timestamp: new Date(),
        data: {
          customers: await this.prisma.customer.findMany(),
          projects: await this.prisma.project.findMany(),
          estimates: await this.prisma.estimate.findMany({
            include: { material_items: true, labor_items: true },
          }),
          materials: await this.prisma.material.findMany(),
          calculations: await this.prisma.calculationLog.findMany(),
        },
      };
      
      // Store backup in memory for now (in production, save to file/S3)
      await this.storeKnowledge(
        backup,
        ['database', 'backup', new Date().toISOString()],
        1.0 // High importance
      );
      
      return {
        success: true,
        timestamp: backup.timestamp,
        recordCounts: {
          customers: backup.data.customers.length,
          projects: backup.data.projects.length,
          estimates: backup.data.estimates.length,
          materials: backup.data.materials.length,
          calculations: backup.data.calculations.length,
        },
      };
    } catch (error) {
      await this.log('Database backup failed', { 
        level: 'error', 
        error: (error as Error).message 
      });
      throw error;
    }
  }

  // Analyze database schema
  private async analyzeSchema(): Promise<any> {
    const analysis = {
      tables: [] as any[],
      recommendations: [] as string[],
      statistics: {} as Record<string, any>,
    };
    
    // Get table information
    const tables = ['customer', 'project', 'estimate', 'material', 'calculationLog'];
    
    for (const table of tables) {
      const count = await (this.prisma as any)[table].count();
      const sample = await (this.prisma as any)[table].findFirst();
      
      analysis.tables.push({
        name: table,
        recordCount: count,
        fields: sample ? Object.keys(sample) : [],
      });
      
      // Generate recommendations based on record count
      if (count > 10000) {
        analysis.recommendations.push(
          `Table ${table} has ${count} records - consider partitioning or archiving old data`
        );
      }
    }
    
    // Check for missing indexes
    analysis.recommendations.push(
      'Ensure indexes exist on frequently queried fields: customer.email, project.status, estimate.customer_id'
    );
    
    return analysis;
  }

  // Helper methods
  private invalidateCache(entity: string): void {
    // Remove all cache entries for this entity
    for (const key of this.queryCache.keys()) {
      if (key.includes(`"entity":"${entity}"`)) {
        this.queryCache.delete(key);
      }
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.queryCache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.queryCache.delete(key);
      }
    }
  }

  private groupByEntity(queries: any[]): Record<string, number> {
    const grouped: Record<string, number> = {};
    queries.forEach(q => {
      const entity = q.content.entity;
      grouped[entity] = (grouped[entity] || 0) + 1;
    });
    return grouped;
  }

  private async preloadCommonQueries(): Promise<void> {
    // Preload frequently used data
    try {
      const customers = await this.prisma.customer.findMany({ take: 100 });
      const materials = await this.prisma.material.findMany({ 
        where: { in_stock: true },
        take: 500,
      });
      
      await this.log('Preloaded common queries', { 
        level: 'info',
        customers: customers.length,
        materials: materials.length,
      });
    } catch (error) {
      await this.log('Failed to preload queries', { 
        level: 'warn', 
        error: (error as Error).message 
      });
    }
  }

  // Override shutdown to close database connection
  async shutdown(): Promise<void> {
    await this.prisma.$disconnect();
    await super.shutdown();
  }
}