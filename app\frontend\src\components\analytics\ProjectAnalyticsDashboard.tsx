import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Treemap,
  ScatterChart,
  Scatter,
  ZAxis,
} from 'recharts';
import { Card } from '../ui/card';
import { analyticsService } from '../../services/analyticsService';
import { format } from 'date-fns';
import { MapPin, TrendingUp, Clock, DollarSign, AlertCircle } from 'lucide-react';

interface ProjectAnalyticsDashboardProps {
  dateRange: { start: Date; end: Date };
}

const ProjectAnalyticsDashboard: React.FC<ProjectAnalyticsDashboardProps> = ({ dateRange }) => {
  const [selectedProjectType, setSelectedProjectType] = useState<string>('all');

  // Fetch project analytics
  const { data: projectData } = useQuery({
    queryKey: ['project-analytics-detailed', dateRange, selectedProjectType],
    queryFn: () => analyticsService.getProjectAnalytics({
      dateRange,
      projectType: selectedProjectType === 'all' ? undefined : selectedProjectType,
    }),
  });

  // Fetch project pipeline data
  const { data: pipelineData } = useQuery({
    queryKey: ['project-pipeline', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { status: 'Planning', count: 12, value: 450000 },
        { status: 'Approved', count: 8, value: 320000 },
        { status: 'In Progress', count: 15, value: 680000 },
        { status: 'Completed', count: 42, value: 1890000 },
        { status: 'On Hold', count: 3, value: 120000 },
      ];
    },
  });

  // Fetch geographic distribution
  const { data: geoData } = useQuery({
    queryKey: ['project-geo-distribution', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { city: 'Los Angeles', projects: 23, revenue: 890000, lat: 34.05, lng: -118.24 },
        { city: 'San Francisco', projects: 18, revenue: 720000, lat: 37.77, lng: -122.42 },
        { city: 'San Diego', projects: 15, revenue: 580000, lat: 32.71, lng: -117.16 },
        { city: 'Sacramento', projects: 12, revenue: 450000, lat: 38.58, lng: -121.49 },
        { city: 'San Jose', projects: 10, revenue: 390000, lat: 37.33, lng: -121.89 },
      ];
    },
  });

  // Fetch time vs budget analysis
  const { data: timeBudgetData } = useQuery({
    queryKey: ['time-budget-analysis', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return Array.from({ length: 50 }, (_, i) => ({
        projectId: `PRJ-${i + 1}`,
        plannedDuration: Math.floor(Math.random() * 60) + 10,
        actualDuration: Math.floor(Math.random() * 70) + 5,
        plannedBudget: Math.floor(Math.random() * 100000) + 20000,
        actualBudget: Math.floor(Math.random() * 110000) + 15000,
        type: ['Residential', 'Commercial', 'Industrial'][Math.floor(Math.random() * 3)],
      }));
    },
  });

  // Fetch change order impact
  const { data: changeOrderData } = useQuery({
    queryKey: ['change-order-impact', dateRange],
    queryFn: async () => {
      // Mock data - replace with actual API call
      return [
        { month: 'Jan', changeOrders: 8, impactPercentage: 12 },
        { month: 'Feb', changeOrders: 12, impactPercentage: 18 },
        { month: 'Mar', changeOrders: 6, impactPercentage: 9 },
        { month: 'Apr', changeOrders: 15, impactPercentage: 22 },
        { month: 'May', changeOrders: 10, impactPercentage: 15 },
        { month: 'Jun', changeOrders: 9, impactPercentage: 13 },
      ];
    },
  });

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6366F1'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <div className="space-y-6">
      {/* Project Type Filter */}
      <div className="flex items-center gap-4">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Project Type:
        </label>
        <select
          value={selectedProjectType}
          onChange={(e) => setSelectedProjectType(e.target.value)}
          className="block w-48 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        >
          <option value="all">All Types</option>
          <option value="RESIDENTIAL">Residential</option>
          <option value="COMMERCIAL">Commercial</option>
          <option value="INDUSTRIAL">Industrial</option>
        </select>
      </div>

      {/* Project Pipeline Visualization */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Project Pipeline
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={pipelineData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="status" />
            <YAxis yAxisId="left" orientation="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip
              formatter={(value: any, name: string) => {
                if (name === 'value') return formatCurrency(value);
                return value;
              }}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="count" fill="#3B82F6" name="Project Count" />
            <Bar yAxisId="right" dataKey="value" fill="#10B981" name="Total Value" />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Profitability Analysis */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Profitability by Project Type
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={projectData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="projectType" />
              <PolarRadiusAxis angle={90} domain={[0, 30]} />
              <Radar
                name="Profit Margin %"
                dataKey="profitMargin"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.6}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </Card>

        {/* Project Duration Analysis */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Average Project Duration
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={projectData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="projectType" />
              <YAxis />
              <Tooltip formatter={(value) => `${value} days`} />
              <Bar dataKey="avgDuration" fill="#F59E0B">
                {projectData?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Time vs Budget Scatter Plot */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Time vs Budget Performance
        </h3>
        <ResponsiveContainer width="100%" height={400}>
          <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid />
            <XAxis
              type="number"
              dataKey="plannedDuration"
              name="Planned Duration"
              unit=" days"
            />
            <YAxis
              type="number"
              dataKey="actualDuration"
              name="Actual Duration"
              unit=" days"
            />
            <ZAxis
              type="number"
              dataKey="actualBudget"
              range={[50, 400]}
              name="Budget"
            />
            <Tooltip
              cursor={{ strokeDasharray: '3 3' }}
              formatter={(value: any, name: string) => {
                if (name === 'Budget') return formatCurrency(value);
                return `${value} days`;
              }}
            />
            <Legend />
            <Scatter
              name="Projects"
              data={timeBudgetData}
              fill="#8884d8"
              shape="circle"
            />
            {/* Reference line for on-time delivery */}
            <Line
              type="monotone"
              dataKey={(data: any) => data.plannedDuration}
              stroke="#ff0000"
              strokeDasharray="5 5"
              dot={false}
            />
          </ScatterChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Change Order Impact */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Change Order Impact Analysis
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={changeOrderData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="changeOrders" fill="#3B82F6" name="Change Orders" />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="impactPercentage"
                stroke="#EF4444"
                name="Budget Impact %"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>

        {/* Geographic Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Geographic Distribution
          </h3>
          <div className="space-y-3">
            {geoData?.map((city, index) => (
              <div key={city.city} className="flex items-center justify-between">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-700 dark:text-gray-300">{city.city}</span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-500">{city.projects} projects</span>
                  <span className="font-semibold">{formatCurrency(city.revenue)}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Project Type Distribution Treemap */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Project Revenue Distribution
        </h3>
        <ResponsiveContainer width="100%" height={400}>
          <Treemap
            data={projectData}
            dataKey="revenue"
            aspectRatio={4 / 3}
            stroke="#fff"
            fill="#8884d8"
          >
            <Tooltip formatter={(value) => formatCurrency(value as number)} />
          </Treemap>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default ProjectAnalyticsDashboard;