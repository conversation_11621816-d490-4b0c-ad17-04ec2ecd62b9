import { Decimal } from 'decimal.js';

// NEC 2023 Constants and Tables

// General lighting loads per NEC Table 220.12
export const GENERAL_LIGHTING_LOADS = {
  DWELLING: 3, // VA per sq ft
  OFFICE: 3.5,
  STORE: 3,
  WAREHOUSE: 0.25,
  GARAGE: 0.5,
  HOSPITAL: 2,
  HOTEL: 2,
  SCHOOL: 3,
  RESTAURANT: 2
} as const;

// Standard service sizes in amperes
export const STANDARD_SERVICE_SIZES = [100, 125, 150, 200, 225, 320, 400, 600, 800, 1000, 1200, 1600, 2000, 2500, 3000] as const;

// Voltage systems
export const VOLTAGE_SYSTEMS = {
  '120/240V_1PH': { voltage: 240, phases: 1, lineToNeutral: 120 },
  '208V_3PH': { voltage: 208, phases: 3, lineToNeutral: 120 },
  '240V_3PH': { voltage: 240, phases: 3, lineToNeutral: null },
  '480V_3PH': { voltage: 480, phases: 3, lineToNeutral: 277 },
  '277/480V_3PH': { voltage: 480, phases: 3, lineToNeutral: 277 }
} as const;

// Wire ampacity table (simplified from NEC Table 310.16)
// Temperature rating: 75°C (167°F), Copper conductors
export const COPPER_AMPACITY_75C = {
  '14': 20,
  '12': 25,
  '10': 35,
  '8': 50,
  '6': 65,
  '4': 85,
  '3': 100,
  '2': 115,
  '1': 130,
  '1/0': 150,
  '2/0': 175,
  '3/0': 200,
  '4/0': 230,
  '250': 255,
  '300': 285,
  '350': 310,
  '400': 335,
  '500': 380,
  '600': 420,
  '700': 460,
  '750': 475,
  '800': 490,
  '900': 520,
  '1000': 545
} as const;

// Aluminum ampacity table (75°C)
export const ALUMINUM_AMPACITY_75C = {
  '12': 20,
  '10': 30,
  '8': 40,
  '6': 50,
  '4': 65,
  '3': 75,
  '2': 90,
  '1': 100,
  '1/0': 120,
  '2/0': 135,
  '3/0': 155,
  '4/0': 180,
  '250': 205,
  '300': 230,
  '350': 250,
  '400': 270,
  '500': 310,
  '600': 340,
  '700': 375,
  '750': 385,
  '800': 395,
  '900': 425,
  '1000': 445
} as const;

// Conduit fill percentages per NEC Chapter 9
export const CONDUIT_FILL_PERCENT = {
  1: 0.53, // 1 conductor
  2: 0.31, // 2 conductors
  OVER_2: 0.40 // More than 2 conductors
} as const;

// EMT conduit dimensions (internal diameter in inches)
export const EMT_CONDUIT_ID = {
  '1/2': 0.622,
  '3/4': 0.824,
  '1': 1.049,
  '1-1/4': 1.380,
  '1-1/2': 1.610,
  '2': 2.067,
  '2-1/2': 2.731,
  '3': 3.356,
  '3-1/2': 3.834,
  '4': 4.334
} as const;

// Wire area in square inches (Chapter 9, Table 5)
export const THHN_WIRE_AREA = {
  '14': 0.0097,
  '12': 0.0133,
  '10': 0.0211,
  '8': 0.0366,
  '6': 0.0507,
  '4': 0.0824,
  '3': 0.0973,
  '2': 0.1158,
  '1': 0.1562,
  '1/0': 0.1855,
  '2/0': 0.2223,
  '3/0': 0.2679,
  '4/0': 0.3237,
  '250': 0.3970,
  '300': 0.4608,
  '350': 0.5242,
  '400': 0.5863,
  '500': 0.7073,
  '600': 0.8676,
  '700': 0.9887,
  '750': 1.0496,
  '800': 1.1085,
  '900': 1.2311,
  '1000': 1.3478
} as const;

// Demand factors for dwelling units (NEC 220.82)
export const DWELLING_DEMAND_FACTORS = [
  { min: 0, max: 3000, factor: 1.00 },
  { min: 3001, max: 120000, factor: 0.35 },
  { min: 120001, max: Infinity, factor: 0.25 }
] as const;

// Voltage drop limits
export const VOLTAGE_DROP_LIMITS = {
  BRANCH_CIRCUIT: 0.03, // 3%
  FEEDER: 0.03, // 3%
  TOTAL: 0.05 // 5% combined
} as const;

// Power factors for common loads
export const TYPICAL_POWER_FACTORS = {
  INCANDESCENT: 1.0,
  FLUORESCENT: 0.85,
  LED: 0.9,
  MOTOR_LOADED: 0.85,
  MOTOR_UNLOADED: 0.5,
  GENERAL: 0.9
} as const;

// Temperature correction factors (NEC Table 310.15(B)(1)(1))
export const TEMP_CORRECTION_FACTORS = {
  '60': 1.29,
  '65': 1.22,
  '70': 1.15,
  '75': 1.08,
  '80': 1.00,
  '85': 0.91,
  '90': 0.82,
  '95': 0.71,
  '100': 0.58
} as const;

// Labor productivity factors (units per hour)
export const LABOR_PRODUCTIVITY = {
  RECEPTACLE: 4, // Devices per hour
  SWITCH: 4,
  LIGHT_FIXTURE: 2,
  PANEL_CIRCUIT: 2, // Circuits terminated per hour
  CONDUIT_EMT: 40, // Feet per hour
  WIRE_PULL: 100, // Feet per hour
  JUNCTION_BOX: 3 // Boxes per hour
} as const;

// Material waste factors
export const MATERIAL_WASTE_FACTORS = {
  WIRE: 0.10, // 10%
  CONDUIT: 0.05, // 5%
  FITTINGS: 0.03, // 3%
  DEVICES: 0.02, // 2%
  FIXTURES: 0.01 // 1%
} as const;

// Safety margins
export const SAFETY_MARGINS = {
  AMPACITY: 0.80, // 80% rule for continuous loads
  BREAKER_SIZE: 1.25, // 125% for continuous loads
  TRANSFORMER: 1.25,
  GENERATOR: 1.10
} as const;

// Helper function to calculate voltage drop
export function calculateVoltageDrop(
  current: number,
  distance: number,
  voltage: number,
  wireSize: string,
  isCoppper: boolean = true,
  is3Phase: boolean = false,
  powerFactor: number = 0.9
): { voltageDrop: number, percentDrop: number } {
  // Get resistance from NEC Chapter 9, Table 8
  // Simplified - using approximate values
  const resistance = getWireResistance(wireSize, isCoppper);
  
  // Calculate voltage drop
  // Single phase: VD = 2 × I × R × L / 1000
  // Three phase: VD = √3 × I × R × L / 1000
  const multiplier = is3Phase ? Math.sqrt(3) : 2;
  const vd = new Decimal(multiplier)
    .times(current)
    .times(resistance)
    .times(distance)
    .times(powerFactor)
    .dividedBy(1000);
  
  const percentDrop = vd.dividedBy(voltage).times(100);
  
  return {
    voltageDrop: vd.toNumber(),
    percentDrop: percentDrop.toNumber()
  };
}

// Wire resistance in ohms per 1000 feet
function getWireResistance(wireSize: string, isCoppper: boolean): number {
  // Simplified resistance values at 75°C
  const copperResistance: Record<string, number> = {
    '14': 3.14,
    '12': 1.98,
    '10': 1.24,
    '8': 0.778,
    '6': 0.491,
    '4': 0.308,
    '2': 0.194,
    '1': 0.154,
    '1/0': 0.122,
    '2/0': 0.0967,
    '3/0': 0.0766,
    '4/0': 0.0608,
    '250': 0.0515,
    '300': 0.0429,
    '350': 0.0367,
    '400': 0.0321,
    '500': 0.0258,
    '600': 0.0214,
    '750': 0.0171,
    '1000': 0.0129
  };
  
  // Aluminum is approximately 1.6x copper resistance
  const resistance = copperResistance[wireSize] || 0.1;
  return isCoppper ? resistance : resistance * 1.6;
}